# Implementação da Feature de Grupos de Estudo

Este documento descreve a implementação da feature de Grupos de Estudo no aplicativo Lia.

## Estrutura do Banco de Dados

A feature de Grupos de Estudo utiliza as seguintes tabelas no Supabase:

1. `study_groups` - Armazena informações sobre os grupos de estudo
2. `study_group_members` - Armazena os membros de cada grupo
3. `study_group_materials` - Armazena os materiais compartilhados em cada grupo
4. `study_group_invites` - Armazena os convites para os grupos

As tabelas são criadas e gerenciadas diretamente no painel de administração do Supabase.

## Estrutura de Arquivos

- `store/studyGroupStore.ts` - Store Zustand para gerenciar o estado e as operações CRUD dos grupos de estudo
- `app/(tabs)/study-groups.tsx` - Página principal de grupos de estudo
- `app/study-groups/[id].tsx` - Página de detalhes de um grupo de estudo
- `app/study-groups/create.tsx` - Página para criar um novo grupo de estudo
- `app/study-groups/invites.tsx` - Página para gerenciar convites de um grupo
- `app/study-groups/timer.tsx` - Página do timer de estudo em grupo
- `app/study-groups/materials/add.tsx` - Página para adicionar materiais a um grupo
- `app/study-groups/materials/[id].tsx` - Página de detalhes de um material
- `components/StudyGroupCard.tsx` - Componente para exibir um card de grupo de estudo
- `components/StudyGroupMemberCard.tsx` - Componente para exibir um card de membro de grupo
- `components/StudyGroupMaterialCard.tsx` - Componente para exibir um card de material de grupo
- `components/StudyGroupTimer.tsx` - Componente do timer Pomodoro para estudo em grupo

## Funcionalidades Implementadas

### Grupos de Estudo
- Criação de grupos de estudo
- Visualização de grupos de estudo
- Edição de grupos de estudo
- Exclusão de grupos de estudo
- Entrada em grupos via código de convite

### Membros
- Visualização de membros de um grupo
- Adição de membros a um grupo
- Remoção de membros de um grupo
- Atualização de função de membros (admin, moderador, membro)
- Saída de um grupo

### Materiais
- Adição de materiais a um grupo
- Visualização de materiais de um grupo
- Edição de materiais
- Exclusão de materiais
- Suporte a diferentes tipos de materiais (notas, flashcards, documentos, links)

### Timer de Estudo
- Timer Pomodoro para estudo em grupo com modos de foco, pausa curta e pausa longa
- Registro automático de tempo de estudo por membro
- Estatísticas de estudo detalhadas
- Sistema de níveis e ranking baseado no tempo de estudo e contribuições

### Convites
- Geração de códigos de convite
- Envio de convites por e-mail
- Aceitação/rejeição de convites

## Políticas de Segurança

As políticas de segurança RLS (Row Level Security) do Supabase garantem que:

1. Usuários só podem ver grupos públicos ou grupos dos quais são membros
2. Apenas administradores podem editar ou excluir grupos
3. Membros só podem ver e adicionar materiais aos grupos dos quais são membros
4. Usuários só podem editar ou excluir seus próprios materiais (ou administradores podem editar/excluir qualquer material)
5. Usuários só podem ver convites para grupos dos quais são membros

## Correção do Erro de Recursão Infinita

O erro de recursão infinita nas políticas da tabela `study_group_members` foi corrigido modificando a política para evitar referências circulares. A política agora usa uma abordagem diferente para verificar a associação do usuário ao grupo.

## Funcionalidades Implementadas Recentemente

1. Sistema de ranking e níveis para membros baseado em tempo de estudo e contribuições
2. Timer Pomodoro avançado com modos de foco, pausa curta e pausa longa
3. Exibição de estatísticas de estudo detalhadas por membro
4. Integração completa com o Supabase para persistência de dados

## Próximos Passos

1. Adicionar suporte a upload de arquivos para materiais
2. Implementar chat em tempo real para grupos
3. Adicionar notificações para atividades do grupo
4. Implementar sessões de estudo em grupo em tempo real
5. Adicionar gráficos de progresso e relatórios de estudo
