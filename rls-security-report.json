{"timestamp": "2025-05-27T13:42:02.568Z", "summary": {"total": 12, "secure": 7, "vulnerabilities": 5, "errors": 0, "status": "VULNERABLE"}, "details": {"selectTests": [{"table": "users", "status": "VULNERABLE", "message": "0 registros acessíveis"}, {"table": "flashcard_sets", "status": "VULNERABLE", "message": "0 registros acessíveis"}, {"table": "quizzes", "status": "VULNERABLE", "message": "0 registros acessíveis"}, {"table": "notes", "status": "VULNERABLE", "message": "0 registros acessíveis"}, {"table": "study_groups", "status": "SECURE", "message": "infinite recursion detected in policy for relation \"study_group_members\""}, {"table": "flashcards", "status": "VULNERABLE", "message": "0 registros acessíveis"}], "fakeAuthTests": [{"table": "users", "status": "SECURE", "message": "Token falso rejeitado"}, {"table": "flashcard_sets", "status": "SECURE", "message": "Token falso rejeitado"}, {"table": "quizzes", "status": "SECURE", "message": "Token falso rejeitado"}], "insertTests": [{"table": "users", "operation": "INSERT", "status": "SECURE"}, {"table": "notes", "operation": "INSERT", "status": "SECURE"}, {"table": "quizzes", "operation": "INSERT", "status": "SECURE"}]}}