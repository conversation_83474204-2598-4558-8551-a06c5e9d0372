-- Final fix for study groups issues
-- This script ensures all RLS policies and functions are working correctly

-- 1. Ensure the check_group_membership function exists and works correctly
CREATE OR REPLACE FUNCTION check_group_membership(
  group_id_param UUID,
  user_id_param UUID
)
RETURNS TABLE(
  is_member BOOLEAN,
  is_admin BOOLEAN,
  is_public BOOLEAN
) AS $$
DECLARE
  group_record RECORD;
  member_record RECORD;
BEGIN
  -- Check if the group exists and get its properties
  SELECT is_open, admin_id INTO group_record
  FROM study_groups
  WHERE id = group_id_param;
  
  -- If the group doesn't exist, return false for everything
  IF group_record IS NULL THEN
    RETURN QUERY SELECT FALSE, FALSE, FALSE;
    RETURN;
  END IF;
  
  -- Check if the user is the admin of the group
  IF group_record.admin_id = user_id_param THEN
    RETURN QUERY SELECT TRUE, TRUE, group_record.is_open;
    RETURN;
  END IF;
  
  -- Check if the user is a member of the group
  SELECT * INTO member_record
  FROM study_group_members
  WHERE group_id = group_id_param AND user_id = user_id_param;
  
  -- Return the results
  IF member_record IS NOT NULL THEN
    RETURN QUERY SELECT TRUE, (member_record.role = 'admin'), group_record.is_open;
  ELSE
    -- User is not a member, but group might be public
    RETURN QUERY SELECT group_record.is_open, FALSE, group_record.is_open;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Ensure the get_study_group_details function exists
CREATE OR REPLACE FUNCTION get_study_group_details(
  group_id_param UUID
)
RETURNS TABLE(
  id UUID,
  name TEXT,
  description TEXT,
  cover_image TEXT,
  admin_id UUID,
  is_open BOOLEAN,
  invite_code TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  member_count BIGINT,
  materials_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sg.id,
    sg.name,
    sg.description,
    sg.cover_image,
    sg.admin_id,
    sg.is_open,
    sg.invite_code,
    sg.created_at,
    sg.updated_at,
    COALESCE((SELECT COUNT(*) FROM study_group_members WHERE group_id = sg.id), 0) as member_count,
    COALESCE((SELECT COUNT(*) FROM study_group_materials WHERE group_id = sg.id), 0) as materials_count
  FROM study_groups sg
  WHERE sg.id = group_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Ensure the get_study_group_members function exists
CREATE OR REPLACE FUNCTION get_study_group_members(
  group_id_param UUID
)
RETURNS SETOF study_group_members AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM study_group_members
  WHERE group_id = group_id_param
  ORDER BY 
    CASE 
      WHEN role = 'admin' THEN 1
      WHEN role = 'moderator' THEN 2
      ELSE 3
    END,
    joined_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Ensure the get_user_study_groups function exists
CREATE OR REPLACE FUNCTION get_user_study_groups(
  user_id_param UUID
)
RETURNS SETOF study_groups AS $$
BEGIN
  RETURN QUERY
  SELECT sg.*
  FROM study_groups sg
  WHERE sg.is_open = true
  OR sg.admin_id = user_id_param
  OR EXISTS (
    SELECT 1 FROM study_group_members sgm
    WHERE sgm.group_id = sg.id AND sgm.user_id = user_id_param
  )
  ORDER BY sg.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant execute permissions to public role
GRANT EXECUTE ON FUNCTION check_group_membership(UUID, UUID) TO public;
GRANT EXECUTE ON FUNCTION get_study_group_details(UUID) TO public;
GRANT EXECUTE ON FUNCTION get_study_group_members(UUID) TO public;
GRANT EXECUTE ON FUNCTION get_user_study_groups(UUID) TO public;

-- 6. Ensure RLS policies are optimized and don't cause recursion
-- Drop existing policies first
DROP POLICY IF EXISTS "Users can view groups they are members of or public groups" ON study_groups;
DROP POLICY IF EXISTS "Members can view other members in the same group" ON study_group_members;
DROP POLICY IF EXISTS "Users can view materials in groups they belong to" ON study_group_materials;

-- Create simplified RLS policies
CREATE POLICY "Users can view groups they are members of or public groups"
  ON study_groups FOR SELECT
  USING (
    is_open = true OR
    admin_id = auth.uid() OR
    id IN (
      SELECT group_id FROM study_group_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Members can view other members in the same group"
  ON study_group_members FOR SELECT
  USING (
    group_id IN (
      SELECT sg.id FROM study_groups sg
      WHERE sg.admin_id = auth.uid()
      OR sg.is_open = true
      OR EXISTS (
        SELECT 1 FROM study_group_members sgm2
        WHERE sgm2.group_id = sg.id AND sgm2.user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can view materials in groups they belong to"
  ON study_group_materials FOR SELECT
  USING (
    group_id IN (
      SELECT sg.id FROM study_groups sg
      WHERE sg.admin_id = auth.uid()
      OR sg.is_open = true
      OR EXISTS (
        SELECT 1 FROM study_group_members sgm
        WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
      )
    )
  );

-- 7. Create indexes for better performance if they don't exist
CREATE INDEX IF NOT EXISTS idx_study_group_members_group_user 
ON study_group_members(group_id, user_id);

CREATE INDEX IF NOT EXISTS idx_study_group_members_user_group 
ON study_group_members(user_id, group_id);

CREATE INDEX IF NOT EXISTS idx_study_groups_admin 
ON study_groups(admin_id);

CREATE INDEX IF NOT EXISTS idx_study_groups_open 
ON study_groups(is_open) WHERE is_open = true;

CREATE INDEX IF NOT EXISTS idx_study_group_materials_group 
ON study_group_materials(group_id);

-- 8. Enable RLS on all tables
ALTER TABLE study_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_group_materials ENABLE ROW LEVEL SECURITY;

-- 9. Test the functions (these are just examples, remove in production)
-- SELECT * FROM check_group_membership('your-group-id'::uuid, 'your-user-id'::uuid);
-- SELECT * FROM get_user_study_groups('your-user-id'::uuid);
