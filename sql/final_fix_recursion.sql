-- FINAL FIX: Eliminate Infinite Recursion in Study Groups
-- This script completely removes and recreates all problematic policies

-- 1. Remove ALL existing policies from study_group_members
DROP POLICY IF EXISTS "Membros podem ver outros membros do mesmo grupo" ON public.study_group_members;
DROP POLICY IF EXISTS "Apenas administradores podem adicionar membros" ON public.study_group_members;
DROP POLICY IF EXISTS "Administradores podem remover membros ou usuários podem sair" ON public.study_group_members;
DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios dados de membro" ON public.study_group_members;

-- 2. Temporarily disable <PERSON><PERSON> to avoid issues
ALTER TABLE public.study_group_members DISABLE ROW LEVEL SECURITY;

-- 3. Re-enable RLS
ALTER TABLE public.study_group_members ENABLE ROW LEVEL SECURITY;

-- 4. Create new, simple policies without recursion

-- Policy 1: SELECT - Allow viewing members
CREATE POLICY "allow_select_members" 
ON public.study_group_members
FOR SELECT
TO public
USING (
  -- Always allow user to see their own record
  user_id = auth.uid()
  OR
  -- Allow if user is admin of the group (direct check in study_groups table)
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id 
    AND sg.admin_id = auth.uid()
  )
  OR
  -- Allow if group is public
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id
    AND sg.is_open = true
  )
);

-- Policy 2: INSERT - Allow adding members
CREATE POLICY "allow_insert_members" 
ON public.study_group_members
FOR INSERT
TO public
WITH CHECK (
  -- Only group admins can add members
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = group_id 
    AND sg.admin_id = auth.uid()
  )
  OR
  -- Or user can add themselves to public groups
  (
    user_id = auth.uid()
    AND EXISTS (
      SELECT 1 FROM study_groups sg
      WHERE sg.id = group_id
      AND sg.is_open = true
    )
  )
);

-- Policy 3: UPDATE - Allow updating member data
CREATE POLICY "allow_update_members" 
ON public.study_group_members
FOR UPDATE
TO public
USING (
  -- User can update their own data
  user_id = auth.uid()
  OR
  -- Group admin can update any member's data
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id 
    AND sg.admin_id = auth.uid()
  )
);

-- Policy 4: DELETE - Allow removing members
CREATE POLICY "allow_delete_members" 
ON public.study_group_members
FOR DELETE
TO public
USING (
  -- User can leave group (remove themselves)
  user_id = auth.uid()
  OR
  -- Group admin can remove any member
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id 
    AND sg.admin_id = auth.uid()
  )
);

-- 5. Fix study_group_settings policies as well
DROP POLICY IF EXISTS "Membros podem ver configurações do grupo" ON public.study_group_settings;
DROP POLICY IF EXISTS "Apenas administradores podem editar configurações" ON public.study_group_settings;

CREATE POLICY "allow_select_settings" 
ON public.study_group_settings
FOR SELECT
TO public
USING (
  -- Group admin can see settings
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_settings.group_id 
    AND sg.admin_id = auth.uid()
  )
  OR
  -- Public groups allow viewing settings
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_settings.group_id
    AND sg.is_open = true
  )
);

CREATE POLICY "allow_insert_settings" 
ON public.study_group_settings
FOR INSERT
TO public
WITH CHECK (
  -- Only group admin can create settings
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = group_id 
    AND sg.admin_id = auth.uid()
  )
);

CREATE POLICY "allow_update_settings" 
ON public.study_group_settings
FOR UPDATE
TO public
USING (
  -- Only group admin can edit settings
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_settings.group_id 
    AND sg.admin_id = auth.uid()
  )
);

-- 6. Refresh schema cache
NOTIFY pgrst, 'reload schema';
