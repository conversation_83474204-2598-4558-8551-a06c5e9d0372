-- Atualizar a estrutura de dados para suportar múltiplos administradores

-- 1. Adicionar tabela de configurações de grupo
CREATE TABLE IF NOT EXISTS public.study_group_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  group_id UUID NOT NULL REFERENCES public.study_groups(id) ON DELETE CASCADE,
  allow_member_content BOOLEAN DEFAULT false,
  allow_member_invites BOOLEAN DEFAULT false,
  require_admin_approval BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(group_id)
);

-- Adicionar políticas RLS para a tabela de configurações
ALTER TABLE public.study_group_settings ENABLE ROW LEVEL SECURITY;

-- Política para visualização de configurações
CREATE POLICY "Membros podem ver configurações do grupo" 
ON public.study_group_settings
FOR SELECT
TO public
USING (
  EXISTS (
    SELECT 1 FROM study_group_members sgm
    WHERE sgm.user_id = auth.uid() 
    AND sgm.group_id = study_group_settings.group_id
  )
);

-- Política para edição de configurações (apenas administradores)
CREATE POLICY "Apenas administradores podem editar configurações" 
ON public.study_group_settings
FOR UPDATE
TO public
USING (
  EXISTS (
    SELECT 1 FROM study_group_members sgm
    WHERE sgm.user_id = auth.uid() 
    AND sgm.group_id = study_group_settings.group_id
    AND sgm.role = 'admin'
  )
);

-- Política para inserção de configurações (apenas administradores)
CREATE POLICY "Apenas administradores podem inserir configurações" 
ON public.study_group_settings
FOR INSERT
TO public
WITH CHECK (
  EXISTS (
    SELECT 1 FROM study_group_members sgm
    WHERE sgm.user_id = auth.uid() 
    AND sgm.group_id = study_group_settings.group_id
    AND sgm.role = 'admin'
  )
);

-- 2. Adicionar campo de XP points na tabela de membros se não existir
ALTER TABLE public.study_group_members 
ADD COLUMN IF NOT EXISTS xp_points INTEGER DEFAULT 0;

-- 3. Criar função para transferir administração
CREATE OR REPLACE FUNCTION transfer_group_admin(
  group_id_param UUID,
  from_user_id UUID,
  to_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
  target_exists BOOLEAN;
BEGIN
  -- Verificar se o usuário atual é administrador
  SELECT EXISTS (
    SELECT 1 FROM study_group_members
    WHERE group_id = group_id_param
    AND user_id = from_user_id
    AND role = 'admin'
  ) INTO is_admin;
  
  IF NOT is_admin THEN
    RAISE EXCEPTION 'Usuário não é administrador do grupo';
    RETURN FALSE;
  END IF;
  
  -- Verificar se o usuário alvo existe no grupo
  SELECT EXISTS (
    SELECT 1 FROM study_group_members
    WHERE group_id = group_id_param
    AND user_id = to_user_id
  ) INTO target_exists;
  
  IF NOT target_exists THEN
    RAISE EXCEPTION 'Usuário alvo não é membro do grupo';
    RETURN FALSE;
  END IF;
  
  -- Promover o usuário alvo para administrador
  UPDATE study_group_members
  SET role = 'admin'
  WHERE group_id = group_id_param
  AND user_id = to_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 4. Criar função para verificar se um grupo tem pelo menos um administrador
CREATE OR REPLACE FUNCTION ensure_group_has_admin()
RETURNS TRIGGER AS $$
DECLARE
  admin_count INTEGER;
BEGIN
  -- Se estamos removendo um membro ou alterando seu papel de 'admin'
  IF (TG_OP = 'DELETE' OR (TG_OP = 'UPDATE' AND OLD.role = 'admin' AND NEW.role != 'admin')) THEN
    -- Contar quantos administradores restam no grupo
    SELECT COUNT(*) INTO admin_count
    FROM study_group_members
    WHERE group_id = OLD.group_id
    AND role = 'admin'
    AND (TG_OP = 'UPDATE' OR user_id != OLD.user_id);
    
    -- Se não houver mais administradores, impedir a operação
    IF admin_count = 0 THEN
      RAISE EXCEPTION 'Não é possível remover o último administrador do grupo. Transfira a administração primeiro.';
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Adicionar trigger para garantir que um grupo sempre tenha pelo menos um administrador
DROP TRIGGER IF EXISTS ensure_admin_trigger ON study_group_members;

CREATE TRIGGER ensure_admin_trigger
BEFORE DELETE OR UPDATE OF role
ON study_group_members
FOR EACH ROW
EXECUTE FUNCTION ensure_group_has_admin();

-- 5. Criar função RPC para remover um membro do grupo
CREATE OR REPLACE FUNCTION remove_group_member(
  group_id_param UUID,
  member_id_param UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
  is_self BOOLEAN;
BEGIN
  -- Verificar se o usuário atual é administrador ou está removendo a si mesmo
  SELECT EXISTS (
    SELECT 1 FROM study_group_members
    WHERE group_id = group_id_param
    AND user_id = auth.uid()
    AND role = 'admin'
  ) INTO is_admin;
  
  SELECT (auth.uid() = member_id_param) INTO is_self;
  
  IF NOT (is_admin OR is_self) THEN
    RAISE EXCEPTION 'Permissão negada: apenas administradores podem remover outros membros';
    RETURN FALSE;
  END IF;
  
  -- Remover o membro
  DELETE FROM study_group_members
  WHERE group_id = group_id_param
  AND user_id = member_id_param;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Criar função RPC para atualizar o papel de um membro
CREATE OR REPLACE FUNCTION update_member_role(
  member_id_param UUID,
  new_role TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  group_id_var UUID;
  is_admin BOOLEAN;
BEGIN
  -- Obter o ID do grupo
  SELECT group_id INTO group_id_var
  FROM study_group_members
  WHERE id = member_id_param;
  
  -- Verificar se o usuário atual é administrador
  SELECT EXISTS (
    SELECT 1 FROM study_group_members
    WHERE group_id = group_id_var
    AND user_id = auth.uid()
    AND role = 'admin'
  ) INTO is_admin;
  
  IF NOT is_admin THEN
    RAISE EXCEPTION 'Permissão negada: apenas administradores podem alterar papéis';
    RETURN FALSE;
  END IF;
  
  -- Atualizar o papel do membro
  UPDATE study_group_members
  SET role = new_role
  WHERE id = member_id_param;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
