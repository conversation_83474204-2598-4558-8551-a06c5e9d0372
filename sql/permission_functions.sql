-- Funções para verificação de permissões no Supabase

-- Função para verificar se um usuário tem permissão para acessar um recurso
CREATE OR REPLACE FUNCTION check_resource_permission(
  resource_id UUID,
  table_name TEXT,
  user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  resource_owner UUID;
  query_text TEXT;
BEGIN
  -- Construir a consulta dinamicamente
  query_text := 'SELECT user_id FROM ' || quote_ident(table_name) || 
                ' WHERE id = $1 LIMIT 1';
  
  -- Executar a consulta
  EXECUTE query_text INTO resource_owner USING resource_id;
  
  -- Verificar se o usuário é o proprietário do recurso
  RETURN resource_owner = user_id;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Erro ao verificar permissão: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se um usuário é membro de um grupo
CREATE OR REPLACE FUNCTION check_group_membership(
  group_id UUID,
  user_id UUID
)
RETURNS TABLE(
  is_member BOOLEAN,
  is_admin BOOLEAN,
  is_public BOOLEAN
) AS $$
DECLARE
  group_record RECORD;
  member_record RECORD;
BEGIN
  -- Verificar se o grupo existe e é público
  SELECT is_open, admin_id INTO group_record
  FROM study_groups
  WHERE id = group_id;
  
  -- Se o grupo não existir, retornar falso para tudo
  IF group_record IS NULL THEN
    RETURN QUERY SELECT FALSE, FALSE, FALSE;
    RETURN;
  END IF;
  
  -- Verificar se o usuário é o administrador do grupo
  IF group_record.admin_id = user_id THEN
    RETURN QUERY SELECT TRUE, TRUE, group_record.is_open;
    RETURN;
  END IF;
  
  -- Verificar se o usuário é membro do grupo
  SELECT role INTO member_record
  FROM study_group_members
  WHERE group_id = group_id AND user_id = user_id;
  
  -- Se o usuário não for membro, verificar se o grupo é público
  IF member_record IS NULL THEN
    RETURN QUERY SELECT FALSE, FALSE, group_record.is_open;
    RETURN;
  END IF;
  
  -- Verificar se o usuário é administrador do grupo
  RETURN QUERY SELECT TRUE, member_record.role = 'admin', group_record.is_open;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se um usuário tem permissão para acessar um material de grupo
CREATE OR REPLACE FUNCTION check_group_material_permission(
  material_id UUID,
  user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  material_record RECORD;
  membership_record RECORD;
BEGIN
  -- Obter o material e o grupo associado
  SELECT group_id, created_by INTO material_record
  FROM study_group_materials
  WHERE id = material_id;
  
  -- Se o material não existir, retornar falso
  IF material_record IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Verificar se o usuário é o criador do material
  IF material_record.created_by = user_id THEN
    RETURN TRUE;
  END IF;
  
  -- Verificar se o usuário é membro do grupo
  SELECT * INTO membership_record
  FROM check_group_membership(material_record.group_id, user_id);
  
  -- Administradores têm permissão para todos os materiais do grupo
  IF membership_record.is_admin THEN
    RETURN TRUE;
  END IF;
  
  -- Membros têm permissão para visualizar materiais do grupo
  RETURN membership_record.is_member;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter todos os grupos de um usuário
CREATE OR REPLACE FUNCTION get_user_groups(
  user_id_param UUID
)
RETURNS SETOF study_groups AS $$
BEGIN
  RETURN QUERY
  SELECT sg.*
  FROM study_groups sg
  WHERE sg.is_open = TRUE
  OR sg.admin_id = user_id_param
  OR EXISTS (
    SELECT 1 FROM study_group_members sgm
    WHERE sgm.group_id = sg.id AND sgm.user_id = user_id_param
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter todos os materiais de um grupo que um usuário pode acessar
CREATE OR REPLACE FUNCTION get_user_group_materials(
  group_id_param UUID,
  user_id_param UUID
)
RETURNS SETOF study_group_materials AS $$
DECLARE
  membership_record RECORD;
BEGIN
  -- Verificar se o usuário é membro do grupo
  SELECT * INTO membership_record
  FROM check_group_membership(group_id_param, user_id_param);
  
  -- Se não for membro e o grupo não for público, não retornar nada
  IF NOT membership_record.is_member AND NOT membership_record.is_public THEN
    RETURN;
  END IF;
  
  -- Retornar todos os materiais do grupo
  RETURN QUERY
  SELECT sgm.*
  FROM study_group_materials sgm
  WHERE sgm.group_id = group_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
