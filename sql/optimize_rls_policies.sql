-- Arquivo para otimizar as políticas RLS (Row Level Security) no Supabase
-- Este arquivo contém políticas para garantir o isolamento adequado de dados de usuários
-- e otimizar o desempenho para alta escala (100.000+ usuários)

-- Habilitar RLS em todas as tabelas principais
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flashcard_sets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_invites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mind_maps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_sessions ENABLE ROW LEVEL SECURITY;

-- Criar índices para melhorar o desempenho das consultas com filtros de usuário
CREATE INDEX IF NOT EXISTS idx_subjects_user_id ON public.subjects(user_id);
CREATE INDEX IF NOT EXISTS idx_flashcard_sets_user_id ON public.flashcard_sets(user_id);
CREATE INDEX IF NOT EXISTS idx_flashcards_user_id ON public.flashcards(user_id);
CREATE INDEX IF NOT EXISTS idx_quizzes_user_id ON public.quizzes(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_user_id ON public.quiz_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_user_id ON public.quiz_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_user_id ON public.activities(user_id);
CREATE INDEX IF NOT EXISTS idx_notes_user_id ON public.notes(user_id);
CREATE INDEX IF NOT EXISTS idx_mind_maps_user_id ON public.mind_maps(user_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON public.study_sessions(user_id);

-- Índices para relações de chave estrangeira
CREATE INDEX IF NOT EXISTS idx_flashcards_set_id ON public.flashcards(set_id);
CREATE INDEX IF NOT EXISTS idx_flashcard_sets_subject_id ON public.flashcard_sets(subject_id);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_quiz_id ON public.quiz_questions(quiz_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_quiz_id ON public.quiz_attempts(quiz_id);
CREATE INDEX IF NOT EXISTS idx_activities_subject_id ON public.activities(subject_id);
CREATE INDEX IF NOT EXISTS idx_study_group_members_group_id ON public.study_group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_study_group_materials_group_id ON public.study_group_materials(group_id);

-- Políticas para a tabela subjects
DROP POLICY IF EXISTS "Usuários podem ver seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem ver seus próprios subjects"
    ON public.subjects FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem inserir seus próprios subjects"
    ON public.subjects FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem atualizar seus próprios subjects"
    ON public.subjects FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem excluir seus próprios subjects"
    ON public.subjects FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela flashcard_sets
DROP POLICY IF EXISTS "Usuários podem ver seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem ver seus próprios flashcard_sets"
    ON public.flashcard_sets FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem inserir seus próprios flashcard_sets"
    ON public.flashcard_sets FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem atualizar seus próprios flashcard_sets"
    ON public.flashcard_sets FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem excluir seus próprios flashcard_sets"
    ON public.flashcard_sets FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela flashcards
DROP POLICY IF EXISTS "Usuários podem ver seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem ver seus próprios flashcards"
    ON public.flashcards FOR SELECT
    USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM flashcard_sets fs
            WHERE fs.id = set_id AND fs.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem inserir seus próprios flashcards"
    ON public.flashcards FOR INSERT
    WITH CHECK (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM flashcard_sets fs
            WHERE fs.id = set_id AND fs.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem atualizar seus próprios flashcards"
    ON public.flashcards FOR UPDATE
    USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM flashcard_sets fs
            WHERE fs.id = set_id AND fs.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem excluir seus próprios flashcards"
    ON public.flashcards FOR DELETE
    USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM flashcard_sets fs
            WHERE fs.id = set_id AND fs.user_id = auth.uid()
        )
    );

-- Políticas para a tabela users
DROP POLICY IF EXISTS "Usuários podem ver apenas seus próprios dados" ON public.users;
CREATE POLICY "Usuários podem ver apenas seus próprios dados"
    ON public.users FOR SELECT
    USING (auth.uid() = id);

DROP POLICY IF EXISTS "Usuários podem atualizar apenas seus próprios dados" ON public.users;
CREATE POLICY "Usuários podem atualizar apenas seus próprios dados"
    ON public.users FOR UPDATE
    USING (auth.uid() = id);

-- Políticas para a tabela notes
DROP POLICY IF EXISTS "Usuários podem ver suas próprias notes" ON public.notes;
CREATE POLICY "Usuários podem ver suas próprias notes"
    ON public.notes FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir suas próprias notes" ON public.notes;
CREATE POLICY "Usuários podem inserir suas próprias notes"
    ON public.notes FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias notes" ON public.notes;
CREATE POLICY "Usuários podem atualizar suas próprias notes"
    ON public.notes FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir suas próprias notes" ON public.notes;
CREATE POLICY "Usuários podem excluir suas próprias notes"
    ON public.notes FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela mind_maps
DROP POLICY IF EXISTS "Usuários podem ver seus próprios mind_maps" ON public.mind_maps;
CREATE POLICY "Usuários podem ver seus próprios mind_maps"
    ON public.mind_maps FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios mind_maps" ON public.mind_maps;
CREATE POLICY "Usuários podem inserir seus próprios mind_maps"
    ON public.mind_maps FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios mind_maps" ON public.mind_maps;
CREATE POLICY "Usuários podem atualizar seus próprios mind_maps"
    ON public.mind_maps FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios mind_maps" ON public.mind_maps;
CREATE POLICY "Usuários podem excluir seus próprios mind_maps"
    ON public.mind_maps FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela quizzes
DROP POLICY IF EXISTS "Usuários podem ver seus próprios quizzes" ON public.quizzes;
CREATE POLICY "Usuários podem ver seus próprios quizzes"
    ON public.quizzes FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios quizzes" ON public.quizzes;
CREATE POLICY "Usuários podem inserir seus próprios quizzes"
    ON public.quizzes FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios quizzes" ON public.quizzes;
CREATE POLICY "Usuários podem atualizar seus próprios quizzes"
    ON public.quizzes FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios quizzes" ON public.quizzes;
CREATE POLICY "Usuários podem excluir seus próprios quizzes"
    ON public.quizzes FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela quiz_questions
DROP POLICY IF EXISTS "Usuários podem ver suas próprias quiz_questions" ON public.quiz_questions;
CREATE POLICY "Usuários podem ver suas próprias quiz_questions"
    ON public.quiz_questions FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM quizzes q
            WHERE q.id = quiz_id AND q.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Usuários podem inserir suas próprias quiz_questions" ON public.quiz_questions;
CREATE POLICY "Usuários podem inserir suas próprias quiz_questions"
    ON public.quiz_questions FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM quizzes q
            WHERE q.id = quiz_id AND q.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias quiz_questions" ON public.quiz_questions;
CREATE POLICY "Usuários podem atualizar suas próprias quiz_questions"
    ON public.quiz_questions FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM quizzes q
            WHERE q.id = quiz_id AND q.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Usuários podem excluir suas próprias quiz_questions" ON public.quiz_questions;
CREATE POLICY "Usuários podem excluir suas próprias quiz_questions"
    ON public.quiz_questions FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM quizzes q
            WHERE q.id = quiz_id AND q.user_id = auth.uid()
        )
    );

-- Políticas para a tabela quiz_attempts
DROP POLICY IF EXISTS "Usuários podem ver suas próprias quiz_attempts" ON public.quiz_attempts;
CREATE POLICY "Usuários podem ver suas próprias quiz_attempts"
    ON public.quiz_attempts FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir suas próprias quiz_attempts" ON public.quiz_attempts;
CREATE POLICY "Usuários podem inserir suas próprias quiz_attempts"
    ON public.quiz_attempts FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias quiz_attempts" ON public.quiz_attempts;
CREATE POLICY "Usuários podem atualizar suas próprias quiz_attempts"
    ON public.quiz_attempts FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir suas próprias quiz_attempts" ON public.quiz_attempts;
CREATE POLICY "Usuários podem excluir suas próprias quiz_attempts"
    ON public.quiz_attempts FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela activities
DROP POLICY IF EXISTS "Usuários podem ver suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem ver suas próprias activities"
    ON public.activities FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem inserir suas próprias activities"
    ON public.activities FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem atualizar suas próprias activities"
    ON public.activities FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem excluir suas próprias activities"
    ON public.activities FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para grupos de estudo
DROP POLICY IF EXISTS "Usuários podem ver grupos públicos ou dos quais são membros" ON public.study_groups;
CREATE POLICY "Usuários podem ver grupos públicos ou dos quais são membros"
    ON public.study_groups FOR SELECT
    USING (
        is_open = true OR
        admin_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_group_members
            WHERE group_id = id AND user_id = auth.uid()
        )
    );

-- Política para edição de grupos
DROP POLICY IF EXISTS "Apenas administradores podem editar grupos" ON public.study_groups;
CREATE POLICY "Apenas administradores podem editar grupos"
    ON public.study_groups FOR UPDATE
    USING (
        admin_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_group_members
            WHERE group_id = id AND user_id = auth.uid() AND role = 'admin'
        )
    );

-- Política para exclusão de grupos
DROP POLICY IF EXISTS "Apenas administradores podem excluir grupos" ON public.study_groups;
CREATE POLICY "Apenas administradores podem excluir grupos"
    ON public.study_groups FOR DELETE
    USING (
        admin_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_group_members
            WHERE group_id = id AND user_id = auth.uid() AND role = 'admin'
        )
    );

-- Política para criação de grupos
DROP POLICY IF EXISTS "Qualquer usuário autenticado pode criar grupos" ON public.study_groups;
CREATE POLICY "Qualquer usuário autenticado pode criar grupos"
    ON public.study_groups FOR INSERT
    WITH CHECK (auth.uid() IS NOT NULL AND admin_id = auth.uid());

-- Políticas para membros de grupos
DROP POLICY IF EXISTS "Membros podem ver outros membros do mesmo grupo" ON public.study_group_members;
CREATE POLICY "Membros podem ver outros membros do mesmo grupo"
    ON public.study_group_members FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.user_id = auth.uid()
            AND sgm.group_id = study_group_members.group_id
        ) OR
        EXISTS (
            SELECT 1 FROM study_groups sg
            WHERE sg.id = study_group_members.group_id AND sg.admin_id = auth.uid()
        )
    );

-- Política para adicionar membros
DROP POLICY IF EXISTS "Apenas administradores podem adicionar membros" ON public.study_group_members;
CREATE POLICY "Apenas administradores podem adicionar membros"
    ON public.study_group_members FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM study_groups sg
            WHERE sg.id = group_id AND sg.admin_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.group_id = group_id AND sgm.user_id = auth.uid() AND sgm.role = 'admin'
        )
    );

-- Política para remover membros
DROP POLICY IF EXISTS "Administradores podem remover membros ou usuários podem sair" ON public.study_group_members;
CREATE POLICY "Administradores podem remover membros ou usuários podem sair"
    ON public.study_group_members FOR DELETE
    USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_groups sg
            WHERE sg.id = group_id AND sg.admin_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.group_id = group_id AND sgm.user_id = auth.uid() AND sgm.role = 'admin'
        )
    );

-- Políticas para materiais de grupos
DROP POLICY IF EXISTS "Membros podem ver materiais do grupo" ON public.study_group_materials;
CREATE POLICY "Membros podem ver materiais do grupo"
    ON public.study_group_materials FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.user_id = auth.uid()
            AND sgm.group_id = study_group_materials.group_id
        ) OR
        EXISTS (
            SELECT 1 FROM study_groups sg
            WHERE sg.id = study_group_materials.group_id
            AND (sg.admin_id = auth.uid() OR sg.is_open = true)
        )
    );

-- Política para adicionar materiais
DROP POLICY IF EXISTS "Membros podem adicionar materiais ao grupo" ON public.study_group_materials;
CREATE POLICY "Membros podem adicionar materiais ao grupo"
    ON public.study_group_materials FOR INSERT
    WITH CHECK (
        created_by = auth.uid() AND
        (
            EXISTS (
                SELECT 1 FROM study_group_members sgm
                WHERE sgm.user_id = auth.uid()
                AND sgm.group_id = group_id
            ) OR
            EXISTS (
                SELECT 1 FROM study_groups sg
                WHERE sg.id = group_id AND sg.admin_id = auth.uid()
            )
        )
    );

-- Política para editar materiais
DROP POLICY IF EXISTS "Criadores e administradores podem editar materiais" ON public.study_group_materials;
CREATE POLICY "Criadores e administradores podem editar materiais"
    ON public.study_group_materials FOR UPDATE
    USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_groups sg
            WHERE sg.id = group_id AND sg.admin_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.group_id = group_id AND sgm.user_id = auth.uid() AND sgm.role = 'admin'
        )
    );

-- Política para excluir materiais
DROP POLICY IF EXISTS "Criadores e administradores podem excluir materiais" ON public.study_group_materials;
CREATE POLICY "Criadores e administradores podem excluir materiais"
    ON public.study_group_materials FOR DELETE
    USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_groups sg
            WHERE sg.id = group_id AND sg.admin_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.group_id = group_id AND sgm.user_id = auth.uid() AND sgm.role = 'admin'
        )
    );

-- Funções de segurança para operações complexas
CREATE OR REPLACE FUNCTION get_user_study_groups(user_id_param UUID)
RETURNS SETOF study_groups AS $$
BEGIN
    RETURN QUERY
    SELECT sg.*
    FROM study_groups sg
    WHERE sg.is_open = true
    OR sg.admin_id = user_id_param
    OR EXISTS (
        SELECT 1 FROM study_group_members sgm
        WHERE sgm.group_id = sg.id AND sgm.user_id = user_id_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter detalhes de um grupo específico
CREATE OR REPLACE FUNCTION get_study_group_details(group_id_param UUID)
RETURNS SETOF study_groups AS $$
BEGIN
    RETURN QUERY
    SELECT sg.*
    FROM study_groups sg
    WHERE sg.id = group_id_param
    AND (
        sg.is_open = true
        OR sg.admin_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter membros de um grupo
CREATE OR REPLACE FUNCTION get_study_group_members(group_id_param UUID)
RETURNS TABLE (
    id UUID,
    group_id UUID,
    user_id UUID,
    role TEXT,
    joined_at TIMESTAMPTZ,
    user_name TEXT,
    user_avatar TEXT
) AS $$
BEGIN
    -- Verificar se o usuário tem permissão para ver os membros
    IF NOT EXISTS (
        SELECT 1 FROM study_groups sg
        WHERE sg.id = group_id_param
        AND (
            sg.is_open = true
            OR sg.admin_id = auth.uid()
            OR EXISTS (
                SELECT 1 FROM study_group_members sgm
                WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
            )
        )
    ) THEN
        RETURN;
    END IF;

    -- Retornar os membros com informações do usuário
    RETURN QUERY
    SELECT
        sgm.id,
        sgm.group_id,
        sgm.user_id,
        sgm.role,
        sgm.joined_at,
        u.name AS user_name,
        u.avatar_url AS user_avatar
    FROM study_group_members sgm
    JOIN users u ON sgm.user_id = u.id
    WHERE sgm.group_id = group_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter materiais de um grupo
CREATE OR REPLACE FUNCTION get_study_group_materials(group_id_param UUID)
RETURNS SETOF study_group_materials AS $$
BEGIN
    -- Verificar se o usuário tem permissão para ver os materiais
    IF NOT EXISTS (
        SELECT 1 FROM study_groups sg
        WHERE sg.id = group_id_param
        AND (
            sg.is_open = true
            OR sg.admin_id = auth.uid()
            OR EXISTS (
                SELECT 1 FROM study_group_members sgm
                WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
            )
        )
    ) THEN
        RETURN;
    END IF;

    -- Retornar os materiais
    RETURN QUERY
    SELECT sgm.*
    FROM study_group_materials sgm
    WHERE sgm.group_id = group_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se um usuário é membro de um grupo
CREATE OR REPLACE FUNCTION check_group_membership(
    group_id_param UUID,
    user_id_param UUID
)
RETURNS TABLE (
    is_member BOOLEAN,
    is_admin BOOLEAN,
    is_public BOOLEAN
) AS $$
DECLARE
    group_record RECORD;
    member_record RECORD;
BEGIN
    -- Obter informações do grupo
    SELECT is_open, admin_id INTO group_record
    FROM study_groups
    WHERE id = group_id_param;

    -- Se o grupo não existir, retornar falso para tudo
    IF group_record IS NULL THEN
        RETURN QUERY SELECT FALSE, FALSE, FALSE;
        RETURN;
    END IF;

    -- Verificar se o usuário é o administrador do grupo
    IF group_record.admin_id = user_id_param THEN
        RETURN QUERY SELECT TRUE, TRUE, group_record.is_open;
        RETURN;
    END IF;

    -- Verificar se o usuário é membro do grupo
    SELECT role INTO member_record
    FROM study_group_members
    WHERE group_id = group_id_param AND user_id = user_id_param;

    -- Se o usuário não for membro, verificar se o grupo é público
    IF member_record IS NULL THEN
        RETURN QUERY SELECT FALSE, FALSE, group_record.is_open;
        RETURN;
    END IF;

    -- Verificar se o usuário é administrador do grupo
    RETURN QUERY SELECT TRUE, member_record.role = 'admin', group_record.is_open;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funções para otimização de desempenho

-- Função para obter todos os flashcards de um usuário com paginação
CREATE OR REPLACE FUNCTION get_user_flashcards(
    user_id_param UUID,
    page_size INT DEFAULT 50,
    page_number INT DEFAULT 1
)
RETURNS TABLE (
    id UUID,
    front TEXT,
    back TEXT,
    set_id UUID,
    difficulty INT,
    next_review TIMESTAMPTZ,
    review_count INT,
    set_title TEXT,
    subject_title TEXT,
    subject_color TEXT
) AS $$
DECLARE
    offset_val INT;
BEGIN
    -- Calcular o offset para paginação
    offset_val := (page_number - 1) * page_size;

    -- Retornar flashcards com informações relacionadas
    RETURN QUERY
    SELECT
        f.id,
        f.front,
        f.back,
        f.set_id,
        f.difficulty,
        f.next_review,
        f.review_count,
        fs.title AS set_title,
        s.title AS subject_title,
        s.color AS subject_color
    FROM flashcards f
    JOIN flashcard_sets fs ON f.set_id = fs.id
    JOIN subjects s ON fs.subject_id = s.id
    WHERE fs.user_id = user_id_param
    ORDER BY f.next_review ASC NULLS LAST
    LIMIT page_size
    OFFSET offset_val;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter estatísticas de estudo de um usuário
CREATE OR REPLACE FUNCTION get_user_study_stats(user_id_param UUID)
RETURNS TABLE (
    total_subjects INT,
    total_flashcards INT,
    total_quizzes INT,
    total_study_time INT,
    flashcards_due INT,
    quizzes_completed INT,
    average_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM subjects WHERE user_id = user_id_param) AS total_subjects,
        (SELECT COUNT(*) FROM flashcards f JOIN flashcard_sets fs ON f.set_id = fs.id WHERE fs.user_id = user_id_param) AS total_flashcards,
        (SELECT COUNT(*) FROM quizzes WHERE user_id = user_id_param) AS total_quizzes,
        (SELECT total_study_time FROM users WHERE id = user_id_param) AS total_study_time,
        (SELECT COUNT(*) FROM flashcards f
         JOIN flashcard_sets fs ON f.set_id = fs.id
         WHERE fs.user_id = user_id_param AND f.next_review <= NOW()) AS flashcards_due,
        (SELECT COUNT(*) FROM quiz_attempts WHERE user_id = user_id_param) AS quizzes_completed,
        (SELECT COALESCE(AVG(score::NUMERIC / total_questions * 100), 0)
         FROM quiz_attempts
         WHERE user_id = user_id_param AND total_questions > 0) AS average_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Configurações para otimização de desempenho
ALTER DATABASE CURRENT SET max_connections = 200;
ALTER DATABASE CURRENT SET shared_buffers = '1GB';
ALTER DATABASE CURRENT SET work_mem = '16MB';
ALTER DATABASE CURRENT SET maintenance_work_mem = '256MB';
ALTER DATABASE CURRENT SET effective_cache_size = '4GB';
ALTER DATABASE CURRENT SET random_page_cost = 1.1;
ALTER DATABASE CURRENT SET effective_io_concurrency = 200;
ALTER DATABASE CURRENT SET default_statistics_target = 100;

-- Comentário final
COMMENT ON DATABASE CURRENT IS 'Banco de dados otimizado para alta escala (100.000+ usuários) com isolamento de dados por usuário';