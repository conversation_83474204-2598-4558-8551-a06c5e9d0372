-- Fix Study Groups Issues
-- This script fixes the infinite recursion, RPC function parameters, and other issues

-- 1. Fix the infinite recursion in study_group_members policy
DROP POLICY IF EXISTS "Membros podem ver outros membros do mesmo grupo" ON public.study_group_members;

CREATE POLICY "Membros podem ver outros membros do mesmo grupo" 
ON public.study_group_members
FOR SELECT
TO public
USING (
  -- O usuário pode ver seu próprio registro
  user_id = auth.uid()
  OR
  -- Administradores do grupo podem ver todos os membros
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id 
    AND sg.admin_id = auth.uid()
  )
  OR
  -- Membros podem ver outros membros se o grupo for aberto
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id
    AND sg.is_open = true
  )
  OR
  -- Membros podem ver outros membros do mesmo grupo (verificação direta)
  EXISTS (
    SELECT 1 FROM study_group_members sgm_check
    WHERE sgm_check.group_id = study_group_members.group_id
    AND sgm_check.user_id = auth.uid()
    LIMIT 1
  )
);

-- 2. Fix the check_group_membership function parameters
DROP FUNCTION IF EXISTS public.check_group_membership(uuid, uuid);

CREATE OR REPLACE FUNCTION public.check_group_membership(
  group_id_param UUID,
  user_id_param UUID
)
RETURNS TABLE(
  is_member BOOLEAN,
  is_admin BOOLEAN,
  is_public BOOLEAN
) AS $$
DECLARE
  group_record RECORD;
  member_record RECORD;
BEGIN
  -- Verificar se o grupo existe e é público
  SELECT is_open, admin_id INTO group_record
  FROM study_groups
  WHERE id = group_id_param;
  
  -- Se o grupo não existir, retornar falso para tudo
  IF group_record IS NULL THEN
    RETURN QUERY SELECT FALSE, FALSE, FALSE;
    RETURN;
  END IF;
  
  -- Verificar se o usuário é o administrador do grupo
  IF group_record.admin_id = user_id_param THEN
    RETURN QUERY SELECT TRUE, TRUE, group_record.is_open;
    RETURN;
  END IF;
  
  -- Verificar se o usuário é membro do grupo
  SELECT role INTO member_record
  FROM study_group_members
  WHERE group_id = group_id_param AND user_id = user_id_param;
  
  -- Se o usuário não for membro, verificar se o grupo é público
  IF member_record IS NULL THEN
    RETURN QUERY SELECT FALSE, FALSE, group_record.is_open;
    RETURN;
  END IF;
  
  -- Verificar se o usuário é administrador do grupo
  RETURN QUERY SELECT TRUE, (member_record.role = 'admin'), group_record.is_open;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Optimize other policies to avoid recursion issues
DROP POLICY IF EXISTS "Membros podem ver configurações do grupo" ON public.study_group_settings;

CREATE POLICY "Membros podem ver configurações do grupo" 
ON public.study_group_settings
FOR SELECT
TO public
USING (
  -- Administradores do grupo podem ver as configurações
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_settings.group_id 
    AND sg.admin_id = auth.uid()
  )
  OR
  -- Membros podem ver configurações usando verificação direta
  EXISTS (
    SELECT 1 FROM study_group_members sgm
    WHERE sgm.group_id = study_group_settings.group_id
    AND sgm.user_id = auth.uid()
    LIMIT 1
  )
);

-- 4. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_study_group_members_group_user 
ON study_group_members(group_id, user_id);

CREATE INDEX IF NOT EXISTS idx_study_group_members_user_group 
ON study_group_members(user_id, group_id);

CREATE INDEX IF NOT EXISTS idx_study_groups_admin 
ON study_groups(admin_id);

CREATE INDEX IF NOT EXISTS idx_study_groups_open 
ON study_groups(is_open) WHERE is_open = true;

-- 5. Grant necessary permissions
GRANT EXECUTE ON FUNCTION check_group_membership(UUID, UUID) TO public;
GRANT EXECUTE ON FUNCTION get_study_group_details(UUID) TO public;
GRANT EXECUTE ON FUNCTION get_study_group_members(UUID) TO public;
GRANT EXECUTE ON FUNCTION get_user_study_groups(UUID) TO public;

-- 6. Refresh schema cache
NOTIFY pgrst, 'reload schema';
