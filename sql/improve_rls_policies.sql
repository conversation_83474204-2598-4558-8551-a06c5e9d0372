-- Arquivo para melhorar as políticas RLS (Row Level Security) no Supabase
-- Este arquivo contém políticas para garantir o isolamento adequado de dados de usuários

-- Habilitar RLS em todas as tabelas principais
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flashcard_sets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_invites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_group_settings ENABLE ROW LEVEL SECURITY;

-- Políticas para a tabela subjects
DROP POLICY IF EXISTS "Usuários podem ver seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem ver seus próprios subjects"
    ON public.subjects FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem inserir seus próprios subjects"
    ON public.subjects FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem atualizar seus próprios subjects"
    ON public.subjects FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios subjects" ON public.subjects;
CREATE POLICY "Usuários podem excluir seus próprios subjects"
    ON public.subjects FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela flashcard_sets
DROP POLICY IF EXISTS "Usuários podem ver seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem ver seus próprios flashcard_sets"
    ON public.flashcard_sets FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem inserir seus próprios flashcard_sets"
    ON public.flashcard_sets FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem atualizar seus próprios flashcard_sets"
    ON public.flashcard_sets FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios flashcard_sets" ON public.flashcard_sets;
CREATE POLICY "Usuários podem excluir seus próprios flashcard_sets"
    ON public.flashcard_sets FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela flashcards
DROP POLICY IF EXISTS "Usuários podem ver seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem ver seus próprios flashcards"
    ON public.flashcards FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem inserir seus próprios flashcards"
    ON public.flashcards FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem atualizar seus próprios flashcards"
    ON public.flashcards FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir seus próprios flashcards" ON public.flashcards;
CREATE POLICY "Usuários podem excluir seus próprios flashcards"
    ON public.flashcards FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela activities
DROP POLICY IF EXISTS "Usuários podem ver suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem ver suas próprias activities"
    ON public.activities FOR SELECT
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem inserir suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem inserir suas próprias activities"
    ON public.activities FOR INSERT
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem atualizar suas próprias activities"
    ON public.activities FOR UPDATE
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem excluir suas próprias activities" ON public.activities;
CREATE POLICY "Usuários podem excluir suas próprias activities"
    ON public.activities FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para a tabela users
DROP POLICY IF EXISTS "Usuários podem ver apenas seus próprios dados" ON public.users;
CREATE POLICY "Usuários podem ver apenas seus próprios dados"
    ON public.users FOR SELECT
    USING (auth.uid() = id);

DROP POLICY IF EXISTS "Usuários podem atualizar apenas seus próprios dados" ON public.users;
CREATE POLICY "Usuários podem atualizar apenas seus próprios dados"
    ON public.users FOR UPDATE
    USING (auth.uid() = id);

-- Políticas para grupos de estudo
-- Política para visualização de grupos
DROP POLICY IF EXISTS "Usuários podem ver grupos públicos ou dos quais são membros" ON public.study_groups;
CREATE POLICY "Usuários podem ver grupos públicos ou dos quais são membros"
    ON public.study_groups FOR SELECT
    USING (
        is_open = true OR
        admin_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_group_members
            WHERE group_id = id AND user_id = auth.uid()
        )
    );

-- Política para edição de grupos
DROP POLICY IF EXISTS "Apenas administradores podem editar grupos" ON public.study_groups;
CREATE POLICY "Apenas administradores podem editar grupos"
    ON public.study_groups FOR UPDATE
    USING (
        admin_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_group_members
            WHERE group_id = id AND user_id = auth.uid() AND role = 'admin'
        )
    );

-- Política para exclusão de grupos
DROP POLICY IF EXISTS "Apenas administradores podem excluir grupos" ON public.study_groups;
CREATE POLICY "Apenas administradores podem excluir grupos"
    ON public.study_groups FOR DELETE
    USING (
        admin_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM study_group_members
            WHERE group_id = id AND user_id = auth.uid() AND role = 'admin'
        )
    );

-- Política para criação de grupos
DROP POLICY IF EXISTS "Qualquer usuário autenticado pode criar grupos" ON public.study_groups;
CREATE POLICY "Qualquer usuário autenticado pode criar grupos"
    ON public.study_groups FOR INSERT
    WITH CHECK (auth.uid() IS NOT NULL);

-- Criar funções RPC para operações seguras em grupos
CREATE OR REPLACE FUNCTION get_user_study_groups(user_id_param UUID)
RETURNS SETOF study_groups AS $$
BEGIN
    RETURN QUERY
    SELECT sg.*
    FROM study_groups sg
    WHERE sg.is_open = true
    OR sg.admin_id = user_id_param
    OR EXISTS (
        SELECT 1 FROM study_group_members sgm
        WHERE sgm.group_id = sg.id AND sgm.user_id = user_id_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter detalhes de um grupo específico
CREATE OR REPLACE FUNCTION get_study_group_details(group_id_param UUID)
RETURNS SETOF study_groups AS $$
BEGIN
    RETURN QUERY
    SELECT sg.*
    FROM study_groups sg
    WHERE sg.id = group_id_param
    AND (
        sg.is_open = true
        OR sg.admin_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM study_group_members sgm
            WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter membros de um grupo
CREATE OR REPLACE FUNCTION get_study_group_members(group_id_param UUID)
RETURNS SETOF study_group_members AS $$
BEGIN
    -- Verificar se o usuário tem acesso ao grupo
    IF EXISTS (
        SELECT 1 FROM study_groups sg
        WHERE sg.id = group_id_param
        AND (
            sg.is_open = true
            OR sg.admin_id = auth.uid()
            OR EXISTS (
                SELECT 1 FROM study_group_members sgm
                WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
            )
        )
    ) THEN
        RETURN QUERY
        SELECT sgm.*
        FROM study_group_members sgm
        WHERE sgm.group_id = group_id_param;
    ELSE
        RAISE EXCEPTION 'Acesso negado ao grupo %', group_id_param;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
