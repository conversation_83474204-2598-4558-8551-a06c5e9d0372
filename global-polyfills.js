/**
 * Global polyfills for the app
 * This file should be imported at the very beginning of the app entry point
 */

// Simple polyfill for require.resolve in Hermes
if (typeof global !== 'undefined' && typeof global.require === 'function' && !global.require.resolve) {
  global.require.resolve = function(moduleName) {
    return moduleName;
  };
  console.log('require.resolve polyfill applied in global-polyfills.js');
}

// Export a dummy value to ensure the file is properly imported
export default true;
