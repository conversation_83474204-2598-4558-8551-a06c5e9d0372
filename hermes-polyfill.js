// Apply polyfills for Hermes JS engine
// This file should be imported at the very beginning of the app

// Polyfill for require.resolve in Hermes
if (typeof global !== 'undefined') {
  if (typeof global.require === 'function' && !global.require.resolve) {
    global.require.resolve = function(moduleName) {
      return moduleName;
    };
    console.log('require.resolve polyfill applied in hermes-polyfill.js');
  }
}

// Export a dummy value to ensure the file is properly imported
export default true;
