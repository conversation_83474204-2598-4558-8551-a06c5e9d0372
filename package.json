{"name": "expo-app", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "postinstall": "patch-package"}, "dependencies": {"@babel/plugin-proposal-decorators": "^7.25.9", "@expo/vector-icons": "^14.0.2", "@invertase/react-native-apple-authentication": "^2.4.1", "@nozbe/watermelondb": "^0.28.0", "@nozbe/with-observables": "^1.6.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^13.2.0", "@react-navigation/native": "^7.0.0", "@shopify/flash-list": "^1.7.6", "@supabase/supabase-js": "^2.49.4", "buffer": "^6.0.3", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "^53.0.7", "expo-blur": "~14.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.4", "expo-file-system": "^18.0.12", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-image-manipulator": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-notifications": "^0.31.1", "expo-router": "~5.0.5", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "^0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "node-libs-react-native": "^1.2.1", "openai": "^4.93.0", "process": "^0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-calendars": "^1.1311.0", "react-native-draggable-flatlist": "^4.0.2", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.11.2", "react-native-svg-web": "^1.0.9", "react-native-url-polyfill": "^1.3.0", "react-native-web": "^0.20.0", "stream-browserify": "^3.0.0", "text-encoding": "^0.7.0", "web-streams-polyfill": "^4.1.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "node-libs-browser": "^2.2.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "~5.8.2"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@nozbe/watermelondb"], "listUnknownPackages": false}}}, "private": true}