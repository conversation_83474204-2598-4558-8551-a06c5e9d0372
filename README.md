# Lia App - Assistente de Estudos Inteligente

Lia é um aplicativo de estudos inteligente que utiliza IA para ajudar estudantes a organizar e otimizar seu aprendizado.

## Funcionalidades

- **Autenticação**: Login com e-mail/senha, Google e Apple (iOS)
- **Matérias**: Organize seus estudos por matérias
- **Flashcards**: Crie e estude com flashcards usando repetição espaçada
- **Anotações**: Editor de texto rico com suporte a vários tipos de blocos
- **Mapas Mentais**: Visualize conceitos e suas conexões
- **Quizzes**: Teste seu conhecimento com quizzes interativos
- **Chat com IA**: Tire dúvidas e receba explicações personalizadas
- **Geração de Conteúdo**: Use IA para gerar flashcards, anotações e quizzes
- **Gamificação**: Sistema de níveis, XP e streak para manter a motivação

## Tecnologias

- **Frontend**: React Native com Expo
- **Backend**: Supabase (PostgreSQL, Autenticação, Storage)
- **IA**: Integração com OpenAI API
- **Armazenamento**: Supabase Storage para imagens e arquivos
- **Autenticação**: Supabase Auth com suporte a provedores sociais

## Instalação

```bash
# Instalar dependências
npm install

# Iniciar o servidor de desenvolvimento
npx expo start
```

## Estrutura do Projeto

- `/app`: Páginas e rotas do aplicativo (usando Expo Router)
- `/components`: Componentes reutilizáveis
- `/constants`: Constantes e dados mockados
- `/contexts`: Contextos React, incluindo autenticação
- `/hooks`: Hooks personalizados para Supabase e outras funcionalidades
- `/lib`: Configuração de bibliotecas externas
- `/services`: Serviços para APIs externas
- `/store`: Gerenciamento de estado global
- `/types`: Definições de tipos TypeScript
- `/utils`: Funções utilitárias

## Recursos

- Sistema de streak para acompanhar dias consecutivos de estudo
- Geração de conteúdo com IA
- Interface intuitiva e moderna
- Sincronização de dados entre dispositivos
- Modo offline para estudar sem internet

## Segurança

- Políticas de segurança Row Level Security (RLS) no Supabase
- Autenticação segura com tokens JWT
- Dados do usuário protegidos e isolados

## Licença

Este projeto está licenciado sob a licença MIT.
