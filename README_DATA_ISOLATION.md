# Isolamento de Dados no Lia App

Este documento descreve a implementação do isolamento de dados no Lia App, garantindo que cada usuário possa acessar apenas dados que pertencem a ele ou a grupos dos quais é membro.

## Visão Geral

O isolamento de dados é implementado em várias camadas:

1. **Políticas RLS no Supabase**: Garantem que os usuários só possam acessar seus próprios dados no banco de dados.
2. **Hooks de acesso a dados**: Garantem que todas as consultas incluam filtros por usuário_id.
3. **Middleware de autenticação**: Verifica se o usuário está autenticado e tem permissão para acessar determinados recursos.
4. **Componentes de proteção de rota**: Garantem que apenas usuários autorizados possam acessar determinadas rotas.
5. **Funções de verificação de permissão**: Verificam se o usuário tem permissão para acessar determinados recursos.

## Políticas RLS no Supabase

As políticas Row Level Security (RLS) no Supabase garantem que os usuários só possam acessar seus próprios dados no banco de dados. Essas políticas são aplicadas no nível do banco de dados, independentemente de como os dados são acessados.

Para aplicar as políticas RLS, execute o script SQL `sql/improve_rls_policies.sql`:

```bash
psql -h <host> -d <database> -U <user> -f sql/improve_rls_policies.sql
```

Ou copie e cole o conteúdo do arquivo no editor SQL do Supabase.

## Hooks de Acesso a Dados

Os hooks `useSupabase` e `useSupabaseOptimized` foram atualizados para garantir que todas as consultas incluam filtros por usuário_id. Isso garante que os usuários só possam acessar seus próprios dados, mesmo que as políticas RLS não estejam funcionando corretamente.

### Exemplo de uso:

```typescript
// Consulta que inclui automaticamente o filtro user_id
const { data, loading, error } = useSupabase('subjects');

// Consulta com filtros adicionais
const { data, loading, error } = useSupabase('subjects', {
  filter: { color: 'red' },
  order: { column: 'created_at', ascending: false },
  limit: 10,
});

// Consulta que pula o filtro de usuário (use com cuidado)
const { data, loading, error } = useSupabase('subjects', {
  skipUserFilter: true,
});
```

## Middleware de Autenticação

O middleware de autenticação fornece funções para verificar se o usuário está autenticado e tem permissão para acessar determinados recursos.

### Exemplo de uso:

```typescript
import { getAuthenticatedUser, checkResourcePermission, checkGroupMembership } from '@/middleware/authMiddleware';

// Verificar se o usuário está autenticado
const user = await getAuthenticatedUser();

if (!user) {
  // Redirecionar para a página de login
  router.replace('/login');
  return;
}

// Verificar se o usuário tem permissão para acessar um recurso
const hasPermission = await checkResourcePermission('resource-id', 'subjects', user.id);

if (!hasPermission) {
  // Exibir mensagem de erro
  Alert.alert('Acesso Negado', 'Você não tem permissão para acessar este recurso.');
  return;
}

// Verificar se o usuário é membro de um grupo
const { isMember, isAdmin } = await checkGroupMembership('group-id', user.id);

if (!isMember) {
  // Exibir mensagem de erro
  Alert.alert('Acesso Negado', 'Você não é membro deste grupo.');
  return;
}
```

## Componente de Proteção de Rota

O componente `RouteGuard` garante que apenas usuários autorizados possam acessar determinadas rotas.

### Exemplo de uso:

```tsx
import { RouteGuard } from '@/components/RouteGuard';
// Ou usando importação padrão
// import RouteGuard from '@/components/RouteGuard';

// Proteger uma rota que requer autenticação
export default function ProtectedScreen() {
  return (
    <RouteGuard>
      <View>
        <Text>Conteúdo protegido</Text>
      </View>
    </RouteGuard>
  );
}

// Proteger uma rota que requer acesso a um recurso específico
export default function SubjectDetailScreen() {
  const { id } = useLocalSearchParams();

  return (
    <RouteGuard resourceId={id} tableName="subjects">
      <View>
        <Text>Detalhes do subject</Text>
      </View>
    </RouteGuard>
  );
}

// Proteger uma rota que requer acesso a um grupo
export default function GroupDetailScreen() {
  const { id } = useLocalSearchParams();

  return (
    <RouteGuard groupId={id}>
      <View>
        <Text>Detalhes do grupo</Text>
      </View>
    </RouteGuard>
  );
}

// Proteger uma rota que requer acesso de administrador a um grupo
export default function GroupSettingsScreen() {
  const { id } = useLocalSearchParams();

  return (
    <RouteGuard groupId={id} requireAdmin={true}>
      <View>
        <Text>Configurações do grupo</Text>
      </View>
    </RouteGuard>
  );
}
```

## Funções de Verificação de Permissão

As funções de verificação de permissão no Supabase fornecem uma camada adicional de segurança, garantindo que os usuários só possam acessar recursos aos quais têm permissão.

Para aplicar as funções de verificação de permissão, execute o script SQL `sql/permission_functions.sql`:

```bash
psql -h <host> -d <database> -U <user> -f sql/permission_functions.sql
```

Ou copie e cole o conteúdo do arquivo no editor SQL do Supabase.

## Testes de Isolamento de Dados

Os testes de isolamento de dados verificam se as políticas de segurança estão funcionando corretamente.

Para executar os testes:

```bash
npx ts-node scripts/runTests.ts
```

Certifique-se de ajustar os IDs de recursos e usuários no arquivo `tests/testDataIsolation.ts` antes de executar os testes.

## Melhores Práticas

1. **Sempre use os hooks de acesso a dados**: Evite fazer consultas diretas ao Supabase sem usar os hooks `useSupabase` ou `useSupabaseOptimized`.
2. **Proteja todas as rotas**: Use o componente `RouteGuard` para proteger todas as rotas que exigem autenticação ou autorização.
3. **Verifique permissões**: Use as funções de verificação de permissão para garantir que os usuários só possam acessar recursos aos quais têm permissão.
4. **Teste regularmente**: Execute os testes de isolamento de dados regularmente para garantir que as políticas de segurança estão funcionando corretamente.
5. **Mantenha as políticas RLS atualizadas**: Sempre que adicionar uma nova tabela, certifique-se de adicionar políticas RLS apropriadas.

## Solução de Problemas

Se você encontrar problemas com o isolamento de dados, verifique:

1. **Políticas RLS**: Certifique-se de que as políticas RLS estão habilitadas e configuradas corretamente para todas as tabelas.
2. **Hooks de acesso a dados**: Certifique-se de que está usando os hooks `useSupabase` ou `useSupabaseOptimized` para todas as consultas.
3. **Middleware de autenticação**: Certifique-se de que o middleware de autenticação está verificando corretamente as permissões.
4. **Componente de proteção de rota**: Certifique-se de que está usando o componente `RouteGuard` para proteger todas as rotas que exigem autenticação ou autorização.
5. **Funções de verificação de permissão**: Certifique-se de que as funções de verificação de permissão estão funcionando corretamente.

Se o problema persistir, execute os testes de isolamento de dados para identificar onde está o problema.
