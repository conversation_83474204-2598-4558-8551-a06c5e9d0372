import { create } from 'zustand';
import { supabase } from '@/lib/supabase';
import { Separator } from '@/types';

interface SeparatorState {
  separators: Separator[];
  loading: boolean;
  error: string | null;
  
  // Ações
  fetchSeparators: () => Promise<void>;
  addSeparator: (separator: Omit<Separator, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<Separator | null>;
  updateSeparator: (id: string, updates: Partial<Separator>) => Promise<void>;
  deleteSeparator: (id: string) => Promise<void>;
}

export const useSeparatorStore = create<SeparatorState>((set, get) => ({
  separators: [],
  loading: false,
  error: null,
  
  fetchSeparators: async () => {
    try {
      set({ loading: true, error: null });
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ loading: false, error: 'Usuário não autenticado' });
        return;
      }
      
      const { data, error } = await supabase
        .from('separators')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: true });
      
      if (error) {
        console.error('Erro ao buscar separadores:', error);
        set({ loading: false, error: error.message });
        return;
      }
      
      set({ separators: data || [], loading: false });
    } catch (error) {
      console.error('Erro ao buscar separadores:', error);
      set({ loading: false, error: 'Erro ao buscar separadores' });
    }
  },
  
  addSeparator: async (separator) => {
    try {
      set({ loading: true, error: null });
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ loading: false, error: 'Usuário não autenticado' });
        return null;
      }
      
      const newSeparator = {
        ...separator,
        user_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      const { data, error } = await supabase
        .from('separators')
        .insert([newSeparator])
        .select()
        .single();
      
      if (error) {
        console.error('Erro ao adicionar separador:', error);
        set({ loading: false, error: error.message });
        return null;
      }
      
      set(state => ({
        separators: [...state.separators, data],
        loading: false,
      }));
      
      return data;
    } catch (error) {
      console.error('Erro ao adicionar separador:', error);
      set({ loading: false, error: 'Erro ao adicionar separador' });
      return null;
    }
  },
  
  updateSeparator: async (id, updates) => {
    try {
      set({ loading: true, error: null });
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ loading: false, error: 'Usuário não autenticado' });
        return;
      }
      
      const { error } = await supabase
        .from('separators')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('user_id', user.id);
      
      if (error) {
        console.error('Erro ao atualizar separador:', error);
        set({ loading: false, error: error.message });
        return;
      }
      
      set(state => ({
        separators: state.separators.map(s => 
          s.id === id ? { ...s, ...updates, updated_at: new Date().toISOString() } : s
        ),
        loading: false,
      }));
    } catch (error) {
      console.error('Erro ao atualizar separador:', error);
      set({ loading: false, error: 'Erro ao atualizar separador' });
    }
  },
  
  deleteSeparator: async (id) => {
    try {
      set({ loading: true, error: null });
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ loading: false, error: 'Usuário não autenticado' });
        return;
      }
      
      // Primeiro, atualizar todas as matérias associadas a este separador
      await supabase
        .from('subjects')
        .update({ separator_id: null })
        .eq('separator_id', id);
      
      // Depois, excluir o separador
      const { error } = await supabase
        .from('separators')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
      
      if (error) {
        console.error('Erro ao excluir separador:', error);
        set({ loading: false, error: error.message });
        return;
      }
      
      set(state => ({
        separators: state.separators.filter(s => s.id !== id),
        loading: false,
      }));
    } catch (error) {
      console.error('Erro ao excluir separador:', error);
      set({ loading: false, error: 'Erro ao excluir separador' });
    }
  },
}));
