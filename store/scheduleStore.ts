import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase } from "@/lib/supabase";
import { scheduleNotificationsForSchedule } from "@/services/notificationService";
import { CalendarEvent, TodoItem } from "@/types";
import { useCalendarStore } from "./calendarStore";
import { format, addDays, parseISO, startOfWeek, endOfWeek, eachDayOfInterval, addMonths } from "date-fns";
import { ptBR } from "date-fns/locale";

export interface Schedule {
  id: string;
  title: string;
  description?: string;
  type: 'weekly' | 'monthly' | '30days' | 'custom';
  startDate: string;
  endDate?: string;
  repeatWeekly: boolean;
  repeatMonthly: boolean;
  active: boolean;
  settings?: any;
  createdAt: string;
  updatedAt: string;
}

export interface ScheduleItem {
  id: string;
  scheduleId: string;
  subjectId?: string;
  subjectTitle: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  specificDate?: string;
  startTime: string;
  endTime: string;
  createEvent: boolean;
  createTodo: boolean;
  color?: string;
  icon?: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

interface ScheduleState {
  schedules: Schedule[];
  currentSchedule: Schedule | null;
  scheduleItems: ScheduleItem[];
  loading: boolean;
  error: Error | null;

  // Schedule actions
  fetchSchedules: () => Promise<void>;
  fetchScheduleItems: (scheduleId: string) => Promise<ScheduleItem[]>;
  createSchedule: (schedule: Omit<Schedule, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Schedule | null>;
  updateSchedule: (id: string, updates: Partial<Schedule>) => Promise<Schedule | null>;
  deleteSchedule: (id: string) => Promise<boolean>;

  // Schedule items actions
  addScheduleItem: (item: Omit<ScheduleItem, 'id' | 'createdAt' | 'updatedAt'>) => Promise<ScheduleItem | null>;
  updateScheduleItem: (id: string, updates: Partial<ScheduleItem>) => Promise<ScheduleItem | null>;
  deleteScheduleItem: (id: string) => Promise<boolean>;

  // Apply schedule
  applySchedule: (scheduleId: string, startDate?: Date, endDate?: Date) => Promise<{ events: number, todos: number, notifications?: { events: number, todos: number } }>;

  // Set current schedule
  setCurrentSchedule: (schedule: Schedule | null) => void;

  // Update items order
  updateItemsOrder: (items: ScheduleItem[]) => Promise<boolean>;
}

export const useScheduleStore = create<ScheduleState>()(
  persist(
    (set, get) => ({
      schedules: [],
      currentSchedule: null,
      scheduleItems: [],
      loading: false,
      error: null,

      fetchSchedules: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return;
          }

          const { data, error } = await supabase
            .from('schedules')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });

          if (error) {
            console.error('Error fetching schedules:', error);
            set({ error, loading: false });
            return;
          }

          if (data) {
            const schedules: Schedule[] = data.map(schedule => ({
              id: schedule.id,
              title: schedule.title,
              description: schedule.description,
              type: schedule.type,
              startDate: schedule.start_date,
              endDate: schedule.end_date,
              repeatWeekly: schedule.repeat_weekly,
              repeatMonthly: schedule.repeat_monthly,
              active: schedule.active,
              settings: schedule.settings,
              createdAt: schedule.created_at,
              updatedAt: schedule.updated_at,
            }));
            set({ schedules, loading: false });
          } else {
            set({ loading: false });
          }
        } catch (error: any) {
          console.error('Error in fetchSchedules:', error);
          set({ error, loading: false });
        }
      },

      fetchScheduleItems: async (scheduleId: string) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return [];
          }

          const { data, error } = await supabase
            .from('schedule_items')
            .select('*')
            .eq('schedule_id', scheduleId)
            .order('order', { ascending: true });

          if (error) {
            console.error('Error fetching schedule items:', error);
            set({ error, loading: false });
            return [];
          }

          if (data) {
            const items: ScheduleItem[] = data.map(item => ({
              id: item.id,
              scheduleId: item.schedule_id,
              subjectId: item.subject_id,
              subjectTitle: item.subject_title,
              dayOfWeek: item.day_of_week,
              dayOfMonth: item.day_of_month,
              specificDate: item.specific_date,
              startTime: item.start_time,
              endTime: item.end_time,
              createEvent: item.create_event,
              createTodo: item.create_todo,
              color: item.color,
              icon: item.icon,
              order: item.order || 0,
              createdAt: item.created_at,
              updatedAt: item.updated_at,
            }));
            set({ scheduleItems: items, loading: false });
            return items;
          }

          set({ loading: false });
          return [];
        } catch (error: any) {
          console.error('Error in fetchScheduleItems:', error);
          set({ error, loading: false });
          return [];
        }
      },

      createSchedule: async (scheduleData) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return null;
          }

          const newSchedule = {
            title: scheduleData.title,
            description: scheduleData.description || "",
            type: scheduleData.type,
            start_date: scheduleData.startDate,
            end_date: scheduleData.endDate,
            repeat_weekly: scheduleData.repeatWeekly,
            repeat_monthly: scheduleData.repeatMonthly,
            active: scheduleData.active,
            settings: scheduleData.settings || {},
            user_id: user.id,
          };

          const { data, error } = await supabase
            .from('schedules')
            .insert([newSchedule])
            .select()
            .single();

          if (error) {
            console.error('Error creating schedule:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const schedule: Schedule = {
              id: data.id,
              title: data.title,
              description: data.description,
              type: data.type,
              startDate: data.start_date,
              endDate: data.end_date,
              repeatWeekly: data.repeat_weekly,
              repeatMonthly: data.repeat_monthly,
              active: data.active,
              settings: data.settings,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              schedules: [schedule, ...state.schedules],
              currentSchedule: schedule,
              loading: false,
            }));

            return schedule;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in createSchedule:', error);
          set({ error, loading: false });
          return null;
        }
      },

      updateSchedule: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return null;
          }

          // Convert to snake_case for the database
          const dbUpdates: any = {};
          if (updates.title !== undefined) dbUpdates.title = updates.title;
          if (updates.description !== undefined) dbUpdates.description = updates.description;
          if (updates.type !== undefined) dbUpdates.type = updates.type;
          if (updates.startDate !== undefined) dbUpdates.start_date = updates.startDate;
          if (updates.endDate !== undefined) dbUpdates.end_date = updates.endDate;
          if (updates.repeatWeekly !== undefined) dbUpdates.repeat_weekly = updates.repeatWeekly;
          if (updates.repeatMonthly !== undefined) dbUpdates.repeat_monthly = updates.repeatMonthly;
          if (updates.active !== undefined) dbUpdates.active = updates.active;
          if (updates.settings !== undefined) dbUpdates.settings = updates.settings;
          dbUpdates.updated_at = new Date().toISOString();

          const { data, error } = await supabase
            .from('schedules')
            .update(dbUpdates)
            .eq('id', id)
            .eq('user_id', user.id)
            .select()
            .single();

          if (error) {
            console.error('Error updating schedule:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const updatedSchedule: Schedule = {
              id: data.id,
              title: data.title,
              description: data.description,
              type: data.type,
              startDate: data.start_date,
              endDate: data.end_date,
              repeatWeekly: data.repeat_weekly,
              repeatMonthly: data.repeat_monthly,
              active: data.active,
              settings: data.settings,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              schedules: state.schedules.map(schedule =>
                schedule.id === id ? updatedSchedule : schedule
              ),
              currentSchedule: state.currentSchedule?.id === id ? updatedSchedule : state.currentSchedule,
              loading: false,
            }));

            return updatedSchedule;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in updateSchedule:', error);
          set({ error, loading: false });
          return null;
        }
      },

      deleteSchedule: async (id) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return false;
          }

          const { error } = await supabase
            .from('schedules')
            .delete()
            .eq('id', id)
            .eq('user_id', user.id);

          if (error) {
            console.error('Error deleting schedule:', error);
            set({ error, loading: false });
            return false;
          }

          set(state => ({
            schedules: state.schedules.filter(schedule => schedule.id !== id),
            currentSchedule: state.currentSchedule?.id === id ? null : state.currentSchedule,
            scheduleItems: state.scheduleItems.filter(item => item.scheduleId !== id),
            loading: false,
          }));

          return true;
        } catch (error: any) {
          console.error('Error in deleteSchedule:', error);
          set({ error, loading: false });
          return false;
        }
      },

      addScheduleItem: async (itemData) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return null;
          }

          // Extrair apenas a parte de hora:minuto:segundo para os campos de hora
          let startTime = itemData.startTime;
          let endTime = itemData.endTime;

          // Se for uma string ISO, extrair apenas a parte de tempo
          if (startTime && startTime.includes('T')) {
            const date = new Date(startTime);
            startTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:00`;
          }

          if (endTime && endTime.includes('T')) {
            const date = new Date(endTime);
            endTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:00`;
          }

          // Obter o maior valor de order para este cronograma
          const { data: existingItems, error: fetchError } = await supabase
            .from('schedule_items')
            .select('order')
            .eq('schedule_id', itemData.scheduleId)
            .order('order', { ascending: false })
            .limit(1);

          // Definir o novo valor de order (maior + 1 ou 0 se não houver itens)
          const nextOrder = existingItems && existingItems.length > 0 ? (existingItems[0].order + 1) : 0;

          const newItem = {
            schedule_id: itemData.scheduleId,
            subject_id: itemData.subjectId,
            subject_title: itemData.subjectTitle,
            day_of_week: itemData.dayOfWeek,
            day_of_month: itemData.dayOfMonth,
            specific_date: itemData.specificDate,
            start_time: startTime,
            end_time: endTime,
            create_event: itemData.createEvent,
            create_todo: itemData.createTodo,
            color: itemData.color,
            icon: itemData.icon,
            order: nextOrder,
          };

          const { data, error } = await supabase
            .from('schedule_items')
            .insert([newItem])
            .select()
            .single();

          if (error) {
            console.error('Error adding schedule item:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const item: ScheduleItem = {
              id: data.id,
              scheduleId: data.schedule_id,
              subjectId: data.subject_id,
              subjectTitle: data.subject_title,
              dayOfWeek: data.day_of_week,
              dayOfMonth: data.day_of_month,
              specificDate: data.specific_date,
              startTime: data.start_time,
              endTime: data.end_time,
              createEvent: data.create_event,
              createTodo: data.create_todo,
              color: data.color,
              icon: data.icon,
              order: data.order || 0,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              scheduleItems: [...state.scheduleItems, item],
              loading: false,
            }));

            return item;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in addScheduleItem:', error);
          set({ error, loading: false });
          return null;
        }
      },

      updateScheduleItem: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return null;
          }

          // Convert to snake_case for the database
          const dbUpdates: any = {};
          if (updates.scheduleId !== undefined) dbUpdates.schedule_id = updates.scheduleId;
          if (updates.subjectId !== undefined) dbUpdates.subject_id = updates.subjectId;
          if (updates.subjectTitle !== undefined) dbUpdates.subject_title = updates.subjectTitle;
          if (updates.dayOfWeek !== undefined) dbUpdates.day_of_week = updates.dayOfWeek;
          if (updates.dayOfMonth !== undefined) dbUpdates.day_of_month = updates.dayOfMonth;
          if (updates.specificDate !== undefined) dbUpdates.specific_date = updates.specificDate;

          // Processar campos de hora
          if (updates.startTime !== undefined) {
            let startTime = updates.startTime;
            if (startTime && startTime.includes('T')) {
              const date = new Date(startTime);
              startTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:00`;
            }
            dbUpdates.start_time = startTime;
          }

          if (updates.endTime !== undefined) {
            let endTime = updates.endTime;
            if (endTime && endTime.includes('T')) {
              const date = new Date(endTime);
              endTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:00`;
            }
            dbUpdates.end_time = endTime;
          }

          if (updates.createEvent !== undefined) dbUpdates.create_event = updates.createEvent;
          if (updates.createTodo !== undefined) dbUpdates.create_todo = updates.createTodo;
          if (updates.color !== undefined) dbUpdates.color = updates.color;
          if (updates.icon !== undefined) dbUpdates.icon = updates.icon;
          if (updates.order !== undefined) dbUpdates.order = updates.order;
          dbUpdates.updated_at = new Date().toISOString();

          const { data, error } = await supabase
            .from('schedule_items')
            .update(dbUpdates)
            .eq('id', id)
            .select()
            .single();

          if (error) {
            console.error('Error updating schedule item:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const updatedItem: ScheduleItem = {
              id: data.id,
              scheduleId: data.schedule_id,
              subjectId: data.subject_id,
              subjectTitle: data.subject_title,
              dayOfWeek: data.day_of_week,
              dayOfMonth: data.day_of_month,
              specificDate: data.specific_date,
              startTime: data.start_time,
              endTime: data.end_time,
              createEvent: data.create_event,
              createTodo: data.create_todo,
              color: data.color,
              icon: data.icon,
              order: data.order || 0,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              scheduleItems: state.scheduleItems.map(item =>
                item.id === id ? updatedItem : item
              ),
              loading: false,
            }));

            return updatedItem;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in updateScheduleItem:', error);
          set({ error, loading: false });
          return null;
        }
      },

      deleteScheduleItem: async (id) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return false;
          }

          const { error } = await supabase
            .from('schedule_items')
            .delete()
            .eq('id', id);

          if (error) {
            console.error('Error deleting schedule item:', error);
            set({ error, loading: false });
            return false;
          }

          set(state => ({
            scheduleItems: state.scheduleItems.filter(item => item.id !== id),
            loading: false,
          }));

          return true;
        } catch (error: any) {
          console.error('Error in deleteScheduleItem:', error);
          set({ error, loading: false });
          return false;
        }
      },

      applySchedule: async (scheduleId, startDate, endDate) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return { events: 0, todos: 0 };
          }

          // Get the schedule
          const schedule = get().schedules.find(s => s.id === scheduleId);
          if (!schedule) {
            set({ loading: false });
            return { events: 0, todos: 0 };
          }

          // Get the schedule items
          const items = await get().fetchScheduleItems(scheduleId);
          if (items.length === 0) {
            set({ loading: false });
            return { events: 0, todos: 0 };
          }

          const calendarStore = useCalendarStore.getState();
          let eventsCreated = 0;
          let todosCreated = 0;

          // Generate dates based on provided date range or schedule type
          const dates: Date[] = [];
          const now = new Date();

          // If startDate and endDate are provided, use them
          if (startDate && endDate) {
            dates.push(...eachDayOfInterval({ start: startDate, end: endDate }));
          } else {
            // Otherwise, use the default behavior based on schedule type
            switch (schedule.type) {
              case 'weekly':
                // Get dates for the current week
                const weekStart = startOfWeek(now, { weekStartsOn: 0 }); // Sunday
                const weekEnd = endOfWeek(now, { weekStartsOn: 0 }); // Saturday
                dates.push(...eachDayOfInterval({ start: weekStart, end: weekEnd }));
                break;

              case 'monthly':
                // Get dates for the current month
                const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
                const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                dates.push(...eachDayOfInterval({ start: monthStart, end: monthEnd }));
                break;

              case '30days':
                // Get dates for the next 30 days
                const thirtyDaysEnd = addDays(now, 30);
                dates.push(...eachDayOfInterval({ start: now, end: thirtyDaysEnd }));
                break;

              case 'custom':
                // Use the specific dates from the schedule items
                for (const item of items) {
                  if (item.specificDate) {
                    dates.push(parseISO(item.specificDate));
                  }
                }
                break;
            }
          }

          // Create events and todos for each item
          for (const item of items) {
            // Determine which dates to use for this item
            const itemDates: Date[] = [];

            if (schedule.type === 'custom' && item.specificDate) {
              // For custom schedules, use the specific date
              itemDates.push(parseISO(item.specificDate));
            } else if (item.dayOfWeek !== undefined) {
              // For weekly schedules, filter dates by day of week
              itemDates.push(...dates.filter(date => date.getDay() === item.dayOfWeek));
            } else if (item.dayOfMonth !== undefined) {
              // For monthly schedules, filter dates by day of month
              itemDates.push(...dates.filter(date => date.getDate() === item.dayOfMonth));
            }

            // Create events for each date
            for (const date of itemDates) {
              if (item.createEvent) {
                // Parse start and end times
                let startHours = 0, startMinutes = 0;
                let endHours = 0, endMinutes = 0;

                if (item.startTime.includes('T')) {
                  // Se for uma string ISO
                  const startDate = new Date(item.startTime);
                  startHours = startDate.getHours();
                  startMinutes = startDate.getMinutes();
                } else {
                  // Se for uma string de hora (HH:MM:SS)
                  [startHours, startMinutes] = item.startTime.split(':').map(Number);
                }

                if (item.endTime.includes('T')) {
                  // Se for uma string ISO
                  const endDate = new Date(item.endTime);
                  endHours = endDate.getHours();
                  endMinutes = endDate.getMinutes();
                } else {
                  // Se for uma string de hora (HH:MM:SS)
                  [endHours, endMinutes] = item.endTime.split(':').map(Number);
                }

                // Create start and end date objects
                const startDate = new Date(date);
                startDate.setHours(startHours, startMinutes, 0, 0);

                const endDate = new Date(date);
                endDate.setHours(endHours, endMinutes, 0, 0);

                // Create the event
                const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
                  title: `Estudar ${item.subjectTitle}`,
                  description: `Sessão de estudo para ${item.subjectTitle} (Cronograma: ${schedule.title})`,
                  startDate: startDate.toISOString(),
                  endDate: endDate.toISOString(),
                  allDay: false,
                  color: item.color || '#4F46E5',
                  subject: item.subjectTitle,
                  subject_id: item.subjectId,
                  type: 'study',
                  completed: false,
                  reminder: true,
                  reminderTime: new Date(startDate.getTime() - 30 * 60000).toISOString(), // 30 minutes before
                  recurrence: schedule.repeatWeekly ? 'weekly' : (schedule.repeatMonthly ? 'monthly' : 'none'),
                  has_todos: item.createTodo // Indicar que o evento terá tarefas associadas
                };

                const savedEvent = await calendarStore.addEvent(event);
                if (savedEvent) {
                  eventsCreated++;

                  // Se o item também deve criar uma tarefa, associar a tarefa ao evento
                  if (item.createTodo && savedEvent.id) {
                    const todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'> = {
                      title: `Revisar ${item.subjectTitle}`,
                      description: `Revisar o conteúdo estudado em ${item.subjectTitle} (Cronograma: ${schedule.title})`,
                      dueDate: addDays(date, 1).toISOString(), // Due the next day
                      priority: 'medium',
                      completed: false,
                      subject: item.subjectTitle,
                      subject_id: item.subjectId,
                      event_id: savedEvent.id, // Associar a tarefa ao evento
                      tags: ['revisão', 'cronograma']
                    };

                    const savedTodo = await calendarStore.addTodo(todo);
                    if (savedTodo) {
                      todosCreated++;
                    }
                  }
                }
              }

              // Se o item não criar evento mas criar tarefa, criar uma tarefa independente
              if (!item.createEvent && item.createTodo) {
                const todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'> = {
                  title: `Revisar ${item.subjectTitle}`,
                  description: `Revisar o conteúdo estudado em ${item.subjectTitle} (Cronograma: ${schedule.title})`,
                  dueDate: addDays(date, 1).toISOString(), // Due the next day
                  priority: 'medium',
                  completed: false,
                  subject: item.subjectTitle,
                  subject_id: item.subjectId,
                  tags: ['revisão', 'cronograma']
                };

                const savedTodo = await calendarStore.addTodo(todo);
                if (savedTodo) {
                  todosCreated++;
                }
              }
            }
          }

          // Coletar todos os eventos e tarefas criados para agendar notificações
          const createdEvents = calendarStore.events.slice(-eventsCreated);
          const createdTodos = calendarStore.todos.slice(-todosCreated);

          // Agendar notificações para os eventos e tarefas criados
          const notificationResults = await scheduleNotificationsForSchedule(createdEvents, createdTodos);
          console.log('Notificações agendadas:', {
            eventos: notificationResults.eventNotifications.length,
            tarefas: notificationResults.todoNotifications.length
          });

          set({ loading: false });
          return {
            events: eventsCreated,
            todos: todosCreated,
            notifications: {
              events: notificationResults.eventNotifications.length,
              todos: notificationResults.todoNotifications.length
            }
          };
        } catch (error: any) {
          console.error('Error in applySchedule:', error);
          set({ error, loading: false });
          return { events: 0, todos: 0 };
        }
      },

      setCurrentSchedule: (schedule) => {
        set({ currentSchedule: schedule });
      },

      updateItemsOrder: async (items) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return false;
          }

          // Atualizar cada item com sua nova ordem
          const updates = items.map((item, index) => ({
            id: item.id,
            order: index
          }));

          // Usar upsert para atualizar vários registros de uma vez
          const { error } = await supabase
            .from('schedule_items')
            .upsert(updates, { onConflict: 'id' });

          if (error) {
            console.error('Error updating items order:', error);
            set({ error, loading: false });
            return false;
          }

          // Atualizar o estado local
          set(state => ({
            scheduleItems: items.map((item, index) => ({
              ...item,
              order: index
            })),
            loading: false,
          }));

          return true;
        } catch (error: any) {
          console.error('Error in updateItemsOrder:', error);
          set({ error, loading: false });
          return false;
        }
      },
    }),
    {
      name: "lia-schedule-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
