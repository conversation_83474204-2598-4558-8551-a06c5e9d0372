import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CalendarEvent, TodoItem } from "@/types";
import { supabase } from "@/lib/supabase";
import { formatDateISO } from "@/utils/dateUtils";

// Sample data for initial testing
const sampleEvents: CalendarEvent[] = [
  {
    id: "event1",
    title: "Estudar Matemática",
    description: "Revisão de álgebra linear",
    startDate: new Date(new Date().setHours(10, 0, 0, 0)).toISOString(),
    endDate: new Date(new Date().setHours(12, 0, 0, 0)).toISOString(),
    allDay: false,
    color: "#4F46E5",
    type: "study",
    subject: "Matemática",
    completed: false,
    reminder: true,
    reminderTime: new Date(new Date().setHours(9, 45, 0, 0)).toISOString(),
    recurrence: "weekly",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "event2",
    title: "Prova de Física",
    description: "Capítulos 5-8 do livro",
    startDate: new Date(new Date().setDate(new Date().getDate() + 3)).toISOString(),
    allDay: true,
    color: "#EF4444",
    type: "exam",
    subject: "Física",
    completed: false,
    reminder: true,
    reminderTime: new Date(new Date().setDate(new Date().getDate() + 2)).toISOString(),
    recurrence: "none",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const sampleTodos: TodoItem[] = [
  {
    id: "todo1",
    title: "Resolver exercícios de cálculo",
    description: "Páginas 45-50",
    dueDate: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString(),
    priority: "high",
    completed: false,
    subject: "Matemática",
    tags: ["cálculo", "exercícios"],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "todo2",
    title: "Ler artigo científico",
    description: "Artigo sobre inteligência artificial",
    dueDate: new Date(new Date().setDate(new Date().getDate() + 2)).toISOString(),
    priority: "medium",
    completed: false,
    subject: "Computação",
    tags: ["leitura", "IA"],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "todo3",
    title: "Revisar anotações de aula",
    priority: "low",
    completed: true,
    subject: "História",
    createdAt: new Date(new Date().setDate(new Date().getDate() - 1)).toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

interface CalendarState {
  events: CalendarEvent[];
  todos: TodoItem[];
  loading: boolean;
  error: Error | null;

  // Event actions
  fetchEvents: () => Promise<void>;
  addEvent: (event: Omit<CalendarEvent, "id" | "createdAt" | "updatedAt">) => Promise<CalendarEvent | null>;
  updateEvent: (id: string, updates: Partial<CalendarEvent>) => Promise<CalendarEvent | null>;
  deleteEvent: (id: string) => Promise<boolean>;
  getEventTodos: (eventId: string) => TodoItem[];

  // Todo actions
  fetchTodos: () => Promise<void>;
  addTodo: (todo: Omit<TodoItem, "id" | "createdAt" | "updatedAt">) => Promise<TodoItem | null>;
  updateTodo: (id: string, updates: Partial<TodoItem>) => Promise<TodoItem | null>;
  deleteTodo: (id: string) => Promise<boolean>;
  toggleTodoCompleted: (id: string) => Promise<void>;
  addEventTodo: (eventId: string, todo: Omit<TodoItem, "id" | "createdAt" | "updatedAt" | "event_id">) => Promise<TodoItem | null>;
}

export const useCalendarStore = create<CalendarState>()(
  persist(
    (set, get) => ({
      events: sampleEvents,
      todos: sampleTodos,
      loading: false,
      error: null,

      fetchEvents: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return;
          }

          const { data, error } = await supabase
            .from('calendar_events')
            .select('*')
            .eq('user_id', user.id);

          if (error) {
            console.error('Error fetching events:', error);
            set({ error, loading: false });
            return;
          }

          if (data) {
            const events: CalendarEvent[] = data.map(event => ({
              id: event.id,
              title: event.title,
              description: event.description,
              startDate: event.start_date,
              endDate: event.end_date,
              allDay: event.all_day,
              color: event.color,
              subject: event.subject,
              subject_id: event.subject_id,
              type: event.type,
              completed: event.completed,
              reminder: event.reminder,
              reminderTime: event.reminder_time,
              recurrence: event.recurrence,
              recurrenceEndDate: event.recurrence_end_date,
              recurrenceSettings: event.recurrence_settings,
              has_todos: event.has_todos,
              createdAt: event.created_at,
              updatedAt: event.updated_at,
            }));
            set({ events, loading: false });
          } else {
            set({ loading: false });
          }
        } catch (error: any) {
          console.error('Error in fetchEvents:', error);
          set({ error, loading: false });
        }
      },

      addEvent: async (eventData) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return null;
          }

          // Find subject_id if subject is provided
          let subject_id = null;
          if (eventData.subject) {
            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', eventData.subject)
              .eq('user_id', user.id)
              .maybeSingle();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          const newEvent = {
            title: eventData.title,
            description: eventData.description || "",
            start_date: eventData.startDate,
            end_date: eventData.endDate,
            all_day: eventData.allDay,
            color: eventData.color,
            subject: eventData.subject,
            subject_id: subject_id,
            type: eventData.type,
            completed: eventData.completed || false,
            reminder: eventData.reminder || false,
            reminder_time: eventData.reminderTime,
            recurrence: eventData.recurrence || "none",
            recurrence_end_date: eventData.recurrenceEndDate,
            recurrence_settings: eventData.recurrenceSettings,
            has_todos: eventData.has_todos || false,
            user_id: user.id,
          };

          const { data, error } = await supabase
            .from('calendar_events')
            .insert([newEvent])
            .select()
            .single();

          if (error) {
            console.error('Error adding event:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const event: CalendarEvent = {
              id: data.id,
              title: data.title,
              description: data.description,
              startDate: data.start_date,
              endDate: data.end_date,
              allDay: data.all_day,
              color: data.color,
              subject: data.subject,
              subject_id: data.subject_id,
              type: data.type,
              completed: data.completed,
              reminder: data.reminder,
              reminderTime: data.reminder_time,
              recurrence: data.recurrence,
              recurrenceEndDate: data.recurrence_end_date,
              recurrenceSettings: data.recurrence_settings,
              has_todos: data.has_todos,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              events: [...state.events, event],
              loading: false,
            }));

            return event;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in addEvent:', error);
          set({ error, loading: false });
          return null;
        }
      },

      updateEvent: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return null;
          }

          // Convert to snake_case for the database
          const dbUpdates: any = {};
          if (updates.title !== undefined) dbUpdates.title = updates.title;
          if (updates.description !== undefined) dbUpdates.description = updates.description;
          if (updates.startDate !== undefined) dbUpdates.start_date = updates.startDate;
          if (updates.endDate !== undefined) dbUpdates.end_date = updates.endDate;
          if (updates.allDay !== undefined) dbUpdates.all_day = updates.allDay;
          if (updates.color !== undefined) dbUpdates.color = updates.color;
          if (updates.subject !== undefined) dbUpdates.subject = updates.subject;
          if (updates.subject_id !== undefined) dbUpdates.subject_id = updates.subject_id;
          if (updates.type !== undefined) dbUpdates.type = updates.type;
          if (updates.completed !== undefined) dbUpdates.completed = updates.completed;
          if (updates.reminder !== undefined) dbUpdates.reminder = updates.reminder;
          if (updates.reminderTime !== undefined) dbUpdates.reminder_time = updates.reminderTime;
          if (updates.recurrence !== undefined) dbUpdates.recurrence = updates.recurrence;
          if (updates.recurrenceEndDate !== undefined) dbUpdates.recurrence_end_date = updates.recurrenceEndDate;
          if (updates.recurrenceSettings !== undefined) dbUpdates.recurrence_settings = updates.recurrenceSettings;
          if (updates.has_todos !== undefined) dbUpdates.has_todos = updates.has_todos;
          dbUpdates.updated_at = new Date().toISOString();

          const { data, error } = await supabase
            .from('calendar_events')
            .update(dbUpdates)
            .eq('id', id)
            .eq('user_id', user.id)
            .select()
            .single();

          if (error) {
            console.error('Error updating event:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const updatedEvent: CalendarEvent = {
              id: data.id,
              title: data.title,
              description: data.description,
              startDate: data.start_date,
              endDate: data.end_date,
              allDay: data.all_day,
              color: data.color,
              subject: data.subject,
              subject_id: data.subject_id,
              type: data.type,
              completed: data.completed,
              reminder: data.reminder,
              reminderTime: data.reminder_time,
              recurrence: data.recurrence,
              recurrenceEndDate: data.recurrence_end_date,
              recurrenceSettings: data.recurrence_settings,
              has_todos: data.has_todos,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              events: state.events.map(event =>
                event.id === id ? updatedEvent : event
              ),
              loading: false,
            }));

            return updatedEvent;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in updateEvent:', error);
          set({ error, loading: false });
          return null;
        }
      },

      deleteEvent: async (id) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return false;
          }

          const { error } = await supabase
            .from('calendar_events')
            .delete()
            .eq('id', id)
            .eq('user_id', user.id);

          if (error) {
            console.error('Error deleting event:', error);
            set({ error, loading: false });
            return false;
          }

          set(state => ({
            events: state.events.filter(event => event.id !== id),
            loading: false,
          }));

          return true;
        } catch (error: any) {
          console.error('Error in deleteEvent:', error);
          set({ error, loading: false });
          return false;
        }
      },

      fetchTodos: async () => {
        try {
          set({ loading: true, error: null });

          // Get current authenticated user
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Authentication error:', authError);
            set({ error: authError, loading: false });
            return;
          }

          if (!user) {
            console.error('User not authenticated');
            set({ loading: false, todos: [] });
            return;
          }

          // Fetch todos with strict user filtering
          const { data, error } = await supabase
            .from('todos')
            .select('*')
            .eq('user_id', user.id);

          if (error) {
            console.error('Error fetching todos:', error);
            set({ error, loading: false });
            return;
          }

          if (data) {
            const todos: TodoItem[] = data.map(todo => ({
              id: todo.id,
              title: todo.title,
              description: todo.description,
              dueDate: todo.due_date,
              priority: todo.priority,
              completed: todo.completed,
              subject: todo.subject,
              subject_id: todo.subject_id,
              event_id: todo.event_id,
              tags: todo.tags,
              reminderTime: todo.reminder_time,
              createdAt: todo.created_at,
              updatedAt: todo.updated_at,
            }));
            set({ todos, loading: false });
          } else {
            set({ loading: false });
          }
        } catch (error: any) {
          console.error('Error in fetchTodos:', error);
          set({ error, loading: false });
        }
      },

      addTodo: async (todoData) => {
        try {
          set({ loading: true, error: null });

          // Get current authenticated user
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Authentication error:', authError);
            set({ error: authError, loading: false });
            return null;
          }

          if (!user) {
            console.error('User not authenticated');
            set({ loading: false, error: 'User not authenticated' });
            return null;
          }

          // Find subject_id if subject is provided
          let subject_id = null;
          if (todoData.subject) {
            const { data: subjectData, error: subjectError } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', todoData.subject)
              .eq('user_id', user.id)
              .maybeSingle();

            if (subjectError) {
              console.error('Error finding subject:', subjectError);
            }

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          const newTodo = {
            title: todoData.title,
            description: todoData.description || "",
            due_date: todoData.dueDate,
            priority: todoData.priority,
            completed: todoData.completed || false,
            subject: todoData.subject,
            subject_id: subject_id,
            event_id: todoData.event_id,
            tags: todoData.tags || [],
            reminder_time: todoData.reminderTime,
            user_id: user.id,
          };

          const { data, error } = await supabase
            .from('todos')
            .insert([newTodo])
            .select()
            .single();

          if (error) {
            console.error('Error adding todo:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const todo: TodoItem = {
              id: data.id,
              title: data.title,
              description: data.description,
              dueDate: data.due_date,
              priority: data.priority,
              completed: data.completed,
              subject: data.subject,
              subject_id: data.subject_id,
              event_id: data.event_id,
              tags: data.tags,
              reminderTime: data.reminder_time,
              recurrence: data.recurrence,
              recurrenceEndDate: data.recurrence_end_date,
              recurrenceSettings: data.recurrence_settings,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              todos: [...state.todos, todo],
              loading: false,
            }));

            return todo;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in addTodo:', error);
          set({ error, loading: false });
          return null;
        }
      },

      updateTodo: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          // Get current authenticated user
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Authentication error:', authError);
            set({ error: authError, loading: false });
            return null;
          }

          if (!user) {
            console.error('User not authenticated');
            set({ loading: false, error: 'User not authenticated' });
            return null;
          }

          // Convert to snake_case for the database
          const dbUpdates: any = {};
          if (updates.title !== undefined) dbUpdates.title = updates.title;
          if (updates.description !== undefined) dbUpdates.description = updates.description;
          if (updates.dueDate !== undefined) dbUpdates.due_date = updates.dueDate;
          if (updates.priority !== undefined) dbUpdates.priority = updates.priority;
          if (updates.completed !== undefined) dbUpdates.completed = updates.completed;
          if (updates.subject !== undefined) dbUpdates.subject = updates.subject;
          if (updates.subject_id !== undefined) dbUpdates.subject_id = updates.subject_id;
          if (updates.tags !== undefined) dbUpdates.tags = updates.tags;
          if (updates.recurrence !== undefined) dbUpdates.recurrence = updates.recurrence;
          if (updates.recurrenceEndDate !== undefined) dbUpdates.recurrence_end_date = updates.recurrenceEndDate;
          if (updates.recurrenceSettings !== undefined) dbUpdates.recurrence_settings = updates.recurrenceSettings;
          dbUpdates.updated_at = new Date().toISOString();

          const { data, error } = await supabase
            .from('todos')
            .update(dbUpdates)
            .eq('id', id)
            .eq('user_id', user.id)
            .select()
            .single();

          if (error) {
            console.error('Error updating todo:', error);
            set({ error, loading: false });
            return null;
          }

          if (data) {
            const updatedTodo: TodoItem = {
              id: data.id,
              title: data.title,
              description: data.description,
              dueDate: data.due_date,
              priority: data.priority,
              completed: data.completed,
              subject: data.subject,
              subject_id: data.subject_id,
              event_id: data.event_id,
              tags: data.tags,
              reminderTime: data.reminder_time,
              recurrence: data.recurrence,
              recurrenceEndDate: data.recurrence_end_date,
              recurrenceSettings: data.recurrence_settings,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            set(state => ({
              todos: state.todos.map(todo =>
                todo.id === id ? updatedTodo : todo
              ),
              loading: false,
            }));

            return updatedTodo;
          }

          set({ loading: false });
          return null;
        } catch (error: any) {
          console.error('Error in updateTodo:', error);
          set({ error, loading: false });
          return null;
        }
      },

      deleteTodo: async (id) => {
        try {
          set({ loading: true, error: null });

          // Get current authenticated user
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Authentication error:', authError);
            set({ error: authError, loading: false });
            return false;
          }

          if (!user) {
            console.error('User not authenticated');
            set({ loading: false, error: 'User not authenticated' });
            return false;
          }

          const { error } = await supabase
            .from('todos')
            .delete()
            .eq('id', id)
            .eq('user_id', user.id);

          if (error) {
            console.error('Error deleting todo:', error);
            set({ error, loading: false });
            return false;
          }

          set(state => ({
            todos: state.todos.filter(todo => todo.id !== id),
            loading: false,
          }));

          return true;
        } catch (error: any) {
          console.error('Error in deleteTodo:', error);
          set({ error, loading: false });
          return false;
        }
      },

      toggleTodoCompleted: async (id) => {
        const todo = get().todos.find(t => t.id === id);
        if (!todo) return;

        await get().updateTodo(id, { completed: !todo.completed });
      },

      getEventTodos: (eventId) => {
        return get().todos.filter(todo => todo.event_id === eventId);
      },

      addEventTodo: async (eventId, todoData) => {
        try {
          set({ loading: true, error: null });

          const event = get().events.find(e => e.id === eventId);
          if (!event) {
            set({ loading: false });
            return null;
          }

          // Prepare todo data with event_id
          const todoWithEvent: Omit<TodoItem, "id" | "createdAt" | "updatedAt"> = {
            ...todoData,
            event_id: eventId
          };

          // Add the todo
          const newTodo = await get().addTodo(todoWithEvent);

          // If todo was added successfully, update the event to indicate it has todos
          if (newTodo && !event.has_todos) {
            await get().updateEvent(eventId, { has_todos: true });
          }

          return newTodo;
        } catch (error: any) {
          console.error('Error in addEventTodo:', error);
          set({ error, loading: false });
          return null;
        }
      },
    }),
    {
      name: "lia-calendar-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
