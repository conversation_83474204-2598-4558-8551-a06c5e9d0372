import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { StudyGroup, StudyGroupMember, StudyGroupMaterial, StudyGroupInvite, StudyGroupSettings, BlockedApp } from "@/types";
import { supabase } from "@/lib/supabase";
import { Database } from "@/types/supabase";

type SupabaseStudyGroup = Database['public']['Tables']['study_groups']['Row'];
type SupabaseStudyGroupMember = Database['public']['Tables']['study_group_members']['Row'];
type SupabaseStudyGroupMaterial = Database['public']['Tables']['study_group_materials']['Row'];
type SupabaseStudyGroupInvite = Database['public']['Tables']['study_group_invites']['Row'];

interface StudyGroupState {
  studyGroups: StudyGroup[];
  members: StudyGroupMember[];
  materials: StudyGroupMaterial[];
  invites: StudyGroupInvite[];
  currentGroup: StudyGroup | null;
  loading: boolean;
  error: Error | null;

  // Study Groups
  fetchStudyGroups: () => Promise<StudyGroup[]>;
  fetchStudyGroup: (id: string) => Promise<StudyGroup | null>;
  createStudyGroup: (group: Partial<StudyGroup>) => Promise<StudyGroup>;
  updateStudyGroup: (id: string, updates: Partial<StudyGroup>) => Promise<StudyGroup>;
  deleteStudyGroup: (id: string) => Promise<void>;
  joinStudyGroup: (inviteCode: string) => Promise<StudyGroup>;
  leaveStudyGroup: (id: string) => Promise<void>;
  setCurrentGroup: (group: StudyGroup | null) => void;

  // Members
  fetchGroupMembers: (groupId: string) => Promise<StudyGroupMember[]>;
  updateMemberRole: (memberId: string, role: 'admin' | 'moderator' | 'member') => Promise<void>;
  removeMember: (memberId: string, groupId: string) => Promise<void>;
  transferAdmin: (groupId: string, newAdminId: string) => Promise<void>;
  updateMemberStudyTime: (groupId: string, minutes: number) => Promise<void>;
  updateMemberContribution: (groupId: string, contributionType: 'flashcard' | 'material' | 'comment', count?: number) => Promise<void>;
  updateMemberXP: (groupId: string, xpAmount: number, activityType?: string) => Promise<void>;
  calculateMemberRanking: (groupId: string) => Promise<void>;
  getMemberStats: (memberId: string) => Promise<{ studyTime: number, contributions: number, rank: number, level: number, xpPoints: number }>;

  // Group Settings
  fetchGroupSettings: (groupId: string) => Promise<StudyGroupSettings>;
  updateGroupSettings: (groupId: string, settings: Partial<StudyGroupSettings>) => Promise<StudyGroupSettings>;
  updateBlockedApps: (groupId: string, blockedApps: BlockedApp[]) => Promise<StudyGroupSettings>;
  toggleAppBlocking: (groupId: string, enabled: boolean) => Promise<StudyGroupSettings>;

  // Materials
  fetchGroupMaterials: (groupId: string) => Promise<StudyGroupMaterial[]>;
  addMaterial: (material: Partial<StudyGroupMaterial>) => Promise<StudyGroupMaterial>;
  updateMaterial: (id: string, updates: Partial<StudyGroupMaterial>) => Promise<StudyGroupMaterial>;
  deleteMaterial: (id: string) => Promise<void>;

  // Invites
  fetchGroupInvites: (groupId: string) => Promise<StudyGroupInvite[]>;
  sendInvite: (invite: Partial<StudyGroupInvite>) => Promise<StudyGroupInvite>;
  cancelInvite: (id: string) => Promise<void>;
}

// Helper function to format study group data from Supabase
const formatStudyGroup = (group: SupabaseStudyGroup): StudyGroup => ({
  id: group.id,
  name: group.name,
  description: group.description || '',
  coverImage: group.cover_image || '',
  adminId: group.admin_id,
  isOpen: group.is_open || false,
  inviteCode: group.invite_code || '',
  createdAt: group.created_at || new Date().toISOString(),
  updatedAt: group.updated_at || new Date().toISOString(),
  memberCount: 0, // This will be populated separately if needed
  materialsCount: 0, // This will be populated separately if needed
});

// Helper function to format study group member data from Supabase
const formatStudyGroupMember = (member: any): StudyGroupMember => ({
  id: member.id,
  groupId: member.group_id,
  userId: member.user_id,
  userName: member.users?.name || 'Usuário',
  userAvatar: member.users?.avatar_url || '',
  role: member.role as 'admin' | 'moderator' | 'member',
  joinedAt: member.joined_at || new Date().toISOString(),
  studyTimeMinutes: member.study_time_minutes || 0,
  flashcardsCreated: member.flashcards_created || 0,
  xpPoints: member.xp_points || 0,
  rank: member.rank || 0,
  level: member.level || 1,
});

// Helper function to format study group material data from Supabase
const formatStudyGroupMaterial = (material: any): StudyGroupMaterial => ({
  id: material.id,
  groupId: material.group_id,
  userId: material.user_id,
  userName: material.users?.name || 'Usuário',
  title: material.title,
  description: material.description || '',
  type: material.type,
  content: material.content,
  fileUrl: material.file_url || '',
  createdAt: material.created_at || new Date().toISOString(),
  updatedAt: material.updated_at || new Date().toISOString(),
});

// Helper function to format study group invite data from Supabase
const formatStudyGroupInvite = (invite: any): StudyGroupInvite => ({
  id: invite.id,
  groupId: invite.group_id,
  inviterId: invite.inviter_id,
  inviterName: invite.users?.name || 'Usuário',
  inviteeEmail: invite.invitee_email,
  status: invite.status as 'pending' | 'accepted' | 'rejected' | 'expired',
  createdAt: invite.created_at || new Date().toISOString(),
  expiresAt: invite.expires_at,
});

export const useStudyGroupStore = create<StudyGroupState>()(
  persist(
    (set, get) => ({
      studyGroups: [],
      members: [],
      materials: [],
      invites: [],
      currentGroup: null,
      loading: false,
      error: null,

      // Study Groups
      fetchStudyGroups: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            set({ loading: false, studyGroups: [] });
            return [];
          }

          // Use a direct SQL query to avoid RLS recursion issues
          const { data: groupsData, error: groupsError } = await supabase.rpc('get_user_study_groups', {
            user_id_param: authUser.id
          });

          if (groupsError) {
            console.error('Error fetching study groups with RPC:', groupsError);

            // Fallback to a simpler approach if RPC fails
            try {
              // Fetch all groups (since we disabled RLS)
              const { data: allGroups, error: allGroupsError } = await supabase
                .from('study_groups')
                .select('*');

              if (allGroupsError) throw allGroupsError;

              // Manually filter groups where user is a member or admin
              const { data: memberData } = await supabase
                .from('study_group_members')
                .select('group_id')
                .eq('user_id', authUser.id);

              const userGroupIds = new Set(memberData?.map(m => m.group_id) || []);
              const filteredGroups = allGroups.filter(g =>
                g.admin_id === authUser.id || userGroupIds.has(g.id) || g.is_open
              );

              // Format the groups data with actual counts
              const formattedGroups: StudyGroup[] = await Promise.all(
                filteredGroups.map(async (group) => {
                  try {
                    // Get member count
                    const { count: memberCount } = await supabase
                      .from('study_group_members')
                      .select('*', { count: 'exact', head: true })
                      .eq('group_id', group.id);

                    // Get materials count
                    const { count: materialsCount } = await supabase
                      .from('study_group_materials')
                      .select('*', { count: 'exact', head: true })
                      .eq('group_id', group.id);

                    return {
                      ...formatStudyGroup(group),
                      memberCount: memberCount || 0,
                      materialsCount: materialsCount || 0,
                    };
                  } catch (error) {
                    console.error('Error getting counts for group:', group.id, error);
                    return {
                      ...formatStudyGroup(group),
                      memberCount: 0,
                      materialsCount: 0,
                    };
                  }
                })
              );

              set({ studyGroups: formattedGroups, loading: false });
              return formattedGroups;
            } catch (fallbackError) {
              console.error('Fallback error:', fallbackError);
              throw fallbackError;
            }
          }

          // Format the groups data
          const formattedGroups: StudyGroup[] = groupsData.map(group => ({
            ...formatStudyGroup(group),
            memberCount: group.member_count || 0,
            materialsCount: group.materials_count || 0,
          }));

          set({ studyGroups: formattedGroups, loading: false });
          return formattedGroups;
        } catch (error: any) {
          console.error('Error fetching study groups:', error);
          set({ error, loading: false });
          return [];
        }
      },

      fetchStudyGroup: async (id: string) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            set({ loading: false, currentGroup: null, error: 'Usuário não autenticado' });
            return null;
          }

          // First check if user has access to this group
          const { data: membershipData, error: membershipError } = await supabase.rpc('check_group_membership', {
            group_id_param: id,
            user_id_param: authUser.id
          });

          if (membershipError) {
            console.error('Error checking group membership:', membershipError);
            // Continue with fallback instead of failing completely
          } else if (!membershipData?.is_member && !membershipData?.is_public) {
            set({ loading: false, currentGroup: null, error: 'Você não tem acesso a este grupo' });
            return null;
          }

          // Use a direct SQL query to avoid RLS recursion issues
          const { data: groupsData, error: groupsError } = await supabase.rpc('get_study_group_details', {
            group_id_param: id
          });

          if (groupsError || !groupsData || groupsData.length === 0) {
            console.error('Error fetching study group with RPC:', groupsError);

            // Fallback to a simpler approach if RPC fails
            try {
              // Fetch group details directly
              const { data: groupData, error: groupError } = await supabase
                .from('study_groups')
                .select('*')
                .eq('id', id)
                .single();

              if (groupError) {
                if (groupError.code === 'PGRST116') {
                  set({ loading: false, currentGroup: null, error: 'Grupo não encontrado' });
                  return null;
                }
                throw groupError;
              }

              const formattedGroup: StudyGroup = {
                ...formatStudyGroup(groupData),
                memberCount: 0, // We'll skip counting to avoid recursion
                materialsCount: 0,
              };

              set({ currentGroup: formattedGroup, loading: false });
              return formattedGroup;
            } catch (fallbackError) {
              console.error('Fallback error:', fallbackError);
              set({ loading: false, currentGroup: null, error: 'Erro ao carregar grupo' });
              return null;
            }
          }

          const groupData = groupsData[0];
          const formattedGroup: StudyGroup = {
            ...formatStudyGroup(groupData),
            memberCount: groupData.member_count || 0,
            materialsCount: groupData.materials_count || 0,
          };

          set({ currentGroup: formattedGroup, loading: false });
          return formattedGroup;
        } catch (error: any) {
          console.error('Error fetching study group:', error);
          const errorMessage = error?.message || 'Erro ao carregar grupo';
          set({ error: errorMessage, loading: false, currentGroup: null });
          return null;
        }
      },

      createStudyGroup: async (group: Partial<StudyGroup>) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Generate a random invite code if not provided
          const inviteCode = group.inviteCode || Math.random().toString(36).substring(2, 10).toUpperCase();

          // Create the group
          const { data: groupData, error: groupError } = await supabase
            .from('study_groups')
            .insert([
              {
                name: group.name,
                description: group.description || '',
                cover_image: group.coverImage || '',
                admin_id: authUser.id,
                is_open: group.isOpen !== undefined ? group.isOpen : true,
                invite_code: inviteCode,
              },
            ])
            .select()
            .single();

          if (groupError) {
            throw groupError;
          }

          // Add the creator as an admin member
          const { error: memberError } = await supabase
            .from('study_group_members')
            .insert([
              {
                group_id: groupData.id,
                user_id: authUser.id,
                role: 'admin',
                study_time_minutes: 0,
                flashcards_created: 0,
              },
            ]);

          if (memberError) {
            // Try to delete the group if adding the member failed
            await supabase
              .from('study_groups')
              .delete()
              .eq('id', groupData.id);

            throw memberError;
          }

          const newGroup = formatStudyGroup(groupData);
          set(state => ({
            studyGroups: [...state.studyGroups, newGroup],
            currentGroup: newGroup,
            loading: false,
          }));

          return newGroup;
        } catch (error: any) {
          console.error('Error creating study group:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateStudyGroup: async (id: string, updates: Partial<StudyGroup>) => {
        try {
          set({ loading: true, error: null });

          const { data: groupData, error: groupError } = await supabase
            .from('study_groups')
            .update({
              name: updates.name,
              description: updates.description,
              cover_image: updates.coverImage,
              is_open: updates.isOpen,
              invite_code: updates.inviteCode,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id)
            .select()
            .single();

          if (groupError) {
            throw groupError;
          }

          const updatedGroup = formatStudyGroup(groupData);
          set(state => ({
            studyGroups: state.studyGroups.map(group =>
              group.id === id ? updatedGroup : group
            ),
            currentGroup: state.currentGroup?.id === id ? updatedGroup : state.currentGroup,
            loading: false,
          }));

          return updatedGroup;
        } catch (error: any) {
          console.error('Error updating study group:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      deleteStudyGroup: async (id: string) => {
        try {
          set({ loading: true, error: null });

          // Delete the group
          const { error: deleteError } = await supabase
            .from('study_groups')
            .delete()
            .eq('id', id);

          if (deleteError) {
            throw deleteError;
          }

          set(state => ({
            studyGroups: state.studyGroups.filter(group => group.id !== id),
            currentGroup: state.currentGroup?.id === id ? null : state.currentGroup,
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error deleting study group:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      joinStudyGroup: async (inviteCode: string) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Find the group with this invite code
          const { data: groupData, error: groupError } = await supabase
            .from('study_groups')
            .select('*')
            .eq('invite_code', inviteCode.trim())
            .single();

          if (groupError) {
            throw new Error('Invalid invite code');
          }

          // Check if user is already a member
          const { data: existingMember, error: memberCheckError } = await supabase
            .from('study_group_members')
            .select('*')
            .eq('group_id', groupData.id)
            .eq('user_id', authUser.id)
            .single();

          if (existingMember) {
            throw new Error('You are already a member of this group');
          }

          // Add user to the group
          const { error: joinError } = await supabase
            .from('study_group_members')
            .insert([
              {
                group_id: groupData.id,
                user_id: authUser.id,
                role: 'member',
                study_time_minutes: 0,
                flashcards_created: 0,
              },
            ]);

          if (joinError) {
            throw joinError;
          }

          const newGroup = formatStudyGroup(groupData);

          // Refresh the groups list
          await get().fetchStudyGroups();

          set({ loading: false });
          return newGroup;
        } catch (error: any) {
          console.error('Error joining study group:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      leaveStudyGroup: async (id: string) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Get the group to check if user is admin
          const group = get().studyGroups.find(g => g.id === id);
          if (group && group.adminId === authUser.id) {
            throw new Error('Admins cannot leave the group. Transfer ownership or delete the group instead.');
          }

          // Remove user from the group
          const { error } = await supabase
            .from('study_group_members')
            .delete()
            .eq('group_id', id)
            .eq('user_id', authUser.id);

          if (error) {
            throw error;
          }

          set(state => ({
            studyGroups: state.studyGroups.filter(group => group.id !== id),
            currentGroup: state.currentGroup?.id === id ? null : state.currentGroup,
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error leaving study group:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      setCurrentGroup: (group) => {
        set({ currentGroup: group });
      },

      // Members
      fetchGroupMembers: async (groupId: string) => {
        try {
          set({ loading: true, error: null });

          // Use a direct SQL query to avoid RLS recursion issues
          const { data: membersData, error: membersError } = await supabase.rpc('get_study_group_members', {
            group_id_param: groupId
          });

          if (membersError) {
            console.error('Error fetching group members with RPC:', membersError);

            // Fallback to a simpler approach if RPC fails
            try {
              // Fetch members directly (since we disabled RLS)
              const { data: directMembersData, error: directMembersError } = await supabase
                .from('study_group_members')
                .select('*, users(name, avatar_url)')
                .eq('group_id', groupId);

              if (directMembersError) throw directMembersError;

              const formattedMembers = directMembersData.map(formatStudyGroupMember);

              set({ members: formattedMembers, loading: false });
              return formattedMembers;
            } catch (fallbackError) {
              console.error('Fallback error:', fallbackError);
              throw fallbackError;
            }
          }

          const formattedMembers = membersData.map(member => ({
            id: member.id,
            groupId: member.group_id,
            userId: member.user_id,
            userName: member.user_name || 'Usuário',
            userAvatar: member.user_avatar || '',
            role: member.role as 'admin' | 'moderator' | 'member',
            joinedAt: member.joined_at || new Date().toISOString(),
            studyTimeMinutes: member.study_time_minutes || 0,
            flashcardsCreated: member.flashcards_created || 0,
            rank: member.rank || 0,
            level: member.level || 1,
          }));

          set({ members: formattedMembers, loading: false });
          return formattedMembers;
        } catch (error: any) {
          console.error('Error fetching group members:', error);
          set({ error, loading: false });
          return [];
        }
      },

      updateMemberRole: async (memberId: string, role: 'admin' | 'moderator' | 'member') => {
        try {
          set({ loading: true, error: null });

          const { error } = await supabase
            .from('study_group_members')
            .update({ role })
            .eq('id', memberId);

          if (error) {
            throw error;
          }

          set(state => ({
            members: state.members.map(member =>
              member.id === memberId ? { ...member, role } : member
            ),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error updating member role:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      removeMember: async (memberId: string, groupId: string) => {
        try {
          set({ loading: true, error: null });

          // Usar a função RPC para remover o membro com segurança
          const { data, error } = await supabase
            .rpc('remove_group_member', {
              group_id_param: groupId,
              member_id_param: memberId
            });

          if (error) {
            throw error;
          }

          set(state => ({
            members: state.members.filter(member => member.id !== memberId),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error removing member:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      transferAdmin: async (groupId: string, newAdminId: string) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Usar a função RPC para transferir a administração
          const { data, error } = await supabase
            .rpc('transfer_group_admin', {
              group_id_param: groupId,
              from_user_id: authUser.id,
              to_user_id: newAdminId
            });

          if (error) {
            throw error;
          }

          // Atualizar a lista de membros
          await get().fetchGroupMembers(groupId);

          set({ loading: false });
        } catch (error: any) {
          console.error('Error transferring admin:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateMemberStudyTime: async (groupId: string, minutes: number) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Get current study time
          const { data: memberData, error: memberError } = await supabase
            .from('study_group_members')
            .select('study_time_minutes')
            .eq('group_id', groupId)
            .eq('user_id', authUser.id)
            .single();

          if (memberError) {
            throw memberError;
          }

          const currentStudyTime = memberData?.study_time_minutes || 0;
          const newStudyTime = currentStudyTime + minutes;

          // Update study time
          const { error: updateError } = await supabase
            .from('study_group_members')
            .update({ study_time_minutes: newStudyTime })
            .eq('group_id', groupId)
            .eq('user_id', authUser.id);

          if (updateError) {
            throw updateError;
          }

          set(state => ({
            members: state.members.map(member =>
              member.groupId === groupId && member.userId === authUser.id
                ? { ...member, studyTimeMinutes: newStudyTime }
                : member
            ),
            loading: false,
          }));

          // Adicionar XP para tempo de estudo (10 XP por hora)
          // Converter minutos para horas e calcular XP
          const xpAmount = Math.floor((minutes / 60) * 10);
          if (xpAmount > 0) {
            await get().updateMemberXP(groupId, xpAmount, 'study_time');
          }

          // Recalculate ranking after updating study time
          await get().calculateMemberRanking(groupId);
        } catch (error: any) {
          console.error('Error updating member study time:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateMemberContribution: async (groupId: string, contributionType: 'flashcard' | 'material' | 'comment', count: number = 1) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Get current member data
          const { data: memberData, error: memberError } = await supabase
            .from('study_group_members')
            .select('*')
            .eq('group_id', groupId)
            .eq('user_id', authUser.id)
            .single();

          if (memberError) {
            throw memberError;
          }

          let updateData: any = {};

          // Update the appropriate contribution field
          if (contributionType === 'flashcard') {
            const currentFlashcards = memberData?.flashcards_created || 0;
            updateData.flashcards_created = currentFlashcards + count;

            // Adicionar XP para criação de flashcards (15 XP por flashcard)
            await get().updateMemberXP(groupId, 15 * count, 'flashcard_creation');
          } else if (contributionType === 'material') {
            // Adicionar XP para criação de materiais (20 XP por material)
            await get().updateMemberXP(groupId, 20 * count, 'material_creation');
          } else if (contributionType === 'comment') {
            // Adicionar XP para comentários (5 XP por comentário)
            await get().updateMemberXP(groupId, 5 * count, 'comment');
          }
          // Add more contribution types as needed

          // Update member data
          const { error: updateError } = await supabase
            .from('study_group_members')
            .update(updateData)
            .eq('group_id', groupId)
            .eq('user_id', authUser.id);

          if (updateError) {
            throw updateError;
          }

          set(state => ({
            members: state.members.map(member =>
              member.groupId === groupId && member.userId === authUser.id
                ? {
                    ...member,
                    flashcardsCreated: contributionType === 'flashcard'
                      ? (member.flashcardsCreated || 0) + count
                      : member.flashcardsCreated
                  }
                : member
            ),
            loading: false,
          }));

          // Recalculate ranking after updating contributions
          await get().calculateMemberRanking(groupId);
        } catch (error: any) {
          console.error('Error updating member contributions:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateMemberXP: async (groupId: string, xpAmount: number, activityType = 'general') => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Find the member record
          const { data: memberData, error: memberError } = await supabase
            .from('study_group_members')
            .select('*')
            .eq('group_id', groupId)
            .eq('user_id', authUser.id)
            .single();

          if (memberError) {
            throw memberError;
          }

          // Atualizar os pontos XP do membro
          const currentXP = memberData.xp_points || 0;
          const newXP = currentXP + xpAmount;

          // Calcular o novo nível com base nos pontos XP
          // Cada 100 XP = 1 nível, começando do nível 1
          const newLevel = Math.max(1, Math.floor(newXP / 100) + 1);

          // Atualizar o registro do membro
          const { error: updateError } = await supabase
            .from('study_group_members')
            .update({
              xp_points: newXP,
              level: newLevel
            })
            .eq('id', memberData.id);

          if (updateError) {
            throw updateError;
          }

          // Atualizar o estado local
          set(state => ({
            members: state.members.map(member => {
              if (member.userId === authUser.id && member.groupId === groupId) {
                return {
                  ...member,
                  xpPoints: newXP,
                  level: newLevel
                };
              }
              return member;
            }),
            loading: false,
          }));

          // Atualizar também os pontos XP do usuário na tabela de usuários
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('xp, level')
            .eq('id', authUser.id)
            .single();

          if (!userError && userData) {
            const userCurrentXP = userData.xp || 0;
            const userNewXP = userCurrentXP + xpAmount;
            const userNewLevel = Math.max(1, Math.floor(userNewXP / 100) + 1);

            await supabase
              .from('users')
              .update({
                xp: userNewXP,
                level: userNewLevel,
                xp_to_next_level: (userNewLevel * 100) - userNewXP
              })
              .eq('id', authUser.id);
          }

          // Recalcular rankings
          await get().calculateMemberRanking(groupId);
        } catch (error: any) {
          console.error('Error updating member XP:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      calculateMemberRanking: async (groupId: string) => {
        try {
          set({ loading: true, error: null });

          // Fetch all members of the group
          const { data: membersData, error: membersError } = await supabase
            .from('study_group_members')
            .select('*')
            .eq('group_id', groupId);

          if (membersError) {
            throw membersError;
          }

          if (!membersData || membersData.length === 0) {
            set({ loading: false });
            return;
          }

          // Ordenar membros por pontos XP (decrescente)
          const sortedByXP = [...membersData].sort((a, b) =>
            (b.xp_points || 0) - (a.xp_points || 0)
          );

          // Atribuir ranks com base nos pontos XP
          const updatedMembers = [];
          for (let i = 0; i < sortedByXP.length; i++) {
            const member = sortedByXP[i];
            const rank = i + 1;

            // Atualizar membro com novo rank
            const { error: updateError } = await supabase
              .from('study_group_members')
              .update({ rank })
              .eq('id', member.id);

            if (updateError) {
              console.error('Error updating member rank:', updateError);
              continue;
            }

            updatedMembers.push({
              ...member,
              rank
            });
          }

          // Atualizar estado local com novos ranks
          set(state => ({
            members: state.members.map(member => {
              const updatedMember = updatedMembers.find(m => m.id === member.id);
              return updatedMember
                ? {
                    ...member,
                    rank: updatedMember.rank
                  }
                : member;
            }),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error calculating member ranking:', error);
          set({ error, loading: false });
        }
      },

      getMemberStats: async (memberId: string) => {
        try {
          set({ loading: true, error: null });

          const { data: memberData, error: memberError } = await supabase
            .from('study_group_members')
            .select('*')
            .eq('id', memberId)
            .single();

          if (memberError) {
            throw memberError;
          }

          const stats = {
            studyTime: memberData.study_time_minutes || 0,
            contributions: memberData.flashcards_created || 0,
            rank: memberData.rank || 0,
            level: memberData.level || 1,
            xpPoints: memberData.xp_points || 0,
          };

          set({ loading: false });
          return stats;
        } catch (error: any) {
          console.error('Error getting member stats:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      // Group Settings
      fetchGroupSettings: async (groupId: string) => {
        try {
          set({ loading: true, error: null });

          // Verificar se as configurações existem
          const { data: settingsData, error: settingsError } = await supabase
            .from('study_group_settings')
            .select('*')
            .eq('group_id', groupId)
            .single();

          // Se não existirem configurações, criar com valores padrão
          if (settingsError && settingsError.code === 'PGRST116') {
            const defaultSettings = {
              group_id: groupId,
              allow_member_content: false,
              allow_member_invites: false,
              require_admin_approval: true,
              enable_app_blocking: false,
              blocked_apps: [],
            };

            const { data: newSettings, error: createError } = await supabase
              .from('study_group_settings')
              .insert([defaultSettings])
              .select()
              .single();

            if (createError) {
              throw createError;
            }

            const formattedSettings: StudyGroupSettings = {
              id: newSettings.id,
              groupId: newSettings.group_id,
              allowMemberContent: newSettings.allow_member_content,
              allowMemberInvites: newSettings.allow_member_invites,
              requireAdminApproval: newSettings.require_admin_approval,
              enableAppBlocking: newSettings.enable_app_blocking || false,
              blockedApps: newSettings.blocked_apps || [],
              createdAt: newSettings.created_at,
              updatedAt: newSettings.updated_at,
            };

            set({ loading: false });
            return formattedSettings;
          }

          if (settingsError) {
            throw settingsError;
          }

          const formattedSettings: StudyGroupSettings = {
            id: settingsData.id,
            groupId: settingsData.group_id,
            allowMemberContent: settingsData.allow_member_content,
            allowMemberInvites: settingsData.allow_member_invites,
            requireAdminApproval: settingsData.require_admin_approval,
            enableAppBlocking: settingsData.enable_app_blocking || false,
            blockedApps: settingsData.blocked_apps || [],
            createdAt: settingsData.created_at,
            updatedAt: settingsData.updated_at,
          };

          set({ loading: false });
          return formattedSettings;
        } catch (error: any) {
          console.error('Error fetching group settings:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateGroupSettings: async (groupId: string, settings: Partial<StudyGroupSettings>) => {
        try {
          set({ loading: true, error: null });

          const updateData = {
            allow_member_content: settings.allowMemberContent,
            allow_member_invites: settings.allowMemberInvites,
            require_admin_approval: settings.requireAdminApproval,
            enable_app_blocking: settings.enableAppBlocking,
            blocked_apps: settings.blockedApps,
            updated_at: new Date().toISOString(),
          };

          const { data: updatedSettings, error: updateError } = await supabase
            .from('study_group_settings')
            .update(updateData)
            .eq('group_id', groupId)
            .select()
            .single();

          if (updateError) {
            throw updateError;
          }

          const formattedSettings: StudyGroupSettings = {
            id: updatedSettings.id,
            groupId: updatedSettings.group_id,
            allowMemberContent: updatedSettings.allow_member_content,
            allowMemberInvites: updatedSettings.allow_member_invites,
            requireAdminApproval: updatedSettings.require_admin_approval,
            enableAppBlocking: updatedSettings.enable_app_blocking || false,
            blockedApps: updatedSettings.blocked_apps || [],
            createdAt: updatedSettings.created_at,
            updatedAt: updatedSettings.updated_at,
          };

          set({ loading: false });
          return formattedSettings;
        } catch (error: any) {
          console.error('Error updating group settings:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateBlockedApps: async (groupId: string, blockedApps: BlockedApp[]) => {
        try {
          set({ loading: true, error: null });

          // Buscar configurações atuais
          const { data: settingsData, error: settingsError } = await supabase
            .from('study_group_settings')
            .select('*')
            .eq('group_id', groupId)
            .single();

          if (settingsError) {
            throw settingsError;
          }

          // Atualizar apenas a lista de aplicativos bloqueados
          const { data: updatedSettings, error: updateError } = await supabase
            .from('study_group_settings')
            .update({
              blocked_apps: blockedApps,
              updated_at: new Date().toISOString(),
            })
            .eq('group_id', groupId)
            .select()
            .single();

          if (updateError) {
            throw updateError;
          }

          const formattedSettings: StudyGroupSettings = {
            id: updatedSettings.id,
            groupId: updatedSettings.group_id,
            allowMemberContent: updatedSettings.allow_member_content,
            allowMemberInvites: updatedSettings.allow_member_invites,
            requireAdminApproval: updatedSettings.require_admin_approval,
            enableAppBlocking: updatedSettings.enable_app_blocking || false,
            blockedApps: updatedSettings.blocked_apps || [],
            createdAt: updatedSettings.created_at,
            updatedAt: updatedSettings.updated_at,
          };

          set({ loading: false });
          return formattedSettings;
        } catch (error: any) {
          console.error('Error updating blocked apps:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      toggleAppBlocking: async (groupId: string, enabled: boolean) => {
        try {
          set({ loading: true, error: null });

          // Atualizar apenas o status de bloqueio de aplicativos
          const { data: updatedSettings, error: updateError } = await supabase
            .from('study_group_settings')
            .update({
              enable_app_blocking: enabled,
              updated_at: new Date().toISOString(),
            })
            .eq('group_id', groupId)
            .select()
            .single();

          if (updateError) {
            throw updateError;
          }

          const formattedSettings: StudyGroupSettings = {
            id: updatedSettings.id,
            groupId: updatedSettings.group_id,
            allowMemberContent: updatedSettings.allow_member_content,
            allowMemberInvites: updatedSettings.allow_member_invites,
            requireAdminApproval: updatedSettings.require_admin_approval,
            enableAppBlocking: updatedSettings.enable_app_blocking || false,
            blockedApps: updatedSettings.blocked_apps || [],
            createdAt: updatedSettings.created_at,
            updatedAt: updatedSettings.updated_at,
          };

          set({ loading: false });
          return formattedSettings;
        } catch (error: any) {
          console.error('Error toggling app blocking:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      // Materials
      fetchGroupMaterials: async (groupId: string) => {
        try {
          set({ loading: true, error: null });

          // Verificar se o usuário está autenticado
          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Use a direct SQL query to avoid RLS recursion issues
          const { data: materialsData, error: materialsError } = await supabase.rpc('get_study_group_materials', {
            group_id_param: groupId
          });

          if (materialsError) {
            console.error('Error fetching group materials with RPC:', materialsError);

            // Fallback to a simpler approach if RPC fails
            try {
              // Fetch materials directly (since we disabled RLS)
              // Filtrar para mostrar apenas materiais do usuário autenticado
              const { data: directMaterialsData, error: directMaterialsError } = await supabase
                .from('study_group_materials')
                .select('*, users(name)')
                .eq('group_id', groupId)
                .eq('user_id', authUser.id); // Filtrar por usuário autenticado

              if (directMaterialsError) throw directMaterialsError;

              const formattedMaterials = directMaterialsData.map(formatStudyGroupMaterial);

              set({ materials: formattedMaterials, loading: false });
              return formattedMaterials;
            } catch (fallbackError) {
              console.error('Fallback error:', fallbackError);
              throw fallbackError;
            }
          }

          // Filtrar materiais para mostrar apenas os do usuário autenticado
          const filteredMaterials = materialsData.filter(material => material.user_id === authUser.id);

          const formattedMaterials = filteredMaterials.map(material => ({
            id: material.id,
            groupId: material.group_id,
            userId: material.user_id,
            userName: material.user_name || 'Usuário',
            title: material.title,
            description: material.description || '',
            type: material.type,
            content: material.content,
            fileUrl: material.file_url || '',
            createdAt: material.created_at || new Date().toISOString(),
            updatedAt: material.updated_at || new Date().toISOString(),
          }));

          set({ materials: formattedMaterials, loading: false });
          return formattedMaterials;
        } catch (error: any) {
          console.error('Error fetching group materials:', error);
          set({ error, loading: false });
          return [];
        }
      },

      addMaterial: async (material: Partial<StudyGroupMaterial>) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Prepare material data
          const materialData: any = {
            group_id: material.groupId,
            user_id: authUser.id,
            title: material.title,
            description: material.description || '',
            type: material.type,
            content: material.content || {},
            file_url: material.fileUrl || '',
          };

          // Save to Supabase
          const { data, error } = await supabase
            .from('study_group_materials')
            .insert([materialData])
            .select()
            .single();

          if (error) {
            throw error;
          }

          const newMaterial = formatStudyGroupMaterial({
            ...data,
            users: { name: authUser.user_metadata?.name || 'Usuário' },
          });

          set(state => ({
            materials: [...state.materials, newMaterial],
            loading: false,
          }));

          return newMaterial;
        } catch (error: any) {
          console.error('Error adding material:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateMaterial: async (id: string, updates: Partial<StudyGroupMaterial>) => {
        try {
          set({ loading: true, error: null });

          const { data, error } = await supabase
            .from('study_group_materials')
            .update({
              title: updates.title,
              description: updates.description,
              content: updates.content,
              file_url: updates.fileUrl,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id)
            .select()
            .single();

          if (error) {
            throw error;
          }

          const updatedMaterial = formatStudyGroupMaterial(data);

          set(state => ({
            materials: state.materials.map(material =>
              material.id === id ? updatedMaterial : material
            ),
            loading: false,
          }));

          return updatedMaterial;
        } catch (error: any) {
          console.error('Error updating material:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      deleteMaterial: async (id: string, groupId: string) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Verificar se o usuário é o criador do material ou um administrador
          const { data: materialData, error: materialError } = await supabase
            .from('study_group_materials')
            .select('user_id, group_id')
            .eq('id', id)
            .single();

          if (materialError) {
            throw materialError;
          }

          // Verificar se o usuário é administrador do grupo
          const { data: memberData, error: memberError } = await supabase
            .from('study_group_members')
            .select('role')
            .eq('group_id', materialData.group_id)
            .eq('user_id', authUser.id)
            .single();

          const isAdmin = !memberError && memberData && memberData.role === 'admin';
          const isOwner = materialData.user_id === authUser.id;

          if (!isAdmin && !isOwner) {
            throw new Error('Você não tem permissão para excluir este material');
          }

          // Excluir o material
          const { error } = await supabase
            .from('study_group_materials')
            .delete()
            .eq('id', id);

          if (error) {
            throw error;
          }

          set(state => ({
            materials: state.materials.filter(material => material.id !== id),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error deleting material:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      // Invites
      fetchGroupInvites: async (groupId: string) => {
        try {
          set({ loading: true, error: null });

          // Use a direct SQL query to avoid RLS recursion issues
          const { data: invitesData, error: invitesError } = await supabase.rpc('get_study_group_invites', {
            group_id_param: groupId
          });

          if (invitesError) {
            console.error('Error fetching group invites with RPC:', invitesError);

            // Fallback to a simpler approach if RPC fails
            try {
              // Fetch invites directly (since we disabled RLS)
              const { data: directInvitesData, error: directInvitesError } = await supabase
                .from('study_group_invites')
                .select('*, users!inviter_id(name)')
                .eq('group_id', groupId);

              if (directInvitesError) throw directInvitesError;

              const formattedInvites = directInvitesData.map(formatStudyGroupInvite);

              set({ invites: formattedInvites, loading: false });
              return formattedInvites;
            } catch (fallbackError) {
              console.error('Fallback error:', fallbackError);
              throw fallbackError;
            }
          }

          const formattedInvites = invitesData.map(invite => ({
            id: invite.id,
            groupId: invite.group_id,
            inviterId: invite.inviter_id,
            inviterName: invite.inviter_name || 'Usuário',
            inviteeEmail: invite.invitee_email,
            status: invite.status as 'pending' | 'accepted' | 'rejected' | 'expired',
            createdAt: invite.created_at || new Date().toISOString(),
            expiresAt: invite.expires_at,
          }));

          set({ invites: formattedInvites, loading: false });
          return formattedInvites;
        } catch (error: any) {
          console.error('Error fetching group invites:', error);
          set({ error, loading: false });
          return [];
        }
      },

      sendInvite: async (invite: Partial<StudyGroupInvite>) => {
        try {
          set({ loading: true, error: null });

          const { data: { user: authUser } } = await supabase.auth.getUser();
          if (!authUser) {
            throw new Error('User not authenticated');
          }

          // Create expiration date (30 days from now)
          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + 30);

          // Create invite
          const { data, error } = await supabase
            .from('study_group_invites')
            .insert([
              {
                group_id: invite.groupId,
                inviter_id: authUser.id,
                invitee_email: invite.inviteeEmail,
                status: 'pending',
                expires_at: expiresAt.toISOString(),
              },
            ])
            .select()
            .single();

          if (error) {
            throw error;
          }

          const newInvite = formatStudyGroupInvite({
            ...data,
            users: { name: authUser.user_metadata?.name || 'Usuário' },
          });

          set(state => ({
            invites: [...state.invites, newInvite],
            loading: false,
          }));

          return newInvite;
        } catch (error: any) {
          console.error('Error sending invite:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      cancelInvite: async (id: string) => {
        try {
          set({ loading: true, error: null });

          const { error } = await supabase
            .from('study_group_invites')
            .delete()
            .eq('id', id);

          if (error) {
            throw error;
          }

          set(state => ({
            invites: state.invites.filter(invite => invite.id !== id),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error canceling invite:', error);
          set({ error, loading: false });
          throw error;
        }
      },
    }),
    {
      name: "lia-study-groups-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
