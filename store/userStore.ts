import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { User } from "@/types";
import { supabase } from "@/lib/supabase";
import { Database } from "@/types/supabase";

type SupabaseUser = Database['public']['Tables']['users']['Row'];

interface UserState {
  user: User;
  supabaseUser: SupabaseUser | null;
  loading: boolean;
  error: Error | null;
  setUser: (user: Partial<User>) => void;
  incrementStreak: () => void;
  addStudyTime: (minutes: number) => void;
  addXP: (amount: number) => void;
  fetchSupabaseUser: () => Promise<void>;
  updateSupabaseUser: (updates: Partial<SupabaseUser>) => Promise<void>;
  syncUserWithSupabase: () => Promise<void>;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: {
        name: "Estudante",
        streak: 0,
        totalStudyTime: 0,
        level: 1,
        xp: 0,
        xpToNextLevel: 100,
      },
      supabaseUser: null,
      loading: false,
      error: null,
      setUser: (userData) =>
        set((state) => ({
          user: { ...state.user, ...userData },
        })),
      incrementStreak: () =>
        set((state) => ({
          user: { ...state.user, streak: state.user.streak + 1 },
        })),
      addStudyTime: (minutes) =>
        set((state) => ({
          user: {
            ...state.user,
            totalStudyTime: state.user.totalStudyTime + minutes,
          },
        })),
      addXP: (amount) =>
        set((state) => {
          const newXP = state.user.xp + amount;
          const xpToNextLevel = state.user.xpToNextLevel;

          // Check if user leveled up
          if (newXP >= xpToNextLevel) {
            const newLevel = state.user.level + 1;
            const remainingXP = newXP - xpToNextLevel;
            const nextLevelXP = Math.round(xpToNextLevel * 1.5);

            return {
              user: {
                ...state.user,
                level: newLevel,
                xp: remainingXP,
                xpToNextLevel: nextLevelXP,
              },
            };
          }

          return {
            user: {
              ...state.user,
              xp: newXP,
            },
          };
        }),
      fetchSupabaseUser: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();

          if (!user) {
            set({ supabaseUser: null, loading: false });
            return;
          }

          const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error) throw error;

          set({ supabaseUser: data, loading: false });

          // Update local user with Supabase data
          set((state) => ({
            user: {
              ...state.user,
              name: data.name,
              streak: data.streak,
              totalStudyTime: data.total_study_time,
              level: data.level,
              xp: data.xp,
              xpToNextLevel: data.xp_to_next_level,
            },
          }));

          // Update streak if needed
          const today = new Date().toISOString().split('T')[0];
          const lastStreakDate = data.last_streak_date ? new Date(data.last_streak_date).toISOString().split('T')[0] : null;

          // If last access was not today, update streak
          if (lastStreakDate !== today) {
            get().updateSupabaseUser({ last_streak_date: today });
          }
        } catch (error: any) {
          console.error('Error fetching user:', error);
          set({ error, loading: false });
        }
      },

      updateSupabaseUser: async (updates) => {
        try {
          const { supabaseUser } = get();
          if (!supabaseUser) return;

          set({ loading: true, error: null });

          const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', supabaseUser.id)
            .select()
            .single();

          if (error) throw error;

          set({ supabaseUser: data, loading: false });
        } catch (error: any) {
          console.error('Error updating user:', error);
          set({ error, loading: false });
        }
      },

      syncUserWithSupabase: async () => {
        const { user, supabaseUser } = get();
        if (!supabaseUser) return;

        await get().updateSupabaseUser({
          streak: user.streak,
          total_study_time: user.totalStudyTime,
          level: user.level,
          xp: user.xp,
          xp_to_next_level: user.xpToNextLevel,
        });
      },
    }),
    {
      name: "lia-user-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);