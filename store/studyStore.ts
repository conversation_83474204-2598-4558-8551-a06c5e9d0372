import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Subject, Activity, FlashcardSet, Flashcard, Quiz, StudyPlan } from "@/types";
// Removido import de dados mock - usando apenas dados do Supabase
import { supabase } from "@/lib/supabase";
import { Database } from "@/types/supabase";
import { calculateNextReview, needsReview, sortByReviewPriority } from "@/utils/spacedRepetition";

type SupabaseSubject = Database['public']['Tables']['subjects']['Row'];
type SupabaseFlashcardSet = Database['public']['Tables']['flashcard_sets']['Row'];
type SupabaseFlashcard = Database['public']['Tables']['flashcards']['Row'];
type SupabaseNote = Database['public']['Tables']['notes']['Row'];
type SupabaseMindMap = Database['public']['Tables']['mind_maps']['Row'];
type SupabaseQuiz = Database['public']['Tables']['quizzes']['Row'];
type SupabaseQuizQuestion = Database['public']['Tables']['quiz_questions']['Row'];
type SupabaseActivity = Database['public']['Tables']['activities']['Row'];

interface StudyState {
  subjects: Subject[];
  activities: Activity[];
  flashcardSets: FlashcardSet[];
  flashcards: Flashcard[];
  quizzes: Quiz[];
  studyPlans: StudyPlan[];
  loading: boolean;
  error: Error | null;

  // Subjects
  addSubject: (subject: Subject) => Promise<Subject>;
  updateSubject: (id: string, updates: Partial<Subject>) => Promise<void>;
  removeSubject: (id: string) => Promise<void>;
  fetchSubjects: () => Promise<void>;

  // Activities
  addActivity: (activity: Activity) => Promise<void>;
  fetchActivities: () => Promise<void>;

  // Flashcards
  addFlashcardSet: (set: FlashcardSet) => Promise<FlashcardSet>;
  updateFlashcardSet: (id: string, updates: Partial<FlashcardSet>) => Promise<void>;
  deleteFlashcardSet: (id: string) => Promise<void>;
  addFlashcard: (flashcard: Flashcard) => Promise<void>;
  updateFlashcard: (id: string, updates: Partial<Flashcard>) => Promise<void>;
  fetchFlashcardSets: () => Promise<void>;
  fetchFlashcards: (setId: string) => Promise<Flashcard[]>;
  getFlashcardsForReview: () => Flashcard[];

  // Quizzes
  addQuiz: (quiz: Quiz) => Promise<Quiz>;
  fetchQuizzes: () => Promise<void>;

  // Study Plans
  addStudyPlan: (plan: StudyPlan) => Promise<void>;
  updateStudyPlan: (id: string, updates: Partial<StudyPlan>) => Promise<void>;
}

export const useStudyStore = create<StudyState>()(
  persist(
    (set, get) => ({
      subjects: [],
      activities: [],
      flashcardSets: [],
      flashcards: [],
      quizzes: [],
      studyPlans: [], // Inicializado vazio - dados virão do Supabase
      loading: false,
      error: null,

      // Subjects
      fetchSubjects: async () => {
        try {
          set({ loading: true, error: null });
          console.log('Buscando matérias...');

          // Primeiro, verificar se há uma sessão ativa
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error('Erro ao obter sessão:', sessionError);
            throw new Error('Erro de autenticação: ' + sessionError.message);
          }

          if (!sessionData.session) {
            console.error('Nenhuma sessão ativa');
            throw new Error('Usuário não autenticado');
          }

          // Se temos uma sessão, tentar obter o usuário
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Erro de autenticação ao buscar matérias:', authError);
            throw new Error('Erro de autenticação: ' + authError.message);
          }

          if (!user) {
            console.error('Usuário não autenticado ao buscar matérias');
            throw new Error('Usuário não autenticado');
          }

          console.log('Usuário autenticado, ID:', user.id);

          const { data, error } = await supabase
            .from('subjects')
            .select('*')
            .eq('user_id', user.id);

          if (error) {
            console.error('Erro ao buscar matérias do Supabase:', error);
            throw error;
          }

          console.log(`Encontradas ${data.length} matérias`);

          // Novos usuários começam com uma lista vazia de matérias
          if (data.length === 0) {
            console.log('Nenhuma matéria encontrada. Novos usuários começam com uma lista vazia.');

            // Não criar matérias padrão, permitir que o usuário crie suas próprias matérias
          }

          const formattedSubjects: Subject[] = data.map(subject => ({
            id: subject.id,
            title: subject.title,
            description: subject.description || '',
            icon: subject.icon || 'book',
            color: subject.color || '#3399FF',
            separator_id: subject.separator_id,
          }));

          console.log('Matérias formatadas:', formattedSubjects);
          set({ subjects: formattedSubjects, loading: false });
          return formattedSubjects;
        } catch (error: any) {
          console.error('Erro ao buscar matérias:', error);
          set({ error, loading: false });
          return [];
        }
      },

      addSubject: async (subject) => {
        try {
          set({ loading: true, error: null });
          console.log('Adicionando matéria:', subject);

          const { data: { user }, error: authError } = await supabase.auth.getUser();
          if (authError) {
            console.error('Erro de autenticação ao adicionar matéria:', authError);
            throw authError;
          }

          if (!user) {
            const error = new Error('Usuário não autenticado');
            console.error('Erro ao adicionar matéria:', error);
            throw error;
          }

          console.log('Usuário autenticado, ID:', user.id);

          // Verificar se a matéria já existe
          const { data: existingSubject, error: checkError } = await supabase
            .from('subjects')
            .select('id, title')
            .eq('title', subject.title)
            .eq('user_id', user.id);

          if (checkError) {
            console.error('Erro ao verificar matéria existente:', checkError);
          }

          if (existingSubject && existingSubject.length > 0) {
            console.log('Matéria já existe:', existingSubject[0]);

            // Retornar a matéria existente
            const existingSubjectData = existingSubject[0];
            const formattedSubject: Subject = {
              id: existingSubjectData.id,
              title: existingSubjectData.title,
              description: subject.description || '',
              icon: subject.icon || 'book',
              color: subject.color || '#3399FF',
              separator_id: null,
            };

            return formattedSubject;
          }

          console.log('Inserindo nova matéria no Supabase');

          const { data, error } = await supabase
            .from('subjects')
            .insert([
              {
                title: subject.title,
                description: subject.description,
                icon: subject.icon,
                color: subject.color,
                user_id: user.id,
                separator_id: subject.separator_id,
              },
            ])
            .select()
            .single();

          if (error) {
            console.error('Erro ao inserir matéria no Supabase:', error);
            throw error;
          }

          console.log('Matéria inserida com sucesso:', data);

          const newSubject: Subject = {
            id: data.id,
            title: data.title,
            description: data.description || '',
            icon: data.icon || 'book',
            color: data.color || '#3399FF',
            separator_id: data.separator_id,
          };

          // Atualizar o estado com a nova matéria
          set((state) => ({
            subjects: [...state.subjects, newSubject],
            loading: false,
          }));

          console.log('Matéria adicionada ao estado com sucesso');
          return newSubject;
        } catch (error: any) {
          console.error('Erro ao adicionar matéria:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateSubject: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          // Obter a matéria atual para verificar o estado antes da atualização
          const { data: currentSubject, error: fetchError } = await supabase
            .from('subjects')
            .select('*')
            .eq('id', id)
            .single();

          if (fetchError) {
            console.error('Erro ao buscar matéria atual:', fetchError);
            throw fetchError;
          }

          console.log('Matéria atual antes da atualização:', currentSubject);

          // Criar um objeto com apenas os campos que foram fornecidos
          const updateData: any = {};
          if (updates.title !== undefined) updateData.title = updates.title;
          if (updates.description !== undefined) updateData.description = updates.description;
          if (updates.icon !== undefined) updateData.icon = updates.icon;
          if (updates.color !== undefined) updateData.color = updates.color;

          // Tratamento especial para separator_id para evitar problemas
          if (updates.separator_id !== undefined) {
            // Se for null, definir explicitamente como null
            // Se for string, usar o valor fornecido
            updateData.separator_id = updates.separator_id;
          }

          // Adicionar timestamp de atualização
          updateData.updated_at = new Date().toISOString();

          console.log('Atualizando matéria no Supabase:', {
            id,
            updateData,
            moving_from: currentSubject.separator_id ? `separador ${currentSubject.separator_id}` : 'nenhum',
            moving_to: updates.separator_id ? `separador ${updates.separator_id}` : 'nenhum'
          });

          // Usar uma abordagem mais simples para atualizar
          const { data, error } = await supabase
            .from('subjects')
            .update(updateData)
            .eq('id', id)
            .select();

          if (error) {
            console.error('Erro ao atualizar matéria no Supabase:', error);
            throw error;
          }

          console.log('Matéria atualizada com sucesso:', data);

          // Atualizar o estado local primeiro para uma resposta mais rápida da UI
          set((state) => {
            const updatedSubjects = state.subjects.map((subject) =>
              subject.id === id ? { ...subject, ...updates } : subject
            );
            console.log('Estado atualizado localmente:', updatedSubjects.find(s => s.id === id));
            return {
              subjects: updatedSubjects,
              loading: false,
            };
          });

          // Em segundo plano, buscar a lista atualizada de matérias para garantir sincronização
          setTimeout(async () => {
            try {
              const { data: updatedSubjectsData, error: listError } = await supabase
                .from('subjects')
                .select('*')
                .eq('user_id', currentSubject.user_id);

              if (listError) {
                console.error('Erro ao buscar lista atualizada de matérias:', listError);
                // Já atualizamos o estado localmente, então não precisamos fazer nada aqui
              } else {
                // Atualizar o estado com os dados mais recentes do banco de dados
                const formattedSubjects = updatedSubjectsData.map(subject => ({
                  id: subject.id,
                  title: subject.title,
                  description: subject.description || '',
                  icon: subject.icon || 'book',
                  color: subject.color || '#3399FF',
                  separator_id: subject.separator_id,
                }));

                console.log('Estado atualizado com dados do banco:', formattedSubjects.find(s => s.id === id));
                set({ subjects: formattedSubjects });
              }
            } catch (syncError) {
              console.error('Erro ao sincronizar dados:', syncError);
              // Não precisamos fazer nada aqui, já que o estado local já foi atualizado
            }
          }, 500);
        } catch (error: any) {
          console.error('Error updating subject:', error);
          set({ error, loading: false });
        }
      },

      removeSubject: async (id) => {
        try {
          set({ loading: true, error: null });

          const { error } = await supabase
            .from('subjects')
            .delete()
            .eq('id', id);

          if (error) throw error;

          set((state) => ({
            subjects: state.subjects.filter((subject) => subject.id !== id),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error removing subject:', error);
          set({ error, loading: false });
        }
      },

      // Activities
      fetchActivities: async () => {
        try {
          set({ loading: true, error: null });

          // Primeiro, verificar se há uma sessão ativa
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

          if (sessionError || !sessionData.session) {
            console.error('Sem sessão ativa para buscar atividades');
            throw new Error('Usuário não autenticado');
          }

          // Se temos uma sessão, tentar obter o usuário
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError || !user) {
            console.error('Erro de autenticação ou usuário não encontrado');
            throw new Error('Usuário não autenticado');
          }

          const { data, error } = await supabase
            .from('activities')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(10);

          if (error) throw error;

          const formattedActivities: Activity[] = data.map(activity => ({
            id: activity.id,
            type: activity.activity_type,
            title: activity.title,
            description: activity.description || '',
            timestamp: new Date(activity.created_at || '').toISOString(),
            subject: activity.subject_id || undefined,
          }));

          set({ activities: formattedActivities, loading: false });
        } catch (error: any) {
          console.error('Error fetching activities:', error);
          set({ error, loading: false });
        }
      },

      addActivity: async (activity) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('User not authenticated');

          const { error } = await supabase
            .from('activities')
            .insert([
              {
                activity_type: activity.type,
                title: activity.title,
                description: activity.description,
                subject_id: activity.subject,
                user_id: user.id,
                related_id: activity.id,
              },
            ]);

          if (error) throw error;

          set((state) => ({
            activities: [activity, ...state.activities],
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error adding activity:', error);
          set({ error, loading: false });
        }
      },

      // Flashcards
      fetchFlashcardSets: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return;

          const { data, error } = await supabase
            .from('flashcard_sets')
            .select('*, subjects(title)')
            .eq('user_id', user.id);

          if (error) throw error;

          const formattedSets: FlashcardSet[] = data.map(set => ({
            id: set.id,
            title: set.title,
            description: set.description || '',
            subject: set.subjects?.title || '',
            cardCount: 0, // Will be updated when fetching flashcards
            lastReviewed: set.updated_at ? new Date(set.updated_at).toISOString() : null,
          }));

          set({ flashcardSets: formattedSets, loading: false });
        } catch (error: any) {
          console.error('Error fetching flashcard sets:', error);
          set({ error, loading: false });
        }
      },

      addFlashcardSet: async (flashcardSet) => {
        try {
          // Definir estado de carregamento
          set({ loading: true, error: null });

          console.log('Adicionando conjunto de flashcards:', flashcardSet);

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Usuário não autenticado');

          console.log('Usuário autenticado:', user.id);

          // Encontrar subject_id a partir do título
          let subject_id = null;
          if (flashcardSet.subject) {
            console.log('Buscando matéria:', flashcardSet.subject);

            try {
              // Verificar se a matéria já existe
              const { data: subjectData, error: subjectError } = await supabase
                .from('subjects')
                .select('id')
                .eq('title', flashcardSet.subject)
                .eq('user_id', user.id);

              if (subjectError) {
                console.error('Erro ao buscar matéria:', subjectError);
              }

              if (subjectData && subjectData.length > 0) {
                subject_id = subjectData[0].id;
                console.log('Matéria encontrada, ID:', subject_id);
              } else {
                console.log('Matéria não encontrada, usando matéria padrão');
                // Não criar nova matéria, apenas continuar sem matéria
                subject_id = null;
              }
            } catch (subjectError) {
              console.error('Erro ao processar matéria:', subjectError);
              // Continuar sem matéria se houver erro
            }
          }

          console.log('Inserindo conjunto no Supabase com subject_id:', subject_id);

          const { data, error } = await supabase
            .from('flashcard_sets')
            .insert([
              {
                title: flashcardSet.title,
                description: flashcardSet.description,
                subject_id,
                user_id: user.id,
              },
            ])
            .select()
            .single();

          if (error) {
            console.error('Erro ao inserir conjunto no Supabase:', error);
            throw error;
          }

          console.log('Conjunto inserido com sucesso:', data);

          const newSet: FlashcardSet = {
            id: data.id,
            title: data.title,
            description: data.description || '',
            subject: flashcardSet.subject || '',
            cardCount: 0,
            lastReviewed: null,
          };

          // Atualizar o estado com o novo conjunto
          set((state) => ({
            flashcardSets: [...state.flashcardSets, newSet],
            loading: false,
          }));

          console.log('Conjunto adicionado ao estado com sucesso');
          return newSet;
        } catch (error: any) {
          console.error('Erro ao adicionar conjunto de flashcards:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      fetchFlashcards: async (setId) => {
        try {
          set({ loading: true, error: null });

          // Verificar se o ID é um UUID válido
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(setId);
          if (!isValidUUID) {
            console.error('ID inválido para busca de flashcards:', setId);
            set({ error: new Error('ID inválido para busca de flashcards'), loading: false });
            return [];
          }

          console.log('Buscando flashcards para o conjunto:', setId);

          const { data, error } = await supabase
            .from('flashcards')
            .select('*')
            .eq('set_id', setId);

          if (error) {
            console.error('Erro do Supabase ao buscar flashcards:', error);
            throw error;
          }

          console.log(`Encontrados ${data.length} flashcards para o conjunto ${setId}`);

          const formattedFlashcards: Flashcard[] = data.map(card => ({
            id: card.id,
            setId: card.set_id,
            front: card.front,
            back: card.back,
            imageUrl: card.image_url || null,
            difficulty: card.difficulty || 0,
            nextReview: card.next_review ? new Date(card.next_review).toISOString() : null,
            reviewCount: card.review_count || 0,
            subject_id: card.subject_id,
            eFactor: card.e_factor || 2.5, // Valor padrão para o E-Factor é 2.5
            lastReviewed: card.last_reviewed ? new Date(card.last_reviewed).toISOString() : null,
          }));

          // Ordenar flashcards por prioridade de revisão
          const sortedFlashcards = sortByReviewPriority(formattedFlashcards);

          set((state) => ({
            flashcards: sortedFlashcards,
            loading: false,
            // Update card count in the set
            flashcardSets: state.flashcardSets.map(s =>
              s.id === setId ? { ...s, cardCount: sortedFlashcards.length } : s
            ),
          }));

          return sortedFlashcards;
        } catch (error: any) {
          console.error('Erro ao buscar flashcards:', error);
          set({ error, loading: false });
          return [];
        }
      },

      // Obter flashcards que precisam ser revisados hoje
      getFlashcardsForReview: () => {
        const { flashcards } = get();
        return flashcards.filter(card => needsReview(card.nextReview ? new Date(card.nextReview) : null));
      },

      addFlashcard: async (flashcard) => {
        try {
          set({ loading: true, error: null });

          // Verificar se o ID do conjunto é um UUID válido
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(flashcard.setId);
          if (!isValidUUID) {
            const error = new Error(`ID do conjunto inválido: ${flashcard.setId}`);
            console.error('Erro ao adicionar flashcard:', error);
            set({ error, loading: false });
            throw error;
          }

          console.log('Adicionando flashcard ao conjunto:', flashcard.setId);

          const { data, error } = await supabase
            .from('flashcards')
            .insert([
              {
                set_id: flashcard.setId,
                front: flashcard.front,
                back: flashcard.back,
                image_url: flashcard.imageUrl,
                difficulty: typeof flashcard.difficulty === 'number' ? flashcard.difficulty : 0,
                next_review: flashcard.nextReview,
                review_count: flashcard.reviewCount || 0,
                subject_id: flashcard.subject_id,
              },
            ])
            .select()
            .single();

          if (error) {
            console.error('Erro do Supabase ao adicionar flashcard:', error);
            throw error;
          }

          console.log('Flashcard adicionado com sucesso:', data.id);

          const newFlashcard: Flashcard = {
            id: data.id,
            setId: data.set_id,
            front: data.front,
            back: data.back,
            imageUrl: data.image_url || null,
            difficulty: data.difficulty || 0,
            nextReview: data.next_review ? new Date(data.next_review).toISOString() : null,
            reviewCount: data.review_count || 0,
            subject_id: data.subject_id,
            eFactor: data.e_factor || 2.5, // Valor padrão para o E-Factor é 2.5
          };

          set((state) => ({
            flashcards: [...state.flashcards, newFlashcard],
            loading: false,
            // Update card count in the set
            flashcardSets: state.flashcardSets.map(s =>
              s.id === flashcard.setId ? { ...s, cardCount: s.cardCount + 1 } : s
            ),
          }));

          return newFlashcard;
        } catch (error: any) {
          console.error('Erro ao adicionar flashcard:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateFlashcard: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          // Prepare update object with only the fields that are provided
          const updateData: any = {};

          if (updates.front !== undefined) updateData.front = updates.front;
          if (updates.back !== undefined) updateData.back = updates.back;
          if (updates.imageUrl !== undefined) updateData.image_url = updates.imageUrl;
          if (updates.lastReviewed !== undefined) updateData.last_reviewed = updates.lastReviewed;

          // Get current flashcard to calculate next review date
          let currentFlashcard = null;
          if (updates.difficulty !== undefined) {
            const { data: flashcardData, error: fetchError } = await supabase
              .from('flashcards')
              .select('*')
              .eq('id', id)
              .single();

            if (fetchError) {
              console.error('Error fetching flashcard for review update:', fetchError);
            } else {
              currentFlashcard = flashcardData;
            }
          }

          // Handle difficulty specially to ensure it's a number
          if (updates.difficulty !== undefined) {
            let difficultyValue = 0;

            if (typeof updates.difficulty === 'number') {
              difficultyValue = updates.difficulty;
            } else if (updates.difficulty === 'easy') {
              difficultyValue = 1;
            } else if (updates.difficulty === 'medium') {
              difficultyValue = 2;
            } else if (updates.difficulty === 'hard') {
              difficultyValue = 3;
            } else {
              difficultyValue = 0;
            }

            updateData.difficulty = difficultyValue;

            // Calculate next review date based on difficulty
            if (currentFlashcard) {
              const reviewCount = (currentFlashcard.review_count || 0) + 1;
              updateData.review_count = reviewCount;

              // Obter o E-Factor atual ou usar o valor padrão
              const currentEFactor = updates.eFactor || currentFlashcard.e_factor || 2.5;

              // Atualizar o E-Factor com base na dificuldade
              if (difficultyValue === 1) { // Fácil
                updateData.e_factor = Math.min(3.0, currentEFactor + 0.1);
              } else if (difficultyValue === 2) { // Médio
                updateData.e_factor = currentEFactor;
              } else if (difficultyValue === 3) { // Difícil
                updateData.e_factor = Math.max(1.3, currentEFactor - 0.3);
              }

              // Calculate next review date using spaced repetition algorithm
              const nextReview = calculateNextReview(
                difficultyValue,
                reviewCount,
                updateData.e_factor
              );
              updateData.next_review = nextReview.toISOString();

              // Atualizar a data da última revisão se não foi fornecida explicitamente
              if (updates.lastReviewed === undefined) {
                updateData.last_reviewed = new Date().toISOString();
              }

              console.log(`Flashcard ${id} updated with difficulty ${difficultyValue}, E-Factor ${updateData.e_factor.toFixed(2)}, next review on ${nextReview.toLocaleDateString()}`);
            }
          } else {
            // If explicitly provided, use those values
            if (updates.nextReview !== undefined) updateData.next_review = updates.nextReview;
            if (updates.reviewCount !== undefined) updateData.review_count = updates.reviewCount;
            if (updates.eFactor !== undefined) updateData.e_factor = updates.eFactor;
          }

          const { error } = await supabase
            .from('flashcards')
            .update(updateData)
            .eq('id', id);

          if (error) throw error;

          // Update local state
          set((state) => {
            const updatedFlashcards = state.flashcards.map((flashcard) => {
              if (flashcard.id === id) {
                const updatedCard = { ...flashcard };

                // Apply all updates
                if (updates.front !== undefined) updatedCard.front = updates.front;
                if (updates.back !== undefined) updatedCard.back = updates.back;
                if (updates.imageUrl !== undefined) updatedCard.imageUrl = updates.imageUrl;
                if (updates.difficulty !== undefined) updatedCard.difficulty = updates.difficulty;
                if (updates.eFactor !== undefined) updatedCard.eFactor = updates.eFactor;

                // Apply calculated values from updateData
                if (updateData.next_review) updatedCard.nextReview = updateData.next_review;
                if (updateData.review_count) updatedCard.reviewCount = updateData.review_count;
                if (updateData.e_factor) updatedCard.eFactor = updateData.e_factor;

                return updatedCard;
              }
              return flashcard;
            });

            return {
              flashcards: updatedFlashcards,
              loading: false,
            };
          });

          return true;
        } catch (error: any) {
          console.error('Error updating flashcard:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      updateFlashcardSet: async (id, updates) => {
        try {
          set({ loading: true, error: null });
          console.log('Atualizando conjunto de flashcards:', id, updates);

          // Verificar se o ID é um UUID válido
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
          if (!isValidUUID) {
            const error = new Error(`ID do conjunto inválido: ${id}`);
            console.error('Erro ao atualizar conjunto de flashcards:', error);
            set({ error, loading: false });
            throw error;
          }

          // Obter o conjunto atual para verificar o estado antes da atualização
          const { data: currentSet, error: fetchError } = await supabase
            .from('flashcard_sets')
            .select('*')
            .eq('id', id)
            .single();

          if (fetchError) {
            console.error('Erro ao buscar conjunto atual:', fetchError);
            throw fetchError;
          }

          console.log('Conjunto atual antes da atualização:', currentSet);

          // Encontrar subject_id a partir do título se fornecido
          let subject_id = currentSet.subject_id;
          if (updates.subject) {
            console.log('Buscando matéria:', updates.subject);

            try {
              // Verificar se a matéria já existe
              const { data: { user } } = await supabase.auth.getUser();
              if (!user) throw new Error('Usuário não autenticado');

              const { data: subjectData, error: subjectError } = await supabase
                .from('subjects')
                .select('id')
                .eq('title', updates.subject)
                .eq('user_id', user.id);

              if (subjectError) {
                console.error('Erro ao buscar matéria:', subjectError);
              }

              if (subjectData && subjectData.length > 0) {
                subject_id = subjectData[0].id;
                console.log('Matéria encontrada, ID:', subject_id);
              } else {
                console.log('Matéria não encontrada, mantendo a matéria atual');
                // Não criar nova matéria, manter a atual
                subject_id = currentSet.subject_id;
              }
            } catch (subjectError) {
              console.error('Erro ao processar matéria:', subjectError);
              // Continuar com a matéria atual se houver erro
            }
          }

          // Criar um objeto com apenas os campos que foram fornecidos
          const updateData: any = {};
          if (updates.title !== undefined) updateData.title = updates.title;
          if (updates.description !== undefined) updateData.description = updates.description;
          if (updates.lastStudied !== undefined) updateData.last_studied = updates.lastStudied;
          if (subject_id !== undefined) updateData.subject_id = subject_id;

          // Adicionar timestamp de atualização
          updateData.updated_at = new Date().toISOString();

          console.log('Atualizando conjunto no Supabase:', {
            id,
            updateData
          });

          // Atualizar o conjunto no Supabase
          const { data, error } = await supabase
            .from('flashcard_sets')
            .update(updateData)
            .eq('id', id)
            .select();

          if (error) {
            console.error('Erro ao atualizar conjunto no Supabase:', error);
            throw error;
          }

          console.log('Conjunto atualizado com sucesso:', data);

          // Atualizar o estado local
          set((state) => {
            const updatedSets = state.flashcardSets.map((set) =>
              set.id === id ? {
                ...set,
                title: updates.title !== undefined ? updates.title : set.title,
                description: updates.description !== undefined ? updates.description : set.description,
                subject: updates.subject !== undefined ? updates.subject : set.subject,
                lastStudied: updates.lastStudied !== undefined ? updates.lastStudied : set.lastStudied
              } : set
            );
            return {
              flashcardSets: updatedSets,
              loading: false,
            };
          });

          console.log('Estado atualizado após atualização do conjunto');
        } catch (error: any) {
          console.error('Erro ao atualizar conjunto de flashcards:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      deleteFlashcardSet: async (id) => {
        try {
          set({ loading: true, error: null });
          console.log('Excluindo conjunto de flashcards:', id);

          // Verificar se o ID é um UUID válido
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
          if (!isValidUUID) {
            const error = new Error(`ID do conjunto inválido: ${id}`);
            console.error('Erro ao excluir conjunto de flashcards:', error);
            set({ error, loading: false });
            throw error;
          }

          // Primeiro excluir todos os flashcards associados ao conjunto
          const { error: flashcardsError } = await supabase
            .from('flashcards')
            .delete()
            .eq('set_id', id);

          if (flashcardsError) {
            console.error('Erro ao excluir flashcards do conjunto:', flashcardsError);
            throw flashcardsError;
          }

          console.log('Flashcards do conjunto excluídos com sucesso');

          // Depois excluir o conjunto
          const { error } = await supabase
            .from('flashcard_sets')
            .delete()
            .eq('id', id);

          if (error) {
            console.error('Erro ao excluir conjunto de flashcards:', error);
            throw error;
          }

          console.log('Conjunto de flashcards excluído com sucesso');

          // Atualizar o estado removendo o conjunto e seus flashcards
          set((state) => ({
            flashcardSets: state.flashcardSets.filter((set) => set.id !== id),
            flashcards: state.flashcards.filter((card) => card.setId !== id),
            loading: false,
          }));

          console.log('Estado atualizado após exclusão do conjunto');
        } catch (error: any) {
          console.error('Erro ao excluir conjunto de flashcards:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      // Quizzes
      fetchQuizzes: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return;

          const { data, error } = await supabase
            .from('quizzes')
            .select('*, subjects(title), quiz_questions(*)')
            .eq('user_id', user.id);

          if (error) throw error;

          const formattedQuizzes: Quiz[] = data.map(quiz => ({
            id: quiz.id,
            title: quiz.title,
            description: quiz.description || '',
            subject: quiz.subjects?.title || '',
            questions: (quiz.quiz_questions || []).map((q: SupabaseQuizQuestion) => ({
              id: q.id,
              question: q.question,
              options: q.options,
              correctOption: q.correct_option,
              explanation: q.explanation || '',
            })),
            createdAt: new Date(quiz.created_at || '').toISOString(),
          }));

          set({ quizzes: formattedQuizzes, loading: false });
        } catch (error: any) {
          console.error('Error fetching quizzes:', error);
          set({ error, loading: false });
        }
      },

      addQuiz: async (quiz) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('User not authenticated');

          // Find subject_id from title
          let subject_id = null;
          if (quiz.subject) {
            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', quiz.subject)
              .eq('user_id', user.id)
              .single();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          // Insert quiz
          const { data, error } = await supabase
            .from('quizzes')
            .insert([
              {
                title: quiz.title,
                description: quiz.description,
                subject_id,
                user_id: user.id,
              },
            ])
            .select()
            .single();

          if (error) throw error;

          // Insert questions
          if (quiz.questions && quiz.questions.length > 0) {
            const questionsToInsert = quiz.questions.map(q => ({
              quiz_id: data.id,
              question: q.question,
              options: q.options,
              correct_option: q.correctOption,
              explanation: q.explanation,
            }));

            const { error: questionsError } = await supabase
              .from('quiz_questions')
              .insert(questionsToInsert);

            if (questionsError) throw questionsError;
          }

          const newQuiz: Quiz = {
            id: data.id,
            title: data.title,
            description: data.description || '',
            subject: quiz.subject || '',
            questions: quiz.questions || [],
            createdAt: new Date().toISOString(),
          };

          set((state) => ({
            quizzes: [...state.quizzes, newQuiz],
            loading: false,
          }));

          return newQuiz;
        } catch (error: any) {
          console.error('Error adding quiz:', error);
          set({ error, loading: false });
          throw error;
        }
      },

      // Study Plans
      addStudyPlan: async (plan) => {
        // For now, we'll just store study plans locally
        set((state) => ({
          studyPlans: [...state.studyPlans, plan],
        }));
      },
      updateStudyPlan: async (id, updates) => {
        // For now, we'll just update study plans locally
        set((state) => ({
          studyPlans: state.studyPlans.map((plan) =>
            plan.id === id ? { ...plan, ...updates } : plan
          ),
        }));
      }
    }),
    {
      name: "lia-study-storage",
      storage: createJSONStorage(() => AsyncStorage)
    }
  )
);