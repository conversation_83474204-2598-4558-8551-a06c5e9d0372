import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Message, Conversation } from "@/types";
import { aiMessages } from "@/constants/mockData";

interface ChatState {
  conversations: {
    [key: string]: Message[];
  };
  conversationMetadata: {
    [key: string]: {
      id: string;
      title: string;
      lastUpdated: string;
      messageCount: number;
      threadId?: string; // Novo campo
    };
  };
  currentConversationId: string | null;

  createConversation: (title?: string) => string;
  setCurrentConversation: (id: string) => void;
  addMessage: (conversationId: string, message: Message) => void;
  clearConversation: (conversationId: string) => void;
  deleteConversation: (conversationId: string) => void;
  updateConversationTitle: (conversationId: string, title: string) => void;
  getAllConversations: () => Conversation[];
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      conversations: {
        "default": aiMessages,
      },
      conversationMetadata: {
        "default": {
          id: "default",
          title: "Nova Conversa",
          lastUpdated: new Date().toISOString(),
          messageCount: aiMessages.length,
        },
      },
      currentConversationId: "default",

      createConversation: (title = "Nova Conversa") => {
        const id = `conv_${Date.now()}`;
        const initialMessage: Message = {
          id: "1",
          role: "system",
          content: "Olá! Sou a LIA, sua assistente de estudos. Como posso ajudar você hoje?",
        };

        set((state) => ({
          conversations: {
            ...state.conversations,
            [id]: [initialMessage],
          },
          conversationMetadata: {
            ...state.conversationMetadata,
            [id]: {
              id,
              title,
              lastUpdated: new Date().toISOString(),
              messageCount: 1,
            },
          },
          currentConversationId: id,
        }));

        return id;
      },

      setCurrentConversation: (id) => {
        set({ currentConversationId: id });
      },

      addMessage: (conversationId, message) => {
        set((state) => {
          const conversation = state.conversations[conversationId] || [];
          const metadata = state.conversationMetadata[conversationId] || {
            id: conversationId,
            title: "Nova Conversa",
            lastUpdated: new Date().toISOString(),
            messageCount: 0,
          };

          // Update conversation title based on first user message if title is default
          let updatedTitle = metadata.title;
          if (metadata.title === "Nova Conversa" && message.role === "user" && conversation.length <= 1) {
            // Use first ~20 chars of user's first message as title
            updatedTitle = message.content.length > 20
              ? `${message.content.substring(0, 20)}...`
              : message.content;
          }

          return {
            conversations: {
              ...state.conversations,
              [conversationId]: [...conversation, message],
            },
            conversationMetadata: {
              ...state.conversationMetadata,
              [conversationId]: {
                ...metadata,
                title: updatedTitle,
                lastUpdated: new Date().toISOString(),
                messageCount: conversation.length + 1,
              },
            },
          };
        });
      },

      clearConversation: (conversationId) => {
        set((state) => {
          const initialMessage: Message = {
            id: "1",
            role: "system",
            content: "Olá! Sou a LIA, sua assistente de estudos. Como posso ajudar você hoje?",
          };

          return {
            conversations: {
              ...state.conversations,
              [conversationId]: [initialMessage],
            },
            conversationMetadata: {
              ...state.conversationMetadata,
              [conversationId]: {
                ...state.conversationMetadata[conversationId],
                lastUpdated: new Date().toISOString(),
                messageCount: 1,
              },
            },
          };
        });
      },

      deleteConversation: (conversationId) => {
        set((state) => {
          const { [conversationId]: _, ...restConversations } = state.conversations;
          const { [conversationId]: __, ...restMetadata } = state.conversationMetadata;

          // If we're deleting the current conversation, set to null or another conversation
          let newCurrentId = state.currentConversationId;
          if (state.currentConversationId === conversationId) {
            const remainingIds = Object.keys(restConversations);
            newCurrentId = remainingIds.length > 0 ? remainingIds[0] : null;
          }

          return {
            conversations: restConversations,
            conversationMetadata: restMetadata,
            currentConversationId: newCurrentId,
          };
        });
      },

      updateConversationTitle: (conversationId, title) => {
        set((state) => ({
          conversationMetadata: {
            ...state.conversationMetadata,
            [conversationId]: {
              ...state.conversationMetadata[conversationId],
              title,
              lastUpdated: new Date().toISOString(),
            },
          },
        }));
      },

      getAllConversations: () => {
        const state = get();
        return Object.values(state.conversationMetadata)
          .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());
      },
    }),
    {
      name: "lia-chat-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
