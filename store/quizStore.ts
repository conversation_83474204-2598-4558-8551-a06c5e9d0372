import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Quiz, QuizQuestion, QuizAttempt, QuizAnswer } from '@/types';
import { supabase } from '@/lib/supabase';

interface QuizStore {
  quizzes: Quiz[];
  loading: boolean;
  error: Error | null;
  fetchQuizzes: () => Promise<void>;
  addQuiz: (quiz: Quiz) => Promise<Quiz>;
  updateQuiz: (id: string, updatedQuiz: Partial<Quiz>) => void;
  deleteQuiz: (id: string) => void;
  getQuizById: (id: string) => Quiz | undefined;
  addQuestionToQuiz: (quizId: string, question: QuizQuestion) => void;
  updateQuestionInQuiz: (quizId: string, questionId: string, updatedQuestion: Partial<QuizQuestion>) => void;
  deleteQuestionFromQuiz: (quizId: string, questionId: string) => void;
  addQuizAttempt: (quizId: string, attempt: Omit<QuizAttempt, 'id' | 'quizId'>) => void;
  getQuizAttempts: (quizId: string) => QuizAttempt[];
  getRecentQuizAttempts: (limit?: number) => QuizAttempt[];
  getQuizStats: (quizId: string) => {
    totalAttempts: number;
    bestScore: number;
    averageScore: number;
    lastAttemptDate: string | null;
    improvement: number; // percentage improvement between first and last attempt
  };
}

// Inicialização com lista vazia para garantir que apenas dados do Supabase sejam exibidos
const sampleQuizzes: Quiz[] = [];

export const useQuizStore = create<QuizStore>(
  persist(
    (set, get) => ({
      quizzes: sampleQuizzes,
      loading: false,
      error: null,

      fetchQuizzes: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return;

          // Limpar quizzes existentes para garantir que apenas dados do Supabase sejam exibidos
          set({ quizzes: [] });

          // Verificar a estrutura da tabela quizzes
          try {
            const { data: tableInfo, error: tableError } = await supabase
              .from('quizzes')
              .select('*')
              .limit(1);

            if (tableInfo && tableInfo.length > 0) {
              console.log('Quiz table structure:', Object.keys(tableInfo[0]));
            }

            if (tableError) {
              console.error('Error fetching quiz table structure:', tableError);
              set({ loading: false, error: new Error('Erro ao acessar a tabela de quizzes') });
              return;
            }
          } catch (e) {
            console.error('Failed to check quiz table structure:', e);
            set({ loading: false, error: new Error('Erro ao verificar a estrutura da tabela de quizzes') });
            return;
          }

          const { data, error } = await supabase
            .from('quizzes')
            .select('*, subjects(title), quiz_questions(*)')
            .eq('user_id', user.id);

          if (error) throw error;

          if (data && data.length > 0) {
            const formattedQuizzes: Quiz[] = data.map(quiz => ({
              id: quiz.id,
              title: quiz.title,
              description: quiz.description || '',
              subject: quiz.subjects?.title || '',
              questions: (quiz.quiz_questions || []).map((q: any) => ({
                id: q.id,
                question: q.question,
                options: q.options,
                correctOption: q.correct_option,
                explanation: q.explanation || '',
              })),
              timeLimit: quiz.time_limit || null,
              lastAttempt: quiz.last_attempt || null,
              bestScore: quiz.best_score || null,
              createdAt: new Date(quiz.created_at || '').toISOString(),
            }));

            set({ quizzes: formattedQuizzes, loading: false });
          } else {
            // Se não houver quizzes no banco de dados, definir uma lista vazia
            set({ quizzes: [], loading: false });
          }
        } catch (error: any) {
          console.error('Error fetching quizzes:', error);
          set({ error, loading: false });
        }
      },

      addQuiz: async (quiz) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('User not authenticated');

          // Find subject_id from title
          let subject_id = null;
          if (quiz.subject) {
            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', quiz.subject)
              .eq('user_id', user.id)
              .single();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          // Insert quiz
          const { data, error } = await supabase
            .from('quizzes')
            .insert([
              {
                title: quiz.title,
                description: quiz.description,
                subject_id,
                user_id: user.id,
                // Usando o nome correto da coluna no Supabase
                time_limit: quiz.timeLimit,
              },
            ])
            .select()
            .single();

          if (error) throw error;

          // Insert questions
          if (quiz.questions && quiz.questions.length > 0) {
            const questionsToInsert = quiz.questions.map(q => ({
              quiz_id: data.id,
              question: q.question,
              options: q.options,
              correct_option: q.correctOption,
              explanation: q.explanation,
            }));

            const { error: questionsError } = await supabase
              .from('quiz_questions')
              .insert(questionsToInsert);

            if (questionsError) throw questionsError;
          }

          const newQuiz: Quiz = {
            id: data.id,
            title: data.title,
            description: data.description || '',
            subject: quiz.subject || '',
            questions: quiz.questions || [],
            timeLimit: quiz.timeLimit,
            lastAttempt: null,
            bestScore: null,
            createdAt: new Date(data.created_at).toISOString(),
          };

          set((state) => ({
            quizzes: [...state.quizzes, newQuiz],
            loading: false,
          }));

          return newQuiz;
        } catch (error: any) {
          console.error('Error adding quiz:', error);
          set({ error, loading: false });
          throw error;
        }
      },

  updateQuiz: async (id, updatedQuiz) => {
    try {
      set({ loading: true, error: null });

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Find subject_id from title if subject is being updated
      let subject_id = null;
      if (updatedQuiz.subject) {
        const { data: subjectData } = await supabase
          .from('subjects')
          .select('id')
          .eq('title', updatedQuiz.subject)
          .eq('user_id', user.id)
          .single();

        if (subjectData) {
          subject_id = subjectData.id;
        }
      }

      // Update quiz basic info
      const updateData: any = {};
      if (updatedQuiz.title) updateData.title = updatedQuiz.title;
      if (updatedQuiz.description) updateData.description = updatedQuiz.description;
      if (subject_id) updateData.subject_id = subject_id;
      // Usando o nome correto da coluna no Supabase
      if (updatedQuiz.timeLimit) updateData.time_limit = updatedQuiz.timeLimit;

      if (Object.keys(updateData).length > 0) {
        const { error } = await supabase
          .from('quizzes')
          .update(updateData)
          .eq('id', id)
          .eq('user_id', user.id);

        if (error) throw error;
      }

      // Update questions if provided
      if (updatedQuiz.questions) {
        // First delete existing questions
        const { error: deleteError } = await supabase
          .from('quiz_questions')
          .delete()
          .eq('quiz_id', id);

        if (deleteError) throw deleteError;

        // Then insert new questions
        const questionsToInsert = updatedQuiz.questions.map(q => ({
          quiz_id: id,
          question: q.question,
          options: q.options,
          correct_option: q.correctOption,
          explanation: q.explanation,
        }));

        const { error: insertError } = await supabase
          .from('quiz_questions')
          .insert(questionsToInsert);

        if (insertError) throw insertError;
      }

      // Update local state
      set((state) => ({
        quizzes: state.quizzes.map((quiz) =>
          quiz.id === id ? { ...quiz, ...updatedQuiz } : quiz
        ),
        loading: false,
      }));
    } catch (error: any) {
      console.error('Error updating quiz:', error);
      set({ error, loading: false });
      throw error;
    }
  },

  deleteQuiz: async (id) => {
    try {
      set({ loading: true, error: null });

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // First delete all questions associated with the quiz
      const { error: questionsError } = await supabase
        .from('quiz_questions')
        .delete()
        .eq('quiz_id', id);

      if (questionsError) throw questionsError;

      // Then delete the quiz itself
      const { error } = await supabase
        .from('quizzes')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      // Update local state
      set((state) => ({
        quizzes: state.quizzes.filter((quiz) => quiz.id !== id),
        loading: false,
      }));
    } catch (error: any) {
      console.error('Error deleting quiz:', error);
      set({ error, loading: false });
      throw error;
    }
  },

  getQuizById: (id) => {
    return get().quizzes.find((quiz) => quiz.id === id);
  },

  addQuestionToQuiz: (quizId, question) => {
    set((state) => ({
      quizzes: state.quizzes.map((quiz) =>
        quiz.id === quizId
          ? { ...quiz, questions: [...quiz.questions, question] }
          : quiz
      )
    }));
  },

  updateQuestionInQuiz: (quizId, questionId, updatedQuestion) => {
    set((state) => ({
      quizzes: state.quizzes.map((quiz) =>
        quiz.id === quizId
          ? {
              ...quiz,
              questions: quiz.questions.map((question) =>
                question.id === questionId
                  ? { ...question, ...updatedQuestion }
                  : question
              )
            }
          : quiz
      )
    }));
  },

  deleteQuestionFromQuiz: (quizId, questionId) => {
    set((state) => ({
      quizzes: state.quizzes.map((quiz) =>
        quiz.id === quizId
          ? {
              ...quiz,
              questions: quiz.questions.filter((question) => question.id !== questionId)
            }
          : quiz
      )
    }));
  },

  addQuizAttempt: async (quizId, attempt) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const newAttempt: QuizAttempt = {
        id: `attempt_${Date.now()}`,
        quizId,
        ...attempt
      };

      // Verificar a estrutura da tabela quiz_attempts
      try {
        const { data: attemptsInfo, error: attemptsTableError } = await supabase
          .from('quiz_attempts')
          .select('*')
          .limit(1);

        if (attemptsInfo && attemptsInfo.length > 0) {
          console.log('Quiz attempts table structure:', Object.keys(attemptsInfo[0]));
        }

        if (attemptsTableError) {
          console.log('Quiz attempts table might not exist:', attemptsTableError);
          // Criar a tabela se ela não existir (isso seria feito no Supabase Studio)
          console.log('Please create the quiz_attempts table in Supabase Studio');
        } else {
          // Save attempt to Supabase
          const { error } = await supabase
            .from('quiz_attempts')
            .insert([
              {
                quiz_id: quizId,
                user_id: user.id,
                score: attempt.score,
                total_questions: attempt.totalQuestions,
                time_spent: attempt.timeSpent,
                date: attempt.date,
                // Usando os nomes de colunas conforme definidos no SQL
                answers: attempt.answers,
              },
            ]);

          if (error) {
            console.log('Error details when inserting quiz attempt:', error);
            // Continuar mesmo com erro para atualizar o estado local
          }
        }
      } catch (insertError) {
        console.log('Failed to check or insert quiz attempt:', insertError);
        // Continuar mesmo com erro para atualizar o estado local
      }

      // Update local state
      set((state) => {
        const quiz = state.quizzes.find(q => q.id === quizId);
        if (!quiz) return state;

        const attempts = quiz.attempts || [];
        const newAttempts = [...attempts, newAttempt];

        // Update best score if needed
        const bestScore = Math.max(
          quiz.bestScore || 0,
          (newAttempt.score / newAttempt.totalQuestions) * 100
        );

        // Update last_attempt and best_score in Supabase
        supabase
          .from('quizzes')
          .update({
            // Usando os nomes corretos das colunas no Supabase
            last_attempt: newAttempt.date,
            best_score: bestScore
          })
          .eq('id', quizId)
          .eq('user_id', user.id)
          .then(({ error: updateError }) => {
            if (updateError) {
              console.log('Error updating quiz stats:', updateError);
            }
          })
          .catch(updateErr => {
            console.error('Error updating quiz stats:', updateErr);
          });

        return {
          quizzes: state.quizzes.map(q =>
            q.id === quizId
              ? {
                  ...q,
                  attempts: newAttempts,
                  lastAttempt: newAttempt.date,
                  bestScore
                }
              : q
          )
        };
      });
    } catch (error: any) {
      console.error('Error adding quiz attempt:', error);
    }
  },

  getQuizAttempts: (quizId) => {
    const quiz = get().quizzes.find(q => q.id === quizId);
    if (!quiz || !quiz.attempts) return [];

    return [...quiz.attempts].sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  },

  getRecentQuizAttempts: (limit = 10) => {
    const allAttempts: QuizAttempt[] = [];

    get().quizzes.forEach(quiz => {
      if (quiz.attempts && quiz.attempts.length > 0) {
        quiz.attempts.forEach(attempt => {
          allAttempts.push({
            ...attempt,
            quizId: quiz.id
          });
        });
      }
    });

    return allAttempts
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit);
  },

  getQuizStats: (quizId) => {
    const quiz = get().quizzes.find(q => q.id === quizId);
    if (!quiz || !quiz.attempts || quiz.attempts.length === 0) {
      return {
        totalAttempts: 0,
        bestScore: 0,
        averageScore: 0,
        lastAttemptDate: null,
        improvement: 0
      };
    }

    const attempts = [...quiz.attempts].sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    const totalAttempts = attempts.length;
    const bestScore = Math.max(...attempts.map(a => (a.score / a.totalQuestions) * 100));
    const averageScore = attempts.reduce((sum, a) => sum + (a.score / a.totalQuestions) * 100, 0) / totalAttempts;
    const lastAttemptDate = attempts[attempts.length - 1].date;

    // Calculate improvement between first and last attempt
    const firstScore = (attempts[0].score / attempts[0].totalQuestions) * 100;
    const lastScore = (attempts[attempts.length - 1].score / attempts[attempts.length - 1].totalQuestions) * 100;
    const improvement = firstScore === 0 ? 0 : ((lastScore - firstScore) / firstScore) * 100;

    return {
      totalAttempts,
      bestScore,
      averageScore,
      lastAttemptDate,
      improvement
    };
  }
}), {
  name: "lia-quiz-storage",
  storage: createJSONStorage(() => AsyncStorage)
}));
