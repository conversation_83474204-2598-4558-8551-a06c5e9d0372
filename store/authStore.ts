import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import {
  signInWithGoogle as signInWithGoogleNative,
  signInWithApple as signInWithAppleNative,
  configureGoogleSignIn,
  signOutGoogle
} from '@/services/socialAuth';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: Error | null;

  // Métodos
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  refreshSession: () => Promise<void>;

  // Métodos internos
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      loading: true,
      error: null,

      // Setters
      setUser: (user) => set({ user }),
      setSession: (session) => set({ session }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      // Função para cadastro
      signUp: async (email, password, name) => {
        try {
          set({ loading: true, error: null });
          const { error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                name,
              },
            },
          });

          if (error) throw error;
          Alert.alert('Cadastro realizado!', 'Verifique seu e-mail para confirmar o cadastro.');
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          Alert.alert('Erro no cadastro', error.message);
        } finally {
          set({ loading: false });
        }
      },

      // Função para login
      signIn: async (email, password) => {
        try {
          set({ loading: true, error: null });
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (error) throw error;

          set({
            user: data.user,
            session: data.session
          });

          router.replace('/(tabs)');
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          Alert.alert('Erro no login', error.message);
        } finally {
          set({ loading: false });
        }
      },

      // Função para logout
      signOut: async () => {
        try {
          set({ loading: true, error: null });

          // Fazer logout do Google se estiver logado
          await signOutGoogle();

          // Fazer logout do Supabase
          const { error } = await supabase.auth.signOut();

          if (error) throw error;

          set({
            user: null,
            session: null
          });

          router.replace('/login');
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          Alert.alert('Erro ao sair', error.message);
        } finally {
          set({ loading: false });
        }
      },

      // Função para login com Google (nativo)
      signInWithGoogle: async () => {
        try {
          set({ loading: true, error: null });

          const { data, error } = await signInWithGoogleNative();

          if (error) {
            throw error;
          }

          if (data?.user && data?.session) {
            set({
              user: data.user,
              session: data.session
            });

            router.replace('/(tabs)');
          }
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          Alert.alert('Erro no login com Google', error.message);
        } finally {
          set({ loading: false });
        }
      },

      // Função para login com Apple (nativo)
      signInWithApple: async () => {
        try {
          set({ loading: true, error: null });

          const { data, error } = await signInWithAppleNative();

          if (error) {
            throw error;
          }

          if (data?.user && data?.session) {
            set({
              user: data.user,
              session: data.session
            });

            router.replace('/(tabs)');
          }
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          Alert.alert('Erro no login com Apple', error.message);
        } finally {
          set({ loading: false });
        }
      },

      // Função para redefinir senha
      resetPassword: async (email) => {
        try {
          set({ loading: true, error: null });
          const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: 'com.liastudyapp://reset-password/',
          });

          if (error) throw error;

          Alert.alert('E-mail enviado', 'Verifique seu e-mail para redefinir sua senha.');
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          Alert.alert('Erro ao redefinir senha', error.message);
        } finally {
          set({ loading: false });
        }
      },

      // Função para atualizar a sessão
      refreshSession: async () => {
        try {
          set({ loading: true, error: null });

          // Verificar se há uma sessão ativa
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            console.error('Erro ao obter sessão:', error);
            throw error;
          }

          // Se não houver sessão, tentar recuperar do storage
          if (!data.session) {
            console.log('Nenhuma sessão ativa encontrada, verificando storage...');

            try {
              // Tentar recuperar a sessão do AsyncStorage
              const storedAuth = await AsyncStorage.getItem('lia-auth-storage');
              if (storedAuth) {
                const parsedAuth = JSON.parse(storedAuth);
                if (parsedAuth.state && parsedAuth.state.session) {
                  console.log('Sessão encontrada no storage, tentando restaurar...');

                  // Tentar restaurar a sessão
                  const { data: refreshData, error: refreshError } =
                    await supabase.auth.setSession({
                      access_token: parsedAuth.state.session.access_token,
                      refresh_token: parsedAuth.state.session.refresh_token
                    });

                  if (refreshError) {
                    console.error('Erro ao restaurar sessão:', refreshError);
                    // Limpar storage se a sessão não puder ser restaurada
                    await AsyncStorage.removeItem('lia-auth-storage');
                  } else if (refreshData.session) {
                    console.log('Sessão restaurada com sucesso');
                    set({
                      session: refreshData.session,
                      user: refreshData.session?.user ?? null,
                    });
                    set({ loading: false });
                    return;
                  }
                }
              }
            } catch (storageError) {
              console.error('Erro ao acessar storage:', storageError);
            }
          }

          set({
            session: data.session,
            user: data.session?.user ?? null,
          });
        } catch (error: any) {
          set({ error: error instanceof Error ? error : new Error(error.message) });
          console.error('Erro ao atualizar sessão:', error);
        } finally {
          set({ loading: false });
        }
      }
    }),
    {
      name: "lia-auth-storage",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // Não persistir estados temporários
        user: state.user,
        session: state.session,
      }),
    }
  )
);

// Configurar listener para mudanças na autenticação
export const setupAuthListener = () => {
  const { setUser, setSession } = useAuthStore.getState();

  // Configurar Google Sign-In
  configureGoogleSignIn();

  const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
    setSession(session);
    setUser(session?.user ?? null);
  });

  return () => {
    subscription.unsubscribe();
  };
};
