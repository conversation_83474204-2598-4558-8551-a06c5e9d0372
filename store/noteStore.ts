import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Note } from "@/types";
import { supabase } from "@/lib/supabase";

interface NoteState {
  notes: Note[];
  loading: boolean;
  error: Error | null;
  fetchNotes: () => Promise<void>;
  addNote: (note: Note) => Promise<Note | null>;
  updateNote: (id: string, updates: Partial<Note>) => Promise<void>;
  deleteNote: (id: string) => Promise<void>;
}

// Inicialização com lista vazia para garantir que apenas dados do Supabase sejam exibidos
const sampleNotes: Note[] = [
/*
  {
    id: "1",
    title: "Fotossíntese",
    content: "A fotossíntese é o processo pelo qual plantas, algas e algumas bactérias convertem luz solar, água e dióxido de carbono em glicose (açúcar) e oxigênio.\n\nEquação simplificada:\n6CO2 + 6H2O + luz → C6H12O6 + 6O2\n\nFases principais:\n1. Fase clara (dependente de luz): ocorre nas membranas dos tilacoides e produz ATP e NADPH.\n2. Fase escura (ciclo de Calvin): ocorre no estroma e utiliza ATP e NADPH para fixar CO2 e produzir glicose.",
    subject: "Biologia",
    tags: ["fotossíntese", "plantas", "energia"],
    createdAt: "2023-06-05T14:30:00",
    updatedAt: "2023-06-05T14:30:00",
    blocks: [
      {
        id: "block_1",
        type: "heading",
        content: "Fotossíntese",
        position: 0
      },
      {
        id: "block_2",
        type: "text",
        content: "A fotossíntese é o processo pelo qual plantas, algas e algumas bactérias convertem luz solar, água e dióxido de carbono em glicose (açúcar) e oxigênio.",
        position: 1
      },
      {
        id: "block_3",
        type: "subheading",
        content: "Equação simplificada",
        position: 2
      },
      {
        id: "block_4",
        type: "code",
        content: "6CO2 + 6H2O + luz → C6H12O6 + 6O2",
        position: 3
      },
      {
        id: "block_5",
        type: "subheading",
        content: "Fases principais",
        position: 4
      },
      {
        id: "block_6",
        type: "list",
        content: "Fase clara (dependente de luz): ocorre nas membranas dos tilacoides e produz ATP e NADPH.\nFase escura (ciclo de Calvin): ocorre no estroma e utiliza ATP e NADPH para fixar CO2 e produzir glicose.",
        position: 5
      }
    ]
  },
  {
    id: "2",
    title: "Revolução Francesa",
    content: "A Revolução Francesa (1789-1799) foi um período de mudanças políticas e sociais radicais na França que teve um impacto profundo na história mundial.\n\nCausas principais:\n- Crise econômica e financeira\n- Desigualdade social (sistema de privilégios)\n- Influência do Iluminismo\n- Crise política durante o reinado de Luís XVI\n\nEventos importantes:\n- Queda da Bastilha (14 de julho de 1789)\n- Declaração dos Direitos do Homem e do Cidadão\n- Execução do rei Luís XVI\n- O Terror\n- Ascensão de Napoleão Bonaparte",
    subject: "História",
    tags: ["revolução", "frança", "história moderna"],
    createdAt: "2023-06-08T10:15:00",
    updatedAt: "2023-06-08T10:15:00",
  },
  {
    id: "3",
    title: "Funções Trigonométricas",
    content: "As funções trigonométricas relacionam ângulos com comprimentos de lados em um triângulo retângulo.\n\nFunções básicas:\n- Seno (sen): cateto oposto / hipotenusa\n- Cosseno (cos): cateto adjacente / hipotenusa\n- Tangente (tg): cateto oposto / cateto adjacente = sen/cos\n\nRelações importantes:\n- sen²θ + cos²θ = 1\n- tg θ = sen θ / cos θ\n\nDomínio e imagem:\n- sen: D = ℝ, Im = [-1, 1]\n- cos: D = ℝ, Im = [-1, 1]\n- tg: D = ℝ - {π/2 + kπ}, Im = ℝ",
    subject: "Matemática",
    tags: ["trigonometria", "funções", "matemática"],
    createdAt: "2023-06-10T16:45:00",
    updatedAt: "2023-06-10T16:45:00",
  },
*/
];

export const useNoteStore = create<NoteState>()(
  persist(
    (set, get) => ({
      notes: [], // Inicializado vazio - dados virão do Supabase
      loading: false,
      error: null,

      fetchNotes: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ loading: false });
            return;
          }

          const { data, error } = await supabase
            .from('notes')
            .select('*, subjects(title)')
            .eq('user_id', user.id);

          if (error) {
            console.error('Error fetching notes:', error);
            set({ error, loading: false });
            return;
          }

          if (data && data.length > 0) {
            // Temos notas no banco de dados
            const formattedNotes: Note[] = data.map(note => {
              // Convert content back to string for the app
              let contentString = '';
              if (note.content) {
                if (typeof note.content === 'object' && note.content.content) {
                  contentString = note.content.content;
                } else if (typeof note.content === 'string') {
                  contentString = note.content;
                }
              }

              return {
                id: note.id,
                title: note.title || '',
                content: contentString,
                subject: note.subjects?.title || '',
                tags: note.tags || [],
                blocks: note.blocks || [],
                attachments: note.attachments || [],
                createdAt: new Date(note.created_at || '').toISOString(),
                updatedAt: new Date(note.updated_at || '').toISOString(),
              };
            });

            set({ notes: formattedNotes, loading: false });
          } else {
            // Não há notas no banco de dados
            set({ notes: [], loading: false });
          }
        } catch (error: any) {
          console.error('Error fetching notes:', error);
          set({ error, loading: false });
        }
      },

      addNote: async (note) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('User not authenticated');

          // Find subject_id from title
          let subject_id = null;
          if (note.subject) {
            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', note.subject)
              .eq('user_id', user.id)
              .single();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          // Ensure blocks and attachments are arrays
          const blocks = Array.isArray(note.blocks) ? note.blocks : [];
          const attachments = Array.isArray(note.attachments) ? note.attachments : [];

          // Convert content to JSON if it's a string
          let contentJson;
          if (typeof note.content === 'string') {
            // If it's a simple string, convert to a text block
            if (note.content.trim()) {
              contentJson = { type: 'text', content: note.content };
            } else {
              contentJson = {};
            }
          } else {
            contentJson = note.content || {};
          }

          const { data, error } = await supabase
            .from('notes')
            .insert([
              {
                title: note.title,
                content: contentJson,
                tags: note.tags || [],
                blocks: blocks,
                attachments: attachments,
                subject_id,
                user_id: user.id,
              },
            ])
            .select()
            .single();

          if (error) throw error;

          // Convert content back to string for the app
          let contentString = '';
          if (data.content) {
            if (typeof data.content === 'object' && data.content.content) {
              contentString = data.content.content;
            } else if (typeof data.content === 'string') {
              contentString = data.content;
            }
          }

          const newNote: Note = {
            id: data.id,
            title: data.title,
            content: contentString,
            subject: note.subject || '',
            tags: data.tags || [],
            blocks: data.blocks || [],
            attachments: data.attachments || [],
            createdAt: new Date(data.created_at || '').toISOString(),
            updatedAt: new Date(data.updated_at || '').toISOString(),
          };

          set((state) => ({
            notes: [...state.notes, newNote],
            loading: false,
          }));

          return newNote;
        } catch (error: any) {
          console.error('Error adding note:', error);
          set({ error, loading: false });
          return null;
        }
      },

      updateNote: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          // Find subject_id from title if subject is being updated
          let subject_id = undefined;
          if (updates.subject) {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) throw new Error('User not authenticated');

            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', updates.subject)
              .eq('user_id', user.id)
              .single();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          // Prepare update object
          const updateObj: any = {
            updated_at: new Date().toISOString(),
          };

          // Only include fields that are actually being updated
          if (updates.title !== undefined) updateObj.title = updates.title;

          // Handle content as JSON
          if (updates.content !== undefined) {
            let contentJson;
            if (typeof updates.content === 'string') {
              // If it's a simple string, convert to a text block
              if (updates.content.trim()) {
                contentJson = { type: 'text', content: updates.content };
              } else {
                contentJson = {};
              }
            } else {
              contentJson = updates.content || {};
            }
            updateObj.content = contentJson;
          }

          if (updates.tags !== undefined) updateObj.tags = updates.tags || [];
          if (updates.blocks !== undefined) {
            updateObj.blocks = Array.isArray(updates.blocks) ? updates.blocks : [];
          }
          if (updates.attachments !== undefined) {
            updateObj.attachments = Array.isArray(updates.attachments) ? updates.attachments : [];
          }
          if (subject_id !== undefined) updateObj.subject_id = subject_id;

          const { error } = await supabase
            .from('notes')
            .update(updateObj)
            .eq('id', id);

          if (error) throw error;

          // Get the updated note to ensure we have the correct data format
          const { data: updatedNote } = await supabase
            .from('notes')
            .select('*, subjects(title)')
            .eq('id', id)
            .single();

          if (updatedNote) {
            // Convert content back to string for the app
            let contentString = '';
            if (updatedNote.content) {
              if (typeof updatedNote.content === 'object' && updatedNote.content.content) {
                contentString = updatedNote.content.content;
              } else if (typeof updatedNote.content === 'string') {
                contentString = updatedNote.content;
              }
            }

            const formattedNote: Note = {
              id: updatedNote.id,
              title: updatedNote.title,
              content: contentString,
              subject: updatedNote.subjects?.title || '',
              tags: updatedNote.tags || [],
              blocks: updatedNote.blocks || [],
              attachments: updatedNote.attachments || [],
              createdAt: new Date(updatedNote.created_at || '').toISOString(),
              updatedAt: new Date(updatedNote.updated_at || '').toISOString(),
            };

            set((state) => ({
              notes: state.notes.map((note) =>
                note.id === id ? formattedNote : note
              ),
              loading: false,
            }));
          } else {
            // If we couldn't get the updated note, just update with what we have
            set((state) => ({
              notes: state.notes.map((note) =>
                note.id === id ? { ...note, ...updates, updatedAt: new Date().toISOString() } : note
              ),
              loading: false,
            }));
          }
        } catch (error: any) {
          console.error('Error updating note:', error);
          set({ error, loading: false });
        }
      },

      deleteNote: async (id) => {
        try {
          set({ loading: true, error: null });

          const { error } = await supabase
            .from('notes')
            .delete()
            .eq('id', id);

          if (error) throw error;

          set((state) => ({
            notes: state.notes.filter((note) => note.id !== id),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error deleting note:', error);
          set({ error, loading: false });
        }
      },
    }),
    {
      name: "lia-notes-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);