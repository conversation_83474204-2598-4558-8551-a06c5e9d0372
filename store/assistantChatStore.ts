import { create } from "zustand";
import {
  createConversation,
  getConversations,
  getMessages,
  deleteConversation,
  updateConversationTitle,
  resetAssistantThreads,
  sendMessage
} from "@/services/openaiAssistants";

// ID do assistente da OpenAI
const ASSISTANT_ID = 'asst_XhJJPU4A7l0neFMxnZImtvMk';

// Limite máximo de conversas por usuário
const MAX_CONVERSATIONS = 7;

// Limite de mensagens por conversa antes de mostrar aviso
const MAX_MESSAGES_WARNING = 50;

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  created_at?: string;
}

interface Conversation {
  id: string;
  title: string;
  thread_id: string;
  assistant_id: string;
  created_at: string;
  updated_at: string;
  messageCount: number;
  lastUpdated: string;
}

interface AssistantChatState {
  conversations: Conversation[];
  currentConversationId: string | null;
  messages: { [key: string]: Message[] };
  isLoading: boolean;
  error: string | null;
  isTyping: boolean; // Indica se a Lia está "digitando"
  showLargeConversationWarning: boolean; // Indica se deve mostrar o aviso de conversa grande

  // Actions
  fetchConversations: () => Promise<void>;
  fetchMessages: (conversationId: string) => Promise<void>;
  createNewConversation: (title?: string) => Promise<string>;
  sendUserMessage: (conversationId: string, content: string) => Promise<void>;
  setCurrentConversation: (conversationId: string) => void;
  removeConversation: (conversationId: string) => Promise<void>;
  updateTitle: (conversationId: string, title: string) => Promise<void>;
  dismissLargeConversationWarning: () => void; // Função para dispensar o aviso
  checkConversationSize: (conversationId: string) => boolean; // Verifica se a conversa está muito grande
}

export const useAssistantChatStore = create<AssistantChatState>((set, get) => ({
  conversations: [],
  currentConversationId: null,
  messages: {},
  isLoading: false,
  error: null,
  isTyping: false,
  showLargeConversationWarning: false,

  fetchConversations: async () => {
    set({ isLoading: true, error: null });
    try {
      const conversations = await getConversations();
      set({ conversations, isLoading: false });
    } catch (error) {
      console.error('Error fetching conversations:', error);
      // Se o erro for de autenticação, não mostramos como erro, apenas definimos uma lista vazia
      if (error instanceof Error && error.message === 'User not authenticated') {
        set({
          conversations: [],
          isLoading: false,
          error: null
        });
      } else {
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch conversations',
          isLoading: false
        });
      }
    }
  },

  fetchMessages: async (conversationId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Check if we already have messages for this conversation
      if (get().messages[conversationId]?.length > 0) {
        set({ isLoading: false });
        return;
      }

      const messages = await getMessages(conversationId);
      set(state => ({
        messages: {
          ...state.messages,
          [conversationId]: messages
        },
        isLoading: false
      }));
    } catch (error) {
      console.error('Error fetching messages:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch messages',
        isLoading: false
      });
    }
  },

  createNewConversation: async (title = 'Nova Conversa') => {
    set({ isLoading: true, error: null });
    try {
      // Armazenar o ID da conversa atual antes de criar uma nova
      const previousConversationId = get().currentConversationId;

      // Verificar se já atingiu o limite de conversas
      const currentConversations = get().conversations;

      // Se já atingiu o limite, excluir a conversa mais antiga
      if (currentConversations.length >= MAX_CONVERSATIONS) {
        // Ordenar conversas por data de atualização (mais antiga primeiro)
        const sortedConversations = [...currentConversations].sort(
          (a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime()
        );

        // Pegar a conversa mais antiga
        const oldestConversation = sortedConversations[0];
        console.log(`Limite de ${MAX_CONVERSATIONS} conversas atingido. Excluindo conversa mais antiga: ${oldestConversation.id}`);

        // Excluir a conversa mais antiga
        try {
          await deleteConversation(oldestConversation.id);

          // Remover a conversa mais antiga do estado
          set(state => {
            // Remover a conversa e suas mensagens
            const { [oldestConversation.id]: _, ...remainingMessages } = state.messages;

            return {
              conversations: state.conversations.filter(c => c.id !== oldestConversation.id),
              messages: remainingMessages,
            };
          });
        } catch (deleteError) {
          console.error('Erro ao excluir conversa mais antiga:', deleteError);
          // Continuar mesmo com erro, para permitir a criação da nova conversa
        }
      }

      const conversation = await createConversation(title);

      // Atualizar o estado com a nova conversa
      set(state => ({
        conversations: [
          {
            id: conversation.id,
            title: conversation.title,
            thread_id: conversation.thread_id,
            assistant_id: conversation.assistant_id,
            created_at: conversation.created_at,
            updated_at: conversation.updated_at,
            messageCount: 0,
            lastUpdated: conversation.updated_at
          },
          ...state.conversations
        ],
        // Definir explicitamente a nova conversa como a conversa atual
        currentConversationId: conversation.id,
        messages: {
          ...state.messages,
          [conversation.id]: []
        },
        isLoading: false
      }));

      // Registrar a mudança de conversa para debug
      console.log(`Conversa criada: ${conversation.id} (anterior: ${previousConversationId || 'nenhuma'})`);

      return conversation.id;
    } catch (error) {
      console.error('Error creating conversation:', error);
      // Se o erro for de autenticação, criamos uma conversa local temporária
      if (error instanceof Error && error.message === 'User not authenticated') {
        const tempId = `temp_${Date.now()}`;
        const now = new Date().toISOString();

        set(state => ({
          conversations: [
            {
              id: tempId,
              title: title,
              thread_id: 'temp_thread',
              assistant_id: ASSISTANT_ID,
              created_at: now,
              updated_at: now,
              messageCount: 0,
              lastUpdated: now
            },
            ...state.conversations
          ],
          currentConversationId: tempId,
          messages: {
            ...state.messages,
            [tempId]: []
          },
          isLoading: false
        }));

        return tempId;
      } else {
        set({
          error: error instanceof Error ? error.message : 'Failed to create conversation',
          isLoading: false
        });
        throw error;
      }
    }
  },

  sendUserMessage: async (conversationId: string, content: string) => {
    // Verificar se a conversa está muito grande
    const currentMessages = get().messages[conversationId] || [];
    if (currentMessages.length >= MAX_MESSAGES_WARNING - 1) {
      // Atualizar o estado para mostrar o aviso
      set({ showLargeConversationWarning: true });
    }

    // First add the user message to the UI immediately
    const timestamp = Date.now();
    const userMessage: Message = {
      id: `temp_${timestamp}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'user',
      content,
      created_at: new Date().toISOString()
    };

    // Adicionar a mensagem do usuário imediatamente e ativar o indicador de digitação
    set(state => {
      // Garantir que a mensagem não seja duplicada
      const currentMessages = state.messages[conversationId] || [];
      const messageExists = currentMessages.some(msg =>
        msg.role === 'user' &&
        msg.content === content &&
        new Date(msg.created_at).getTime() > Date.now() - 5000
      );

      if (messageExists) {
        // Se a mensagem já existe, apenas ativar o indicador de digitação
        return { isTyping: true };
      }

      // Caso contrário, adicionar a mensagem e ativar o indicador
      return {
        messages: {
          ...state.messages,
          [conversationId]: [
            ...currentMessages,
            userMessage
          ]
        },
        isTyping: true
      };
    });

    try {
      // Enviar a mensagem para a API da OpenAI (otimizada para respostas rápidas)
      const assistantResponse = await sendMessage(conversationId, content);

      // Update the messages with the assistant's response
      set(state => {
        // Get the current messages for this conversation
        const currentMessages = state.messages[conversationId] || [];

        // Verificar se a mensagem do usuário já está na lista
        const userMessageExists = currentMessages.some(msg =>
          msg.role === 'user' && msg.content === content && msg.created_at >= userMessage.created_at
        );

        // Criar a nova lista de mensagens
        const newMessages = userMessageExists
          ? [...currentMessages] // Se a mensagem do usuário já existe, manter a lista atual
          : [...currentMessages, { // Caso contrário, adicionar a mensagem do usuário
              ...userMessage,
              id: `user_${timestamp}_${Math.random().toString(36).substring(2, 9)}` // ID permanente
            }];

        // Adicionar a resposta do assistente
        newMessages.push({
          id: assistantResponse.id,
          role: 'assistant',
          content: assistantResponse.content,
          created_at: new Date().toISOString()
        });

        // Retornar o novo estado
        return {
          messages: {
            ...state.messages,
            [conversationId]: newMessages
          },
          // Update the conversation list to show the new message count
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? {
                  ...conv,
                  messageCount: conv.messageCount + 2, // +2 for user and assistant messages
                  lastUpdated: new Date().toISOString()
                }
              : conv
          ),
          // Desativar o indicador de digitação
          isTyping: false
        };
      });
    } catch (error) {
      console.error('Error sending message:', error);

      // Tratar diferentes tipos de erros de forma mais específica
      if (error instanceof Error && error.message === 'User not authenticated') {
        // Erro de autenticação - simular resposta do assistente
        set(state => {
          // Get the current messages for this conversation
          const currentMessages = state.messages[conversationId] || [];

          // Verificar se a mensagem do usuário já está na lista
          const userMessageExists = currentMessages.some(msg =>
            msg.role === 'user' && msg.content === content && msg.created_at >= userMessage.created_at
          );

          // Criar a nova lista de mensagens
          const newMessages = userMessageExists
            ? [...currentMessages] // Se a mensagem do usuário já existe, manter a lista atual
            : [...currentMessages, { // Caso contrário, adicionar a mensagem do usuário
                ...userMessage,
                id: `user_${timestamp}_${Math.random().toString(36).substring(2, 9)}` // ID permanente
              }];

          // Adicionar a resposta do assistente
          newMessages.push({
            id: `assistant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            role: 'assistant',
            content: 'Olá! Sou a Lia, sua assistente de estudos. Como posso ajudar você hoje? (Nota: Você está usando o modo offline, algumas funcionalidades podem estar limitadas)',
            created_at: new Date().toISOString()
          });

          return {
            messages: {
              ...state.messages,
              [conversationId]: newMessages
            },
            // Update the conversation list
            conversations: state.conversations.map(conv =>
              conv.id === conversationId
                ? {
                    ...conv,
                    messageCount: conv.messageCount + 2,
                    lastUpdated: new Date().toISOString()
                  }
                : conv
            ),
            // Desativar o indicador de digitação
            isTyping: false
          };
        });
      } else if (error instanceof Error && error.message.includes('cancelled')) {
        // Erro de cancelamento - mostrar mensagem amigável
        set(state => {
          // Get the current messages for this conversation
          const currentMessages = state.messages[conversationId] || [];

          // Verificar se a mensagem do usuário já está na lista
          const userMessageExists = currentMessages.some(msg =>
            msg.role === 'user' && msg.content === content && msg.created_at >= userMessage.created_at
          );

          // Criar a nova lista de mensagens
          const newMessages = userMessageExists
            ? [...currentMessages] // Se a mensagem do usuário já existe, manter a lista atual
            : [...currentMessages, { // Caso contrário, adicionar a mensagem do usuário
                ...userMessage,
                id: `user_${timestamp}_${Math.random().toString(36).substring(2, 9)}` // ID permanente
              }];

          // Adicionar a resposta do assistente
          newMessages.push({
            id: `assistant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            role: 'assistant',
            content: 'Desculpe pela interrupção. Estou pronta para continuar nossa conversa. Como posso ajudar?',
            created_at: new Date().toISOString()
          });

          return {
            messages: {
              ...state.messages,
              [conversationId]: newMessages
            },
            // Update the conversation list
            conversations: state.conversations.map(conv =>
              conv.id === conversationId
                ? {
                    ...conv,
                    messageCount: conv.messageCount + 2,
                    lastUpdated: new Date().toISOString()
                  }
                : conv
            ),
            // Desativar o indicador de digitação
            isTyping: false
          };
        });
      } else if (error instanceof Error && error.message.includes('já está sendo processada')) {
        // Erro de concorrência - mensagem já está sendo processada
        console.log('Detectado erro de concorrência, tentando novamente em 1 segundo...');

        // Mostrar mensagem de erro temporariamente
        set(state => ({
          error: 'Processando sua mensagem anterior. Aguarde um momento...',
          isTyping: true // Manter o indicador de digitação
        }));

        // Limpar o erro imediatamente
        set({ error: null });

        // Tentar enviar a mensagem novamente imediatamente
        (async () => {
            try {
              // Tentar enviar a mensagem novamente
              await sendMessage(conversationId, content);

              // Atualizar a UI com a resposta
              set(state => ({
                isTyping: false,
                error: null
              }));
            } catch (retryError) {
              console.error('Erro ao reenviar mensagem:', retryError);
              // Se falhar novamente, desistir e mostrar erro
              set(state => ({
                error: 'Não foi possível processar sua mensagem. Por favor, tente novamente.',
                isTyping: false
              }));
            }
          })();
      } else {
        // Add an error message to the UI
        set(state => {
          // Get the current messages for this conversation
          const currentMessages = state.messages[conversationId] || [];

          // Verificar se a mensagem do usuário já está na lista
          const userMessageExists = currentMessages.some(msg =>
            msg.role === 'user' && msg.content === content && msg.created_at >= userMessage.created_at
          );

          // Criar a nova lista de mensagens
          const newMessages = userMessageExists
            ? [...currentMessages] // Se a mensagem do usuário já existe, manter a lista atual
            : [...currentMessages, { // Caso contrário, adicionar a mensagem do usuário
                ...userMessage,
                id: `user_${timestamp}_${Math.random().toString(36).substring(2, 9)}` // ID permanente
              }];

          // Adicionar a resposta do assistente
          newMessages.push({
            id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            role: 'assistant',
            content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.',
            created_at: new Date().toISOString()
          });

          return {
            messages: {
              ...state.messages,
              [conversationId]: newMessages
            },
            error: error instanceof Error ? error.message : 'Failed to send message',
            // Desativar o indicador de digitação
            isTyping: false
          };
        });
      }
    }
  },

  setCurrentConversation: (conversationId: string) => {
    const currentId = get().currentConversationId;

    // Se estamos mudando de conversa, cancelar qualquer processamento na conversa anterior
    if (currentId && currentId !== conversationId) {
      // Resetar os threads ativos para cancelar qualquer processamento em andamento
      resetAssistantThreads();
      console.log(`Mudando de conversa: ${currentId} -> ${conversationId}. Threads ativos resetados.`);
    }

    set({ currentConversationId: conversationId, isTyping: false, error: null });

    // Fetch messages if we don't have them yet
    if (!get().messages[conversationId]) {
      get().fetchMessages(conversationId);
    }
  },

  removeConversation: async (conversationId: string) => {
    // Atualizar a UI imediatamente para feedback instantâneo
    set(state => {
      // Remove the conversation and its messages from UI immediately
      const { [conversationId]: _, ...remainingMessages } = state.messages;

      // If we're deleting the current conversation, set a new one
      let newCurrentId = state.currentConversationId;
      if (state.currentConversationId === conversationId) {
        const remainingConversations = state.conversations.filter(c => c.id !== conversationId);
        newCurrentId = remainingConversations.length > 0 ? remainingConversations[0].id : null;
      }

      return {
        conversations: state.conversations.filter(c => c.id !== conversationId),
        messages: remainingMessages,
        currentConversationId: newCurrentId,
        isLoading: true, // Indicar que a exclusão está em andamento
        error: null
      };
    });

    try {
      // Executar a exclusão no banco de dados em segundo plano
      await deleteConversation(conversationId);

      // Atualizar o estado para indicar que a exclusão foi concluída
      set({ isLoading: false });
    } catch (error) {
      console.error('Error removing conversation:', error);

      // Em caso de erro, restaurar a conversa na UI
      // Mas isso só é possível se tivermos os dados em cache
      // Caso contrário, apenas mostrar o erro
      set({
        error: error instanceof Error ? error.message : 'Falha ao excluir conversa. Tente novamente.',
        isLoading: false
      });

      // Recarregar as conversas para garantir que a UI esteja sincronizada com o banco de dados
      get().fetchConversations();
    }
  },

  updateTitle: async (conversationId: string, title: string) => {
    set({ isLoading: true, error: null });
    try {
      await updateConversationTitle(conversationId, title);

      set(state => ({
        conversations: state.conversations.map(conv =>
          conv.id === conversationId
            ? { ...conv, title }
            : conv
        ),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error updating title:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to update title',
        isLoading: false
      });
    }
  },

  // Verifica se a conversa está muito grande e atualiza o estado
  checkConversationSize: (conversationId: string) => {
    const currentMessages = get().messages[conversationId] || [];
    const isLarge = currentMessages.length >= MAX_MESSAGES_WARNING;

    // Se a conversa estiver grande, atualizar o estado para mostrar o aviso
    if (isLarge) {
      set({ showLargeConversationWarning: true });
    }

    return isLarge;
  },

  // Dispensar o aviso de conversa grande
  dismissLargeConversationWarning: () => {
    set({ showLargeConversationWarning: false });
  }
}));
