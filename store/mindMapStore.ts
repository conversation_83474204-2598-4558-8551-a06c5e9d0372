import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { MindMap } from "@/types";
import { colors } from "@/constants/colors";
import { supabase } from "@/lib/supabase";
import { sampleMindMap } from "@/data/sampleMindMap";

interface MindMapState {
  mindMaps: MindMap[];
  loading: boolean;
  error: Error | null;
  fetchMindMaps: () => Promise<void>;
  addMindMap: (mindMap: MindMap) => Promise<MindMap | null>;
  updateMindMap: (id: string, updates: Partial<MindMap>) => Promise<void>;
  deleteMindMap: (id: string) => Promise<void>;
}

// Sample mind maps
const sampleMindMaps: MindMap[] = [
  // Novo exemplo de mapa mental com estrutura de árvore
  sampleMindMap,
  {
    id: "1",
    title: "Fotossíntes<PERSON>",
    subject: "Biologia",
    description: "Processo de produção de energia nas plantas",
    lastEdited: "2023-06-05T14:30:00",
    type: "concept",
    color: colors.accent1,
    nodes: [
      {
        id: "node_1",
        text: "Fotossíntese",
        x: 400,
        y: 300,
        color: "#10B981",
        shape: "circle",
        style: "filled",
        size: "large",
      },
      {
        id: "node_2",
        text: "Luz Solar",
        x: 250,
        y: 200,
        color: "#F59E0B",
        shape: "triangle",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_3",
        text: "Água",
        x: 300,
        y: 400,
        color: "#3B82F6",
        shape: "square",
        style: "outline",
        size: "medium",
      },
      {
        id: "node_4",
        text: "CO2",
        x: 500,
        y: 400,
        color: "#6366F1",
        shape: "hexagon",
        style: "outline",
        size: "medium",
      },
      {
        id: "node_5",
        text: "Glicose",
        x: 550,
        y: 200,
        color: "#EC4899",
        shape: "circle",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_6",
        text: "Oxigênio",
        x: 650,
        y: 300,
        color: "#8B5CF6",
        shape: "circle",
        style: "filled",
        size: "medium",
      },
    ],
    connections: [
      {
        id: "conn_1",
        source: "node_1",
        target: "node_2",
        color: "#F59E0B",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_2",
        source: "node_1",
        target: "node_3",
        color: "#3B82F6",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_3",
        source: "node_1",
        target: "node_4",
        color: "#6366F1",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_4",
        source: "node_1",
        target: "node_5",
        color: "#EC4899",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_5",
        source: "node_1",
        target: "node_6",
        color: "#8B5CF6",
        width: 2,
        style: "solid",
      },
    ],
  },
  {
    id: "2",
    title: "Revolução Francesa",
    subject: "História",
    description: "Principais eventos e causas da Revolução Francesa",
    lastEdited: "2023-06-08T10:15:00",
    type: "brain",
    color: colors.primary,
    nodes: [
      {
        id: "node_1",
        text: "Revolução Francesa",
        x: 400,
        y: 300,
        color: "#EC4899",
        shape: "circle",
        style: "filled",
        size: "large",
      },
      {
        id: "node_2",
        text: "Crise Econômica",
        x: 250,
        y: 200,
        color: "#F59E0B",
        shape: "square",
        style: "outline",
        size: "medium",
      },
      {
        id: "node_3",
        text: "Iluminismo",
        x: 300,
        y: 400,
        color: "#3B82F6",
        shape: "hexagon",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_4",
        text: "Queda da Bastilha",
        x: 550,
        y: 200,
        color: "#10B981",
        shape: "triangle",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_5",
        text: "Guilhotina",
        x: 500,
        y: 400,
        color: "#6366F1",
        shape: "square",
        style: "filled",
        size: "medium",
      },
    ],
    connections: [
      {
        id: "conn_1",
        source: "node_1",
        target: "node_2",
        color: "#F59E0B",
        width: 2,
        style: "dashed",
      },
      {
        id: "conn_2",
        source: "node_1",
        target: "node_3",
        color: "#3B82F6",
        width: 3,
        style: "solid",
      },
      {
        id: "conn_3",
        source: "node_1",
        target: "node_4",
        color: "#10B981",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_4",
        source: "node_1",
        target: "node_5",
        color: "#6366F1",
        width: 2,
        style: "dotted",
      },
    ],
  },
  {
    id: "3",
    title: "Funções Trigonométricas",
    subject: "Matemática",
    description: "Relações entre as funções trigonométricas",
    lastEdited: "2023-06-10T16:45:00",
    type: "flow",
    color: colors.secondary,
    nodes: [
      {
        id: "node_1",
        text: "Trigonometria",
        x: 400,
        y: 300,
        color: "#6366F1",
        shape: "circle",
        style: "filled",
        size: "large",
      },
      {
        id: "node_2",
        text: "Seno",
        x: 250,
        y: 200,
        color: "#F59E0B",
        shape: "square",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_3",
        text: "Cosseno",
        x: 550,
        y: 200,
        color: "#10B981",
        shape: "square",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_4",
        text: "Tangente",
        x: 400,
        y: 150,
        color: "#EC4899",
        shape: "square",
        style: "filled",
        size: "medium",
      },
      {
        id: "node_5",
        text: "Radianos",
        x: 300,
        y: 400,
        color: "#3B82F6",
        shape: "circle",
        style: "outline",
        size: "medium",
      },
      {
        id: "node_6",
        text: "Círculo Trigonométrico",
        x: 500,
        y: 400,
        color: "#8B5CF6",
        shape: "circle",
        style: "filled",
        size: "large",
      },
    ],
    connections: [
      {
        id: "conn_1",
        source: "node_1",
        target: "node_2",
        color: "#F59E0B",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_2",
        source: "node_1",
        target: "node_3",
        color: "#10B981",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_3",
        source: "node_1",
        target: "node_4",
        color: "#EC4899",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_4",
        source: "node_1",
        target: "node_5",
        color: "#3B82F6",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_5",
        source: "node_1",
        target: "node_6",
        color: "#8B5CF6",
        width: 2,
        style: "solid",
      },
      {
        id: "conn_6",
        source: "node_2",
        target: "node_4",
        color: "#EC4899",
        width: 2,
        style: "dashed",
      },
      {
        id: "conn_7",
        source: "node_3",
        target: "node_4",
        color: "#EC4899",
        width: 2,
        style: "dashed",
      },
    ],
  },
];

export const useMindMapStore = create<MindMapState>()(
  persist(
    (set, get) => ({
      mindMaps: sampleMindMaps,
      loading: false,
      error: null,

      fetchMindMaps: async () => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return;

          const { data, error } = await supabase
            .from('mind_maps')
            .select('*, subjects(title)')
            .eq('user_id', user.id);

          if (error) throw error;

          if (data && data.length > 0) {
            const formattedMindMaps: MindMap[] = data.map(mindMap => ({
              id: mindMap.id,
              title: mindMap.title,
              subject: mindMap.subjects?.title || '',
              description: mindMap.description || '',
              lastEdited: new Date(mindMap.updated_at || '').toISOString(),
              type: mindMap.type || 'concept',
              color: mindMap.color || colors.primary,
              nodes: mindMap.nodes || [],
              connections: mindMap.connections || [],
            }));

            set({ mindMaps: formattedMindMaps, loading: false });
          } else {
            // If no mind maps in database, save the sample mind maps
            const savePromises = sampleMindMaps.map(async (mindMap) => {
              try {
                // Find subject_id from title
                let subject_id = null;
                if (mindMap.subject) {
                  const { data: subjectData } = await supabase
                    .from('subjects')
                    .select('id')
                    .eq('title', mindMap.subject)
                    .eq('user_id', user.id)
                    .single();

                  if (subjectData) {
                    subject_id = subjectData.id;
                  }
                }

                // Insert the mind map
                await supabase
                  .from('mind_maps')
                  .insert([
                    {
                      id: mindMap.id,
                      title: mindMap.title,
                      description: mindMap.description,
                      type: mindMap.type,
                      color: mindMap.color,
                      nodes: mindMap.nodes,
                      connections: mindMap.connections,
                      subject_id,
                      user_id: user.id,
                      created_at: new Date().toISOString(),
                      updated_at: mindMap.lastEdited || new Date().toISOString(),
                    },
                  ]);
              } catch (insertError) {
                console.error('Error inserting sample mind map:', insertError);
              }
            });

            try {
              await Promise.all(savePromises);
              // Fetch the mind maps again after saving
              const { data: freshData, error: freshError } = await supabase
                .from('mind_maps')
                .select('*, subjects(title)')
                .eq('user_id', user.id);

              if (freshError) throw freshError;

              if (freshData && freshData.length > 0) {
                const formattedMindMaps: MindMap[] = freshData.map(mindMap => ({
                  id: mindMap.id,
                  title: mindMap.title,
                  subject: mindMap.subjects?.title || '',
                  description: mindMap.description || '',
                  lastEdited: new Date(mindMap.updated_at || '').toISOString(),
                  type: mindMap.type || 'concept',
                  color: mindMap.color || colors.primary,
                  nodes: mindMap.nodes || [],
                  connections: mindMap.connections || [],
                }));

                set({ mindMaps: formattedMindMaps, loading: false });
              } else {
                // If still no mind maps, keep the sample mind maps
                set({ loading: false });
              }
            } catch (batchError) {
              console.error('Error in batch operation:', batchError);
              set({ loading: false });
            }
          }
        } catch (error: any) {
          console.error('Error fetching mind maps:', error);
          set({ error, loading: false });
        }
      },

      addMindMap: async (mindMap) => {
        try {
          set({ loading: true, error: null });

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('User not authenticated');

          // Find subject_id from title
          let subject_id = null;
          if (mindMap.subject) {
            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', mindMap.subject)
              .eq('user_id', user.id)
              .single();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          const { data, error } = await supabase
            .from('mind_maps')
            .insert([
              {
                title: mindMap.title,
                description: mindMap.description,
                type: mindMap.type,
                color: mindMap.color,
                nodes: mindMap.nodes,
                connections: mindMap.connections,
                subject_id,
                user_id: user.id,
              },
            ])
            .select()
            .single();

          if (error) throw error;

          const newMindMap: MindMap = {
            id: data.id,
            title: data.title,
            subject: mindMap.subject || '',
            description: data.description || '',
            lastEdited: new Date(data.updated_at || '').toISOString(),
            type: data.type || 'concept',
            color: data.color || colors.primary,
            nodes: data.nodes || [],
            connections: data.connections || [],
          };

          set((state) => ({
            mindMaps: [...state.mindMaps, newMindMap],
            loading: false,
          }));

          return newMindMap;
        } catch (error: any) {
          console.error('Error adding mind map:', error);
          set({ error, loading: false });
          return null;
        }
      },

      updateMindMap: async (id, updates) => {
        try {
          set({ loading: true, error: null });

          // Find subject_id from title if subject is being updated
          let subject_id = undefined;
          if (updates.subject) {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) throw new Error('User not authenticated');

            const { data: subjectData } = await supabase
              .from('subjects')
              .select('id')
              .eq('title', updates.subject)
              .eq('user_id', user.id)
              .single();

            if (subjectData) {
              subject_id = subjectData.id;
            }
          }

          // Prepare update object
          const updateObj: any = {
            updated_at: new Date().toISOString(),
          };

          // Only include fields that are actually being updated
          if (updates.title !== undefined) updateObj.title = updates.title;
          if (updates.description !== undefined) updateObj.description = updates.description;
          if (updates.type !== undefined) updateObj.type = updates.type;
          if (updates.color !== undefined) updateObj.color = updates.color;
          if (updates.nodes !== undefined) updateObj.nodes = updates.nodes;
          if (updates.connections !== undefined) updateObj.connections = updates.connections;
          if (subject_id !== undefined) updateObj.subject_id = subject_id;

          const { error } = await supabase
            .from('mind_maps')
            .update(updateObj)
            .eq('id', id);

          if (error) throw error;

          set((state) => ({
            mindMaps: state.mindMaps.map((mindMap) =>
              mindMap.id === id ? { ...mindMap, ...updates, lastEdited: new Date().toISOString() } : mindMap
            ),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error updating mind map:', error);
          set({ error, loading: false });
        }
      },

      deleteMindMap: async (id) => {
        try {
          set({ loading: true, error: null });

          const { error } = await supabase
            .from('mind_maps')
            .delete()
            .eq('id', id);

          if (error) throw error;

          set((state) => ({
            mindMaps: state.mindMaps.filter((mindMap) => mindMap.id !== id),
            loading: false,
          }));
        } catch (error: any) {
          console.error('Error deleting mind map:', error);
          set({ error, loading: false });
        }
      },
    }),
    {
      name: "lia-mindmap-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);