import { create } from 'zustand';
import { supabase } from '@/lib/supabase';
import { formatDateISO } from '@/utils/dateUtils';

export type TimerMode = 'focus' | 'break' | 'idle';

interface TimerState {
  isActive: boolean;
  mode: TimerMode;
  focusTime: number; // em minutos
  breakTime: number; // em minutos
  timeRemaining: number; // em segundos
  totalStudyTime: number; // em segundos
  sessionsCompleted: number;
  startTime: number | null;

  // Ações
  startTimer: (focusTime?: number, breakTime?: number) => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
  switchMode: () => void;
  tick: () => void;
  saveStudySession: () => Promise<void>;
  fetchTodayStudyTime: () => Promise<number>;
}

export const useTimerStore = create<TimerState>((set, get) => ({
  isActive: false,
  mode: 'idle',
  focusTime: 25, // padrão: 25 minutos
  breakTime: 5, // padrão: 5 minutos
  timeRemaining: 25 * 60, // em segundos
  totalStudyTime: 0,
  sessionsCompleted: 0,
  startTime: null,

  startTimer: (focusTime = 25, breakTime = 5) => {
    const now = Date.now();
    set({
      isActive: true,
      mode: 'focus',
      focusTime,
      breakTime,
      timeRemaining: focusTime * 60,
      startTime: now,
    });
  },

  pauseTimer: () => {
    set({ isActive: false });
  },

  resumeTimer: () => {
    set({ isActive: true });
  },

  stopTimer: async () => {
    const { saveStudySession } = get();
    await saveStudySession();
    set({
      isActive: false,
      mode: 'idle',
      timeRemaining: get().focusTime * 60,
      startTime: null,
    });
  },

  resetTimer: () => {
    const { mode, focusTime, breakTime } = get();
    set({
      timeRemaining: mode === 'focus' ? focusTime * 60 : breakTime * 60,
    });
  },

  switchMode: () => {
    const { mode, focusTime, breakTime, sessionsCompleted, totalStudyTime } = get();

    // Se estamos saindo do modo de foco, incrementar o contador de sessões
    // e adicionar o tempo de foco ao tempo total de estudo
    if (mode === 'focus') {
      set({
        mode: 'break',
        timeRemaining: breakTime * 60,
        sessionsCompleted: sessionsCompleted + 1,
        totalStudyTime: totalStudyTime + (focusTime * 60),
      });
    } else {
      set({
        mode: 'focus',
        timeRemaining: focusTime * 60,
      });
    }
  },

  tick: () => {
    const { timeRemaining, isActive, switchMode } = get();

    if (!isActive) return;

    if (timeRemaining <= 1) {
      // Quando o tempo acabar, mudar de modo
      switchMode();
    } else {
      set({ timeRemaining: timeRemaining - 1 });
    }
  },

  saveStudySession: async () => {
    try {
      const { totalStudyTime, sessionsCompleted, startTime } = get();

      if (totalStudyTime <= 0 || !startTime) return;

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const today = formatDateISO(new Date());

      // Verificar se já existe um registro para hoje
      const { data: existingData, error: existingError } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      console.log('Salvando sessão de estudo:', { totalStudyTime, sessionsCompleted, today });
      console.log('Registro existente:', existingData, existingError);

      if (existingData) {
        // Atualizar registro existente
        await supabase
          .from('study_sessions')
          .update({
            total_time: existingData.total_time + totalStudyTime,
            sessions_completed: existingData.sessions_completed + sessionsCompleted,
            updated_at: new Date().toISOString(),
          })
          .eq('id', existingData.id);
      } else {
        // Criar novo registro
        await supabase
          .from('study_sessions')
          .insert([
            {
              user_id: user.id,
              date: today,
              total_time: totalStudyTime,
              sessions_completed: sessionsCompleted,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ]);
      }

      // Resetar contadores após salvar
      set({
        totalStudyTime: 0,
        sessionsCompleted: 0,
      });
    } catch (error) {
      console.error('Erro ao salvar sessão de estudo:', error);
    }
  },

  fetchTodayStudyTime: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return 0;

      const today = formatDateISO(new Date());

      const { data, error } = await supabase
        .from('study_sessions')
        .select('total_time, sessions_completed')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      console.log('Buscando tempo de estudo:', { today, data, error });

      if (error && error.code === 'PGRST116') {
        // Nenhum registro encontrado
        return 0;
      }
      return data?.total_time || 0;
    } catch (error) {
      console.error('Erro ao buscar tempo de estudo:', error);
      return 0;
    }
  },
}));

// Iniciar o ticker
let interval: NodeJS.Timeout | null = null;

// Função para iniciar o ticker
export const startTimerTicker = () => {
  if (interval) return;

  interval = setInterval(() => {
    const { tick, isActive } = useTimerStore.getState();
    if (isActive) {
      tick();
    }
  }, 1000);
};

// Função para parar o ticker
export const stopTimerTicker = () => {
  if (interval) {
    clearInterval(interval);
    interval = null;
  }
};
