# Configuração do Banco de Dados Supabase

Este documento contém informações sobre a configuração do banco de dados Supabase para o aplicativo Lia.

## Tabelas Necessárias

O aplicativo requer as seguintes tabelas no Supabase:

1. `quizzes` - Armazena informações sobre os quizzes
2. `quiz_questions` - Armazena as perguntas dos quizzes
3. `quiz_attempts` - Armazena as tentativas de quiz dos usuários

## Configuração Automática

As tabelas e colunas necessárias são criadas automaticamente pelo aplicativo usando a API do Supabase. Não é necessário executar scripts SQL manualmente.

## Estrutura das Tabelas

### Tabela `quizzes`

| Coluna | Tipo | Descrição |
|--------|------|------------|
| id | UUID | Identificador único do quiz |
| title | TEXT | Título do quiz |
| description | TEXT | Descrição do quiz |
| subject_id | UUID | Referência à tabela subjects |
| user_id | UUID | Referência ao usuário que criou o quiz |
| created_at | TIMESTAMP | Data de criação do quiz |
| updated_at | TIMESTAMP | Data de atualização do quiz |
| last_attempt | TIMESTAMP | Data da última tentativa do quiz |
| best_score | NUMERIC | Melhor pontuação obtida no quiz |
| time_limit | INTEGER | Limite de tempo para completar o quiz (em segundos) |

### Tabela `quiz_questions`

| Coluna | Tipo | Descrição |
|--------|------|------------|
| id | UUID | Identificador único da pergunta |
| quiz_id | UUID | Referência ao quiz |
| question | TEXT | Texto da pergunta |
| options | JSONB | Opções de resposta |
| correct_option | INTEGER | Índice da opção correta |
| explanation | TEXT | Explicação da resposta |
| created_at | TIMESTAMP | Data de criação da pergunta |
| updated_at | TIMESTAMP | Data de atualização da pergunta |

### Tabela `quiz_attempts`

| Coluna | Tipo | Descrição |
|--------|------|------------|
| id | UUID | Identificador único da tentativa |
| quiz_id | UUID | Referência ao quiz |
| user_id | UUID | Referência ao usuário que fez a tentativa |
| score | INTEGER | Pontuação obtida |
| total_questions | INTEGER | Número total de perguntas |
| time_spent | INTEGER | Tempo gasto (em segundos) |
| date | TIMESTAMP | Data da tentativa |
| answers | JSONB | Respostas dadas pelo usuário |
| created_at | TIMESTAMP | Data de criação do registro |

## Políticas de Segurança

Todas as tabelas têm políticas de segurança RLS (Row Level Security) configuradas para garantir que os usuários só possam acessar seus próprios dados.

## Solução de Problemas

Se você encontrar erros relacionados a colunas ausentes ou tabelas inexistentes, verifique se o aplicativo está usando a versão mais recente do código. O aplicativo tenta criar automaticamente as tabelas e colunas necessárias, mas pode ser necessário reiniciar o aplicativo para que as alterações sejam aplicadas.
