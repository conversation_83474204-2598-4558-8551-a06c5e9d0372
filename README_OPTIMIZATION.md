# Otimização de Conexões com Banco de Dados para Alta Escala

Este documento descreve as otimizações implementadas para suportar alta escala (aproximadamente 100.000 usuários) e garantir o isolamento adequado de dados entre usuários no aplicativo Lia.

## Índice

1. [Visão Geral](#visão-geral)
2. [Políticas RLS (Row Level Security)](#políticas-rls-row-level-security)
3. [Otimização de Conexões](#otimização-de-conexões)
4. [Sistema de Cache](#sistema-de-cache)
5. [Proteção de Rotas](#proteção-de-rotas)
6. [Middleware de Autenticação](#middleware-de-autenticação)
7. [Scripts de Configuração e Teste](#scripts-de-configuração-e-teste)
8. [Melhores Práticas](#melhores-práticas)

## Visão Geral

As otimizações implementadas visam garantir:

1. **Segurança no acesso aos dados**: Cada usuário só pode acessar seus próprios dados, com exceção dos grupos compartilhados.
2. **Performance otimizada**: Mesmo com grande volume de usuários, o aplicativo mantém boa performance.
3. **Escalabilidade**: O sistema está preparado para crescer com o aumento da base de usuários.
4. **Eficiência nas consultas**: As consultas ao banco de dados são otimizadas para minimizar o uso de recursos.

## Políticas RLS (Row Level Security)

As políticas RLS são a base do isolamento de dados no Supabase. Elas garantem que cada usuário só possa acessar seus próprios dados, mesmo que tenha conhecimento dos IDs de recursos de outros usuários.

### Principais Características

- **Políticas por Tabela**: Cada tabela tem políticas específicas para operações SELECT, INSERT, UPDATE e DELETE.
- **Verificação de Propriedade**: As políticas verificam se o usuário é o proprietário do recurso antes de permitir o acesso.
- **Suporte a Grupos**: Políticas especiais para grupos de estudo, permitindo acesso compartilhado.
- **Índices Otimizados**: Índices criados para melhorar o desempenho das consultas com filtros de usuário.

### Arquivo de Políticas

O arquivo `sql/optimize_rls_policies.sql` contém todas as políticas RLS implementadas. Para aplicá-las, execute o script `scripts/apply_rls_policies.js`.

## Otimização de Conexões

O hook `useSupabaseOptimized` foi melhorado para otimizar as conexões com o banco de dados:

### Principais Melhorias

- **Pool de Conexões**: Reutilização de conexões para reduzir a sobrecarga.
- **Retry e Circuit Breaker**: Mecanismos para lidar com falhas temporárias.
- **Timeout**: Configuração de timeout para evitar consultas lentas.
- **Paginação**: Suporte a paginação para grandes conjuntos de dados.
- **Monitoramento**: Métricas de desempenho para identificar gargalos.

### Exemplo de Uso

```typescript
const { 
  data, 
  loading, 
  error, 
  page, 
  totalPages, 
  fetchNextPage 
} = useSupabaseOptimized('subjects', {
  select: 'id, title, description',
  filter: { is_active: true },
  order: { column: 'created_at', ascending: false },
  limit: 20,
  page: 1,
  useRPC: true,
  rpcFunction: 'get_user_subjects'
});
```

## Sistema de Cache

O sistema de cache foi melhorado para reduzir a carga no banco de dados:

### Principais Melhorias

- **Cache em Memória**: Cache rápido para dados frequentemente acessados.
- **Cache Persistente**: Armazenamento de dados para uso offline.
- **Estratégias de Armazenamento**: Suporte a AsyncStorage e FileSystem.
- **Priorização**: Cache inteligente que prioriza dados importantes.
- **Limpeza Automática**: Remoção de dados antigos ou menos utilizados.

### Exemplo de Uso

```typescript
// O cache é usado automaticamente pelo hook useSupabaseOptimized
const { data, fromCache } = useSupabaseOptimized('subjects');

// Verificar se os dados vieram do cache
if (fromCache) {
  console.log('Dados carregados do cache');
}
```

## Proteção de Rotas

O componente `RouteGuard` foi melhorado para garantir que apenas usuários autorizados possam acessar determinadas rotas:

### Principais Melhorias

- **Cache de Permissões**: Reduz verificações repetidas.
- **Timeout**: Evita bloqueios em caso de falha.
- **Mensagens de Erro**: Feedback claro para o usuário.
- **Callbacks**: Funções para personalizar o comportamento.

### Exemplo de Uso

```tsx
<RouteGuard
  resourceId={subjectId}
  tableName="subjects"
  cachePermission={true}
  redirectTo="/subjects"
  showErrorMessage={true}
  errorMessage="Você não tem permissão para acessar este assunto."
  onPermissionDenied={() => console.log('Acesso negado')}
  onPermissionGranted={() => console.log('Acesso permitido')}
>
  <SubjectDetails />
</RouteGuard>
```

## Middleware de Autenticação

O middleware de autenticação foi melhorado para otimizar as verificações de permissão:

### Principais Melhorias

- **Cache de Usuário**: Reduz chamadas à API de autenticação.
- **Cache de Permissões**: Armazena resultados de verificações anteriores.
- **Funções RPC**: Usa funções otimizadas no banco de dados.
- **Função Unificada**: `hasPermission` simplifica verificações.

### Exemplo de Uso

```typescript
// Verificar se o usuário tem permissão para acessar um recurso
const hasAccess = await hasPermission(
  userId,
  'subject',
  subjectId,
  'owner'
);

if (hasAccess) {
  // Permitir acesso
} else {
  // Negar acesso
}
```

## Scripts de Configuração e Teste

Foram criados scripts para configurar e testar as otimizações:

### Scripts Disponíveis

- **apply_rls_policies.js**: Aplica as políticas RLS no Supabase.
- **test_data_isolation.js**: Testa o isolamento de dados entre usuários.

### Exemplo de Uso

```bash
# Aplicar políticas RLS
node scripts/apply_rls_policies.js

# Testar isolamento de dados
node scripts/test_data_isolation.js
```

## Melhores Práticas

Para manter a segurança e o desempenho do aplicativo, siga estas melhores práticas:

1. **Sempre use o hook useSupabaseOptimized** para consultas ao banco de dados.
2. **Proteja todas as rotas** com o componente RouteGuard.
3. **Adicione índices** para campos frequentemente consultados.
4. **Use paginação** para grandes conjuntos de dados.
5. **Monitore o desempenho** das consultas.
6. **Teste o isolamento de dados** regularmente.
7. **Mantenha as políticas RLS atualizadas** quando adicionar novas tabelas.

---

Estas otimizações garantem que o aplicativo Lia possa escalar para suportar aproximadamente 100.000 usuários, mantendo a segurança e o desempenho adequados.
