export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: string
          created_at: string | null
          description: string | null
          id: string
          related_id: string | null
          subject_id: string | null
          title: string
          user_id: string
          xp_earned: number | null
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          description?: string | null
          id?: string
          related_id?: string | null
          subject_id?: string | null
          title: string
          user_id: string
          xp_earned?: number | null
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          description?: string | null
          id?: string
          related_id?: string | null
          subject_id?: string | null
          title?: string
          user_id?: string
          xp_earned?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_conversations: {
        Row: {
          created_at: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_conversations_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_messages: {
        Row: {
          content: string
          conversation_id: string
          created_at: string | null
          id: string
          role: string
        }
        Insert: {
          content: string
          conversation_id: string
          created_at?: string | null
          id?: string
          role: string
        }
        Update: {
          content?: string
          conversation_id?: string
          created_at?: string | null
          id?: string
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            referencedRelation: "ai_conversations"
            referencedColumns: ["id"]
          }
        ]
      }
      flashcard_sets: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          subject_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          subject_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          subject_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcard_sets_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcard_sets_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      flashcards: {
        Row: {
          back: string
          created_at: string | null
          difficulty: number | null
          front: string
          id: string
          image_url: string | null
          next_review: string | null
          review_count: number | null
          set_id: string
          updated_at: string | null
        }
        Insert: {
          back: string
          created_at?: string | null
          difficulty?: number | null
          front: string
          id?: string
          image_url?: string | null
          next_review?: string | null
          review_count?: number | null
          set_id: string
          updated_at?: string | null
        }
        Update: {
          back?: string
          created_at?: string | null
          difficulty?: number | null
          front?: string
          id?: string
          image_url?: string | null
          next_review?: string | null
          review_count?: number | null
          set_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_set_id_fkey"
            columns: ["set_id"]
            referencedRelation: "flashcard_sets"
            referencedColumns: ["id"]
          }
        ]
      }
      mind_maps: {
        Row: {
          content: Json
          created_at: string | null
          description: string | null
          id: string
          subject_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          subject_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          subject_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "mind_maps_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mind_maps_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      notes: {
        Row: {
          content: Json
          created_at: string | null
          id: string
          subject_id: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content?: Json
          created_at?: string | null
          id?: string
          subject_id?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: Json
          created_at?: string | null
          id?: string
          subject_id?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notes_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notes_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      quiz_questions: {
        Row: {
          correct_option: number
          created_at: string | null
          explanation: string | null
          id: string
          options: string[]
          question: string
          quiz_id: string
          updated_at: string | null
        }
        Insert: {
          correct_option: number
          created_at?: string | null
          explanation?: string | null
          id?: string
          options: string[]
          question: string
          quiz_id: string
          updated_at?: string | null
        }
        Update: {
          correct_option?: number
          created_at?: string | null
          explanation?: string | null
          id?: string
          options?: string[]
          question?: string
          quiz_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quiz_questions_quiz_id_fkey"
            columns: ["quiz_id"]
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          }
        ]
      }
      quizzes: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          subject_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          subject_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          subject_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "quizzes_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quizzes_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      study_sessions: {
        Row: {
          id: string
          user_id: string
          date: string
          total_time: number
          sessions_completed: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          date: string
          total_time?: number
          sessions_completed?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          date?: string
          total_time?: number
          sessions_completed?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_sessions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      study_groups: {
        Row: {
          id: string
          name: string
          description: string | null
          cover_image: string | null
          admin_id: string
          is_open: boolean
          invite_code: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          cover_image?: string | null
          admin_id: string
          is_open?: boolean
          invite_code?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          cover_image?: string | null
          admin_id?: string
          is_open?: boolean
          invite_code?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_groups_admin_id_fkey"
            columns: ["admin_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      study_group_settings: {
        Row: {
          id: string
          group_id: string
          allow_member_content: boolean
          allow_member_invites: boolean
          require_admin_approval: boolean
          enable_app_blocking: boolean
          blocked_apps: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          group_id: string
          allow_member_content?: boolean
          allow_member_invites?: boolean
          require_admin_approval?: boolean
          enable_app_blocking?: boolean
          blocked_apps?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          group_id?: string
          allow_member_content?: boolean
          allow_member_invites?: boolean
          require_admin_approval?: boolean
          enable_app_blocking?: boolean
          blocked_apps?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_group_settings_group_id_fkey"
            columns: ["group_id"]
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          }
        ]
      },
      study_group_members: {
        Row: {
          id: string
          group_id: string
          user_id: string
          role: string
          study_time_minutes: number
          flashcards_created: number
          xp_points: number
          rank: number | null
          level: number | null
          joined_at: string | null
        }
        Insert: {
          id?: string
          group_id: string
          user_id: string
          role: string
          study_time_minutes?: number
          flashcards_created?: number
          xp_points?: number
          rank?: number | null
          level?: number | null
          joined_at?: string | null
        }
        Update: {
          id?: string
          group_id?: string
          user_id?: string
          role?: string
          study_time_minutes?: number
          flashcards_created?: number
          xp_points?: number
          rank?: number | null
          level?: number | null
          joined_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_members_group_id_fkey"
            columns: ["group_id"]
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_members_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      study_group_materials: {
        Row: {
          id: string
          group_id: string
          user_id: string
          title: string
          description: string | null
          type: string
          content: Json | null
          file_url: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          group_id: string
          user_id: string
          title: string
          description?: string | null
          type: string
          content?: Json | null
          file_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          group_id?: string
          user_id?: string
          title?: string
          description?: string | null
          type?: string
          content?: Json | null
          file_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_materials_group_id_fkey"
            columns: ["group_id"]
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_materials_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      study_group_invites: {
        Row: {
          id: string
          group_id: string
          inviter_id: string
          invitee_email: string
          status: string
          created_at: string | null
          expires_at: string | null
        }
        Insert: {
          id?: string
          group_id: string
          inviter_id: string
          invitee_email: string
          status: string
          created_at?: string | null
          expires_at?: string | null
        }
        Update: {
          id?: string
          group_id?: string
          inviter_id?: string
          invitee_email?: string
          status?: string
          created_at?: string | null
          expires_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_invites_group_id_fkey"
            columns: ["group_id"]
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_invites_inviter_id_fkey"
            columns: ["inviter_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      subjects: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          icon: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subjects_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          id: string
          is_premium: boolean
          last_streak_date: string | null
          level: number
          name: string
          streak: number
          total_study_time: number
          updated_at: string | null
          xp: number
          xp_to_next_level: number
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          id: string
          is_premium?: boolean
          last_streak_date?: string | null
          level?: number
          name: string
          streak?: number
          total_study_time?: number
          updated_at?: string | null
          xp?: number
          xp_to_next_level?: number
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          id?: string
          is_premium?: boolean
          last_streak_date?: string | null
          level?: number
          name?: string
          streak?: number
          total_study_time?: number
          updated_at?: string | null
          xp?: number
          xp_to_next_level?: number
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
