// User types
export interface User {
  id?: string;
  name: string;
  email?: string;
  avatar?: string;
  preferences?: UserPreferences;
  streak?: number;
  totalStudyTime?: number;
  level: number;
  xp?: number;
  xpToNextLevel?: number;
}

export interface UserPreferences {
  theme: 'light' | 'dark';
  notifications: boolean;
  studyReminders: boolean;
}

// Study types
export interface Subject {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  progress: number;
  separator_id?: string;
}

export interface Separator {
  id: string;
  title: string;
  description?: string;
  color: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface Activity {
  id: string;
  title: string;
  description: string;
  type: 'flashcards' | 'quiz' | 'chat' | 'mindmap';
  subject: string;
  date: string;
  completed: boolean;
  icon: string;
}

export interface FlashcardSet {
  id: string;
  title: string;
  description: string;
  subject: string;
  count: number;  // Alterado de totalCards para count
  mastered: number;
  lastReviewed: string | null;  // Alterado de lastStudied para lastReviewed
  color?: string;
}

export interface Flashcard {
  id: string;
  setId: string;
  front: string;
  back: string;
  difficulty: number;
  nextReview: string | null;
  reviewCount: number;
  imageUrl: string | null;
  subject_id?: string;
  eFactor?: number; // Fator de facilidade para o algoritmo de repetição espaçada
  lastReviewed?: string | null; // Data da última revisão
}

export interface FlashcardConversation {
  id: string;
  flashcardId: string;
  messages: {
    role: 'student' | 'tutor';
    content: string;
  }[];
}

export interface StudyPlan {
  id: string;
  title: string;
  description: string;
  subject: string;
  startDate: string;
  endDate: string;
  completed: boolean;
  progress: number;
  tasks: StudyTask[];
}

export interface StudyTask {
  id: string;
  title: string;
  completed: boolean;
  date: string;
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  subject: string;
  questions: QuizQuestion[];
  timeLimit: number | null;
  lastAttempt: string | null;
  bestScore: number | null;
  attempts?: QuizAttempt[];
  createdAt?: string;
}

export interface QuizAttempt {
  id: string;
  quizId: string;
  date: string;
  score: number;
  totalQuestions: number;
  timeSpent: number; // in seconds
  answers: QuizAnswer[];
}

export interface QuizAnswer {
  questionId: string;
  selectedOption: number;
  correct: boolean;
  timeSpent?: number; // in seconds
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctOption: number;
  explanation: string;
}

export interface Message {
  id: string;
  role: 'user' | 'system' | 'assistant';
  content: string;
}

export interface Conversation {
  id: string;
  title: string;
  lastUpdated: string;
  messageCount: number;
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'assistant';
  message: string;
  timestamp: string;
}

// Node shape types
export type NodeShape = 'circle' | 'square' | 'triangle' | 'hexagon';

// Node style types
export type NodeStyle = 'filled' | 'outline' | 'minimal';

// Mind Map types
export interface MindMap {
  id: string;
  title: string;
  description: string;
  subject: string;
  lastEdited: string;
  nodes: MindMapNode[];
  connections: MindMapConnection[];
  type?: 'concept' | 'brain' | 'flow' | 'general';
  color?: string;
  aiGenerated?: boolean;
}

export interface MindMapNode {
  id: string;
  text: string;
  x: number;
  y: number;
  color: string;
  shape?: NodeShape;
  style?: NodeStyle;
  size?: 'small' | 'medium' | 'large';
}

export interface MindMapConnection {
  id: string;
  source: string;
  target: string;
  color?: string;
  width?: number;
  style?: 'solid' | 'dashed' | 'dotted';
  type?: string;
  animated?: boolean;
}

// Note types
export interface Note {
  id: string;
  title: string;
  content: string;
  subject: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  blocks?: NoteBlock[];
  attachments?: Attachment[];
}

export type NoteBlockType = 'text' | 'heading' | 'subheading' | 'image' | 'table' | 'list' | 'code' | 'quote' | 'divider' | 'file';

export interface NoteBlock {
  id: string;
  type: NoteBlockType;
  content: string;
  metadata?: any;
  position: number;
  selection?: { start: number; end: number };
}

export interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'pdf' | 'document' | 'spreadsheet' | 'other';
  url: string;
  size?: number;
  createdAt: string;
  thumbnailUrl?: string;
}

// Calendar and To-Do types
export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startDate: string;
  endDate?: string;
  allDay: boolean;
  color: string;
  subject?: string;
  subject_id?: string;
  type: 'study' | 'exam' | 'assignment' | 'meeting' | 'other';
  completed?: boolean;
  reminder?: boolean;
  reminderTime?: string;
  recurrence?: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none';
  recurrenceEndDate?: string;
  recurrenceSettings?: {
    interval?: number;
    weekdays?: number[];
    monthDay?: number;
    endType?: 'never' | 'on_date' | 'after_occurrences';
    occurrences?: number;
  };
  has_todos?: boolean;
  todos?: TodoItem[];
  createdAt: string;
  updatedAt: string;
}

export interface Schedule {
  id: string;
  title: string;
  description?: string;
  type: 'weekly' | 'monthly' | '30days' | 'custom';
  startDate: string;
  endDate?: string;
  repeatWeekly: boolean;
  repeatMonthly: boolean;
  active: boolean;
  settings?: any;
  createdAt: string;
  updatedAt: string;
}

export interface ScheduleItem {
  id: string;
  scheduleId: string;
  subjectId?: string;
  subjectTitle: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  specificDate?: string;
  startTime: string;
  endTime: string;
  createEvent: boolean;
  createTodo: boolean;
  color?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  dueDate?: string;
  priority: 'high' | 'medium' | 'low';
  completed: boolean;
  subject?: string;
  subject_id?: string;
  event_id?: string;
  tags?: string[];
  reminderTime?: string;
  recurrence?: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none';
  recurrenceEndDate?: string;
  recurrenceSettings?: {
    interval?: number;
    weekdays?: number[];
    monthDay?: number;
    endType?: 'never' | 'on_date' | 'after_occurrences';
    occurrences?: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Study Group types
export interface StudyGroup {
  id: string;
  name: string;
  description?: string;
  coverImage?: string;
  adminId?: string; // Mantido para compatibilidade, mas será removido no futuro
  isOpen: boolean;
  inviteCode?: string;
  createdAt: string;
  updatedAt: string;
  memberCount?: number;
  materialsCount?: number;
  settings?: StudyGroupSettings;
}

export interface StudyGroupSettings {
  id: string;
  groupId: string;
  allowMemberContent: boolean;
  allowMemberInvites: boolean;
  requireAdminApproval: boolean;
  enableAppBlocking: boolean;
  blockedApps: BlockedApp[];
  createdAt: string;
  updatedAt: string;
}

export interface BlockedApp {
  id: string;
  name: string;
  packageName: string;
  icon?: string;
}

export interface StudyGroupMember {
  id: string;
  groupId: string;
  userId: string;
  userName?: string;
  userAvatar?: string;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: string;
  studyTimeMinutes: number;
  flashcardsCreated: number;
  xpPoints: number;
  rank?: number;
  level?: number;
}

export interface StudyGroupInvite {
  id: string;
  groupId: string;
  inviterId: string;
  inviterName?: string;
  inviteeEmail: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: string;
  expiresAt?: string;
}

export interface StudyGroupMaterial {
  id: string;
  groupId: string;
  userId: string;
  userName?: string;
  title: string;
  description?: string;
  type: 'note' | 'flashcard' | 'mindmap' | 'quiz' | 'document' | 'link' | 'image' | 'video';
  content?: any;
  fileUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface StudySession {
  id: string;
  groupId?: string;
  userId: string;
  startTime: string;
  endTime?: string;
  duration: number;
  isCompleted: boolean;
  isPaused: boolean;
  pausedTime?: number;
  technique: 'pomodoro' | 'custom';
  appBlockingEnabled?: boolean;
  settings?: {
    workDuration: number;
    breakDuration: number;
    longBreakDuration?: number;
    sessionsBeforeLongBreak?: number;
  };
}