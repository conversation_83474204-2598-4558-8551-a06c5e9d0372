{"timestamp": "2025-05-27T13:52:15.443Z", "summary": {"total": 9, "secure": 3, "vulnerable": 0, "emptyAllowed": 5, "errors": 1}, "results": [{"table": "users", "status": "EMPTY_ALLOWED", "records": 0}, {"table": "flashcard_sets", "status": "EMPTY_ALLOWED", "records": 0}, {"table": "quizzes", "status": "EMPTY_ALLOWED", "records": 0}, {"table": "notes", "status": "EMPTY_ALLOWED", "records": 0}, {"table": "study_groups", "status": "UNKNOWN", "statusCode": 500, "message": "infinite recursion detected in policy for relation \"study_groups\""}, {"table": "flashcards", "status": "EMPTY_ALLOWED", "records": 0}, {"table": "users", "operation": "INSERT", "status": "SECURE", "message": "new row violates row-level security policy for table \"users\""}, {"table": "notes", "operation": "INSERT", "status": "SECURE", "message": "new row violates row-level security policy for table \"notes\""}, {"table": "quizzes", "operation": "INSERT", "status": "SECURE", "message": "new row violates row-level security policy for table \"quizzes\""}]}