/**
 * Script para testar o isolamento de dados entre usuários
 * Este script deve ser executado manualmente para verificar se as políticas de segurança estão funcionando corretamente
 */

import { supabase } from '@/lib/supabase';
import { getAuthenticatedUser, checkResourcePermission, checkGroupMembership } from '@/middleware/authMiddleware';

// Cores para saída no console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Função para imprimir mensagens coloridas
const log = {
  info: (message: string) => console.log(`${colors.blue}[INFO]${colors.reset} ${message}`),
  success: (message: string) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${message}`),
  error: (message: string) => console.log(`${colors.red}[ERROR]${colors.reset} ${message}`),
  warning: (message: string) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${message}`),
  test: (name: string) => console.log(`\n${colors.cyan}[TEST]${colors.reset} ${name}`),
  result: (result: boolean) => console.log(
    result 
      ? `${colors.green}[PASSED]${colors.reset}` 
      : `${colors.red}[FAILED]${colors.reset}`
  ),
};

// Função para testar o acesso a recursos
const testResourceAccess = async (
  tableName: string,
  resourceId: string,
  expectedResult: boolean
) => {
  log.test(`Testando acesso a ${tableName} com ID ${resourceId}`);
  
  try {
    const user = await getAuthenticatedUser();
    
    if (!user) {
      log.error('Usuário não autenticado');
      log.result(false);
      return false;
    }
    
    const hasPermission = await checkResourcePermission(resourceId, tableName, user.id);
    
    log.info(`Permissão esperada: ${expectedResult}, Permissão obtida: ${hasPermission}`);
    log.result(hasPermission === expectedResult);
    
    return hasPermission === expectedResult;
  } catch (error) {
    log.error(`Erro ao testar acesso: ${error}`);
    log.result(false);
    return false;
  }
};

// Função para testar o acesso a grupos
const testGroupAccess = async (
  groupId: string,
  expectedMember: boolean,
  expectedAdmin: boolean
) => {
  log.test(`Testando acesso ao grupo com ID ${groupId}`);
  
  try {
    const user = await getAuthenticatedUser();
    
    if (!user) {
      log.error('Usuário não autenticado');
      log.result(false);
      return false;
    }
    
    const { isMember, isAdmin } = await checkGroupMembership(groupId, user.id);
    
    log.info(`Membro esperado: ${expectedMember}, Membro obtido: ${isMember}`);
    log.info(`Admin esperado: ${expectedAdmin}, Admin obtido: ${isAdmin}`);
    
    const result = isMember === expectedMember && isAdmin === expectedAdmin;
    log.result(result);
    
    return result;
  } catch (error) {
    log.error(`Erro ao testar acesso ao grupo: ${error}`);
    log.result(false);
    return false;
  }
};

// Função para testar a consulta direta ao Supabase
const testDirectQuery = async (
  tableName: string,
  filter: Record<string, any>,
  expectedCount: number
) => {
  log.test(`Testando consulta direta a ${tableName}`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .match(filter);
    
    if (error) {
      log.error(`Erro na consulta: ${error.message}`);
      log.result(false);
      return false;
    }
    
    const count = data?.length || 0;
    log.info(`Contagem esperada: ${expectedCount}, Contagem obtida: ${count}`);
    
    const result = count === expectedCount;
    log.result(result);
    
    return result;
  } catch (error) {
    log.error(`Erro ao testar consulta: ${error}`);
    log.result(false);
    return false;
  }
};

// Função para testar a tentativa de acessar dados de outro usuário
const testCrossUserAccess = async (
  tableName: string,
  otherUserId: string
) => {
  log.test(`Testando tentativa de acesso a dados de outro usuário em ${tableName}`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('user_id', otherUserId);
    
    // Se houver erro, é porque a política RLS está funcionando
    if (error) {
      log.success(`Política RLS bloqueou o acesso: ${error.message}`);
      log.result(true);
      return true;
    }
    
    // Se não houver erro, verificar se os dados estão vazios
    const count = data?.length || 0;
    
    if (count === 0) {
      log.success('Política RLS retornou conjunto vazio');
      log.result(true);
      return true;
    }
    
    // Se houver dados, a política RLS não está funcionando
    log.error(`Política RLS falhou: ${count} registros acessados`);
    log.result(false);
    return false;
  } catch (error) {
    log.error(`Erro ao testar acesso cruzado: ${error}`);
    log.result(false);
    return false;
  }
};

// Função principal para executar todos os testes
export const runDataIsolationTests = async () => {
  log.info('Iniciando testes de isolamento de dados');
  
  const user = await getAuthenticatedUser();
  
  if (!user) {
    log.error('Usuário não autenticado. Faça login antes de executar os testes.');
    return;
  }
  
  log.info(`Usuário autenticado: ${user.email}`);
  
  // Testar acesso a recursos próprios
  // Substitua os IDs pelos IDs reais dos recursos do usuário
  await testResourceAccess('subjects', 'ID_DO_SUBJECT_DO_USUARIO', true);
  
  // Testar acesso a recursos de outro usuário
  // Substitua o ID pelo ID de um recurso que pertence a outro usuário
  await testResourceAccess('subjects', 'ID_DO_SUBJECT_DE_OUTRO_USUARIO', false);
  
  // Testar acesso a grupos
  // Substitua os IDs pelos IDs reais dos grupos
  await testGroupAccess('ID_DO_GRUPO_ONDE_E_MEMBRO', true, false);
  await testGroupAccess('ID_DO_GRUPO_ONDE_E_ADMIN', true, true);
  await testGroupAccess('ID_DO_GRUPO_ONDE_NAO_E_MEMBRO', false, false);
  
  // Testar consultas diretas
  await testDirectQuery('subjects', { user_id: user.id }, 5); // Ajuste o número esperado
  
  // Testar tentativa de acesso a dados de outro usuário
  // Substitua o ID pelo ID de outro usuário
  await testCrossUserAccess('subjects', 'ID_DE_OUTRO_USUARIO');
  
  log.info('Testes de isolamento de dados concluídos');
};

// Exportar funções para uso em outros arquivos
export {
  testResourceAccess,
  testGroupAccess,
  testDirectQuery,
  testCrossUserAccess,
};
