module.exports = function(api) {
  api.cache(true);
  return {
    presets: [
      ['babel-preset-expo', {
        unstable_transformImportMeta: true
      }]
    ],
    plugins: [
      // Support for decorators used by WatermelonDB
      ['@babel/plugin-proposal-decorators', { legacy: true }],

      // Make sure the Reanimated plugin is the last in the list
      'react-native-reanimated/plugin',
    ],
  };
};
