/**
 * Polyfills for Metro bundler
 * This file is used by metro.config.js
 */

// Simple polyfill function
module.exports = {
  applyPolyfills: function() {
    // Polyfill for require.resolve in Hermes
    if (typeof global !== 'undefined' && typeof global.require === 'function' && !global.require.resolve) {
      global.require.resolve = function(moduleName) {
        return moduleName;
      };
      console.log('Metro: require.resolve polyfill applied');
    }
  }
};
