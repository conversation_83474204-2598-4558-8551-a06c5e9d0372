# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Google OAuth Configuration
# Obtenha essas credenciais em: https://console.cloud.google.com/apis/credentials
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_google_web_client_id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=your_google_ios_client_id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID=your_google_android_client_id.apps.googleusercontent.com

# Apple OAuth Configuration  
# Obtenha essas credenciais em: https://developer.apple.com/account/resources/identifiers
EXPO_PUBLIC_APPLE_SERVICE_ID=com.yourapp.signin
EXPO_PUBLIC_APPLE_REDIRECT_URI=https://your-project.supabase.co/auth/v1/callback

# App Configuration
EXPO_PUBLIC_APP_SCHEME=com.liastudyapp
EXPO_PUBLIC_APP_NAME=Lia Study App
