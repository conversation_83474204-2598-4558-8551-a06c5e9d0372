{"expo": {"name": "<PERSON>", "slug": "lia-app", "version": "1.0.0", "orientation": "portrait", "sdkVersion": "53.0.0", "icon": "./assets/images/icon.png", "scheme": "lias<PERSON><PERSON>app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.liastudyapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.liastudyapp"}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "@react-native-google-signin/google-signin"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}}}