-- Remover políticas problemáticas que causam recursão infinita
DROP POLICY IF EXISTS "Membros podem ver outros membros do mesmo grupo" ON public.study_group_members;

-- Criar nova política corrigida sem recursão
CREATE POLICY "Membros podem ver outros membros do mesmo grupo"
ON public.study_group_members
FOR SELECT
TO public
USING (
  -- Permitir que administradores do grupo vejam todos os membros
  EXISTS (
    SELECT 1 FROM study_groups sg
    WHERE sg.id = study_group_members.group_id
    AND sg.admin_id = auth.uid()
  )
  OR
  -- Permitir que membros vejam outros membros do mesmo grupo
  -- Usando uma subconsulta direta sem auto-referência
  user_id = auth.uid()
  OR
  EXISTS (
    SELECT 1 FROM study_group_members sgm2
    WHERE sgm2.user_id = auth.uid()
    AND sgm2.group_id = study_group_members.group_id
    AND sgm2.id != study_group_members.id
  )
);

-- Adicionar campo de ranking na tabela study_group_members se não existir
ALTER TABLE public.study_group_members
ADD COLUMN IF NOT EXISTS rank INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS level INTEGER DEFAULT 1;

-- Criar função para calcular ranking
CREATE OR REPLACE FUNCTION calculate_member_ranking(group_id_param UUID)
RETURNS VOID AS $$
DECLARE
  member_record RECORD;
  rank_counter INTEGER := 1;
BEGIN
  -- Ordenar membros por tempo de estudo (decrescente)
  FOR member_record IN
    SELECT id, study_time_minutes, flashcards_created
    FROM study_group_members
    WHERE group_id = group_id_param
    ORDER BY study_time_minutes DESC, flashcards_created DESC
  LOOP
    -- Atualizar rank
    UPDATE study_group_members
    SET rank = rank_counter
    WHERE id = member_record.id;

    -- Incrementar contador
    rank_counter := rank_counter + 1;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para atualizar ranking automaticamente
CREATE OR REPLACE FUNCTION update_member_ranking()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM calculate_member_ranking(NEW.group_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Remover trigger se existir
DROP TRIGGER IF EXISTS member_ranking_trigger ON study_group_members;

-- Criar trigger
CREATE TRIGGER member_ranking_trigger
AFTER UPDATE OF study_time_minutes, flashcards_created
ON study_group_members
FOR EACH ROW
EXECUTE FUNCTION update_member_ranking();
