// app.config.js
module.exports = {
  name: "<PERSON>",
  slug: "lia",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/icon.png",
  userInterfaceStyle: "light",
  splash: {
    image: "./assets/images/splash-icon.png",
    resizeMode: "contain",
    backgroundColor: "#ffffff"
  },
  assetBundlePatterns: [
    "**/*"
  ],
  ios: {
    supportsTablet: true
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff"
    }
  },
  web: {
    favicon: "./assets/images/favicon.png"
  },
  plugins: [
    "expo-router"
  ],
  scheme: "lia",
  extra: {
    // Variáveis de ambiente - usando process.env para segurança
    supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL || "https://wyjpmzfijtufgxgdivgl.supabase.co",
    supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5anBtemZpanR1Zmd4Z2RpdmdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NDY3MTcsImV4cCI6MjA2MDIyMjcxN30.wYZyPO_3q6i9EnSRm2QYeZOMVH0X-pUuBj2pQ7lzmq4",
    apiUrl: process.env.EXPO_PUBLIC_API_URL || "https://api.liaapp.com",
    environment: process.env.NODE_ENV || "development",
    eas: {
      projectId: process.env.EAS_PROJECT_ID || "your-project-id"
    }
  }
};
