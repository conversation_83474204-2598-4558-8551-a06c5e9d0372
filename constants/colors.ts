export const colors = {
  // Cores primárias - Azul moderno e vibrante
  primary: "#3399FF", // Azul brilhante
  primaryLight: "#66B2FF",
  primaryDark: "#0077E6",
  primaryGradient: ["#3399FF", "#66B2FF"] as const,
  primaryGradientVertical: ["#3399FF", "#1E88E5"] as const,
  primaryGradientDiagonal: ["#3399FF", "#667eea"] as const,

  // Cores secundárias - Âmbar elegante
  secondary: "#F59E0B", // Âmbar
  secondaryLight: "#FBBF24",
  secondaryDark: "#D97706",
  secondaryGradient: ["#F59E0B", "#FBBF24"] as const,
  secondaryGradientVertical: ["#F59E0B", "#FF8A65"] as const,

  // Cores de texto - Melhor contraste
  text: "#1F2937",
  textLight: "#4B5563",
  textMedium: "#6B7280",
  textDark: "#111827",
  textInverse: "#FFFFFF",

  // Cores de fundo - Gradientes suaves
  background: "#FFFFFF",
  backgroundLight: "#F9FAFB",
  backgroundDark: "#F3F4F6",
  backgroundGradient: ["#F9FAFB", "#F3F4F6"] as const,
  backgroundGradientModern: ["#FAFBFC", "#F0F4F8"] as const,

  // Cores de feedback - Mais vibrantes
  success: "#10B981",
  successLight: "#34D399",
  successDark: "#059669",
  successGradient: ["#10B981", "#34D399"] as const,
  successGradientModern: ["#00C851", "#00E676"] as const,

  error: "#EF4444",
  errorLight: "#F87171",
  errorDark: "#DC2626",
  errorGradient: ["#EF4444", "#F87171"] as const,
  errorGradientModern: ["#FF5252", "#FF8A80"] as const,

  warning: "#F59E0B",
  warningLight: "#FBBF24",
  warningDark: "#D97706",
  warningGradient: ["#F59E0B", "#FBBF24"] as const,
  warningGradientModern: ["#FF9800", "#FFB74D"] as const,

  info: "#3B82F6",
  infoLight: "#60A5FA",
  infoDark: "#2563EB",
  infoGradient: ["#3B82F6", "#60A5FA"] as const,
  infoGradientModern: ["#2196F3", "#64B5F6"] as const,

  // Cores de UI - Mais refinadas
  border: "#E5E7EB",
  borderLight: "#F3F4F6",
  borderDark: "#D1D5DB",
  card: "#FFFFFF",
  cardElevated: "#FEFEFE",
  divider: "#E5E7EB",
  dividerLight: "#F3F4F6",

  // Efeitos - Mais sofisticados
  glass: "rgba(255, 255, 255, 0.85)",
  glassDark: "rgba(255, 255, 255, 0.65)",
  glassBlur: "rgba(255, 255, 255, 0.9)",
  shadow: "rgba(0, 0, 0, 0.08)",
  shadowMedium: "rgba(0, 0, 0, 0.12)",
  shadowStrong: "rgba(0, 0, 0, 0.16)",
  overlay: "rgba(0, 0, 0, 0.5)",
  overlayLight: "rgba(0, 0, 0, 0.3)",

  // Cores de destaque - Gradientes modernos
  accent1: "#8B5CF6", // Roxo
  accent1Gradient: ["#8B5CF6", "#A78BFA"] as const,
  accent1GradientModern: ["#9C27B0", "#E1BEE7"] as const,

  accent2: "#EC4899", // Rosa
  accent2Gradient: ["#EC4899", "#F472B6"] as const,
  accent2GradientModern: ["#E91E63", "#F8BBD9"] as const,

  accent3: "#3B82F6", // Azul
  accent3Gradient: ["#3B82F6", "#60A5FA"] as const,
  accent3GradientModern: ["#2196F3", "#BBDEFB"] as const,

  accent4: "#10B981", // Verde
  accent4Gradient: ["#10B981", "#34D399"] as const,
  accent4GradientModern: ["#4CAF50", "#C8E6C9"] as const,

  accent5: "#F59E0B", // Âmbar
  accent5Gradient: ["#F59E0B", "#FBBF24"] as const,
  accent5GradientModern: ["#FF9800", "#FFE0B2"] as const,

  // Gradientes especiais para hero sections - Tema azul predominante
  heroGradient1: ["#3399FF", "#66B2FF"] as const, // Azul principal
  heroGradient2: ["#667eea", "#764ba2"] as const, // Azul-roxo
  heroGradient3: ["#4facfe", "#00f2fe"] as const, // Azul ciano
  heroGradient4: ["#3399FF", "#5DADE2"] as const, // Azul suave
  heroGradient5: ["#2E86AB", "#A23B72"] as const, // Azul escuro

  // Gradientes para cards especiais - Variações de azul
  cardGradient1: ["#3399FF", "#66B2FF"] as const, // Azul principal
  cardGradient2: ["#667eea", "#93A5CF"] as const, // Azul-roxo suave
  cardGradient3: ["#4facfe", "#00f2fe"] as const, // Azul ciano
  cardGradient4: ["#5DADE2", "#85C1E9"] as const, // Azul claro
  cardGradient5: ["#2E86AB", "#5499C7"] as const, // Azul médio

  // Utilitários
  white: "#FFFFFF",
  black: "#000000",
  transparent: "transparent",
};