import { Dimensions } from 'react-native';
import { colors } from './colors';

// Obter dimensões da tela
const { width, height } = Dimensions.get('window');

// Tipografia
export const typography = {
  // Tamanhos de fonte
  size: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 30,
    display: 36,
    giant: 48,
  },

  // Pesos de fonte
  weight: {
    thin: "300",
    regular: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    black: "900",
  },

  // Estilos de texto pré-definidos
  heading1: {
    fontSize: 30,
    fontWeight: "700",
    color: colors.text,
    lineHeight: 36,
  },

  heading2: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    lineHeight: 32,
  },

  heading3: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
    lineHeight: 28,
  },

  heading4: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    lineHeight: 24,
  },

  subtitle1: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textLight,
    lineHeight: 24,
  },

  subtitle2: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textLight,
    lineHeight: 20,
  },

  body1: {
    fontSize: 16,
    fontWeight: "400",
    color: colors.text,
    lineHeight: 24,
  },

  body2: {
    fontSize: 14,
    fontWeight: "400",
    color: colors.text,
    lineHeight: 20,
  },

  caption: {
    fontSize: 12,
    fontWeight: "400",
    color: colors.textMedium,
    lineHeight: 16,
  },

  button: {
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
  },
};

// Espaçamento - Sistema mais refinado
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
  // Novos espaçamentos para micro-ajustes
  xxs: 2,
  micro: 1,
  // Espaçamentos específicos para componentes
  cardPadding: 20,
  sectionSpacing: 28,
  componentGap: 12,
};

// Bordas e arredondamentos - Mais opções modernas
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 28,
  round: 9999,
  // Raios específicos para componentes
  card: 18,
  button: 14,
  input: 12,
  modal: 24,
  hero: 20,
};

// Sombras - Sistema mais sofisticado
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.16,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.20,
    shadowRadius: 20,
    elevation: 12,
  },
  xxl: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.24,
    shadowRadius: 28,
    elevation: 16,
  },
  // Sombras coloridas para elementos especiais
  primary: {
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  success: {
    shadowColor: colors.success,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
};

// Dimensões responsivas
export const layout = {
  window: {
    width,
    height,
  },
  isSmallDevice: width < 375,
  screen: {
    width,
    height,
  },
};

// Sistema de tamanhos padronizados baseado no menu flutuante
export const sizes = {
  // Tamanhos de ícones padronizados
  icon: {
    xs: 16,    // Ícones pequenos em badges, detalhes
    sm: 20,    // Ícones padrão em botões, cards
    md: 24,    // Ícones principais (baseado no FAB)
    lg: 28,    // Ícones de destaque
    xl: 32,    // Ícones grandes em headers
    xxl: 40,   // Ícones muito grandes
    xxxl: 48,  // Ícones gigantes para estados vazios
  },

  // Tamanhos de botões padronizados
  button: {
    height: {
      xs: 32,    // Botões muito pequenos
      sm: 40,    // Botões pequenos
      md: 48,    // Botões padrão (baseado no Button component)
      lg: 56,    // Botões grandes
      xl: 64,    // Botões extra grandes
    },
    minWidth: {
      xs: 60,
      sm: 80,
      md: 120,
      lg: 160,
      xl: 200,
    },
  },

  // Tamanhos de FAB padronizados
  fab: {
    main: 56,      // FAB principal (baseado no FloatingActionButton)
    secondary: 48, // FABs secundários
    mini: 40,      // Mini FABs
  },

  // Tamanhos de containers de ícones
  iconContainer: {
    xs: 32,    // Containers pequenos
    sm: 40,    // Containers padrão
    md: 48,    // Containers médios
    lg: 56,    // Containers grandes (baseado no FeatureCard)
    xl: 64,    // Containers extra grandes
  },

  // Tamanhos de touch targets (área mínima tocável)
  touchTarget: {
    min: 44,   // Tamanho mínimo recomendado
    comfortable: 48, // Tamanho confortável
    large: 56, // Tamanho grande
  },
};

// Animações - Sistema completo
export const animation = {
  timing: {
    instant: 100,
    fast: 200,
    normal: 300,
    slow: 500,
    slower: 700,
    slowest: 1000,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    // Curvas personalizadas para React Native
    spring: {
      tension: 100,
      friction: 8,
    },
    bounce: {
      tension: 180,
      friction: 12,
    },
  },
  // Configurações pré-definidas para animações comuns
  presets: {
    fadeIn: {
      duration: 300,
      useNativeDriver: true,
    },
    slideUp: {
      duration: 400,
      useNativeDriver: true,
    },
    scaleIn: {
      duration: 250,
      useNativeDriver: true,
    },
    press: {
      duration: 150,
      useNativeDriver: true,
    },
  },
};

// Exportar tudo como um objeto de tema
export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
  sizes,
  animation,
};

export default theme;
