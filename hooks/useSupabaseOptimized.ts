import { useState, useEffect, useCallback, useRef } from 'react';
import { cachedSelect, cachedQuery, clearCache } from '@/services/supabaseCache';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import NetInfo from '@react-native-community/netinfo';
import { measureFunction } from '@/services/performanceMonitor';
import { debounce, throttle } from '@/utils/performance';

// Constantes para otimização
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000;
const BATCH_SIZE = 100;
const CONNECTION_TIMEOUT = 10000; // 10 segundos

export function useSupabaseOptimized<T>(
  tableName: string,
  options: {
    select?: string;
    filter?: Record<string, any>;
    order?: { column: string; ascending?: boolean };
    limit?: number;
    page?: number;
    cacheTime?: number;
    autoRefresh?: boolean;
    refreshInterval?: number;
    dependencies?: any[];
    skipUserFilter?: boolean;
    useRPC?: boolean;
    rpcFunction?: string;
    retryOnError?: boolean;
    maxRetries?: number;
    batchSize?: number;
    timeout?: number;
    priority?: 'high' | 'normal' | 'low';
  } = {}
) {
  const {
    select,
    filter,
    order,
    limit,
    page = 1,
    cacheTime,
    autoRefresh = false,
    refreshInterval = 60000, // 1 minute
    dependencies = [],
    skipUserFilter = false,
    useRPC = false,
    rpcFunction = '',
    retryOnError = true,
    maxRetries = RETRY_ATTEMPTS,
    batchSize = BATCH_SIZE,
    timeout = CONNECTION_TIMEOUT,
    priority = 'normal',
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [fromCache, setFromCache] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [totalCount, setTotalCount] = useState<number | null>(null);
  const [totalPages, setTotalPages] = useState<number | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connecting' | 'connected' | 'error'>('idle');

  // Referências para controle de conexão
  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingRequestsRef = useRef<number>(0);
  const lastSuccessfulRequestRef = useRef<number>(Date.now());

  const { user } = useAuth();

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const online = state.isConnected && state.isInternetReachable;
      setIsOnline(online);

      // Se ficar online e estava em erro, tentar novamente
      if (online && connectionStatus === 'error') {
        setConnectionStatus('idle');
        setRetryCount(0);
      }
    });

    return () => {
      unsubscribe();
      // Limpar timeouts e abortar requisições pendentes
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [connectionStatus]);

  // Função para criar um timeout para a requisição
  const createRequestTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        setError(new Error(`A requisição excedeu o tempo limite de ${timeout}ms`));
        setConnectionStatus('error');
      }
    }, timeout);
  }, [timeout]);

  // Função para limpar o timeout da requisição
  const clearRequestTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Função para tentar novamente após erro
  const retryAfterError = useCallback(async (fn: () => Promise<any>) => {
    let attempts = 0;
    let lastError: Error | null = null;

    while (attempts < maxRetries) {
      try {
        return await fn();
      } catch (err) {
        lastError = err instanceof Error ? err : new Error(String(err));
        attempts++;
        setRetryCount(attempts);

        if (attempts < maxRetries) {
          // Esperar antes de tentar novamente (com backoff exponencial)
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * Math.pow(2, attempts - 1)));
        }
      }
    }

    throw lastError;
  }, [maxRetries]);

  // Function to fetch data
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!user) {
      setLoading(false);
      setData([]);
      return;
    }

    // Se não estiver online, usar cache
    if (!isOnline && !forceRefresh) {
      try {
        const operation = `fetch_${tableName}_offline`;

        // Preparar os filtros
        const filters = { ...filter };

        // Adicionar filtro de usuário automaticamente, a menos que seja explicitamente ignorado
        if (!skipUserFilter && !filters.user_id && tableName !== 'users') {
          filters.user_id = user.id;
        }

        // Usar cache offline
        const result = await measureFunction(
          operation,
          async () => {
            return cachedSelect<T>(tableName, {
              select,
              filter: filters,
              order,
              limit,
              page,
              forceRefresh: false,
              cacheTime,
              offlineOnly: true,
            });
          },
          { tableName, forceRefresh: false, isOnline: false }
        );

        if (result.data) {
          setData(result.data);
          setFromCache(true);
          setError(null);
        } else {
          setError(new Error('Sem dados disponíveis offline'));
        }

        setLoading(false);
        setIsRefreshing(false);
        return;
      } catch (err) {
        console.error(`Error fetching offline data for ${tableName}:`, err);
        setError(err instanceof Error ? err : new Error(`Failed to fetch offline data for ${tableName}`));
        setLoading(false);
        setIsRefreshing(false);
        return;
      }
    }

    // Incrementar contador de requisições pendentes
    pendingRequestsRef.current += 1;

    // Criar um novo AbortController para esta requisição
    abortControllerRef.current = new AbortController();

    // Configurar timeout
    createRequestTimeout();

    // Atualizar status de conexão
    setConnectionStatus('connecting');

    try {
      const operation = `fetch_${tableName}`;

      // Preparar os filtros
      const filters = { ...filter };

      // Adicionar filtro de usuário automaticamente, a menos que seja explicitamente ignorado
      if (!skipUserFilter && !filters.user_id && tableName !== 'users') {
        filters.user_id = user.id;
      }

      // Função para executar a consulta
      const executeQuery = async () => {
        if (useRPC && rpcFunction) {
          // Usar função RPC para consulta otimizada
          const { data, error } = await supabase.rpc(rpcFunction, {
            user_id_param: user.id,
            page_size: limit || batchSize,
            page_number: page,
            ...filters
          });

          return { data, error, fromCache: false };
        } else {
          // Usar cachedSelect padrão
          return cachedSelect<T>(tableName, {
            select,
            filter: filters,
            order,
            limit,
            page,
            forceRefresh,
            cacheTime,
            offlineOnly: false,
          });
        }
      };

      // Executar a consulta com retry se necessário
      const result = await measureFunction(
        operation,
        async () => {
          if (retryOnError) {
            return retryAfterError(executeQuery);
          } else {
            return executeQuery();
          }
        },
        { tableName, forceRefresh, isOnline, useRPC, page }
      );

      // Limpar timeout
      clearRequestTimeout();

      if (result.error) {
        throw result.error;
      }

      // Atualizar dados
      setData(result.data || []);
      setFromCache(result.fromCache);
      setError(null);

      // Calcular total de páginas se houver cabeçalhos de contagem
      if (result.count !== undefined) {
        setTotalCount(result.count);
        setTotalPages(Math.ceil(result.count / (limit || batchSize)));
      }

      // Atualizar status de conexão
      setConnectionStatus('connected');
      lastSuccessfulRequestRef.current = Date.now();
      setRetryCount(0);
    } catch (err) {
      console.error(`Error fetching ${tableName}:`, err);
      setError(err instanceof Error ? err : new Error(`Failed to fetch ${tableName}`));
      setConnectionStatus('error');
    } finally {
      // Decrementar contador de requisições pendentes
      pendingRequestsRef.current -= 1;

      // Limpar timeout se ainda existir
      clearRequestTimeout();

      setLoading(false);
      setIsRefreshing(false);
    }
  }, [
    tableName,
    select,
    filter,
    order,
    limit,
    page,
    cacheTime,
    user,
    isOnline,
    skipUserFilter,
    useRPC,
    rpcFunction,
    retryOnError,
    batchSize,
    createRequestTimeout,
    clearRequestTimeout,
    retryAfterError,
    connectionStatus,
    ...dependencies
  ]);

  // Initial data fetch
  useEffect(() => {
    setLoading(true);
    fetchData(false);
  }, [fetchData]);

  // Auto-refresh if enabled
  useEffect(() => {
    if (!autoRefresh || !isOnline) return;

    const interval = setInterval(() => {
      fetchData(true);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchData, isOnline]);

  // Function to manually refresh data
  const refresh = useCallback(async () => {
    setIsRefreshing(true);
    await fetchData(true);
  }, [fetchData]);

  // Function to add an item
  const addItem = useCallback(async (item: Partial<T>) => {
    if (!user) return null;

    // Verificar se está online
    if (!isOnline) {
      setError(new Error('Não é possível adicionar itens offline'));
      return null;
    }

    // Incrementar contador de requisições pendentes
    pendingRequestsRef.current += 1;

    // Criar um novo AbortController para esta requisição
    abortControllerRef.current = new AbortController();

    // Configurar timeout
    createRequestTimeout();

    try {
      // Garantir que o user_id seja incluído, a menos que seja a tabela users
      const itemData = { ...item };
      if (!skipUserFilter && tableName !== 'users' && !itemData.user_id) {
        itemData.user_id = user.id;
      }

      // Função para executar a inserção
      const executeInsert = async () => {
        const { data, error } = await supabase
          .from(tableName)
          .insert(itemData)
          .select();

        if (error) throw error;
        return data[0];
      };

      // Executar com retry se necessário
      const result = await measureFunction(
        `add_${tableName}`,
        async () => {
          if (retryOnError) {
            return retryAfterError(executeInsert);
          } else {
            return executeInsert();
          }
        },
        { tableName, operation: 'add', itemData }
      );

      // Update local data
      setData(prevData => [...prevData, result as T]);

      // Clear cache for this table
      await clearCache();

      // Atualizar status de conexão
      setConnectionStatus('connected');
      lastSuccessfulRequestRef.current = Date.now();

      return result;
    } catch (err) {
      console.error(`Error adding ${tableName}:`, err);
      setError(err instanceof Error ? err : new Error(`Failed to add ${tableName}`));
      setConnectionStatus('error');
      return null;
    } finally {
      // Decrementar contador de requisições pendentes
      pendingRequestsRef.current -= 1;

      // Limpar timeout se ainda existir
      clearRequestTimeout();
    }
  }, [
    tableName,
    user,
    skipUserFilter,
    isOnline,
    retryOnError,
    createRequestTimeout,
    clearRequestTimeout,
    retryAfterError
  ]);

  // Function to update an item
  const updateItem = useCallback(async (id: string, updates: Partial<T>) => {
    if (!user) return null;

    // Verificar se está online
    if (!isOnline) {
      setError(new Error('Não é possível atualizar itens offline'));
      return null;
    }

    // Incrementar contador de requisições pendentes
    pendingRequestsRef.current += 1;

    // Criar um novo AbortController para esta requisição
    abortControllerRef.current = new AbortController();

    // Configurar timeout
    createRequestTimeout();

    try {
      // Função para executar a atualização
      const executeUpdate = async () => {
        // Construir a consulta com verificação de propriedade
        let query = supabase
          .from(tableName)
          .update(updates)
          .eq('id', id);

        // Adicionar verificação de propriedade para garantir que o usuário só atualize seus próprios dados
        if (!skipUserFilter && tableName !== 'users') {
          query = query.eq('user_id', user.id);
        } else if (tableName === 'users') {
          // Para a tabela users, verificar se o ID corresponde ao usuário atual
          query = query.eq('id', user.id);
        }

        const { data, error } = await query.select();

        if (error) throw error;

        // Se não houver resultados, significa que o usuário tentou atualizar um item que não lhe pertence
        if (data.length === 0) {
          throw new Error('Você não tem permissão para atualizar este item');
        }

        return data[0];
      };

      // Executar com retry se necessário
      const result = await measureFunction(
        `update_${tableName}`,
        async () => {
          if (retryOnError) {
            return retryAfterError(executeUpdate);
          } else {
            return executeUpdate();
          }
        },
        { tableName, operation: 'update', id, updates }
      );

      // Update local data
      setData(prevData =>
        prevData.map(item =>
          (item as any).id === id ? { ...item, ...result } : item
        )
      );

      // Clear cache for this table
      await clearCache();

      // Atualizar status de conexão
      setConnectionStatus('connected');
      lastSuccessfulRequestRef.current = Date.now();

      return result;
    } catch (err) {
      console.error(`Error updating ${tableName}:`, err);
      setError(err instanceof Error ? err : new Error(`Failed to update ${tableName}`));
      setConnectionStatus('error');
      return null;
    } finally {
      // Decrementar contador de requisições pendentes
      pendingRequestsRef.current -= 1;

      // Limpar timeout se ainda existir
      clearRequestTimeout();
    }
  }, [
    tableName,
    user,
    skipUserFilter,
    isOnline,
    retryOnError,
    createRequestTimeout,
    clearRequestTimeout,
    retryAfterError
  ]);

  // Function to delete an item
  const deleteItem = useCallback(async (id: string) => {
    if (!user) return false;

    // Verificar se está online
    if (!isOnline) {
      setError(new Error('Não é possível excluir itens offline'));
      return false;
    }

    // Incrementar contador de requisições pendentes
    pendingRequestsRef.current += 1;

    // Criar um novo AbortController para esta requisição
    abortControllerRef.current = new AbortController();

    // Configurar timeout
    createRequestTimeout();

    try {
      // Função para executar a exclusão
      const executeDelete = async () => {
        // Construir a consulta com verificação de propriedade
        let query = supabase
          .from(tableName)
          .delete();

        // Adicionar verificação de propriedade para garantir que o usuário só exclua seus próprios dados
        if (!skipUserFilter && tableName !== 'users') {
          query = query.eq('id', id).eq('user_id', user.id);
        } else if (tableName === 'users') {
          // Para a tabela users, verificar se o ID corresponde ao usuário atual
          if (id !== user.id) {
            throw new Error('Você não tem permissão para excluir esta conta');
          }
          query = query.eq('id', user.id);
        } else {
          query = query.eq('id', id);
        }

        const { error } = await query;

        if (error) throw error;

        return true;
      };

      // Executar com retry se necessário
      const success = await measureFunction(
        `delete_${tableName}`,
        async () => {
          if (retryOnError) {
            return retryAfterError(executeDelete);
          } else {
            return executeDelete();
          }
        },
        { tableName, operation: 'delete', id }
      );

      if (success) {
        // Update local data
        setData(prevData =>
          prevData.filter(item => (item as any).id !== id)
        );

        // Clear cache for this table
        await clearCache();

        // Atualizar status de conexão
        setConnectionStatus('connected');
        lastSuccessfulRequestRef.current = Date.now();
      }

      return success;
    } catch (err) {
      console.error(`Error deleting ${tableName}:`, err);
      setError(err instanceof Error ? err : new Error(`Failed to delete ${tableName}`));
      setConnectionStatus('error');
      return false;
    } finally {
      // Decrementar contador de requisições pendentes
      pendingRequestsRef.current -= 1;

      // Limpar timeout se ainda existir
      clearRequestTimeout();
    }
  }, [
    tableName,
    user,
    skipUserFilter,
    isOnline,
    retryOnError,
    createRequestTimeout,
    clearRequestTimeout,
    retryAfterError
  ]);

  // Função para cancelar requisições pendentes
  const cancelRequests = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    setConnectionStatus('idle');
  }, []);

  // Função para obter uma página específica
  const fetchPage = useCallback((pageNumber: number) => {
    return fetchData(true);
  }, [fetchData]);

  // Função para obter a próxima página
  const fetchNextPage = useCallback(() => {
    if (page < (totalPages || Infinity)) {
      return fetchPage(page + 1);
    }
    return Promise.resolve();
  }, [fetchPage, page, totalPages]);

  // Função para obter a página anterior
  const fetchPreviousPage = useCallback(() => {
    if (page > 1) {
      return fetchPage(page - 1);
    }
    return Promise.resolve();
  }, [fetchPage, page]);

  return {
    // Dados
    data,
    loading,
    error,
    isRefreshing,
    fromCache,
    isOnline,

    // Informações de paginação
    page,
    totalPages,
    totalCount,

    // Status de conexão
    connectionStatus,
    retryCount,
    pendingRequests: pendingRequestsRef.current,
    lastSuccessfulRequest: lastSuccessfulRequestRef.current,

    // Funções de manipulação de dados
    refresh,
    addItem,
    updateItem,
    deleteItem,

    // Funções de paginação
    fetchPage,
    fetchNextPage,
    fetchPreviousPage,

    // Funções de controle de conexão
    cancelRequests,
  };
}
