import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';

type SupabaseUser = Database['public']['Tables']['users']['Row'];

interface UseSupabaseUserReturn {
  supabaseUser: SupabaseUser | null;
  loading: boolean;
  error: Error | null;
  refreshUser: () => Promise<void>;
  updateUser: (updates: Partial<SupabaseUser>) => Promise<boolean>;
}

/**
 * Hook para gerenciar dados do usuário no Supabase
 * Sincroniza automaticamente com a autenticação
 */
export function useSupabaseUser(): UseSupabaseUserReturn {
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuthStore();

  // Função para buscar dados do usuário
  const fetchUser = async () => {
    if (!user?.id) {
      setSupabaseUser(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        // Se o usuário não existe, criar um novo registro
        if (fetchError.code === 'PGRST116') {
          const newUser = {
            id: user.id,
            email: user.email || '',
            name: user.user_metadata?.name || user.email?.split('@')[0] || 'Usuário',
            avatar_url: user.user_metadata?.avatar_url || null,
            xp: 0,
            level: 1,
            streak: 0,
            study_time: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          const { data: createdUser, error: createError } = await supabase
            .from('users')
            .insert([newUser])
            .select()
            .single();

          if (createError) throw createError;
          setSupabaseUser(createdUser);
        } else {
          throw fetchError;
        }
      } else {
        setSupabaseUser(data);
      }
    } catch (err) {
      console.error('Erro ao buscar usuário:', err);
      setError(err instanceof Error ? err : new Error('Erro desconhecido'));
    } finally {
      setLoading(false);
    }
  };

  // Função para atualizar dados do usuário
  const updateUser = async (updates: Partial<SupabaseUser>): Promise<boolean> => {
    if (!user?.id || !supabaseUser) return false;

    try {
      setError(null);

      const { data, error: updateError } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (updateError) throw updateError;

      setSupabaseUser(data);
      return true;
    } catch (err) {
      console.error('Erro ao atualizar usuário:', err);
      setError(err instanceof Error ? err : new Error('Erro ao atualizar usuário'));
      return false;
    }
  };

  // Função para refresh manual
  const refreshUser = async () => {
    await fetchUser();
  };

  // Effect para buscar dados quando o usuário muda
  useEffect(() => {
    fetchUser();
  }, [user?.id]);

  // Effect para configurar listener de mudanças em tempo real
  useEffect(() => {
    if (!user?.id) return;

    const subscription = supabase
      .channel('user_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.id}`,
        },
        (payload) => {
          if (payload.eventType === 'UPDATE' && payload.new) {
            setSupabaseUser(payload.new as SupabaseUser);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user?.id]);

  return {
    supabaseUser,
    loading,
    error,
    refreshUser,
    updateUser,
  };
}
