import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Alert } from 'react-native';
import { Database } from '@/types/supabase';

type Profile = Database['public']['Tables']['users']['Row'];

export function useProfile() {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setProfile(null);
        return;
      }

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      setProfile(data);
    } catch (err: any) {
      setError(err);
      Alert.alert('Erro ao carregar perfil', err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      setLoading(true);
      
      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;
      setProfile(data);
      return data;
    } catch (err: any) {
      setError(err);
      Alert.alert('Erro ao atualizar perfil', err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateStreak = async () => {
    try {
      if (!profile) return;

      const today = new Date().toISOString().split('T')[0];
      const lastStreakDate = profile.last_streak_date ? new Date(profile.last_streak_date).toISOString().split('T')[0] : null;

      // Se o último acesso foi hoje, não faz nada
      if (lastStreakDate === today) return;

      // Se o último acesso foi ontem, incrementa o streak
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      let newStreak = profile.streak;
      
      if (lastStreakDate === yesterdayStr) {
        // Continua o streak
        newStreak += 1;
      } else {
        // Reinicia o streak
        newStreak = 1;
      }

      await updateProfile({
        streak: newStreak,
        last_streak_date: today,
      });
    } catch (err: any) {
      console.error('Erro ao atualizar streak:', err);
    }
  };

  const addXP = async (amount: number) => {
    try {
      if (!profile) return;

      let newXP = profile.xp + amount;
      let newLevel = profile.level;
      let newXPToNextLevel = profile.xp_to_next_level;

      // Verifica se subiu de nível
      while (newXP >= newXPToNextLevel) {
        newXP -= newXPToNextLevel;
        newLevel += 1;
        newXPToNextLevel = Math.floor(newXPToNextLevel * 1.5); // Aumenta o XP necessário para o próximo nível
      }

      await updateProfile({
        xp: newXP,
        level: newLevel,
        xp_to_next_level: newXPToNextLevel,
      });
    } catch (err: any) {
      console.error('Erro ao adicionar XP:', err);
    }
  };

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user]);

  useEffect(() => {
    if (profile) {
      updateStreak();
    }
  }, [profile]);

  return {
    profile,
    loading,
    error,
    refresh: fetchProfile,
    updateProfile,
    addXP,
  };
}
