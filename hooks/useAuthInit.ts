import { useEffect, useState } from 'react';
import { useAuthStore, setupAuthListener } from '@/store/authStore';
import { supabase } from '@/lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Hook para inicializar a autenticação
 * Substitui o AuthProvider do contexto
 */
export function useAuthInit() {
  const { refreshSession, loading, setSession, setUser } = useAuthStore();
  const [initComplete, setInitComplete] = useState(false);

  useEffect(() => {
    // Função para inicializar a autenticação
    const initAuth = async () => {
      try {
        // Verificar se há uma sessão ativa
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Erro ao obter sessão:', error);

          // Tentar recuperar a sessão do storage
          try {
            const storedAuth = await AsyncStorage.getItem('lia-auth-storage');
            if (storedAuth) {
              const parsedAuth = JSON.parse(storedAuth);
              if (parsedAuth.state && parsedAuth.state.session) {
                console.log('Sessão encontrada no storage, tentando restaurar...');

                // Tentar restaurar a sessão
                const { data: refreshData, error: refreshError } =
                  await supabase.auth.setSession({
                    access_token: parsedAuth.state.session.access_token,
                    refresh_token: parsedAuth.state.session.refresh_token
                  });

                if (refreshError) {
                  console.error('Erro ao restaurar sessão:', refreshError);
                } else if (refreshData.session) {
                  console.log('Sessão restaurada com sucesso');
                  setSession(refreshData.session);
                  setUser(refreshData.session?.user ?? null);
                }
              }
            }
          } catch (storageError) {
            console.error('Erro ao acessar storage:', storageError);
          }
        } else {
          // Sessão encontrada, atualizar o estado
          setSession(data.session);
          setUser(data.session?.user ?? null);
        }

        // Verificar sessão atual usando o método do store
        await refreshSession();

        // Configurar listener para mudanças na autenticação
        const unsubscribe = setupAuthListener();

        setInitComplete(true);

        // Limpar listener ao desmontar
        return () => {
          unsubscribe();
        };
      } catch (error) {
        console.error('Erro na inicialização da autenticação:', error);
        setInitComplete(true);
      }
    };

    initAuth();
  }, []);

  return { loading, initComplete };
}
