import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Alert } from 'react-native';

export function useSupabase<T>(
  tableName: string,
  options?: {
    select?: string;
    filter?: Record<string, any>;
    order?: { column: string; ascending?: boolean };
    limit?: number;
    skipUserFilter?: boolean; // Novo parâmetro para pular o filtro de usuário em casos específicos
  }
) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setData([]);
        return;
      }

      let query = supabase
        .from(tableName)
        .select(options?.select || '*');

      // Adicionar filtros
      const filters = { ...options?.filter };

      // Adicionar filtro de usuário automaticamente, a menos que seja explicitamente ignorado
      if (!options?.skipUserFilter && !filters.user_id && tableName !== 'users') {
        filters.user_id = user.id;
      }

      // Aplicar todos os filtros
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      // Adicionar ordenação
      if (options?.order) {
        query = query.order(options.order.column, {
          ascending: options.order.ascending ?? true,
        });
      }

      // Adicionar limite
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data: result, error } = await query;

      if (error) throw error;
      setData(result as T[]);
    } catch (err: any) {
      setError(err);
      Alert.alert('Erro ao carregar dados', err.message);
    } finally {
      setLoading(false);
    }
  };

  const addItem = async (item: Partial<T>) => {
    try {
      if (!user) {
        Alert.alert('Erro', 'Usuário não autenticado');
        return null;
      }

      setLoading(true);

      // Garantir que o user_id seja incluído, a menos que seja a tabela users
      const itemData = { ...item };
      if (tableName !== 'users' && !itemData.user_id) {
        itemData.user_id = user.id;
      }

      const { data: result, error } = await supabase
        .from(tableName)
        .insert([itemData])
        .select();

      if (error) throw error;
      setData((prev) => [...prev, ...(result as T[])]);
      return result[0];
    } catch (err: any) {
      setError(err);
      Alert.alert('Erro ao adicionar item', err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateItem = async (id: string, updates: Partial<T>) => {
    try {
      if (!user) {
        Alert.alert('Erro', 'Usuário não autenticado');
        return null;
      }

      setLoading(true);

      // Construir a consulta com verificação de propriedade
      let query = supabase
        .from(tableName)
        .update(updates)
        .eq('id', id);

      // Adicionar verificação de propriedade para garantir que o usuário só atualize seus próprios dados
      if (tableName !== 'users') {
        query = query.eq('user_id', user.id);
      } else {
        // Para a tabela users, verificar se o ID corresponde ao usuário atual
        query = query.eq('id', user.id);
      }

      const { data: result, error } = await query.select();

      if (error) throw error;

      // Se não houver resultados, significa que o usuário tentou atualizar um item que não lhe pertence
      if (result.length === 0) {
        throw new Error('Você não tem permissão para atualizar este item');
      }

      setData((prev) =>
        prev.map((item: any) => (item.id === id ? { ...item, ...updates } : item))
      );
      return result[0];
    } catch (err: any) {
      setError(err);
      Alert.alert('Erro ao atualizar item', err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteItem = async (id: string) => {
    try {
      if (!user) {
        Alert.alert('Erro', 'Usuário não autenticado');
        return false;
      }

      setLoading(true);

      // Construir a consulta com verificação de propriedade
      let query = supabase
        .from(tableName)
        .delete();

      // Adicionar verificação de propriedade para garantir que o usuário só exclua seus próprios dados
      if (tableName !== 'users') {
        query = query.eq('id', id).eq('user_id', user.id);
      } else {
        // Para a tabela users, verificar se o ID corresponde ao usuário atual
        // Normalmente, não permitimos que usuários excluam suas próprias contas desta forma
        if (id !== user.id) {
          throw new Error('Você não tem permissão para excluir esta conta');
        }
        query = query.eq('id', user.id);
      }

      const { error } = await query;

      if (error) throw error;

      setData((prev) => prev.filter((item: any) => item.id !== id));
      return true;
    } catch (err: any) {
      setError(err);
      Alert.alert('Erro ao excluir item', err.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  return {
    data,
    loading,
    error,
    refresh: fetchData,
    addItem,
    updateItem,
    deleteItem,
  };
}
