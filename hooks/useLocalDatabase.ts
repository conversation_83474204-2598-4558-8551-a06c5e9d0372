import { useEffect, useState } from 'react';
import { database, syncDatabase } from '@/lib/database';
import { Q } from '@nozbe/watermelondb';
import { useAuth } from '@/contexts/AuthContext';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

// Verificar se estamos no Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Minimum time between syncs (5 minutes)
const MIN_SYNC_INTERVAL = 5 * 60 * 1000;

export function useLocalDatabase() {
  const { user } = useAuth();
  const [isOnline, setIsOnline] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected && state.isInternetReachable);
    });

    // Load last sync time
    AsyncStorage.getItem('lastSyncTimestamp')
      .then(timestamp => {
        if (timestamp) {
          setLastSyncTime(parseInt(timestamp, 10));
        }
      })
      .catch(err => console.error('Error loading last sync time:', err));

    return () => {
      unsubscribe();
    };
  }, []);

  // Sync when coming online
  useEffect(() => {
    if (isOnline && user && !isSyncing) {
      const now = Date.now();

      // Only sync if enough time has passed since last sync
      if (!lastSyncTime || (now - lastSyncTime) > MIN_SYNC_INTERVAL) {
        syncWithServer();
      }
    }
  }, [isOnline, user]);

  // Function to manually trigger sync
  const syncWithServer = async () => {
    if (!user || isSyncing) return;

    try {
      setIsSyncing(true);
      setError(null);

      await syncDatabase();

      const now = Date.now();
      setLastSyncTime(now);
      await AsyncStorage.setItem('lastSyncTimestamp', now.toString());

    } catch (err) {
      console.error('Sync error:', err);
      setError(err instanceof Error ? err : new Error('Unknown sync error'));
    } finally {
      setIsSyncing(false);
    }
  };

  // Get subjects from local database
  const getSubjects = async () => {
    try {
      if (!user) return [];

      if (isExpoGo) {
        // Implementação para Expo Go usando AsyncStorage
        const keys = await AsyncStorage.getAllKeys();
        const subjectKeys = keys.filter(key => key.startsWith('subjects_'));

        const subjects = [];
        for (const key of subjectKeys) {
          const data = await AsyncStorage.getItem(key);
          if (data) {
            const subject = JSON.parse(data);
            if (subject.user_id === user.id) {
              subjects.push(subject);
            }
          }
        }

        return subjects;
      } else {
        // Implementação normal com WatermelonDB
        const subjectsCollection = database.get('subjects');
        const subjects = await subjectsCollection
          .query(Q.where('user_id', user.id))
          .fetch();

        return subjects;
      }
    } catch (err) {
      console.error('Error fetching subjects from local DB:', err);
      throw err;
    }
  };

  // Get activities from local database
  const getActivities = async () => {
    try {
      if (!user) return [];

      if (isExpoGo) {
        // Implementação para Expo Go usando AsyncStorage
        const keys = await AsyncStorage.getAllKeys();
        const activityKeys = keys.filter(key => key.startsWith('activities_'));

        const activities = [];
        for (const key of activityKeys) {
          const data = await AsyncStorage.getItem(key);
          if (data) {
            const activity = JSON.parse(data);
            if (activity.user_id === user.id) {
              activities.push(activity);
            }
          }
        }

        return activities;
      } else {
        // Implementação normal com WatermelonDB
        const activitiesCollection = database.get('activities');
        const activities = await activitiesCollection
          .query(Q.where('user_id', user.id))
          .fetch();

        return activities;
      }
    } catch (err) {
      console.error('Error fetching activities from local DB:', err);
      throw err;
    }
  };

  // Create a new subject
  const createSubject = async (subjectData) => {
    try {
      let newSubject;

      if (isExpoGo) {
        // Implementação para Expo Go usando AsyncStorage
        newSubject = {
          id: `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          title: subjectData.title,
          description: subjectData.description || '',
          color: subjectData.color,
          icon: subjectData.icon,
          user_id: user.id,
          created_at: Date.now(),
          updated_at: Date.now(),
          synced: false
        };

        // Salvar no AsyncStorage
        const key = `subjects_${newSubject.id}`;
        await AsyncStorage.setItem(key, JSON.stringify(newSubject));
      } else {
        // Implementação normal com WatermelonDB
        const subjectsCollection = database.get('subjects');

        await database.write(async () => {
          newSubject = await subjectsCollection.create(subject => {
            subject.title = subjectData.title;
            subject.description = subjectData.description || '';
            subject.color = subjectData.color;
            subject.icon = subjectData.icon;
            subject.userId = user.id;
            subject.synced = false;
          });
        });
      }

      // Trigger sync if online
      if (isOnline) {
        syncWithServer();
      }

      return newSubject;
    } catch (err) {
      console.error('Error creating subject in local DB:', err);
      throw err;
    }
  };

  // Update a subject
  const updateSubject = async (id, updates) => {
    try {
      let updatedSubject;

      if (isExpoGo) {
        // Implementação para Expo Go usando AsyncStorage
        const key = `subjects_${id}`;
        const data = await AsyncStorage.getItem(key);

        if (!data) {
          throw new Error(`Subject with id ${id} not found`);
        }

        const subject = JSON.parse(data);

        // Atualizar campos
        if (updates.title) subject.title = updates.title;
        if (updates.description !== undefined) subject.description = updates.description;
        if (updates.color) subject.color = updates.color;
        if (updates.icon) subject.icon = updates.icon;
        subject.synced = false;
        subject.updated_at = Date.now();

        // Salvar no AsyncStorage
        await AsyncStorage.setItem(key, JSON.stringify(subject));
        updatedSubject = subject;
      } else {
        // Implementação normal com WatermelonDB
        const subjectsCollection = database.get('subjects');
        const subject = await subjectsCollection.find(id);

        await database.write(async () => {
          await subject.update(subj => {
            if (updates.title) subj.title = updates.title;
            if (updates.description !== undefined) subj.description = updates.description;
            if (updates.color) subj.color = updates.color;
            if (updates.icon) subj.icon = updates.icon;
            subj.synced = false;
          });
        });

        updatedSubject = subject;
      }

      // Trigger sync if online
      if (isOnline) {
        syncWithServer();
      }

      return updatedSubject;
    } catch (err) {
      console.error('Error updating subject in local DB:', err);
      throw err;
    }
  };

  // Delete a subject
  const deleteSubject = async (id) => {
    try {
      if (isExpoGo) {
        // Implementação para Expo Go usando AsyncStorage
        const key = `subjects_${id}`;
        await AsyncStorage.removeItem(key);
      } else {
        // Implementação normal com WatermelonDB
        const subjectsCollection = database.get('subjects');
        const subject = await subjectsCollection.find(id);

        await database.write(async () => {
          await subject.markAsDeleted();
        });
      }

      // Trigger sync if online
      if (isOnline) {
        syncWithServer();
      }

      return true;
    } catch (err) {
      console.error('Error deleting subject from local DB:', err);
      throw err;
    }
  };

  return {
    database,
    isOnline,
    isSyncing,
    lastSyncTime,
    error,
    syncWithServer,
    getSubjects,
    getActivities,
    createSubject,
    updateSubject,
    deleteSubject,
  };
}
