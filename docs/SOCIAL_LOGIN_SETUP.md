# 🔐 Configuração do Login Social

Este guia explica como configurar o login social com Google e Apple no app Lia.

## 📋 Pré-requisitos

- Projeto Supabase configurado
- Conta Google Cloud Platform
- Conta Apple Developer (para Apple Sign-In)

## 🔧 Configuração do Google Sign-In

### 1. Google Cloud Console

1. Acesse [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto ou selecione um existente
3. Vá para **APIs & Services > Credentials**
4. Clique em **Create Credentials > OAuth 2.0 Client IDs**

### 2. Configurar OAuth Client IDs

#### Web Client ID (Obrigatório)
- **Application type**: Web application
- **Name**: Lia Study App Web
- **Authorized redirect URIs**: 
  - `https://your-project.supabase.co/auth/v1/callback`

#### iOS Client ID (Opcional)
- **Application type**: iOS
- **Name**: Lia Study App iOS
- **Bundle ID**: `com.liastudyapp` (ou seu bundle ID)

#### Android Client ID (Opcional)
- **Application type**: Android
- **Name**: Lia Study App Android
- **Package name**: `com.liastudyapp`
- **SHA-1 certificate fingerprint**: Obtenha com `keytool -list -v -keystore ~/.android/debug.keystore`

### 3. Configurar Supabase

1. Acesse seu projeto Supabase
2. Vá para **Authentication > Providers**
3. Habilite **Google**
4. Configure:
   - **Client ID**: Seu Web Client ID
   - **Client Secret**: Seu Web Client Secret
   - **Redirect URL**: `https://your-project.supabase.co/auth/v1/callback`

## 🍎 Configuração do Apple Sign-In

### 1. Apple Developer Console

1. Acesse [Apple Developer](https://developer.apple.com/account/)
2. Vá para **Certificates, Identifiers & Profiles**
3. Selecione **Identifiers**

### 2. Configurar App ID

1. Clique no **+** para criar um novo identifier
2. Selecione **App IDs**
3. Configure:
   - **Description**: Lia Study App
   - **Bundle ID**: `com.liastudyapp`
   - **Capabilities**: Habilite **Sign In with Apple**

### 3. Configurar Service ID

1. Crie um novo identifier
2. Selecione **Services IDs**
3. Configure:
   - **Description**: Lia Study App Sign In
   - **Identifier**: `com.liastudyapp.signin`
   - **Sign In with Apple**: Habilite e configure
   - **Primary App ID**: Selecione o App ID criado anteriormente
   - **Domains and Subdomains**: `your-project.supabase.co`
   - **Return URLs**: `https://your-project.supabase.co/auth/v1/callback`

### 4. Configurar Supabase

1. Acesse seu projeto Supabase
2. Vá para **Authentication > Providers**
3. Habilite **Apple**
4. Configure:
   - **Service ID**: `com.liastudyapp.signin`
   - **Secret Key**: Gere uma chave privada no Apple Developer Console

## ⚙️ Configuração do App

### 1. Variáveis de Ambiente

Copie `.env.example` para `.env` e configure:

```env
# Google OAuth
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=your_ios_client_id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID=your_android_client_id.apps.googleusercontent.com

# Apple OAuth
EXPO_PUBLIC_APPLE_SERVICE_ID=com.liastudyapp.signin
EXPO_PUBLIC_APPLE_REDIRECT_URI=https://your-project.supabase.co/auth/v1/callback
```

### 2. Configuração do Expo

No `app.config.js`, adicione:

```javascript
export default {
  expo: {
    // ... outras configurações
    scheme: "com.liastudyapp",
    ios: {
      bundleIdentifier: "com.liastudyapp",
      // ... outras configurações iOS
    },
    android: {
      package: "com.liastudyapp",
      // ... outras configurações Android
    },
  },
};
```

## 🧪 Testando o Login Social

### 1. Desenvolvimento

```bash
# Instalar dependências
npm install

# Iniciar o servidor de desenvolvimento
npx expo start --tunnel
```

### 2. Teste no Dispositivo

1. Abra o app no Expo Go
2. Tente fazer login com Google
3. Tente fazer login com Apple (apenas iOS)
4. Verifique se o usuário é criado no Supabase

### 3. Verificação no Supabase

1. Acesse **Authentication > Users**
2. Verifique se os usuários aparecem com provider `google` ou `apple`
3. Confirme que os dados do perfil foram importados

## 🔍 Troubleshooting

### Erro: "Google Sign-In não configurado"
- Verifique se o `EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID` está correto
- Confirme que o projeto Google Cloud tem as APIs habilitadas

### Erro: "Apple Sign-In não disponível"
- Verifique se está testando em um dispositivo iOS real
- Confirme que o Service ID está configurado corretamente

### Erro: "Redirect URI mismatch"
- Verifique se as URLs de redirecionamento estão corretas
- Confirme que o Supabase URL está correto

### Erro: "Invalid client"
- Verifique se os Client IDs estão corretos
- Confirme que os secrets estão configurados no Supabase

## 📱 Funcionalidades Implementadas

### ✅ Login Nativo
- Google Sign-In SDK nativo
- Apple Sign-In SDK nativo (iOS)
- Fallback para OAuth web

### ✅ Interface Melhorada
- Logo branca para melhor visibilidade
- Botões de login social redesenhados
- Animações e feedback visual

### ✅ Segurança
- Tokens JWT seguros
- Validação de sessão
- Logout completo (app + provedores)

### ✅ Experiência do Usuário
- Login rápido com um toque
- Importação automática de dados do perfil
- Tratamento de erros amigável

## 🚀 Próximos Passos

1. **Configurar credenciais reais** nos provedores
2. **Testar em dispositivos físicos**
3. **Configurar deep linking** para produção
4. **Implementar refresh tokens** para sessões longas
5. **Adicionar analytics** para monitorar conversão

---

**Nota**: Este guia assume que você já tem um projeto Supabase configurado. Se precisar de ajuda com a configuração inicial do Supabase, consulte a documentação oficial.
