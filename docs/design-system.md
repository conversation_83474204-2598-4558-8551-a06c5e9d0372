# Sistema de Design - Padronização UI/UX

## Visão Geral

Este documento descreve o sistema de padronização implementado no app, baseado no menu flutuante como referência de tamanhos para garantir consistência visual em todos os componentes.

## Filosofia de Design

- **Consistência**: Todos os elementos seguem os mesmos padrões de tamanho
- **Acessibilidade**: Touch targets respeitam as diretrizes de acessibilidade (mínimo 44px)
- **Escalabilidade**: Sistema flexível que permite ajustes mantendo proporções
- **Referência**: Menu flutuante (FAB) como base para todos os tamanhos

## Tamanhos Padronizados

### Ícones (`theme.sizes.icon`)

```typescript
icon: {
  xs: 16,    // Ícones pequenos em badges, detalhes
  sm: 20,    // Ícones padrão em botões, cards
  md: 24,    // Ícones principais (baseado no FAB)
  lg: 28,    // Ícones de destaque
  xl: 32,    // Ícones grandes em headers
  xxl: 40,   // Ícones muito grandes
  xxxl: 48,  // Ícones gigantes para estados vazios
}
```

**Uso recomendado:**
- `xs`: Badges, indicadores, ícones de status
- `sm`: Botões padrão, navegação, cards pequenos
- `md`: FABs, cards principais, ações importantes
- `lg`: Headers, ícones de destaque
- `xl+`: Estados vazios, ilustrações

### Botões (`theme.sizes.button`)

```typescript
button: {
  height: {
    xs: 32,    // Botões muito pequenos
    sm: 40,    // Botões pequenos
    md: 48,    // Botões padrão
    lg: 56,    // Botões grandes
    xl: 64,    // Botões extra grandes
  }
}
```

### FABs (`theme.sizes.fab`)

```typescript
fab: {
  main: 56,      // FAB principal (referência base)
  secondary: 48, // FABs secundários
  mini: 40,      // Mini FABs
}
```

### Containers de Ícones (`theme.sizes.iconContainer`)

```typescript
iconContainer: {
  xs: 32,    // Containers pequenos
  sm: 40,    // Containers padrão
  md: 48,    // Containers médios
  lg: 56,    // Containers grandes (cards principais)
  xl: 64,    // Containers extra grandes
}
```

### Touch Targets (`theme.sizes.touchTarget`)

```typescript
touchTarget: {
  min: 44,         // Tamanho mínimo recomendado
  comfortable: 48, // Tamanho confortável
  large: 56,       // Tamanho grande
}
```

## Utilitários de Design System

### Importação

```typescript
import { theme } from '@/constants/theme';
import { applyStandardSizes, getStandardIconProps } from '@/utils/designSystem';
```

### Funções Utilitárias

#### `getStandardIconProps(context)`

Retorna propriedades padronizadas para ícones baseado no contexto:

```typescript
// Contextos disponíveis:
'button'  // Ícones em botões
'card'    // Ícones em cards
'fab'     // Ícones em FABs
'detail'  // Ícones de detalhes/status
'header'  // Ícones em headers
'empty'   // Ícones em estados vazios

// Exemplo de uso:
const iconProps = getStandardIconProps('button');
<Icon {...iconProps} />
```

#### `applyStandardSizes`

Objeto com tamanhos padronizados para diferentes contextos:

```typescript
// Exemplos:
applyStandardSizes.buttonIcon('small')     // 16px
applyStandardSizes.cardIcon()              // 24px
applyStandardSizes.cardIconContainer()     // 56px
applyStandardSizes.fabIcon()               // 24px
```

## Implementação em Componentes

### Exemplo: Button Component

```typescript
import { theme } from '@/constants/theme';

// Tamanhos de ícones baseados no tamanho do botão
const iconSize = size === 'small' 
  ? theme.sizes.icon.xs 
  : size === 'large' 
    ? theme.sizes.icon.md 
    : theme.sizes.icon.sm;

// Altura do botão
const buttonHeight = theme.sizes.button.height[size];
```

### Exemplo: Card Component

```typescript
import { theme } from '@/constants/theme';

const styles = StyleSheet.create({
  iconContainer: {
    width: theme.sizes.iconContainer.lg,
    height: theme.sizes.iconContainer.lg,
    borderRadius: 16,
  }
});

// Ícone do card
<Icon size={theme.sizes.icon.md} />
```

### Exemplo: FAB Component

```typescript
import { theme } from '@/constants/theme';

const styles = StyleSheet.create({
  mainButton: {
    width: theme.sizes.fab.main,
    height: theme.sizes.fab.main,
    borderRadius: theme.sizes.fab.main / 2,
  }
});

// Ícone do FAB
<Icon size={theme.sizes.icon.md} />
```

## Componentes Atualizados

### ✅ Componentes Padronizados

- `Button.tsx` - Ícones e alturas padronizados
- `FloatingActionButton.tsx` - Tamanhos baseados no sistema
- `FeatureCard.tsx` - Ícones e containers padronizados
- `SubjectCard.tsx` - Ícones e containers padronizados
- `ActivityCard.tsx` - Ícones e containers padronizados
- `QuizCard.tsx` - Ícones e containers padronizados
- `TaskFAB.tsx` - FABs padronizados
- `MainResources.tsx` - Ícones e containers padronizados
- `StudyStats.tsx` - Ícones e containers padronizados
- `QuickActionsCarousel.tsx` - Ícones e containers padronizados

### 📱 Páginas Atualizadas

- `app/(tabs)/index.tsx` - Ícones padronizados
- `app/(tabs)/study-groups.tsx` - Ícones e FABs padronizados

## Diretrizes de Uso

### ✅ Faça

- Use sempre `theme.sizes` para tamanhos de ícones e containers
- Mantenha proporções baseadas no FAB principal (56px)
- Use utilitários do `designSystem.ts` quando disponível
- Respeite os touch targets mínimos (44px)

### ❌ Não Faça

- Não use tamanhos hardcoded (ex: `size={24}`)
- Não crie tamanhos personalizados sem justificativa
- Não ignore os touch targets mínimos
- Não misture diferentes sistemas de tamanhos

## Validação

Use a função `validateSizeConsistency()` para verificar a consistência:

```typescript
import { validateSizeConsistency } from '@/utils/designSystem';

const warnings = validateSizeConsistency();
console.log(warnings); // Array de avisos sobre inconsistências
```

## Próximos Passos

1. Aplicar padronização em componentes restantes
2. Criar testes automatizados para validação
3. Documentar padrões de cores e tipografia
4. Implementar sistema de spacing padronizado
