import { InteractionManager } from 'react-native';

/**
 * Runs a task after interactions have completed to avoid UI jank
 * @param task Function to run after interactions
 * @param timeout Optional timeout in ms (default: 500ms)
 */
export const runAfterInteractions = (task: () => any, timeout = 500): Promise<any> => {
  return new Promise((resolve) => {
    InteractionManager.runAfterInteractions(() => {
      // Add a small delay to ensure the UI is responsive
      setTimeout(() => {
        const result = task();
        resolve(result);
      }, timeout);
    });
  });
};

/**
 * Batches multiple updates to avoid excessive renders
 * @param updates Array of update functions to run
 * @param callback Function to call after all updates
 */
export const batchUpdates = (updates: (() => void)[], callback?: () => void): void => {
  // Use requestAnimationFrame to batch updates in the next frame
  requestAnimationFrame(() => {
    updates.forEach(update => update());
    if (callback) callback();
  });
};

/**
 * Debounces a function to limit how often it can be called
 * @param func Function to debounce
 * @param wait Wait time in ms
 * @param immediate Whether to call immediately on the leading edge
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait = 300,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function(this: any, ...args: Parameters<T>) {
    const context = this;
    
    const later = function() {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(context, args);
  };
}

/**
 * Throttles a function to limit how often it can be called
 * @param func Function to throttle
 * @param limit Limit in ms
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit = 300
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  let lastFunc: NodeJS.Timeout;
  let lastRan: number;
  
  return function(this: any, ...args: Parameters<T>) {
    const context = this;
    
    if (!inThrottle) {
      func.apply(context, args);
      lastRan = Date.now();
      inThrottle = true;
      
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          func.apply(context, args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
}

/**
 * Memoizes a function to cache its results
 * @param fn Function to memoize
 */
export function memoize<T extends (...args: any[]) => any>(fn: T): T {
  const cache = new Map();
  
  return function(this: any, ...args: Parameters<T>): ReturnType<T> {
    const key = JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = fn.apply(this, args);
    cache.set(key, result);
    
    return result;
  } as T;
}
