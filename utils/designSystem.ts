import { theme } from "@/constants/theme";

/**
 * Utilitários para padronização do Design System
 * Baseado no menu flutuante como referência de tamanhos
 */

// Tamanhos de ícones padronizados
export const getIconSize = (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'xxxl') => {
  return theme.sizes.icon[size];
};

// Tamanhos de botões padronizados
export const getButtonHeight = (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl') => {
  return theme.sizes.button.height[size];
};

// Tamanhos de containers de ícones padronizados
export const getIconContainerSize = (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl') => {
  return theme.sizes.iconContainer[size];
};

// Tamanhos de FAB padronizados
export const getFabSize = (type: 'main' | 'secondary' | 'mini') => {
  return theme.sizes.fab[type];
};

// Tamanhos de touch targets padronizados
export const getTouchTargetSize = (size: 'min' | 'comfortable' | 'large') => {
  return theme.sizes.touchTarget[size];
};

// Função para garantir que elementos tocáveis tenham tamanho mínimo
export const ensureMinTouchTarget = (size: number) => {
  return Math.max(size, theme.sizes.touchTarget.min);
};

// Estilos padronizados para ícones em diferentes contextos
export const getStandardIconProps = (context: 'button' | 'card' | 'fab' | 'detail' | 'header' | 'empty') => {
  switch (context) {
    case 'button':
      return {
        size: theme.sizes.icon.sm,
        strokeWidth: 2,
      };
    case 'card':
      return {
        size: theme.sizes.icon.md,
        strokeWidth: 2,
      };
    case 'fab':
      return {
        size: theme.sizes.icon.md,
        strokeWidth: 2,
      };
    case 'detail':
      return {
        size: theme.sizes.icon.xs,
        strokeWidth: 2,
      };
    case 'header':
      return {
        size: theme.sizes.icon.lg,
        strokeWidth: 2,
      };
    case 'empty':
      return {
        size: theme.sizes.icon.xxxl,
        strokeWidth: 1.5,
      };
    default:
      return {
        size: theme.sizes.icon.sm,
        strokeWidth: 2,
      };
  }
};

// Estilos padronizados para containers de ícones
export const getStandardIconContainerStyle = (context: 'small' | 'medium' | 'large' | 'feature') => {
  const baseStyle = {
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  };

  switch (context) {
    case 'small':
      return {
        ...baseStyle,
        width: theme.sizes.iconContainer.sm,
        height: theme.sizes.iconContainer.sm,
        borderRadius: theme.sizes.iconContainer.sm / 4,
      };
    case 'medium':
      return {
        ...baseStyle,
        width: theme.sizes.iconContainer.md,
        height: theme.sizes.iconContainer.md,
        borderRadius: theme.sizes.iconContainer.md / 4,
      };
    case 'large':
      return {
        ...baseStyle,
        width: theme.sizes.iconContainer.lg,
        height: theme.sizes.iconContainer.lg,
        borderRadius: 16,
      };
    case 'feature':
      return {
        ...baseStyle,
        width: theme.sizes.iconContainer.lg,
        height: theme.sizes.iconContainer.lg,
        borderRadius: 16,
      };
    default:
      return {
        ...baseStyle,
        width: theme.sizes.iconContainer.md,
        height: theme.sizes.iconContainer.md,
        borderRadius: theme.sizes.iconContainer.md / 4,
      };
  }
};

// Estilos padronizados para FABs
export const getStandardFabStyle = (type: 'main' | 'secondary' | 'mini') => {
  const size = getFabSize(type);
  return {
    width: size,
    height: size,
    borderRadius: size / 2,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: type === 'main' ? 0.3 : 0.2,
    shadowRadius: 3,
    elevation: type === 'main' ? 5 : 3,
  };
};

// Validação de consistência de tamanhos
export const validateSizeConsistency = () => {
  const warnings: string[] = [];

  // Verificar se os tamanhos de ícones estão em ordem crescente
  const iconSizes = theme.sizes.icon;
  if (iconSizes.xs >= iconSizes.sm) warnings.push('Icon XS should be smaller than SM');
  if (iconSizes.sm >= iconSizes.md) warnings.push('Icon SM should be smaller than MD');
  if (iconSizes.md >= iconSizes.lg) warnings.push('Icon MD should be smaller than LG');

  // Verificar se os containers de ícones são maiores que os ícones
  const containerSizes = theme.sizes.iconContainer;
  if (containerSizes.sm < iconSizes.sm + 8) warnings.push('Small icon container might be too small');
  if (containerSizes.md < iconSizes.md + 8) warnings.push('Medium icon container might be too small');
  if (containerSizes.lg < iconSizes.lg + 8) warnings.push('Large icon container might be too small');

  // Verificar se os touch targets atendem às diretrizes de acessibilidade
  const touchTargets = theme.sizes.touchTarget;
  if (touchTargets.min < 44) warnings.push('Minimum touch target is below recommended 44px');

  return warnings;
};

// Função para aplicar padronização em componentes existentes
export const applyStandardSizes = {
  // Para ícones em botões
  buttonIcon: (size: 'small' | 'medium' | 'large') => {
    switch (size) {
      case 'small': return theme.sizes.icon.xs;
      case 'large': return theme.sizes.icon.md;
      default: return theme.sizes.icon.sm;
    }
  },

  // Para ícones em cards
  cardIcon: () => theme.sizes.icon.md,

  // Para containers de ícones em cards
  cardIconContainer: () => theme.sizes.iconContainer.lg,

  // Para ícones em FABs
  fabIcon: () => theme.sizes.icon.md,

  // Para ícones de detalhes/status
  detailIcon: () => theme.sizes.icon.xs,

  // Para ícones em estados vazios
  emptyStateIcon: () => theme.sizes.icon.xxxl,
};

// Exportar constantes para fácil acesso
export const STANDARD_SIZES = {
  ICON: theme.sizes.icon,
  BUTTON: theme.sizes.button,
  FAB: theme.sizes.fab,
  ICON_CONTAINER: theme.sizes.iconContainer,
  TOUCH_TARGET: theme.sizes.touchTarget,
} as const;
