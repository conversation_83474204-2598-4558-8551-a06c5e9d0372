import { Image, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { debounce } from './performance';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Constants for cache management
const CACHE_VERSION = '1.0.0'; // Increment when cache structure changes
const MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
const MAX_CACHE_AGE = 7 * 24 * 60 * 60 * 1000; // 7 days
const CACHE_INFO_KEY = 'image_cache_info';

// Directories for different types of cached images
const imageDirectory = `${FileSystem.cacheDirectory}images/`;
const thumbnailDirectory = `${FileSystem.cacheDirectory}thumbnails/`;

// Cache info structure
interface CacheInfo {
  version: string;
  size: number;
  lastCleanup: number;
  entries: {
    [key: string]: {
      url: string;
      size: number;
      timestamp: number;
      accessCount: number;
      lastAccessed: number;
    };
  };
}

// Default cache info
const defaultCacheInfo: CacheInfo = {
  version: CACHE_VERSION,
  size: 0,
  lastCleanup: Date.now(),
  entries: {},
};

// In-memory cache for faster access
const memoryCache: Map<string, string> = new Map();

// Ensure cache directories exist
const ensureDirsExist = async () => {
  const dirs = [imageDirectory, thumbnailDirectory];

  for (const dir of dirs) {
    const dirInfo = await FileSystem.getInfoAsync(dir);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(dir, { intermediates: true });
    }
  }
};

// Load cache info from storage
const loadCacheInfo = async (): Promise<CacheInfo> => {
  try {
    const data = await AsyncStorage.getItem(CACHE_INFO_KEY);
    if (data) {
      const cacheInfo = JSON.parse(data) as CacheInfo;

      // If cache version has changed, reset the cache
      if (cacheInfo.version !== CACHE_VERSION) {
        await clearImageCache();
        return defaultCacheInfo;
      }

      return cacheInfo;
    }
  } catch (error) {
    console.error('Error loading cache info:', error);
  }

  return defaultCacheInfo;
};

// Save cache info to storage
const saveCacheInfo = debounce(async (cacheInfo: CacheInfo) => {
  try {
    await AsyncStorage.setItem(CACHE_INFO_KEY, JSON.stringify(cacheInfo));
  } catch (error) {
    console.error('Error saving cache info:', error);
  }
}, 1000);

// Generate a filename based on the URL
const generateFilename = async (url: string): Promise<string> => {
  const hash = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    url
  );
  return hash;
};

// Update cache info when a new image is added
const updateCacheInfo = async (
  url: string,
  filePath: string,
  size: number
): Promise<void> => {
  const cacheInfo = await loadCacheInfo();
  const filename = filePath.split('/').pop() || '';

  // Add or update entry
  cacheInfo.entries[filename] = {
    url,
    size,
    timestamp: Date.now(),
    accessCount: 1,
    lastAccessed: Date.now(),
  };

  // Update total cache size
  cacheInfo.size += size;

  // Check if cleanup is needed
  if (cacheInfo.size > MAX_CACHE_SIZE) {
    await cleanupCache(cacheInfo);
  }

  await saveCacheInfo(cacheInfo);
};

// Update access info when an image is accessed
const updateAccessInfo = async (filePath: string): Promise<void> => {
  const cacheInfo = await loadCacheInfo();
  const filename = filePath.split('/').pop() || '';

  if (cacheInfo.entries[filename]) {
    cacheInfo.entries[filename].accessCount += 1;
    cacheInfo.entries[filename].lastAccessed = Date.now();
    await saveCacheInfo(cacheInfo);
  }
};

// Clean up old or less used images when cache is full
const cleanupCache = async (cacheInfo: CacheInfo): Promise<void> => {
  // Only clean up once a day at most
  const now = Date.now();
  if (now - cacheInfo.lastCleanup < 24 * 60 * 60 * 1000) {
    return;
  }

  console.log('Cleaning up image cache...');

  // Sort entries by last accessed (oldest first) and access count (least used first)
  const entries = Object.entries(cacheInfo.entries)
    .sort(([, a], [, b]) => {
      // First sort by age (older files first)
      const ageComparison = a.timestamp - b.timestamp;
      if (ageComparison > MAX_CACHE_AGE / 2) return -1;

      // Then by access count (less used first)
      return a.accessCount - b.accessCount;
    });

  // Remove entries until we're under 70% of max cache size
  let currentSize = cacheInfo.size;
  const targetSize = MAX_CACHE_SIZE * 0.7;

  for (const [filename, entry] of entries) {
    // Skip if file is recent and frequently used
    const isRecent = now - entry.timestamp < MAX_CACHE_AGE / 2;
    const isFrequentlyUsed = entry.accessCount > 5;

    if (currentSize <= targetSize || (isRecent && isFrequentlyUsed)) {
      break;
    }

    try {
      // Delete the file
      const filePath = `${imageDirectory}${filename}`;
      const fileInfo = await FileSystem.getInfoAsync(filePath);

      if (fileInfo.exists) {
        await FileSystem.deleteAsync(filePath);
      }

      // Also check for thumbnail
      const thumbnailPath = `${thumbnailDirectory}${filename}`;
      const thumbnailInfo = await FileSystem.getInfoAsync(thumbnailPath);

      if (thumbnailInfo.exists) {
        await FileSystem.deleteAsync(thumbnailPath);
      }

      // Update cache size and remove entry
      currentSize -= entry.size;
      delete cacheInfo.entries[filename];

    } catch (error) {
      console.error(`Error deleting cached file ${filename}:`, error);
    }
  }

  // Update cache info
  cacheInfo.size = currentSize;
  cacheInfo.lastCleanup = now;
  await saveCacheInfo(cacheInfo);

  console.log(`Cache cleanup complete. New size: ${(currentSize / 1024 / 1024).toFixed(2)}MB`);
};

// Get cached image with optimizations
export const getCachedImage = async (
  url: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    thumbnail?: boolean;
  } = {}
): Promise<string | null> => {
  if (!url) return null;

  try {
    // Check memory cache first for fastest access
    const cacheKey = `${url}_${options.width || 0}_${options.height || 0}_${options.thumbnail ? 'thumb' : 'full'}`;
    if (memoryCache.has(cacheKey)) {
      // Update access info in the background
      updateAccessInfo(memoryCache.get(cacheKey)!).catch(console.error);
      return memoryCache.get(cacheKey)!;
    }

    await ensureDirsExist();
    const filename = await generateFilename(url);

    // Determine which directory to use based on thumbnail option
    const directory = options.thumbnail ? thumbnailDirectory : imageDirectory;
    const filePath = `${directory}${filename}`;

    // Check if file exists in cache
    const fileInfo = await FileSystem.getInfoAsync(filePath);
    if (fileInfo.exists) {
      // Update access info and add to memory cache
      updateAccessInfo(filePath).catch(console.error);
      memoryCache.set(cacheKey, filePath);
      return filePath;
    }

    // Check network connectivity before downloading
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      console.log('No network connection, cannot download image:', url);
      return null;
    }

    // Download the image
    const downloadResult = await FileSystem.downloadAsync(url, filePath);
    if (downloadResult.status !== 200) {
      console.error(`Failed to download image: ${url}, status: ${downloadResult.status}`);
      return null;
    }

    // Get file info for size
    const downloadedFileInfo = await FileSystem.getInfoAsync(filePath);

    // Process image if resize options are provided
    if ((options.width || options.height) && downloadedFileInfo.exists) {
      try {
        const manipResult = await manipulateAsync(
          filePath,
          [{ resize: { width: options.width, height: options.height } }],
          { compress: options.quality ? options.quality / 100 : 0.8, format: SaveFormat.JPEG }
        );

        // Save the processed image
        await FileSystem.moveAsync({
          from: manipResult.uri,
          to: filePath,
        });

        // Update file info after processing
        const processedFileInfo = await FileSystem.getInfoAsync(filePath);
        if (processedFileInfo.exists) {
          await updateCacheInfo(url, filePath, processedFileInfo.size || 0);
        }
      } catch (error) {
        console.error('Error processing image:', error);
        // If processing fails, still return the original downloaded image
      }
    } else if (downloadedFileInfo.exists) {
      // Update cache info for the original image
      await updateCacheInfo(url, filePath, downloadedFileInfo.size || 0);
    }

    // Add to memory cache
    memoryCache.set(cacheKey, filePath);
    return filePath;

  } catch (error) {
    console.error('Error getting cached image:', error);
    return null;
  }
};

// Preload images with optimizations
export const preloadImages = async (
  urls: string[],
  options: {
    width?: number;
    height?: number;
    quality?: number;
    thumbnail?: boolean;
    priority?: 'high' | 'normal' | 'low';
  } = {}
): Promise<void> => {
  try {
    await ensureDirsExist();

    // Process high priority images first, then normal, then low
    const priorityValue = { high: 0, normal: 1, low: 2 };
    const priority = options.priority || 'normal';

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      console.log('No network connection, skipping image preloading');
      return;
    }

    // Use a smaller batch size for better performance
    const batchSize = 3;
    const batches = [];

    for (let i = 0; i < urls.length; i += batchSize) {
      batches.push(urls.slice(i, i + batchSize));
    }

    // Process batches sequentially
    for (const batch of batches) {
      const promises = batch.map(url => getCachedImage(url, options));
      await Promise.all(promises);
    }

  } catch (error) {
    console.error('Error preloading images:', error);
  }
};

// Clear the image cache
export const clearImageCache = async (): Promise<void> => {
  try {
    // Clear memory cache
    memoryCache.clear();

    // Clear file cache
    const dirs = [imageDirectory, thumbnailDirectory];

    for (const dir of dirs) {
      const dirInfo = await FileSystem.getInfoAsync(dir);
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(dir);
        await FileSystem.makeDirectoryAsync(dir, { intermediates: true });
      }
    }

    // Reset cache info
    await AsyncStorage.setItem(CACHE_INFO_KEY, JSON.stringify(defaultCacheInfo));

    console.log('Image cache cleared successfully');
  } catch (error) {
    console.error('Error clearing image cache:', error);
  }
};

// Preload local images for memory cache
export const preloadLocalImages = (images: Record<string, any>): void => {
  if (!images) return;

  // Use a smaller batch size for better performance
  const batchSize = 5;
  const imageValues = Object.values(images);
  const batches = [];

  for (let i = 0; i < imageValues.length; i += batchSize) {
    batches.push(imageValues.slice(i, i + batchSize));
  }

  // Process batches with slight delays to avoid blocking the UI
  batches.forEach((batch, index) => {
    setTimeout(() => {
      batch.forEach(image => {
        if (typeof image === 'number') {
          Image.prefetch(Image.resolveAssetSource(image).uri);
        }
      });
    }, index * 50); // 50ms delay between batches
  });
};

// Get cache statistics
export const getCacheStats = async (): Promise<{
  size: number;
  count: number;
  lastCleanup: Date;
}> => {
  const cacheInfo = await loadCacheInfo();
  return {
    size: cacheInfo.size,
    count: Object.keys(cacheInfo.entries).length,
    lastCleanup: new Date(cacheInfo.lastCleanup),
  };
};
