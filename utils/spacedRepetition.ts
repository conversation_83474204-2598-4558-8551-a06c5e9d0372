/**
 * Sistema de Repetição Espaçada Avançado (Advanced Spaced Repetition System)
 *
 * Este sistema implementa um algoritmo de repetição espaçada baseado no SM-2 (SuperMemo 2),
 * com melhorias para otimizar a retenção de memória a longo prazo.
 *
 * Níveis de dificuldade:
 * 0 - <PERSON><PERSON> estudado
 * 1 - <PERSON><PERSON><PERSON><PERSON> (Dominado) - Intervalo longo, E-Factor aumenta
 * 2 - Médio (Em aprendizado) - Intervalo médio, E-Factor mantido
 * 3 - Difícil (Precisa revisar) - Intervalo curto, E-Factor diminui
 *
 * Características:
 * - Utiliza E-Factor (fator de facilidade) para ajustar intervalos
 * - Intervalos aumentam exponencialmente para cartões bem aprendidos
 * - Cartões difíceis são revisados com mais frequência
 * - Priorização inteligente de cartões para revisão
 */

// Fator de facilidade inicial (quanto maior, mais fácil o cartão é considerado)
const INITIAL_E_FACTOR = 2.5;

// Intervalos iniciais em dias
const INTERVALS = {
  HARD: 1,    // Difícil: revisar em 1 dia
  MEDIUM: 3,  // Médio: revisar em 3 dias
  EASY: 7     // Fácil: revisar em 7 dias
};

// Multiplicadores para repetições subsequentes
const MULTIPLIERS = {
  HARD: 1.5,   // Difícil: intervalo * 1.5
  MEDIUM: 2,   // Médio: intervalo * 2
  EASY: 2.5    // Fácil: intervalo * 2.5
};

// Ajustes do E-Factor com base na dificuldade
const E_FACTOR_ADJUSTMENTS = {
  HARD: -0.3,   // Difícil: reduzir E-Factor
  MEDIUM: 0,    // Médio: manter E-Factor
  EASY: 0.1     // Fácil: aumentar E-Factor
};

// Limite mínimo para o E-Factor
const MIN_E_FACTOR = 1.3;

/**
 * Calcula a próxima data de revisão com base na dificuldade e histórico
 * @param difficulty Nível de dificuldade (1=fácil, 2=médio, 3=difícil)
 * @param reviewCount Número de revisões anteriores
 * @param eFactor Fator de facilidade atual (opcional)
 * @returns Data da próxima revisão
 */
export function calculateNextReview(
  difficulty: number,
  reviewCount: number,
  eFactor: number = INITIAL_E_FACTOR
): Date {
  // Ajustar o E-Factor com base na dificuldade
  let newEFactor = eFactor;

  if (difficulty === 1) { // Fácil
    newEFactor += E_FACTOR_ADJUSTMENTS.EASY;
  } else if (difficulty === 2) { // Médio
    newEFactor += E_FACTOR_ADJUSTMENTS.MEDIUM;
  } else if (difficulty === 3) { // Difícil
    newEFactor += E_FACTOR_ADJUSTMENTS.HARD;
  }

  // Garantir que o E-Factor não fique abaixo do mínimo
  newEFactor = Math.max(newEFactor, MIN_E_FACTOR);

  // Calcular o intervalo em dias
  let intervalDays: number;

  if (reviewCount <= 1) {
    // Primeira ou segunda revisão: usar intervalos fixos
    if (difficulty === 1) { // Fácil
      intervalDays = INTERVALS.EASY;
    } else if (difficulty === 2) { // Médio
      intervalDays = INTERVALS.MEDIUM;
    } else { // Difícil
      intervalDays = INTERVALS.HARD;
    }
  } else {
    // Revisões subsequentes: aplicar multiplicadores e E-Factor
    let lastInterval: number;

    if (difficulty === 1) { // Fácil
      lastInterval = INTERVALS.EASY * Math.pow(MULTIPLIERS.EASY, reviewCount - 1);
    } else if (difficulty === 2) { // Médio
      lastInterval = INTERVALS.MEDIUM * Math.pow(MULTIPLIERS.MEDIUM, reviewCount - 1);
    } else { // Difícil
      lastInterval = INTERVALS.HARD * Math.pow(MULTIPLIERS.HARD, reviewCount - 1);
    }

    // Aplicar E-Factor para ajustar o intervalo
    intervalDays = Math.round(lastInterval * newEFactor);

    // Limitar o intervalo máximo a 6 meses (180 dias) para evitar intervalos muito longos
    intervalDays = Math.min(intervalDays, 180);
  }

  // Para cartões difíceis, garantir que sejam revisados mais frequentemente
  if (difficulty === 3 && intervalDays > 7) {
    intervalDays = Math.max(7, Math.floor(intervalDays / 2));
  }

  // Adicionar um pouco de aleatoriedade para evitar que todos os cartões apareçam no mesmo dia
  const randomFactor = Math.random() * 0.2 + 0.9; // Entre 0.9 e 1.1
  intervalDays = Math.round(intervalDays * randomFactor);

  // Calcular a próxima data de revisão
  const nextReview = new Date();
  nextReview.setDate(nextReview.getDate() + intervalDays);

  return nextReview;
}

// Função para verificar se um flashcard precisa ser revisado hoje
export function needsReview(nextReview: Date | null): boolean {
  if (!nextReview) return true; // Se não tiver data de revisão, precisa revisar

  const now = new Date();

  // Resetar as horas para comparar apenas as datas
  now.setHours(0, 0, 0, 0);
  const reviewDate = new Date(nextReview);
  reviewDate.setHours(0, 0, 0, 0);

  // Retorna true se a data de revisão for hoje ou no passado
  return reviewDate <= now;
}

/**
 * Calcula a prioridade de revisão de um flashcard
 * Quanto maior o valor, maior a prioridade
 *
 * Fatores considerados:
 * 1. Se está atrasado para revisão
 * 2. Nível de dificuldade
 * 3. Número de revisões anteriores
 * 4. Tempo desde a última revisão
 *
 * @param flashcard Objeto com informações do flashcard
 * @returns Valor numérico de prioridade (maior = mais prioritário)
 */
export function calculateReviewPriority(flashcard: {
  difficulty: number;
  nextReview: Date | null;
  reviewCount: number;
}): number {
  if (!flashcard.nextReview) return 100; // Máxima prioridade se nunca foi revisado

  const now = new Date();
  const reviewDate = new Date(flashcard.nextReview);

  // Calcular dias de atraso (negativo se a revisão ainda não estiver atrasada)
  const daysOverdue = Math.floor((now.getTime() - reviewDate.getTime()) / (1000 * 60 * 60 * 24));

  // Base da prioridade: dias de atraso
  let priority = daysOverdue;

  // Ajustar com base na dificuldade - cartões difíceis têm maior prioridade
  switch (flashcard.difficulty) {
    case 3: // Difícil
      priority += 15; // Prioridade muito alta para cartões difíceis
      break;
    case 2: // Médio
      priority += 7; // Prioridade moderada
      break;
    case 1: // Fácil
      priority += 3; // Prioridade baixa
      break;
    default: // Não estudado
      priority += 20; // Prioridade máxima para cartões não estudados
      break;
  }

  // Ajustar com base no número de revisões (cartões menos revisados têm prioridade)
  // Fórmula exponencial decrescente: quanto mais revisões, menor o bônus
  const reviewBonus = Math.max(0, 15 * Math.exp(-0.3 * (flashcard.reviewCount || 0)));
  priority += reviewBonus;

  // Bônus para cartões que estão próximos da data ideal de revisão
  // (nem muito cedo, nem muito tarde)
  if (daysOverdue >= -1 && daysOverdue <= 1) {
    priority += 5; // Bônus para cartões na janela ideal de revisão
  }

  return priority;
}

// Função para ordenar flashcards por prioridade de revisão
export function sortByReviewPriority(flashcards: Array<{
  difficulty: number;
  nextReview: Date | null;
  reviewCount: number;
}>): Array<{
  difficulty: number;
  nextReview: Date | null;
  reviewCount: number;
}> {
  return [...flashcards].sort((a, b) => {
    const priorityA = calculateReviewPriority(a);
    const priorityB = calculateReviewPriority(b);
    return priorityB - priorityA; // Ordem decrescente de prioridade
  });
}

// Função para obter uma descrição textual de quando o flashcard deve ser revisado
export function getNextReviewDescription(nextReview: Date | null): string {
  if (!nextReview) return "Não revisado";

  const now = new Date();
  const reviewDate = new Date(nextReview);

  // Calcular a diferença em dias
  const diffTime = reviewDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return `Atrasado por ${Math.abs(diffDays)} ${Math.abs(diffDays) === 1 ? 'dia' : 'dias'}`;
  } else if (diffDays === 0) {
    return "Hoje";
  } else if (diffDays === 1) {
    return "Amanhã";
  } else if (diffDays < 7) {
    return `Em ${diffDays} dias`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `Em ${weeks} ${weeks === 1 ? 'semana' : 'semanas'}`;
  } else {
    const months = Math.floor(diffDays / 30);
    return `Em ${months} ${months === 1 ? 'mês' : 'meses'}`;
  }
}
