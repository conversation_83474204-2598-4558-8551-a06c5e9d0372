/**
 * Utilitários para validação de dados
 */

// Validar email
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validar senha (mínimo 8 caracteres, pelo menos 1 letra e 1 número)
export const isValidPassword = (password: string): boolean => {
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
  return passwordRegex.test(password);
};

// Validar nome (pelo menos 2 caracteres, apenas letras e espaços)
export const isValidName = (name: string): boolean => {
  const nameRegex = /^[A-Za-zÀ-ÖØ-öø-ÿ\s]{2,}$/;
  return nameRegex.test(name);
};

// Validar número de telefone
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone);
};

// Validar URL
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

// Validar CPF
export const isValidCPF = (cpf: string): boolean => {
  // Remover caracteres não numéricos
  cpf = cpf.replace(/[^\d]/g, '');
  
  // Verificar se tem 11 dígitos
  if (cpf.length !== 11) return false;
  
  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(cpf)) return false;
  
  // Validar dígitos verificadores
  let sum = 0;
  let remainder;
  
  // Primeiro dígito verificador
  for (let i = 1; i <= 9; i++) {
    sum += parseInt(cpf.substring(i - 1, i)) * (11 - i);
  }
  
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cpf.substring(9, 10))) return false;
  
  // Segundo dígito verificador
  sum = 0;
  for (let i = 1; i <= 10; i++) {
    sum += parseInt(cpf.substring(i - 1, i)) * (12 - i);
  }
  
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cpf.substring(10, 11))) return false;
  
  return true;
};

// Sanitizar texto para prevenir XSS
export const sanitizeText = (text: string): string => {
  return text
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

// Sanitizar HTML para permitir apenas tags seguras
export const sanitizeHtml = (html: string): string => {
  // Lista de tags permitidas
  const allowedTags = ['b', 'i', 'u', 'p', 'br', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
  
  // Remover todas as tags não permitidas
  const sanitized = html.replace(/<\/?([a-z][a-z0-9]*)\b[^>]*>/gi, (tag, tagName) => {
    return allowedTags.includes(tagName.toLowerCase()) ? tag : '';
  });
  
  // Remover atributos potencialmente perigosos
  return sanitized.replace(/(javascript:|onclick|onload|onerror|onmouseover|onmouseout|onfocus|onblur|onkeydown|onkeypress|onkeyup)/gi, '');
};

// Validar data (formato YYYY-MM-DD)
export const isValidDate = (dateString: string): boolean => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  
  if (!dateRegex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};

// Validar hora (formato HH:MM)
export const isValidTime = (timeString: string): boolean => {
  const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
  return timeRegex.test(timeString);
};

// Validar número
export const isValidNumber = (value: any): boolean => {
  return !isNaN(parseFloat(value)) && isFinite(value);
};

// Validar inteiro positivo
export const isPositiveInteger = (value: any): boolean => {
  return Number.isInteger(Number(value)) && Number(value) > 0;
};

// Validar se o texto está vazio ou contém apenas espaços
export const isEmptyText = (text: string): boolean => {
  return text.trim().length === 0;
};

// Validar tamanho máximo de texto
export const isValidLength = (text: string, maxLength: number): boolean => {
  return text.length <= maxLength;
};

// Validar se o valor está dentro de um intervalo
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

// Validar se o arquivo tem uma extensão permitida
export const hasValidExtension = (filename: string, allowedExtensions: string[]): boolean => {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  return allowedExtensions.includes(extension);
};

// Validar tamanho máximo de arquivo (em bytes)
export const isValidFileSize = (fileSize: number, maxSizeInBytes: number): boolean => {
  return fileSize <= maxSizeInBytes;
};

// Exportar todas as funções de validação
export const validators = {
  isValidEmail,
  isValidPassword,
  isValidName,
  isValidPhone,
  isValidUrl,
  isValidCPF,
  sanitizeText,
  sanitizeHtml,
  isValidDate,
  isValidTime,
  isValidNumber,
  isPositiveInteger,
  isEmptyText,
  isValidLength,
  isInRange,
  hasValidExtension,
  isValidFileSize,
};

export default validators;
