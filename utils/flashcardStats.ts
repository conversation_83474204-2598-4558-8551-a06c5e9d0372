import { Flashcard } from "@/types";

/**
 * Calcula estatísticas de dificuldade dos flashcards
 * @param flashcards Lista de flashcards
 * @returns Objeto com estatísticas de dificuldade
 */
export function calculateDifficultyStats(flashcards: Flashcard[]) {
  if (!flashcards || flashcards.length === 0) {
    return {
      easy: 0,
      medium: 0,
      hard: 0,
      notReviewed: 0,
      total: 0,
      easyPercentage: 0,
      mediumPercentage: 0,
      hardPercentage: 0,
      notReviewedPercentage: 100,
    };
  }

  const total = flashcards.length;
  const easy = flashcards.filter(card => card.difficulty === 1).length;
  const medium = flashcards.filter(card => card.difficulty === 2).length;
  const hard = flashcards.filter(card => card.difficulty === 3).length;
  const notReviewed = flashcards.filter(card => card.difficulty === 0).length;

  return {
    easy,
    medium,
    hard,
    notReviewed,
    total,
    easyPercentage: Math.round((easy / total) * 100),
    mediumPercentage: Math.round((medium / total) * 100),
    hardPercentage: Math.round((hard / total) * 100),
    notReviewedPercentage: Math.round((notReviewed / total) * 100),
  };
}

/**
 * Calcula estatísticas de progresso de estudo
 * @param flashcards Lista de flashcards
 * @returns Objeto com estatísticas de progresso
 */
export function calculateProgressStats(flashcards: Flashcard[]) {
  if (!flashcards || flashcards.length === 0) {
    return {
      mastered: 0,
      learning: 0,
      needsReview: 0,
      notStarted: 0,
      total: 0,
      masteredPercentage: 0,
      learningPercentage: 0,
      needsReviewPercentage: 0,
      notStartedPercentage: 100,
    };
  }

  const total = flashcards.length;
  
  // Cartões dominados: dificuldade fácil e revisados pelo menos 3 vezes
  const mastered = flashcards.filter(card => 
    card.difficulty === 1 && (card.reviewCount || 0) >= 3
  ).length;
  
  // Cartões em aprendizado: revisados pelo menos uma vez, mas não dominados
  const learning = flashcards.filter(card => 
    (card.reviewCount || 0) > 0 && !(card.difficulty === 1 && (card.reviewCount || 0) >= 3)
  ).length;
  
  // Cartões que precisam de revisão: próxima revisão é hoje ou antes
  const needsReview = flashcards.filter(card => {
    if (!card.nextReview) return false;
    const nextReview = new Date(card.nextReview);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return nextReview <= today && (card.reviewCount || 0) > 0;
  }).length;
  
  // Cartões não iniciados: nunca revisados
  const notStarted = flashcards.filter(card => (card.reviewCount || 0) === 0).length;

  return {
    mastered,
    learning,
    needsReview,
    notStarted,
    total,
    masteredPercentage: Math.round((mastered / total) * 100),
    learningPercentage: Math.round((learning / total) * 100),
    needsReviewPercentage: Math.round((needsReview / total) * 100),
    notStartedPercentage: Math.round((notStarted / total) * 100),
  };
}

/**
 * Calcula estatísticas de atividade de estudo
 * @param flashcards Lista de flashcards
 * @returns Objeto com estatísticas de atividade
 */
export function calculateActivityStats(flashcards: Flashcard[]) {
  if (!flashcards || flashcards.length === 0) {
    return {
      lastWeek: [0, 0, 0, 0, 0, 0, 0],
      totalReviews: 0,
      averagePerDay: 0,
      streakDays: 0,
    };
  }

  // Obter datas de revisão
  const reviewDates = flashcards
    .filter(card => card.reviewCount && card.reviewCount > 0)
    .map(card => {
      // Usar a data de próxima revisão e subtrair o intervalo para estimar quando foi a última revisão
      if (card.nextReview) {
        const nextReview = new Date(card.nextReview);
        // Estimar a última revisão (simplificado)
        const lastReview = new Date(nextReview);
        lastReview.setDate(lastReview.getDate() - 1); // Simplificação
        return lastReview;
      }
      return null;
    })
    .filter(date => date !== null) as Date[];

  // Calcular atividade da última semana
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const lastWeek = Array(7).fill(0);
  
  reviewDates.forEach(date => {
    const daysDiff = Math.floor((today.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff >= 0 && daysDiff < 7) {
      lastWeek[6 - daysDiff]++;
    }
  });

  // Calcular total de revisões
  const totalReviews = flashcards.reduce((sum, card) => sum + (card.reviewCount || 0), 0);
  
  // Calcular média por dia (considerando os últimos 7 dias)
  const averagePerDay = Math.round(lastWeek.reduce((sum, count) => sum + count, 0) / 7);
  
  // Calcular dias de sequência
  let streakDays = 0;
  for (let i = 6; i >= 0; i--) {
    if (lastWeek[i] > 0) {
      streakDays++;
    } else {
      break;
    }
  }

  return {
    lastWeek,
    totalReviews,
    averagePerDay,
    streakDays,
  };
}

/**
 * Calcula todas as estatísticas de flashcards
 * @param flashcards Lista de flashcards
 * @returns Objeto com todas as estatísticas
 */
export function calculateAllStats(flashcards: Flashcard[]) {
  return {
    difficulty: calculateDifficultyStats(flashcards),
    progress: calculateProgressStats(flashcards),
    activity: calculateActivityStats(flashcards),
  };
}
