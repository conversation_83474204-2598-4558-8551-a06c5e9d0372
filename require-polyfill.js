/**
 * Simple polyfill for require.resolve
 * This file should be imported at the very beginning of the app
 */

// Check if require exists but resolve doesn't
if (typeof global !== 'undefined' && typeof global.require === 'function' && !global.require.resolve) {
  // Add a simple implementation of resolve
  global.require.resolve = function(moduleName) {
    return moduleName;
  };
  console.log('require.resolve polyfill applied in require-polyfill.js');
}

// Export a dummy value to ensure the file is properly imported
export default true;
