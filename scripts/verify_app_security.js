/**
 * Script para verificar a segurança do aplicativo
 * 
 * Este script verifica:
 * 1. Se todas as rotas estão protegidas pelo componente RouteGuard
 * 2. Se todos os componentes usam o hook useSupabaseOptimized em vez de useSupabase
 * 3. Se todas as tabelas têm políticas RLS configuradas corretamente
 * 
 * Uso:
 * node scripts/verify_app_security.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Diretórios a serem ignorados
const IGNORED_DIRS = [
  'node_modules',
  '.git',
  'build',
  'dist',
  '.expo',
  '.expo-shared',
  'scripts',
];

// Diretórios de rotas
const ROUTE_DIRS = [
  'app',
  'app/(tabs)',
  'app/(auth)',
  'app/subject',
  'app/group',
  'app/quiz',
  'app/flashcard',
];

// Rotas que não precisam de proteção (login, registro, etc.)
const PUBLIC_ROUTES = [
  'login',
  'register',
  'forgot-password',
  'reset-password',
  'welcome',
  'onboarding',
  '_layout',
];

// Extensões de arquivo a serem verificadas
const FILE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx'];

// Função para buscar arquivos recursivamente
function findFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) {
    return fileList;
  }

  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !IGNORED_DIRS.includes(file)) {
      findFiles(filePath, fileList);
    } else if (stat.isFile() && FILE_EXTENSIONS.includes(path.extname(file))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Função para verificar se um arquivo usa RouteGuard
function usesRouteGuard(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('<RouteGuard') || 
         content.includes('from \'@/components/RouteGuard\'') ||
         content.includes('from "@/components/RouteGuard"');
}

// Função para verificar se um arquivo usa useSupabase
function usesUseSupabase(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('import { useSupabase }') || 
         content.includes('import {useSupabase}') ||
         content.match(/import.*useSupabase.*from/);
}

// Função para verificar se um arquivo usa useSupabaseOptimized
function usesUseSupabaseOptimized(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('import { useSupabaseOptimized }') || 
         content.includes('import {useSupabaseOptimized}') ||
         content.match(/import.*useSupabaseOptimized.*from/);
}

// Função para verificar se um arquivo é uma rota pública
function isPublicRoute(filePath) {
  const fileName = path.basename(filePath, path.extname(filePath));
  return PUBLIC_ROUTES.includes(fileName);
}

// Função para verificar a segurança das rotas
function checkRoutesSecurity() {
  console.log('Verificando segurança das rotas...');

  // Obter o diretório raiz do projeto
  const rootDir = process.cwd();
  console.log(`Diretório raiz: ${rootDir}`);

  // Buscar todos os arquivos de rota
  let routeFiles = [];
  ROUTE_DIRS.forEach(dir => {
    const fullDir = path.join(rootDir, dir);
    routeFiles = routeFiles.concat(findFiles(fullDir));
  });
  
  console.log(`Encontrados ${routeFiles.length} arquivos de rota para verificar.`);

  // Filtrar arquivos que não são rotas públicas
  const protectedRouteFiles = routeFiles.filter(file => !isPublicRoute(file));
  console.log(`Encontrados ${protectedRouteFiles.length} arquivos de rota que precisam de proteção.`);

  // Verificar quais rotas não usam RouteGuard
  const unprotectedRoutes = protectedRouteFiles.filter(file => !usesRouteGuard(file));
  
  if (unprotectedRoutes.length > 0) {
    console.log('\n⚠️ Rotas não protegidas:');
    unprotectedRoutes.forEach(file => {
      const relativePath = path.relative(rootDir, file);
      console.log(`- ${relativePath}`);
    });
  } else {
    console.log('\n✅ Todas as rotas estão protegidas pelo componente RouteGuard!');
  }

  return unprotectedRoutes.length === 0;
}

// Função para verificar o uso de hooks otimizados
function checkOptimizedHooks() {
  console.log('\nVerificando uso de hooks otimizados...');

  // Obter o diretório raiz do projeto
  const rootDir = process.cwd();

  // Buscar todos os arquivos
  const files = findFiles(rootDir);
  console.log(`Encontrados ${files.length} arquivos para verificar.`);

  // Filtrar arquivos que usam useSupabase
  const filesUsingUseSupabase = files.filter(usesUseSupabase);
  console.log(`Encontrados ${filesUsingUseSupabase.length} arquivos que usam useSupabase.`);

  // Filtrar arquivos que usam useSupabaseOptimized
  const filesUsingUseSupabaseOptimized = files.filter(usesUseSupabaseOptimized);
  console.log(`Encontrados ${filesUsingUseSupabaseOptimized.length} arquivos que usam useSupabaseOptimized.`);

  if (filesUsingUseSupabase.length > 0) {
    console.log('\n⚠️ Arquivos que ainda usam useSupabase (não otimizado):');
    filesUsingUseSupabase.forEach(file => {
      const relativePath = path.relative(rootDir, file);
      console.log(`- ${relativePath}`);
    });
  } else {
    console.log('\n✅ Todos os arquivos usam o hook useSupabaseOptimized!');
  }

  return filesUsingUseSupabase.length === 0;
}

// Função para verificar as políticas RLS
function checkRLSPolicies() {
  console.log('\nVerificando políticas RLS...');

  try {
    // Executar consulta para verificar se todas as tabelas têm RLS habilitado
    const command = `
      echo "SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = false ORDER BY tablename;" | 
      PGPASSWORD=\\$SUPABASE_DB_PASSWORD psql -h \\$SUPABASE_DB_HOST -U postgres -d postgres
    `;

    const result = execSync(command, { encoding: 'utf8' });

    if (result.includes('(0 rows)')) {
      console.log('\n✅ Todas as tabelas têm RLS habilitado!');
      return true;
    } else {
      console.log('\n⚠️ Tabelas sem RLS habilitado:');
      console.log(result);
      return false;
    }
  } catch (error) {
    console.error('\n❌ Erro ao verificar políticas RLS:', error.message);
    console.log('Você pode verificar manualmente executando a consulta SQL:');
    console.log('SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = \'public\' AND rowsecurity = false ORDER BY tablename;');
    return false;
  }
}

// Função principal
function main() {
  console.log('Iniciando verificação de segurança do aplicativo...');
  console.log('=================================================');

  // Verificar segurança das rotas
  const routesSecure = checkRoutesSecurity();

  // Verificar uso de hooks otimizados
  const hooksOptimized = checkOptimizedHooks();

  // Verificar políticas RLS
  const rlsPoliciesConfigured = checkRLSPolicies();

  // Resumo
  console.log('\n=================================================');
  console.log('Resumo da verificação de segurança:');
  console.log(`- Rotas protegidas: ${routesSecure ? '✅' : '❌'}`);
  console.log(`- Hooks otimizados: ${hooksOptimized ? '✅' : '❌'}`);
  console.log(`- Políticas RLS: ${rlsPoliciesConfigured ? '✅' : '❌'}`);
  console.log('=================================================');

  if (routesSecure && hooksOptimized && rlsPoliciesConfigured) {
    console.log('\n✅ O aplicativo está seguro e otimizado para alta escala!');
    return 0;
  } else {
    console.log('\n⚠️ O aplicativo precisa de melhorias para garantir segurança e otimização para alta escala.');
    console.log('Execute os scripts de migração para corrigir os problemas identificados:');
    console.log('- node scripts/migrate_to_optimized_hook.js');
    console.log('- node scripts/check_route_protection.js');
    return 1;
  }
}

// Executar a função principal
process.exit(main());
