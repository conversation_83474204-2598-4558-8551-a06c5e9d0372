/**
 * Script para verificar quais rotas não estão protegidas pelo componente RouteGuard
 * 
 * Este script busca todos os arquivos de rota e verifica se eles usam o componente RouteGuard
 * para proteger o acesso aos dados.
 * 
 * Uso:
 * node scripts/check_route_protection.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Diretórios a serem ignorados
const IGNORED_DIRS = [
  'node_modules',
  '.git',
  'build',
  'dist',
  '.expo',
  '.expo-shared',
  'scripts',
];

// Diretórios de rotas
const ROUTE_DIRS = [
  'app',
  'app/(tabs)',
  'app/(auth)',
  'app/subject',
  'app/group',
  'app/quiz',
  'app/flashcard',
];

// Rotas que não precisam de proteção (login, registro, etc.)
const PUBLIC_ROUTES = [
  'login',
  'register',
  'forgot-password',
  'reset-password',
  'welcome',
  'onboarding',
  '_layout',
];

// Extensões de arquivo a serem verificadas
const FILE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx'];

// Função para buscar arquivos recursivamente
function findFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) {
    return fileList;
  }

  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !IGNORED_DIRS.includes(file)) {
      findFiles(filePath, fileList);
    } else if (stat.isFile() && FILE_EXTENSIONS.includes(path.extname(file))) {
      // Verificar se é um arquivo de rota (não inclui _layout, etc.)
      const fileName = path.basename(file, path.extname(file));
      if (!fileName.startsWith('_') || fileName === '_layout') {
        fileList.push(filePath);
      }
    }
  });

  return fileList;
}

// Função para verificar se um arquivo usa RouteGuard
function usesRouteGuard(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('<RouteGuard') || 
         content.includes('from \'@/components/RouteGuard\'') ||
         content.includes('from "@/components/RouteGuard"');
}

// Função para verificar se um arquivo é uma rota pública
function isPublicRoute(filePath) {
  const fileName = path.basename(filePath, path.extname(filePath));
  return PUBLIC_ROUTES.includes(fileName);
}

// Função para gerar um template de RouteGuard para um arquivo
function generateRouteGuardTemplate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath, path.extname(filePath));
  
  // Verificar se o arquivo exporta um componente React
  if (!content.includes('export default') || !content.includes('return (')) {
    return null;
  }
  
  // Extrair o nome do componente
  const match = content.match(/export\s+default\s+function\s+(\w+)/);
  if (!match) {
    return null;
  }
  
  const componentName = match[1];
  
  // Gerar template
  return `
// Importar o componente RouteGuard
import { RouteGuard } from '@/components/RouteGuard';

// Modificar o componente para usar RouteGuard
export default function ${componentName}() {
  // ... seu código existente ...

  return (
    <RouteGuard>
      {/* Seu conteúdo existente */}
    </RouteGuard>
  );
}
`;
}

// Função principal
function main() {
  console.log('Verificando proteção de rotas...');

  // Obter o diretório raiz do projeto
  const rootDir = process.cwd();
  console.log(`Diretório raiz: ${rootDir}`);

  // Buscar todos os arquivos de rota
  let routeFiles = [];
  ROUTE_DIRS.forEach(dir => {
    const fullDir = path.join(rootDir, dir);
    routeFiles = routeFiles.concat(findFiles(fullDir));
  });
  
  console.log(`Encontrados ${routeFiles.length} arquivos de rota para verificar.`);

  // Filtrar arquivos que não são rotas públicas
  const protectedRouteFiles = routeFiles.filter(file => !isPublicRoute(file));
  console.log(`Encontrados ${protectedRouteFiles.length} arquivos de rota que precisam de proteção.`);

  // Verificar quais rotas não usam RouteGuard
  const unprotectedRoutes = protectedRouteFiles.filter(file => !usesRouteGuard(file));
  console.log(`Encontrados ${unprotectedRoutes.length} arquivos de rota que não usam RouteGuard.`);

  // Exibir rotas não protegidas
  if (unprotectedRoutes.length > 0) {
    console.log('\nRotas não protegidas:');
    unprotectedRoutes.forEach(file => {
      const relativePath = path.relative(rootDir, file);
      console.log(`- ${relativePath}`);
      
      // Gerar template de RouteGuard
      const template = generateRouteGuardTemplate(file);
      if (template) {
        console.log('  Sugestão de implementação:');
        console.log(template);
      }
    });
  } else {
    console.log('\nTodas as rotas estão protegidas pelo componente RouteGuard!');
  }
}

// Executar a função principal
main();
