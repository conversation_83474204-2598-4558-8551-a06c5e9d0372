#!/usr/bin/env ts-node

/**
 * Script para validar a consistência do Design System
 * Verifica se os componentes estão seguindo os padrões estabelecidos
 */

import * as fs from 'fs';
import * as path from 'path';
import { validateSizeConsistency } from '../utils/designSystem';

interface ValidationResult {
  file: string;
  issues: string[];
  suggestions: string[];
}

class DesignSystemValidator {
  private results: ValidationResult[] = [];
  private componentsDir = path.join(__dirname, '../components');
  private pagesDir = path.join(__dirname, '../app');

  // Padrões que devem ser evitados
  private antiPatterns = [
    {
      pattern: /size=\{?\d+\}?/g,
      message: 'Tamanho hardcoded encontrado. Use theme.sizes.icon.* ou applyStandardSizes.*',
      severity: 'warning'
    },
    {
      pattern: /width:\s*\d+,?\s*height:\s*\d+/g,
      message: 'Dimensões hardcoded encontradas. Use theme.sizes.* para consistência',
      severity: 'warning'
    },
    {
      pattern: /borderRadius:\s*\d+/g,
      message: 'BorderRadius hardcoded. Considere usar theme.borderRadius.*',
      severity: 'info'
    }
  ];

  // Padrões recomendados
  private recommendedPatterns = [
    {
      pattern: /theme\.sizes\.icon\./g,
      message: 'Uso correto de theme.sizes.icon',
      severity: 'good'
    },
    {
      pattern: /applyStandardSizes\./g,
      message: 'Uso correto de applyStandardSizes',
      severity: 'good'
    },
    {
      pattern: /getStandardIconProps/g,
      message: 'Uso correto de getStandardIconProps',
      severity: 'good'
    }
  ];

  async validateFile(filePath: string): Promise<ValidationResult> {
    const content = fs.readFileSync(filePath, 'utf-8');
    const relativePath = path.relative(process.cwd(), filePath);
    const result: ValidationResult = {
      file: relativePath,
      issues: [],
      suggestions: []
    };

    // Verificar anti-padrões
    this.antiPatterns.forEach(({ pattern, message, severity }) => {
      const matches = content.match(pattern);
      if (matches) {
        const issue = `${severity.toUpperCase()}: ${message} (${matches.length} ocorrências)`;
        if (severity === 'warning') {
          result.issues.push(issue);
        } else {
          result.suggestions.push(issue);
        }
      }
    });

    // Verificar padrões recomendados
    this.recommendedPatterns.forEach(({ pattern, message }) => {
      const matches = content.match(pattern);
      if (matches) {
        result.suggestions.push(`✅ ${message} (${matches.length} ocorrências)`);
      }
    });

    // Verificações específicas para componentes React Native
    this.validateReactNativeSpecific(content, result);

    return result;
  }

  private validateReactNativeSpecific(content: string, result: ValidationResult) {
    // Verificar se está importando theme quando usa tamanhos
    const usesThemeSizes = /theme\.sizes/.test(content);
    const importsTheme = /import.*theme.*from.*@\/constants\/theme/.test(content);
    
    if (usesThemeSizes && !importsTheme) {
      result.issues.push('WARNING: Usa theme.sizes mas não importa theme');
    }

    // Verificar se componentes de ícone têm tamanhos consistentes
    const iconComponents = content.match(/<[A-Z][a-zA-Z]*\s+[^>]*size=\{[^}]+\}/g);
    if (iconComponents) {
      iconComponents.forEach(component => {
        if (!/theme\.sizes\.icon|applyStandardSizes/.test(component)) {
          result.issues.push(`WARNING: Ícone com tamanho não padronizado: ${component.substring(0, 50)}...`);
        }
      });
    }

    // Verificar touch targets
    const touchableComponents = content.match(/<Pressable[^>]*>/g);
    if (touchableComponents) {
      touchableComponents.forEach(component => {
        if (/style=\{[^}]*width:\s*[1-3]\d[^}]*\}/.test(component)) {
          result.suggestions.push('INFO: Possível touch target pequeno detectado. Verifique se atende aos 44px mínimos');
        }
      });
    }
  }

  async validateDirectory(dirPath: string) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Diretório não encontrado: ${dirPath}`);
      return;
    }

    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await this.validateDirectory(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        const result = await this.validateFile(filePath);
        if (result.issues.length > 0 || result.suggestions.length > 0) {
          this.results.push(result);
        }
      }
    }
  }

  generateReport() {
    console.log('\n🎨 RELATÓRIO DE VALIDAÇÃO DO DESIGN SYSTEM\n');
    console.log('='.repeat(60));

    // Validação do tema
    console.log('\n📏 VALIDAÇÃO DO TEMA:');
    const themeWarnings = validateSizeConsistency();
    if (themeWarnings.length === 0) {
      console.log('✅ Tema está consistente');
    } else {
      themeWarnings.forEach(warning => {
        console.log(`⚠️  ${warning}`);
      });
    }

    // Estatísticas gerais
    const totalFiles = this.results.length;
    const totalIssues = this.results.reduce((sum, r) => sum + r.issues.length, 0);
    const totalSuggestions = this.results.reduce((sum, r) => sum + r.suggestions.length, 0);

    console.log('\n📊 ESTATÍSTICAS:');
    console.log(`Arquivos analisados: ${totalFiles}`);
    console.log(`Issues encontrados: ${totalIssues}`);
    console.log(`Sugestões: ${totalSuggestions}`);

    // Detalhes por arquivo
    if (this.results.length > 0) {
      console.log('\n📁 DETALHES POR ARQUIVO:\n');
      
      this.results.forEach(result => {
        console.log(`📄 ${result.file}`);
        
        if (result.issues.length > 0) {
          console.log('  🚨 Issues:');
          result.issues.forEach(issue => {
            console.log(`    • ${issue}`);
          });
        }
        
        if (result.suggestions.length > 0) {
          console.log('  💡 Sugestões:');
          result.suggestions.forEach(suggestion => {
            console.log(`    • ${suggestion}`);
          });
        }
        
        console.log('');
      });
    }

    // Recomendações
    console.log('\n🎯 RECOMENDAÇÕES GERAIS:');
    console.log('• Use theme.sizes.icon.* para tamanhos de ícones');
    console.log('• Use theme.sizes.button.height.* para alturas de botões');
    console.log('• Use theme.sizes.iconContainer.* para containers de ícones');
    console.log('• Use applyStandardSizes.* para tamanhos contextuais');
    console.log('• Garanta touch targets mínimos de 44px');
    console.log('• Importe theme quando usar theme.sizes');

    console.log('\n' + '='.repeat(60));
    
    if (totalIssues === 0) {
      console.log('🎉 PARABÉNS! Nenhum issue crítico encontrado!');
    } else {
      console.log(`⚠️  ${totalIssues} issues encontrados. Revise os arquivos acima.`);
    }
  }

  async run() {
    console.log('🔍 Iniciando validação do Design System...\n');
    
    // Validar componentes
    console.log('Validando componentes...');
    await this.validateDirectory(this.componentsDir);
    
    // Validar páginas
    console.log('Validando páginas...');
    await this.validateDirectory(this.pagesDir);
    
    // Gerar relatório
    this.generateReport();
  }
}

// Executar validação se chamado diretamente
if (require.main === module) {
  const validator = new DesignSystemValidator();
  validator.run().catch(console.error);
}

export { DesignSystemValidator };
