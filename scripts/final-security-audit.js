#!/usr/bin/env node

/**
 * AUDITORIA FINAL DE SEGURANÇA - LIA APP
 * Teste abrangente de todas as funcionalidades de segurança
 */

const fs = require('fs');
const path = require('path');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Resultados da auditoria
const auditResults = {
  critical: [],
  high: [],
  medium: [],
  low: [],
  passed: []
};

function addResult(level, test, status, details) {
  const result = { test, status, details, timestamp: new Date().toISOString() };
  auditResults[level].push(result);
  
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  log(`[${level.toUpperCase()}] ${test}: ${status}`, statusColor);
  if (details) log(`  └─ ${details}`, 'cyan');
}

// 1. Verificar implementações de autenticação no código
function checkAuthImplementations() {
  log('\n🔐 Verificando implementações de autenticação...', 'blue');
  
  const filesToCheck = [
    'hooks/useSupabase.ts',
    'hooks/useSupabaseOptimized.ts',
    'contexts/AuthContext.tsx',
    'components/RouteGuard.tsx'
  ];

  for (const file of filesToCheck) {
    try {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Verificar se há verificação de usuário
        const hasUserCheck = content.includes('if (!user)') || content.includes('if (!user?.id)');
        const hasAuthCheck = content.includes('auth.uid()') || content.includes('user_id');
        
        if (hasUserCheck && hasAuthCheck) {
          addResult('passed', `Auth Implementation - ${file}`, 'PASS', 'Verificações de autenticação presentes');
        } else if (hasUserCheck || hasAuthCheck) {
          addResult('medium', `Auth Implementation - ${file}`, 'WARN', 'Verificações parciais de autenticação');
        } else {
          addResult('high', `Auth Implementation - ${file}`, 'FAIL', 'Sem verificações de autenticação adequadas');
        }
        
        // Verificar filtros user_id
        const userIdFilters = (content.match(/\.eq\(['"]user_id['"].*\)/g) || []).length;
        const queries = (content.match(/\.from\(['"]\w+['"]\)/g) || []).length;
        
        if (queries > 0) {
          const ratio = userIdFilters / queries;
          if (ratio >= 0.8) {
            addResult('passed', `User Filter Ratio - ${file}`, 'PASS', `${Math.round(ratio * 100)}% das consultas filtram por user_id`);
          } else if (ratio >= 0.5) {
            addResult('medium', `User Filter Ratio - ${file}`, 'WARN', `${Math.round(ratio * 100)}% das consultas filtram por user_id`);
          } else {
            addResult('high', `User Filter Ratio - ${file}`, 'FAIL', `Apenas ${Math.round(ratio * 100)}% das consultas filtram por user_id`);
          }
        }
      } else {
        addResult('medium', `File Check - ${file}`, 'WARN', 'Arquivo não encontrado');
      }
    } catch (error) {
      addResult('low', `File Analysis - ${file}`, 'ERROR', `Erro ao analisar: ${error.message}`);
    }
  }
}

// 2. Verificar proteção de rotas
function checkRouteProtection() {
  log('\n🛡️ Verificando proteção de rotas...', 'blue');
  
  try {
    const appDir = path.join(process.cwd(), 'app');
    if (!fs.existsSync(appDir)) {
      addResult('critical', 'Route Protection', 'FAIL', 'Diretório app não encontrado');
      return;
    }
    
    const routeFiles = [];
    function findRoutes(dir) {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const fullPath = path.join(dir, file);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory() && !file.startsWith('.')) {
          findRoutes(fullPath);
        } else if (file.endsWith('.tsx') && !file.startsWith('_')) {
          routeFiles.push(fullPath);
        }
      }
    }
    
    findRoutes(appDir);
    
    const publicRoutes = ['index', 'login', 'register', 'welcome'];
    const protectedRoutes = routeFiles.filter(file => {
      const fileName = path.basename(file, '.tsx');
      return !publicRoutes.includes(fileName);
    });
    
    let protectedCount = 0;
    for (const route of protectedRoutes) {
      const content = fs.readFileSync(route, 'utf8');
      if (content.includes('RouteGuard') || content.includes('useAuth')) {
        protectedCount++;
      }
    }
    
    const protectionRatio = protectedRoutes.length > 0 ? protectedCount / protectedRoutes.length : 1;
    
    if (protectionRatio >= 0.9) {
      addResult('passed', 'Route Protection', 'PASS', `${Math.round(protectionRatio * 100)}% das rotas protegidas`);
    } else if (protectionRatio >= 0.7) {
      addResult('medium', 'Route Protection', 'WARN', `${Math.round(protectionRatio * 100)}% das rotas protegidas`);
    } else {
      addResult('high', 'Route Protection', 'FAIL', `Apenas ${Math.round(protectionRatio * 100)}% das rotas protegidas`);
    }
    
  } catch (error) {
    addResult('high', 'Route Protection Check', 'ERROR', `Erro: ${error.message}`);
  }
}

// 3. Verificar configurações de segurança
function checkSecurityConfig() {
  log('\n⚙️ Verificando configurações de segurança...', 'blue');
  
  // Verificar se há chaves expostas
  const configFiles = ['app.config.js', 'config/env.ts', 'lib/supabase.ts'];
  
  for (const file of configFiles) {
    try {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Verificar se há service keys expostas
        if (content.includes('service_role') || content.includes('SERVICE_KEY')) {
          addResult('critical', `Security Config - ${file}`, 'FAIL', 'Service key detectada no código');
        } else {
          addResult('passed', `Security Config - ${file}`, 'PASS', 'Sem chaves de serviço expostas');
        }
        
        // Verificar se usa variáveis de ambiente
        if (content.includes('process.env') || content.includes('Constants.expoConfig')) {
          addResult('passed', `Env Variables - ${file}`, 'PASS', 'Usa variáveis de ambiente');
        } else {
          addResult('medium', `Env Variables - ${file}`, 'WARN', 'Pode não estar usando variáveis de ambiente');
        }
      }
    } catch (error) {
      addResult('low', `Config Check - ${file}`, 'ERROR', `Erro: ${error.message}`);
    }
  }
}

// 4. Verificar conectividade de dados
function checkDataConnectivity() {
  log('\n📊 Verificando conectividade de dados...', 'blue');
  
  const storeFiles = [
    'store/studyStore.ts',
    'store/quizStore.ts',
    'store/noteStore.ts',
    'store/timerStore.ts'
  ];
  
  for (const file of storeFiles) {
    try {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Verificar se usa Supabase
        const usesSupabase = content.includes('supabase.from') || content.includes('useSupabase');
        const hasMockData = content.includes('mockData') || content.includes('MOCK') || content.includes('fake');
        
        if (usesSupabase && !hasMockData) {
          addResult('passed', `Data Connectivity - ${file}`, 'PASS', 'Conectado ao Supabase sem dados mock');
        } else if (usesSupabase && hasMockData) {
          addResult('medium', `Data Connectivity - ${file}`, 'WARN', 'Conectado ao Supabase mas com dados mock');
        } else if (!usesSupabase && hasMockData) {
          addResult('high', `Data Connectivity - ${file}`, 'FAIL', 'Usando apenas dados mock');
        } else {
          addResult('medium', `Data Connectivity - ${file}`, 'WARN', 'Conectividade não clara');
        }
        
        // Verificar operações CRUD
        const hasCreate = content.includes('.insert') || content.includes('create');
        const hasRead = content.includes('.select') || content.includes('fetch') || content.includes('get');
        const hasUpdate = content.includes('.update') || content.includes('edit');
        const hasDelete = content.includes('.delete') || content.includes('remove');
        
        const crudCount = [hasCreate, hasRead, hasUpdate, hasDelete].filter(Boolean).length;
        
        if (crudCount >= 3) {
          addResult('passed', `CRUD Operations - ${file}`, 'PASS', `${crudCount}/4 operações CRUD implementadas`);
        } else if (crudCount >= 2) {
          addResult('medium', `CRUD Operations - ${file}`, 'WARN', `${crudCount}/4 operações CRUD implementadas`);
        } else {
          addResult('high', `CRUD Operations - ${file}`, 'FAIL', `Apenas ${crudCount}/4 operações CRUD implementadas`);
        }
      } else {
        addResult('low', `Store Check - ${file}`, 'WARN', 'Arquivo não encontrado');
      }
    } catch (error) {
      addResult('low', `Store Analysis - ${file}`, 'ERROR', `Erro: ${error.message}`);
    }
  }
}

// 5. Gerar relatório final
function generateFinalReport() {
  log('\n📋 RELATÓRIO FINAL DE AUDITORIA DE SEGURANÇA', 'magenta');
  log('=' * 60, 'magenta');
  
  const totalIssues = auditResults.critical.length + auditResults.high.length + 
                     auditResults.medium.length + auditResults.low.length;
  const totalTests = totalIssues + auditResults.passed.length;
  
  log(`\n📊 Resumo Executivo:`, 'cyan');
  log(`  Total de verificações: ${totalTests}`);
  log(`  ✅ Aprovadas: ${auditResults.passed.length}`, 'green');
  log(`  🚨 Críticas: ${auditResults.critical.length}`, auditResults.critical.length > 0 ? 'red' : 'green');
  log(`  ⚠️  Altas: ${auditResults.high.length}`, auditResults.high.length > 0 ? 'red' : 'green');
  log(`  ⚡ Médias: ${auditResults.medium.length}`, auditResults.medium.length > 0 ? 'yellow' : 'green');
  log(`  💡 Baixas: ${auditResults.low.length}`, auditResults.low.length > 0 ? 'yellow' : 'green');
  
  // Mostrar problemas por categoria
  const categories = [
    { name: 'CRÍTICOS', items: auditResults.critical, color: 'red' },
    { name: 'ALTOS', items: auditResults.high, color: 'red' },
    { name: 'MÉDIOS', items: auditResults.medium, color: 'yellow' },
    { name: 'BAIXOS', items: auditResults.low, color: 'yellow' }
  ];
  
  categories.forEach(category => {
    if (category.items.length > 0) {
      log(`\n🔍 Problemas ${category.name}:`, category.color);
      category.items.forEach(item => {
        log(`  • ${item.test}: ${item.details}`, 'white');
      });
    }
  });
  
  // Recomendações
  log('\n💡 RECOMENDAÇÕES:', 'cyan');
  
  if (auditResults.critical.length > 0) {
    log('  🚨 AÇÃO IMEDIATA NECESSÁRIA:', 'red');
    log('     - Corrigir todos os problemas críticos antes do lançamento', 'red');
    log('     - Implementar testes de segurança automatizados', 'red');
  }
  
  if (auditResults.high.length > 0) {
    log('  ⚠️  ALTA PRIORIDADE:', 'yellow');
    log('     - Revisar e corrigir problemas de alta prioridade', 'yellow');
    log('     - Implementar monitoramento de segurança', 'yellow');
  }
  
  if (auditResults.medium.length > 0) {
    log('  📋 MELHORIAS RECOMENDADAS:', 'cyan');
    log('     - Implementar melhorias de segurança sugeridas', 'cyan');
    log('     - Revisar políticas de acesso regularmente', 'cyan');
  }
  
  // Status final
  log('\n🎯 VEREDICTO FINAL:', 'magenta');
  
  if (auditResults.critical.length > 0) {
    log('❌ NÃO PRONTO PARA PRODUÇÃO', 'red');
    log('   Problemas críticos de segurança detectados!', 'red');
    log('   Requer correções imediatas antes do lançamento.', 'red');
  } else if (auditResults.high.length > 3) {
    log('⚠️  REQUER CORREÇÕES SIGNIFICATIVAS', 'yellow');
    log('   Muitos problemas de alta prioridade encontrados.', 'yellow');
    log('   Recomenda-se corrigir antes do lançamento.', 'yellow');
  } else if (auditResults.high.length > 0 || auditResults.medium.length > 5) {
    log('✅ PRONTO COM RESSALVAS', 'yellow');
    log('   Alguns problemas encontrados, mas não críticos.', 'yellow');
    log('   Pode ser lançado com monitoramento adicional.', 'yellow');
  } else {
    log('✅ PRONTO PARA PRODUÇÃO', 'green');
    log('   Todas as verificações de segurança passaram!', 'green');
    log('   Sistema seguro para lançamento.', 'green');
  }
  
  // Salvar relatório detalhado
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalTests,
      passed: auditResults.passed.length,
      critical: auditResults.critical.length,
      high: auditResults.high.length,
      medium: auditResults.medium.length,
      low: auditResults.low.length
    },
    status: auditResults.critical.length === 0 ? 
            (auditResults.high.length <= 3 ? 'READY' : 'NEEDS_FIXES') : 'NOT_READY',
    results: auditResults,
    recommendations: generateRecommendations()
  };
  
  fs.writeFileSync('final-security-audit-report.json', JSON.stringify(reportData, null, 2));
  log('\n📄 Relatório completo salvo em: final-security-audit-report.json', 'cyan');
  
  return reportData.status;
}

function generateRecommendations() {
  const recommendations = [];
  
  if (auditResults.critical.length > 0) {
    recommendations.push('Implementar políticas RLS mais restritivas');
    recommendations.push('Revisar todas as consultas ao banco de dados');
    recommendations.push('Implementar testes de segurança automatizados');
  }
  
  if (auditResults.high.length > 0) {
    recommendations.push('Adicionar verificações de autenticação em todas as rotas');
    recommendations.push('Implementar logging de segurança');
    recommendations.push('Revisar configurações de CORS e headers de segurança');
  }
  
  if (auditResults.medium.length > 0) {
    recommendations.push('Implementar rate limiting');
    recommendations.push('Adicionar validação de entrada mais rigorosa');
    recommendations.push('Implementar monitoramento de anomalias');
  }
  
  return recommendations;
}

// Executar auditoria completa
async function runCompleteAudit() {
  log('🔍 INICIANDO AUDITORIA COMPLETA DE SEGURANÇA', 'magenta');
  log('=' * 50, 'magenta');
  log('Verificando todos os aspectos de segurança do Lia App...', 'white');
  
  checkAuthImplementations();
  checkRouteProtection();
  checkSecurityConfig();
  checkDataConnectivity();
  
  const status = generateFinalReport();
  
  // Exit code baseado no status
  process.exit(status === 'NOT_READY' ? 1 : 0);
}

if (require.main === module) {
  runCompleteAudit().catch(console.error);
}

module.exports = { runCompleteAudit, auditResults };
