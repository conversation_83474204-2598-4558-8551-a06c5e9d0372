#!/usr/bin/env node

/**
 * Script para verificar se todas as páginas de tipos de estudo estão 
 * adequadamente conectadas ao banco de dados com RLS
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuração do Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente EXPO_PUBLIC_SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Definir os tipos de estudo e suas configurações
const studyTypes = {
  flashcards: {
    tables: ['flashcard_sets', 'flashcards'],
    pages: ['app/(tabs)/flashcards.tsx'],
    stores: ['store/studyStore.ts'],
    description: 'Sistema de flashcards com repetição espaçada'
  },
  notes: {
    tables: ['notes'],
    pages: ['app/(tabs)/notes.tsx'],
    stores: ['store/noteStore.ts'],
    description: 'Sistema de anotações de estudo'
  },
  mindMaps: {
    tables: ['mind_maps'],
    pages: ['app/(tabs)/mind-maps.tsx'],
    stores: ['store/mindMapStore.ts'],
    description: 'Sistema de mapas mentais e fluxogramas'
  },
  quizzes: {
    tables: ['quizzes', 'quiz_questions', 'quiz_attempts'],
    pages: ['app/quizzes.tsx', 'app/quiz/[id].tsx'],
    stores: ['store/quizStore.ts'],
    description: 'Sistema de quizzes e avaliações'
  }
};

async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .single();

    return !error && data;
  } catch {
    return false;
  }
}

async function checkRLSEnabled(tableName) {
  try {
    const { data, error } = await supabase
      .from('pg_tables')
      .select('rowsecurity')
      .eq('schemaname', 'public')
      .eq('tablename', tableName)
      .single();

    return !error && data?.rowsecurity === true;
  } catch {
    return false;
  }
}

async function checkRLSPolicies(tableName) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT COUNT(*) as policy_count 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = '${tableName}';
      `
    });

    return !error && data && data[0]?.policy_count > 0;
  } catch {
    return false;
  }
}

function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  return fs.existsSync(fullPath);
}

function checkStoreConnection(filePath) {
  try {
    const fullPath = path.join(__dirname, '..', filePath);
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Verificar se usa Supabase
    const hasSupabase = content.includes('supabase') || content.includes('@supabase');
    
    // Verificar se tem autenticação
    const hasAuth = content.includes('auth.getUser()') || content.includes('auth.uid()');
    
    // Verificar se tem operações CRUD
    const hasCRUD = content.includes('.select(') || content.includes('.insert(') || 
                   content.includes('.update(') || content.includes('.delete(');
    
    return {
      hasSupabase,
      hasAuth,
      hasCRUD,
      score: (hasSupabase ? 1 : 0) + (hasAuth ? 1 : 0) + (hasCRUD ? 1 : 0)
    };
  } catch {
    return { hasSupabase: false, hasAuth: false, hasCRUD: false, score: 0 };
  }
}

async function verifyStudyType(typeName, config) {
  console.log(`\n📚 Verificando ${typeName.toUpperCase()}: ${config.description}`);
  console.log('=' .repeat(60));

  let totalScore = 0;
  let maxScore = 0;

  // Verificar tabelas
  console.log('\n🗄️ Verificação de Tabelas:');
  for (const table of config.tables) {
    maxScore += 3; // 1 para existir, 1 para RLS, 1 para políticas
    
    const exists = await checkTableExists(table);
    const rlsEnabled = exists ? await checkRLSEnabled(table) : false;
    const hasPolicies = rlsEnabled ? await checkRLSPolicies(table) : false;

    console.log(`  📋 ${table}:`);
    console.log(`    ${exists ? '✅' : '❌'} Tabela existe`);
    console.log(`    ${rlsEnabled ? '✅' : '❌'} RLS habilitado`);
    console.log(`    ${hasPolicies ? '✅' : '❌'} Políticas RLS definidas`);

    totalScore += (exists ? 1 : 0) + (rlsEnabled ? 1 : 0) + (hasPolicies ? 1 : 0);
  }

  // Verificar páginas
  console.log('\n📱 Verificação de Páginas:');
  for (const page of config.pages) {
    maxScore += 1;
    
    const exists = checkFileExists(page);
    console.log(`  📄 ${page}: ${exists ? '✅ Existe' : '❌ Não encontrada'}`);
    
    totalScore += exists ? 1 : 0;
  }

  // Verificar stores
  console.log('\n🏪 Verificação de Stores:');
  for (const store of config.stores) {
    maxScore += 3; // 1 para existir, 1 para Supabase, 1 para auth, 1 para CRUD
    
    const exists = checkFileExists(store);
    if (exists) {
      const connection = checkStoreConnection(store);
      console.log(`  🏬 ${store}:`);
      console.log(`    ✅ Arquivo existe`);
      console.log(`    ${connection.hasSupabase ? '✅' : '❌'} Usa Supabase`);
      console.log(`    ${connection.hasAuth ? '✅' : '❌'} Implementa autenticação`);
      console.log(`    ${connection.hasCRUD ? '✅' : '❌'} Operações CRUD`);
      
      totalScore += 1 + connection.score;
    } else {
      console.log(`  🏬 ${store}: ❌ Não encontrado`);
    }
  }

  // Calcular pontuação
  const percentage = Math.round((totalScore / maxScore) * 100);
  const status = percentage >= 90 ? '🟢 EXCELENTE' : 
                percentage >= 70 ? '🟡 BOM' : 
                percentage >= 50 ? '🟠 REGULAR' : '🔴 CRÍTICO';

  console.log(`\n📊 Pontuação: ${totalScore}/${maxScore} (${percentage}%) - ${status}`);
  
  return { typeName, totalScore, maxScore, percentage, status };
}

async function generateReport(results) {
  console.log('\n' + '='.repeat(80));
  console.log('📋 RELATÓRIO FINAL - CONEXÃO DAS PÁGINAS DE ESTUDO');
  console.log('='.repeat(80));

  let totalScore = 0;
  let totalMaxScore = 0;

  for (const result of results) {
    totalScore += result.totalScore;
    totalMaxScore += result.maxScore;
    
    console.log(`\n${result.status} ${result.typeName.toUpperCase()}: ${result.percentage}%`);
  }

  const overallPercentage = Math.round((totalScore / totalMaxScore) * 100);
  const overallStatus = overallPercentage >= 90 ? '🟢 EXCELENTE' : 
                       overallPercentage >= 70 ? '🟡 BOM' : 
                       overallPercentage >= 50 ? '🟠 REGULAR' : '🔴 CRÍTICO';

  console.log('\n' + '-'.repeat(80));
  console.log(`📊 PONTUAÇÃO GERAL: ${totalScore}/${totalMaxScore} (${overallPercentage}%) - ${overallStatus}`);

  // Recomendações
  console.log('\n💡 RECOMENDAÇÕES:');
  
  if (overallPercentage < 100) {
    console.log('• Execute o script apply_missing_rls_policies.js para corrigir políticas RLS');
    console.log('• Verifique se todas as tabelas necessárias existem no banco de dados');
    console.log('• Confirme se os stores estão implementando autenticação adequada');
  } else {
    console.log('• ✅ Todas as páginas estão adequadamente conectadas!');
    console.log('• ✅ RLS está configurado corretamente');
    console.log('• ✅ Isolamento de dados por usuário está funcionando');
  }

  console.log('\n🔒 SEGURANÇA:');
  console.log('• Cada usuário só pode acessar seus próprios dados');
  console.log('• Row Level Security (RLS) está ativo em todas as tabelas');
  console.log('• Autenticação é obrigatória para todas as operações');
}

async function main() {
  console.log('🚀 VERIFICAÇÃO DE CONEXÃO DAS PÁGINAS DE ESTUDO');
  console.log('Verificando se flashcards, notas, mapas mentais e quizzes estão conectados ao banco');

  const results = [];

  for (const [typeName, config] of Object.entries(studyTypes)) {
    const result = await verifyStudyType(typeName, config);
    results.push(result);
  }

  await generateReport(results);
  
  console.log('\n✨ Verificação concluída!');
}

main().catch(console.error);
