#!/usr/bin/env node

/**
 * Script de Auditoria de Segurança para o Lia App
 * Verifica políticas RLS, consultas com filtros de usuário e conectividade
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuração do Supabase
const SUPABASE_URL = 'https://wyjpmzfijtufgxgdivgl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5anBtemZpanR1Zmd4Z2RpdmdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NDY3MTcsImV4cCI6MjA2MDIyMjcxN30.wYZyPO_3q6i9EnSRm2QYeZOMVH0X-pUuBj2pQ7lzmq4';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Cores para output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

// Resultados da auditoria
const auditResults = {
  critical: [],
  high: [],
  medium: [],
  low: [],
  passed: []
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function addResult(level, test, status, details) {
  const result = { test, status, details, timestamp: new Date().toISOString() };
  auditResults[level].push(result);
  
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  log(`[${level.toUpperCase()}] ${test}: ${status}`, statusColor);
  if (details) log(`  └─ ${details}`, 'cyan');
}

// 1. Verificar RLS em todas as tabelas
async function checkRLSPolicies() {
  log('\n🔒 Verificando políticas RLS...', 'blue');
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: "SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;"
    });

    if (error) {
      // Fallback: usar query direta
      const { data: tables, error: tablesError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');

      if (tablesError) {
        addResult('critical', 'RLS Status Check', 'FAIL', `Erro ao verificar tabelas: ${tablesError.message}`);
        return;
      }

      // Verificar algumas tabelas críticas manualmente
      const criticalTables = ['users', 'flashcards', 'quizzes', 'notes', 'study_groups'];
      for (const table of criticalTables) {
        try {
          // Tentar acessar sem autenticação
          const { data, error } = await supabase.from(table).select('*').limit(1);
          if (!error) {
            addResult('critical', `RLS Check - ${table}`, 'FAIL', 'Tabela acessível sem autenticação');
          } else if (error.message.includes('RLS')) {
            addResult('passed', `RLS Check - ${table}`, 'PASS', 'RLS ativo');
          }
        } catch (e) {
          addResult('medium', `RLS Check - ${table}`, 'WARN', `Erro ao testar: ${e.message}`);
        }
      }
    } else {
      const tablesWithoutRLS = data.filter(row => !row.rowsecurity);
      if (tablesWithoutRLS.length > 0) {
        addResult('critical', 'RLS Status', 'FAIL', 
          `Tabelas sem RLS: ${tablesWithoutRLS.map(t => t.tablename).join(', ')}`);
      } else {
        addResult('passed', 'RLS Status', 'PASS', 'Todas as tabelas têm RLS habilitado');
      }
    }
  } catch (error) {
    addResult('high', 'RLS Status Check', 'FAIL', `Erro na verificação: ${error.message}`);
  }
}

// 2. Verificar consultas no código
async function checkCodeQueries() {
  log('\n🔍 Analisando consultas no código...', 'blue');
  
  const problematicPatterns = [
    { pattern: /\.from\(['"]\w+['"]\)\.select\([^)]+\)(?!.*\.eq\(['"]user_id['"])/g, 
      severity: 'high', 
      description: 'Consulta sem filtro user_id' },
    { pattern: /supabase\.from\([^)]+\)\.select\([^)]+\)\.eq\(['"]id['"].*\)(?!.*user_id)/g, 
      severity: 'medium', 
      description: 'Consulta por ID sem verificação de usuário' },
    { pattern: /\.delete\(\)\.eq\(['"]id['"].*\)(?!.*user_id)/g, 
      severity: 'high', 
      description: 'Exclusão sem verificação de usuário' },
    { pattern: /SUPABASE_SERVICE_KEY|service_role/g, 
      severity: 'critical', 
      description: 'Uso de chave de serviço no código cliente' }
  ];

  const filesToCheck = [
    'store/studyStore.ts',
    'store/quizStore.ts', 
    'store/noteStore.ts',
    'hooks/useSupabase.ts',
    'services/supabaseCache.ts'
  ];

  for (const file of filesToCheck) {
    try {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        for (const { pattern, severity, description } of problematicPatterns) {
          const matches = content.match(pattern);
          if (matches) {
            addResult(severity, `Code Analysis - ${file}`, 'FAIL', 
              `${description}: ${matches.length} ocorrências`);
          }
        }
        
        // Verificar se há filtros user_id adequados
        const userIdFilters = content.match(/\.eq\(['"]user_id['"].*\)/g);
        const queries = content.match(/\.from\(['"]\w+['"]\)\.select/g);
        
        if (queries && queries.length > 0) {
          const ratio = userIdFilters ? userIdFilters.length / queries.length : 0;
          if (ratio < 0.8) {
            addResult('medium', `User Filter Ratio - ${file}`, 'WARN', 
              `${Math.round(ratio * 100)}% das consultas têm filtro user_id`);
          } else {
            addResult('passed', `User Filter Ratio - ${file}`, 'PASS', 
              `${Math.round(ratio * 100)}% das consultas têm filtro user_id`);
          }
        }
      }
    } catch (error) {
      addResult('low', `Code Analysis - ${file}`, 'WARN', `Erro ao analisar: ${error.message}`);
    }
  }
}

// 3. Testar conectividade e dados dinâmicos
async function checkDataConnectivity() {
  log('\n📊 Verificando conectividade de dados...', 'blue');
  
  const tables = ['users', 'flashcard_sets', 'quizzes', 'notes', 'study_groups'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('id')
        .limit(1);
        
      if (error) {
        if (error.message.includes('RLS') || error.message.includes('permission')) {
          addResult('passed', `Data Connectivity - ${table}`, 'PASS', 'RLS bloqueando acesso não autorizado');
        } else {
          addResult('high', `Data Connectivity - ${table}`, 'FAIL', `Erro: ${error.message}`);
        }
      } else {
        addResult('critical', `Data Connectivity - ${table}`, 'FAIL', 'Dados acessíveis sem autenticação');
      }
    } catch (error) {
      addResult('medium', `Data Connectivity - ${table}`, 'WARN', `Erro na conexão: ${error.message}`);
    }
  }
}

// 4. Verificar configurações de autenticação
async function checkAuthConfig() {
  log('\n🔐 Verificando configurações de autenticação...', 'blue');
  
  try {
    // Verificar se conseguimos acessar dados sem autenticação
    const { data: session } = await supabase.auth.getSession();
    
    if (!session.session) {
      addResult('passed', 'Auth Session', 'PASS', 'Nenhuma sessão ativa (esperado)');
    } else {
      addResult('medium', 'Auth Session', 'WARN', 'Sessão ativa encontrada');
    }
    
    // Verificar configurações do cliente
    const config = supabase.supabaseUrl && supabase.supabaseKey;
    if (config) {
      addResult('passed', 'Auth Config', 'PASS', 'Cliente Supabase configurado');
    } else {
      addResult('critical', 'Auth Config', 'FAIL', 'Cliente Supabase não configurado');
    }
    
  } catch (error) {
    addResult('high', 'Auth Config Check', 'FAIL', `Erro: ${error.message}`);
  }
}

// 5. Gerar relatório final
function generateReport() {
  log('\n📋 RELATÓRIO DE AUDITORIA DE SEGURANÇA', 'magenta');
  log('=' * 50, 'magenta');
  
  const totalIssues = auditResults.critical.length + auditResults.high.length + 
                     auditResults.medium.length + auditResults.low.length;
  const totalTests = totalIssues + auditResults.passed.length;
  
  log(`\n📊 Resumo:`, 'cyan');
  log(`  Total de testes: ${totalTests}`);
  log(`  Aprovados: ${auditResults.passed.length}`, 'green');
  log(`  Críticos: ${auditResults.critical.length}`, 'red');
  log(`  Altos: ${auditResults.high.length}`, 'red');
  log(`  Médios: ${auditResults.medium.length}`, 'yellow');
  log(`  Baixos: ${auditResults.low.length}`, 'yellow');
  
  // Mostrar problemas por prioridade
  ['critical', 'high', 'medium', 'low'].forEach(level => {
    if (auditResults[level].length > 0) {
      log(`\n🚨 Problemas ${level.toUpperCase()}:`, 'red');
      auditResults[level].forEach(result => {
        log(`  • ${result.test}: ${result.details}`, 'white');
      });
    }
  });
  
  // Status final
  log('\n🎯 STATUS FINAL:', 'magenta');
  if (auditResults.critical.length > 0) {
    log('❌ NÃO PRONTO PARA PRODUÇÃO', 'red');
    log('   Problemas críticos de segurança encontrados!', 'red');
  } else if (auditResults.high.length > 0) {
    log('⚠️  REQUER CORREÇÕES', 'yellow');
    log('   Problemas de alta prioridade encontrados.', 'yellow');
  } else if (auditResults.medium.length > 0) {
    log('✅ PRONTO COM RESSALVAS', 'yellow');
    log('   Alguns problemas menores encontrados.', 'yellow');
  } else {
    log('✅ PRONTO PARA PRODUÇÃO', 'green');
    log('   Todos os testes de segurança passaram!', 'green');
  }
  
  // Salvar relatório
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalTests,
      passed: auditResults.passed.length,
      critical: auditResults.critical.length,
      high: auditResults.high.length,
      medium: auditResults.medium.length,
      low: auditResults.low.length
    },
    results: auditResults
  };
  
  fs.writeFileSync('security-audit-report.json', JSON.stringify(reportData, null, 2));
  log('\n📄 Relatório salvo em: security-audit-report.json', 'cyan');
}

// Executar auditoria
async function runAudit() {
  log('🔍 INICIANDO AUDITORIA DE SEGURANÇA DO LIA APP', 'magenta');
  log('=' * 50, 'magenta');
  
  await checkRLSPolicies();
  await checkCodeQueries();
  await checkDataConnectivity();
  await checkAuthConfig();
  
  generateReport();
}

// Executar se chamado diretamente
if (require.main === module) {
  runAudit().catch(console.error);
}

module.exports = { runAudit, auditResults };
