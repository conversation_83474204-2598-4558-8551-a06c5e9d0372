/**
 * Script para migrar componentes que usam useSupabase para useSupabaseOptimized
 * 
 * Este script busca todos os arquivos que importam e usam o hook useSupabase
 * e os migra para usar o hook useSupabaseOptimized, que é otimizado para alta escala.
 * 
 * Uso:
 * node scripts/migrate_to_optimized_hook.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Diretórios a serem ignorados
const IGNORED_DIRS = [
  'node_modules',
  '.git',
  'build',
  'dist',
  '.expo',
  '.expo-shared',
  'scripts',
];

// Extensões de arquivo a serem verificadas
const FILE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx'];

// Função para buscar arquivos recursivamente
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !IGNORED_DIRS.includes(file)) {
      findFiles(filePath, fileList);
    } else if (stat.isFile() && FILE_EXTENSIONS.includes(path.extname(file))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Função para verificar se um arquivo importa useSupabase
function importsUseSupabase(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('import { useSupabase }') || 
         content.includes('import {useSupabase}') ||
         content.match(/import.*useSupabase.*from/);
}

// Função para verificar se um arquivo usa useSupabase
function usesUseSupabase(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.match(/const\s+\{[^}]*\}\s+=\s+useSupabase\(/);
}

// Função para migrar um arquivo
function migrateFile(filePath) {
  console.log(`Migrando arquivo: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');

  // Substituir a importação
  content = content.replace(
    /import\s+\{\s*useSupabase\s*\}\s+from\s+['"]@\/hooks\/useSupabase['"]/g,
    "import { useSupabaseOptimized } from '@/hooks/useSupabaseOptimized'"
  );

  // Substituir o uso do hook
  content = content.replace(
    /const\s+\{([^}]*)\}\s+=\s+useSupabase\(([^)]*)\)/g,
    'const {$1} = useSupabaseOptimized($2)'
  );

  // Salvar o arquivo
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✅ Arquivo migrado com sucesso: ${filePath}`);
}

// Função principal
function main() {
  console.log('Iniciando migração de useSupabase para useSupabaseOptimized...');

  // Obter o diretório raiz do projeto
  const rootDir = process.cwd();
  console.log(`Diretório raiz: ${rootDir}`);

  // Buscar todos os arquivos
  const files = findFiles(rootDir);
  console.log(`Encontrados ${files.length} arquivos para verificar.`);

  // Filtrar arquivos que importam useSupabase
  const filesWithImport = files.filter(importsUseSupabase);
  console.log(`Encontrados ${filesWithImport.length} arquivos que importam useSupabase.`);

  // Filtrar arquivos que usam useSupabase
  const filesToMigrate = filesWithImport.filter(usesUseSupabase);
  console.log(`Encontrados ${filesToMigrate.length} arquivos que usam useSupabase e precisam ser migrados.`);

  // Migrar cada arquivo
  filesToMigrate.forEach(migrateFile);

  console.log('Migração concluída!');
  console.log(`Total de arquivos migrados: ${filesToMigrate.length}`);
}

// Executar a função principal
main();
