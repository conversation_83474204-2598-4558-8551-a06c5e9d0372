#!/usr/bin/env node

/**
 * Teste direto de RLS usando apenas REST API com chave anônima
 */

const https = require('https');

const SUPABASE_URL = 'https://wyjpmzfijtufgxgdivgl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5anBtemZpanR1Zmd4Z2RpdmdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NDY3MTcsImV4cCI6MjA2MDIyMjcxN30.wYZyPO_3q6i9EnSRm2QYeZOMVH0X-pUuBj2pQ7lzmq4';

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const defaultHeaders = {
      'apikey': SUPABASE_ANON_KEY,
      'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=minimal',
      ...headers
    };

    const options = {
      hostname: 'wyjpmzfijtufgxgdivgl.supabase.co',
      port: 443,
      path: `/rest/v1/${path}`,
      method: method,
      headers: defaultHeaders
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testTableAccess(tableName) {
  try {
    log(`\nTestando acesso à tabela: ${tableName}`, 'cyan');
    
    const result = await makeRequest(`${tableName}?select=*&limit=1`);
    
    if (result.statusCode === 200) {
      const recordCount = Array.isArray(result.body) ? result.body.length : 0;
      if (recordCount === 0) {
        log(`  ⚠️  Consulta permitida mas sem dados (${recordCount} registros)`, 'yellow');
        return { table: tableName, status: 'EMPTY_ALLOWED', records: recordCount };
      } else {
        log(`  ❌ VULNERABILIDADE: ${recordCount} registros acessíveis!`, 'red');
        return { table: tableName, status: 'VULNERABLE', records: recordCount };
      }
    } else if (result.statusCode === 401) {
      log(`  ✅ Acesso negado (401): ${result.body?.message || 'Unauthorized'}`, 'green');
      return { table: tableName, status: 'SECURE', message: result.body?.message };
    } else if (result.statusCode === 403) {
      log(`  ✅ Acesso proibido (403): ${result.body?.message || 'Forbidden'}`, 'green');
      return { table: tableName, status: 'SECURE', message: result.body?.message };
    } else {
      log(`  ⚠️  Status inesperado (${result.statusCode}): ${result.body?.message || result.body}`, 'yellow');
      return { table: tableName, status: 'UNKNOWN', statusCode: result.statusCode, message: result.body?.message };
    }
  } catch (error) {
    log(`  ❌ Erro na requisição: ${error.message}`, 'red');
    return { table: tableName, status: 'ERROR', error: error.message };
  }
}

async function testInsertAccess(tableName, testData) {
  try {
    log(`\nTestando inserção em: ${tableName}`, 'cyan');
    
    const result = await makeRequest(tableName, 'POST', testData, {
      'Prefer': 'return=representation'
    });
    
    if (result.statusCode === 201) {
      log(`  ❌ VULNERABILIDADE: Inserção permitida!`, 'red');
      
      // Tentar limpar o dado inserido
      if (result.body && result.body[0] && result.body[0].id) {
        await makeRequest(`${tableName}?id=eq.${result.body[0].id}`, 'DELETE');
      }
      
      return { table: tableName, operation: 'INSERT', status: 'VULNERABLE' };
    } else if (result.statusCode === 401 || result.statusCode === 403) {
      log(`  ✅ Inserção bloqueada (${result.statusCode}): ${result.body?.message || 'Forbidden'}`, 'green');
      return { table: tableName, operation: 'INSERT', status: 'SECURE', message: result.body?.message };
    } else {
      log(`  ⚠️  Status inesperado (${result.statusCode}): ${result.body?.message || result.body}`, 'yellow');
      return { table: tableName, operation: 'INSERT', status: 'UNKNOWN', statusCode: result.statusCode };
    }
  } catch (error) {
    log(`  ❌ Erro na requisição: ${error.message}`, 'red');
    return { table: tableName, operation: 'INSERT', status: 'ERROR', error: error.message };
  }
}

async function main() {
  log('🔍 TESTE DIRETO DE RLS - LIA APP', 'magenta');
  log('=' * 40, 'magenta');
  log('Testando via REST API com chave anônima...', 'white');
  
  const results = [];
  
  // Teste 1: Acesso de leitura
  log('\n📖 TESTANDO ACESSO DE LEITURA', 'blue');
  const tables = ['users', 'flashcard_sets', 'quizzes', 'notes', 'study_groups', 'flashcards'];
  
  for (const table of tables) {
    const result = await testTableAccess(table);
    results.push(result);
  }
  
  // Teste 2: Acesso de escrita
  log('\n📝 TESTANDO ACESSO DE ESCRITA', 'blue');
  const insertTests = [
    { table: 'users', data: { name: 'Test User', email: '<EMAIL>' } },
    { table: 'notes', data: { title: 'Test Note', content: 'Test content' } },
    { table: 'quizzes', data: { title: 'Test Quiz', description: 'Test description' } }
  ];
  
  for (const test of insertTests) {
    const result = await testInsertAccess(test.table, test.data);
    results.push(result);
  }
  
  // Análise dos resultados
  log('\n📋 ANÁLISE DOS RESULTADOS', 'magenta');
  log('=' * 30, 'magenta');
  
  const vulnerable = results.filter(r => r.status === 'VULNERABLE');
  const secure = results.filter(r => r.status === 'SECURE');
  const emptyAllowed = results.filter(r => r.status === 'EMPTY_ALLOWED');
  const errors = results.filter(r => r.status === 'ERROR' || r.status === 'UNKNOWN');
  
  log(`\n📊 Resumo:`, 'cyan');
  log(`  Total de testes: ${results.length}`);
  log(`  Seguros: ${secure.length}`, 'green');
  log(`  Vulneráveis: ${vulnerable.length}`, vulnerable.length > 0 ? 'red' : 'green');
  log(`  Consultas vazias permitidas: ${emptyAllowed.length}`, emptyAllowed.length > 0 ? 'yellow' : 'green');
  log(`  Erros: ${errors.length}`, errors.length > 0 ? 'yellow' : 'green');
  
  if (vulnerable.length > 0) {
    log(`\n🚨 VULNERABILIDADES CRÍTICAS:`, 'red');
    vulnerable.forEach(vuln => {
      log(`  • ${vuln.table} (${vuln.operation || 'SELECT'}): ${vuln.records || 'Acesso permitido'}`, 'red');
    });
  }
  
  if (emptyAllowed.length > 0) {
    log(`\n⚠️  CONSULTAS VAZIAS PERMITIDAS (POSSÍVEL PROBLEMA):`, 'yellow');
    emptyAllowed.forEach(empty => {
      log(`  • ${empty.table}: Consulta permitida mas retornou ${empty.records} registros`, 'yellow');
    });
    log(`\n   NOTA: Isso pode indicar que o RLS não está funcionando corretamente.`, 'yellow');
    log(`   Uma consulta deveria ser REJEITADA, não retornar vazio.`, 'yellow');
  }
  
  // Status final
  log('\n🎯 STATUS FINAL:', 'magenta');
  if (vulnerable.length > 0) {
    log('❌ VULNERABILIDADES CRÍTICAS ENCONTRADAS', 'red');
    log('   NÃO PRONTO PARA PRODUÇÃO', 'red');
  } else if (emptyAllowed.length > 0) {
    log('⚠️  RLS POSSIVELMENTE NÃO FUNCIONANDO', 'yellow');
    log('   Consultas deveriam ser rejeitadas, não retornar vazio', 'yellow');
    log('   REQUER INVESTIGAÇÃO ANTES DA PRODUÇÃO', 'yellow');
  } else {
    log('✅ RLS FUNCIONANDO CORRETAMENTE', 'green');
    log('   PRONTO PARA PRODUÇÃO (aspecto RLS)', 'green');
  }
  
  // Salvar relatório
  const fs = require('fs');
  fs.writeFileSync('rls-direct-test-report.json', JSON.stringify({
    timestamp: new Date().toISOString(),
    summary: {
      total: results.length,
      secure: secure.length,
      vulnerable: vulnerable.length,
      emptyAllowed: emptyAllowed.length,
      errors: errors.length
    },
    results: results
  }, null, 2));
  
  log('\n📄 Relatório salvo em: rls-direct-test-report.json', 'cyan');
  
  // Exit code
  process.exit(vulnerable.length > 0 || emptyAllowed.length > 0 ? 1 : 0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
