#!/usr/bin/env node

/**
 * Teste específico para verificar se as políticas RLS estão funcionando
 * usando apenas a chave anônima (não a service key)
 */

const { createClient } = require('@supabase/supabase-js');

// Usar apenas a chave anônima para simular um cliente real
const SUPABASE_URL = 'https://wyjpmzfijtufgxgdivgl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5anBtemZpanR1Zmd4Z2RpdmdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NDY3MTcsImV4cCI6MjA2MDIyMjcxN30.wYZyPO_3q6i9EnSRm2QYeZOMVH0X-pUuBj2pQ7lzmq4';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testRLSWithoutAuth() {
  log('\n🔒 Testando RLS sem autenticação (usando chave anônima)...', 'blue');
  
  const tables = ['users', 'flashcard_sets', 'quizzes', 'notes', 'study_groups', 'flashcards'];
  const results = [];
  
  for (const table of tables) {
    try {
      log(`\nTestando tabela: ${table}`, 'cyan');
      
      // Tentar acessar dados sem autenticação
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        if (error.message.includes('RLS') || 
            error.message.includes('permission') || 
            error.message.includes('policy') ||
            error.code === 'PGRST116') {
          log(`  ✅ RLS funcionando: ${error.message}`, 'green');
          results.push({ table, status: 'SECURE', message: error.message });
        } else {
          log(`  ⚠️  Erro inesperado: ${error.message}`, 'yellow');
          results.push({ table, status: 'ERROR', message: error.message });
        }
      } else {
        log(`  ❌ VULNERABILIDADE: Dados acessíveis sem autenticação!`, 'red');
        log(`  └─ Retornou ${data ? data.length : 0} registros`, 'red');
        results.push({ table, status: 'VULNERABLE', message: `${data ? data.length : 0} registros acessíveis` });
      }
    } catch (error) {
      log(`  ⚠️  Erro na conexão: ${error.message}`, 'yellow');
      results.push({ table, status: 'CONNECTION_ERROR', message: error.message });
    }
  }
  
  return results;
}

async function testRLSWithFakeAuth() {
  log('\n🔐 Testando RLS com token falso...', 'blue');
  
  // Criar um cliente com token JWT falso
  const fakeToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.fake';
  
  const fakeClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
    global: {
      headers: {
        Authorization: `Bearer ${fakeToken}`
      }
    }
  });
  
  const results = [];
  const tables = ['users', 'flashcard_sets', 'quizzes'];
  
  for (const table of tables) {
    try {
      log(`\nTestando ${table} com token falso...`, 'cyan');
      
      const { data, error } = await fakeClient
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        log(`  ✅ Token falso rejeitado: ${error.message}`, 'green');
        results.push({ table, status: 'SECURE', message: 'Token falso rejeitado' });
      } else {
        log(`  ❌ VULNERABILIDADE: Token falso aceito!`, 'red');
        results.push({ table, status: 'VULNERABLE', message: 'Token falso aceito' });
      }
    } catch (error) {
      log(`  ✅ Erro esperado: ${error.message}`, 'green');
      results.push({ table, status: 'SECURE', message: 'Erro de autenticação' });
    }
  }
  
  return results;
}

async function testInsertWithoutAuth() {
  log('\n📝 Testando inserção sem autenticação...', 'blue');
  
  const results = [];
  const testData = {
    users: { name: 'Test User', email: '<EMAIL>' },
    notes: { title: 'Test Note', content: 'Test content' },
    quizzes: { title: 'Test Quiz', description: 'Test description' }
  };
  
  for (const [table, data] of Object.entries(testData)) {
    try {
      log(`\nTestando inserção em ${table}...`, 'cyan');
      
      const { data: result, error } = await supabase
        .from(table)
        .insert([data])
        .select();
      
      if (error) {
        if (error.message.includes('RLS') || 
            error.message.includes('permission') || 
            error.message.includes('policy')) {
          log(`  ✅ Inserção bloqueada pelo RLS`, 'green');
          results.push({ table, operation: 'INSERT', status: 'SECURE' });
        } else {
          log(`  ⚠️  Erro inesperado: ${error.message}`, 'yellow');
          results.push({ table, operation: 'INSERT', status: 'ERROR', message: error.message });
        }
      } else {
        log(`  ❌ VULNERABILIDADE: Inserção permitida sem autenticação!`, 'red');
        results.push({ table, operation: 'INSERT', status: 'VULNERABLE' });
        
        // Tentar limpar o dado inserido
        if (result && result[0]) {
          await supabase.from(table).delete().eq('id', result[0].id);
        }
      }
    } catch (error) {
      log(`  ✅ Erro esperado: ${error.message}`, 'green');
      results.push({ table, operation: 'INSERT', status: 'SECURE' });
    }
  }
  
  return results;
}

async function generateSecurityReport(selectResults, fakeAuthResults, insertResults) {
  log('\n📋 RELATÓRIO DE SEGURANÇA RLS', 'magenta');
  log('=' * 50, 'magenta');
  
  const allResults = [...selectResults, ...fakeAuthResults, ...insertResults];
  const vulnerabilities = allResults.filter(r => r.status === 'VULNERABLE');
  const secure = allResults.filter(r => r.status === 'SECURE');
  const errors = allResults.filter(r => r.status === 'ERROR' || r.status === 'CONNECTION_ERROR');
  
  log(`\n📊 Resumo dos Testes:`, 'cyan');
  log(`  Total de testes: ${allResults.length}`);
  log(`  Seguros: ${secure.length}`, 'green');
  log(`  Vulnerabilidades: ${vulnerabilities.length}`, vulnerabilities.length > 0 ? 'red' : 'green');
  log(`  Erros: ${errors.length}`, errors.length > 0 ? 'yellow' : 'green');
  
  if (vulnerabilities.length > 0) {
    log(`\n🚨 VULNERABILIDADES ENCONTRADAS:`, 'red');
    vulnerabilities.forEach(vuln => {
      log(`  • ${vuln.table || 'N/A'} (${vuln.operation || 'SELECT'}): ${vuln.message || 'Acesso não autorizado'}`, 'red');
    });
  }
  
  if (errors.length > 0) {
    log(`\n⚠️  ERROS ENCONTRADOS:`, 'yellow');
    errors.forEach(err => {
      log(`  • ${err.table || 'N/A'}: ${err.message || 'Erro desconhecido'}`, 'yellow');
    });
  }
  
  // Status final
  log('\n🎯 STATUS FINAL:', 'magenta');
  if (vulnerabilities.length > 0) {
    log('❌ FALHA DE SEGURANÇA CRÍTICA', 'red');
    log('   RLS não está funcionando corretamente!', 'red');
    log('   NÃO PRONTO PARA PRODUÇÃO', 'red');
  } else if (errors.length > allResults.length * 0.5) {
    log('⚠️  MUITOS ERROS DE CONEXÃO', 'yellow');
    log('   Verifique a conectividade com o banco.', 'yellow');
  } else {
    log('✅ RLS FUNCIONANDO CORRETAMENTE', 'green');
    log('   Todas as políticas de segurança estão ativas!', 'green');
    log('   PRONTO PARA PRODUÇÃO (aspecto RLS)', 'green');
  }
  
  return {
    total: allResults.length,
    secure: secure.length,
    vulnerabilities: vulnerabilities.length,
    errors: errors.length,
    status: vulnerabilities.length === 0 ? 'SECURE' : 'VULNERABLE'
  };
}

async function main() {
  log('🔍 TESTE DE SEGURANÇA RLS - LIA APP', 'magenta');
  log('=' * 40, 'magenta');
  log('Testando políticas Row Level Security...', 'white');
  
  try {
    // Teste 1: Acesso sem autenticação
    const selectResults = await testRLSWithoutAuth();
    
    // Teste 2: Token falso
    const fakeAuthResults = await testRLSWithFakeAuth();
    
    // Teste 3: Inserção sem autenticação
    const insertResults = await testInsertWithoutAuth();
    
    // Gerar relatório
    const report = await generateSecurityReport(selectResults, fakeAuthResults, insertResults);
    
    // Salvar relatório
    const fs = require('fs');
    fs.writeFileSync('rls-security-report.json', JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: report,
      details: {
        selectTests: selectResults,
        fakeAuthTests: fakeAuthResults,
        insertTests: insertResults
      }
    }, null, 2));
    
    log('\n📄 Relatório detalhado salvo em: rls-security-report.json', 'cyan');
    
    // Exit code baseado no resultado
    process.exit(report.vulnerabilities > 0 ? 1 : 0);
    
  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
