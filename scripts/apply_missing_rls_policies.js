#!/usr/bin/env node

/**
 * Script para aplicar as políticas RLS faltantes no banco de dados Supabase
 * Este script garante que todas as tabelas de tipos de estudo tenham RLS adequado
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuração do Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente EXPO_PUBLIC_SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyRLSPolicies() {
  console.log('🔒 Aplicando políticas RLS faltantes...\n');

  try {
    // Ler o arquivo SQL com as políticas
    const sqlFilePath = path.join(__dirname, '..', 'sql', 'optimize_rls_policies.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Dividir o SQL em comandos individuais
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

    console.log(`📝 Executando ${commands.length} comandos SQL...\n`);

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      
      // Pular comentários e comandos vazios
      if (command.startsWith('--') || command.trim() === '') {
        continue;
      }

      try {
        console.log(`⏳ Executando comando ${i + 1}/${commands.length}...`);
        
        const { error } = await supabase.rpc('exec_sql', { 
          sql_query: command + ';' 
        });

        if (error) {
          console.error(`❌ Erro no comando ${i + 1}:`, error.message);
          errorCount++;
        } else {
          console.log(`✅ Comando ${i + 1} executado com sucesso`);
          successCount++;
        }
      } catch (err) {
        console.error(`❌ Erro inesperado no comando ${i + 1}:`, err.message);
        errorCount++;
      }
    }

    console.log('\n📊 Resumo da execução:');
    console.log(`✅ Comandos executados com sucesso: ${successCount}`);
    console.log(`❌ Comandos com erro: ${errorCount}`);

    if (errorCount === 0) {
      console.log('\n🎉 Todas as políticas RLS foram aplicadas com sucesso!');
    } else {
      console.log('\n⚠️ Algumas políticas falharam. Verifique os erros acima.');
    }

  } catch (error) {
    console.error('❌ Erro ao aplicar políticas RLS:', error.message);
    process.exit(1);
  }
}

async function verifyRLSStatus() {
  console.log('\n🔍 Verificando status do RLS nas tabelas...\n');

  const tables = [
    'subjects',
    'flashcard_sets', 
    'flashcards',
    'notes',
    'mind_maps',
    'quizzes',
    'quiz_questions',
    'quiz_attempts',
    'activities',
    'users',
    'study_groups',
    'study_group_members',
    'study_group_materials'
  ];

  try {
    for (const table of tables) {
      const { data, error } = await supabase
        .from('pg_tables')
        .select('tablename, rowsecurity')
        .eq('schemaname', 'public')
        .eq('tablename', table)
        .single();

      if (error) {
        console.log(`❌ ${table}: Erro ao verificar - ${error.message}`);
      } else if (data) {
        const status = data.rowsecurity ? '✅ RLS Habilitado' : '❌ RLS Desabilitado';
        console.log(`${status} - ${table}`);
      } else {
        console.log(`⚠️ ${table}: Tabela não encontrada`);
      }
    }
  } catch (error) {
    console.error('❌ Erro ao verificar status do RLS:', error.message);
  }
}

async function testUserIsolation() {
  console.log('\n🧪 Testando isolamento de dados por usuário...\n');

  try {
    // Tentar acessar dados sem autenticação (deve falhar)
    const testTables = ['notes', 'mind_maps', 'quizzes', 'flashcards'];
    
    for (const table of testTables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error && error.message.includes('RLS')) {
        console.log(`✅ ${table}: RLS funcionando corretamente`);
      } else if (error) {
        console.log(`⚠️ ${table}: Erro inesperado - ${error.message}`);
      } else {
        console.log(`❌ ${table}: RLS pode não estar funcionando (dados acessíveis sem auth)`);
      }
    }
  } catch (error) {
    console.error('❌ Erro ao testar isolamento:', error.message);
  }
}

// Executar o script
async function main() {
  console.log('🚀 Iniciando aplicação de políticas RLS...\n');
  
  await applyRLSPolicies();
  await verifyRLSStatus();
  await testUserIsolation();
  
  console.log('\n✨ Script concluído!');
}

main().catch(console.error);
