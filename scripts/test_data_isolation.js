/**
 * Script para testar o isolamento de dados entre usuários
 * Este script deve ser executado para verificar se as políticas de segurança estão funcionando corretamente
 * 
 * Uso:
 * node scripts/test_data_isolation.js
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const chalk = require('chalk');

// Carregar variáveis de ambiente
dotenv.config();

// Verificar se as variáveis de ambiente necessárias estão definidas
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
  console.error('Erro: As variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_KEY devem estar definidas.');
  console.error('Adicione-as ao arquivo .env ou defina-as antes de executar o script.');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Função para criar um cliente Supabase com a chave de usuário
function createUserClient(token) {
  return createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY,
    {
      global: {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    }
  );
}

// Função para criar usuários de teste
async function createTestUsers() {
  console.log(chalk.blue('Criando usuários de teste...'));
  
  // Criar usuário 1
  const { data: user1, error: error1 } = await supabaseAdmin.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'password123',
    email_confirm: true
  });
  
  if (error1) {
    console.error(chalk.red('Erro ao criar usuário 1:'), error1);
    return null;
  }
  
  // Criar usuário 2
  const { data: user2, error: error2 } = await supabaseAdmin.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'password123',
    email_confirm: true
  });
  
  if (error2) {
    console.error(chalk.red('Erro ao criar usuário 2:'), error2);
    return null;
  }
  
  console.log(chalk.green('Usuários de teste criados com sucesso!'));
  
  return {
    user1: user1.user,
    user2: user2.user
  };
}

// Função para autenticar usuários
async function authenticateUsers(users) {
  console.log(chalk.blue('Autenticando usuários...'));
  
  // Autenticar usuário 1
  const { data: auth1, error: authError1 } = await supabaseAdmin.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (authError1) {
    console.error(chalk.red('Erro ao autenticar usuário 1:'), authError1);
    return null;
  }
  
  // Autenticar usuário 2
  const { data: auth2, error: authError2 } = await supabaseAdmin.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (authError2) {
    console.error(chalk.red('Erro ao autenticar usuário 2:'), authError2);
    return null;
  }
  
  console.log(chalk.green('Usuários autenticados com sucesso!'));
  
  return {
    client1: createUserClient(auth1.session.access_token),
    client2: createUserClient(auth2.session.access_token),
    user1: users.user1,
    user2: users.user2
  };
}

// Função para criar dados de teste
async function createTestData(clients) {
  console.log(chalk.blue('Criando dados de teste...'));
  
  // Criar assunto para usuário 1
  const { data: subject1, error: subjectError1 } = await clients.client1
    .from('subjects')
    .insert({
      title: 'Assunto do Usuário 1',
      description: 'Este assunto pertence ao usuário 1',
      color: '#FF0000',
      user_id: clients.user1.id
    })
    .select()
    .single();
  
  if (subjectError1) {
    console.error(chalk.red('Erro ao criar assunto para usuário 1:'), subjectError1);
    return null;
  }
  
  // Criar assunto para usuário 2
  const { data: subject2, error: subjectError2 } = await clients.client2
    .from('subjects')
    .insert({
      title: 'Assunto do Usuário 2',
      description: 'Este assunto pertence ao usuário 2',
      color: '#00FF00',
      user_id: clients.user2.id
    })
    .select()
    .single();
  
  if (subjectError2) {
    console.error(chalk.red('Erro ao criar assunto para usuário 2:'), subjectError2);
    return null;
  }
  
  // Criar grupo para usuário 1
  const { data: group1, error: groupError1 } = await clients.client1
    .from('study_groups')
    .insert({
      name: 'Grupo do Usuário 1',
      description: 'Este grupo pertence ao usuário 1',
      is_open: false,
      admin_id: clients.user1.id
    })
    .select()
    .single();
  
  if (groupError1) {
    console.error(chalk.red('Erro ao criar grupo para usuário 1:'), groupError1);
    return null;
  }
  
  // Criar grupo para usuário 2
  const { data: group2, error: groupError2 } = await clients.client2
    .from('study_groups')
    .insert({
      name: 'Grupo do Usuário 2',
      description: 'Este grupo pertence ao usuário 2',
      is_open: false,
      admin_id: clients.user2.id
    })
    .select()
    .single();
  
  if (groupError2) {
    console.error(chalk.red('Erro ao criar grupo para usuário 2:'), groupError2);
    return null;
  }
  
  console.log(chalk.green('Dados de teste criados com sucesso!'));
  
  return {
    subject1,
    subject2,
    group1,
    group2
  };
}

// Função para testar o isolamento de dados
async function testDataIsolation(clients, testData) {
  console.log(chalk.blue('Testando isolamento de dados...'));
  
  // Teste 1: Usuário 1 tenta acessar assunto do usuário 2
  console.log(chalk.yellow('Teste 1: Usuário 1 tenta acessar assunto do usuário 2'));
  const { data: test1Data, error: test1Error } = await clients.client1
    .from('subjects')
    .select('*')
    .eq('id', testData.subject2.id)
    .single();
  
  if (test1Error && test1Error.code === 'PGRST116') {
    console.log(chalk.green('✅ Teste 1 passou: Usuário 1 não pode acessar assunto do usuário 2'));
  } else {
    console.log(chalk.red('❌ Teste 1 falhou: Usuário 1 conseguiu acessar assunto do usuário 2'));
    console.log('Dados acessados:', test1Data);
  }
  
  // Teste 2: Usuário 2 tenta acessar assunto do usuário 1
  console.log(chalk.yellow('Teste 2: Usuário 2 tenta acessar assunto do usuário 1'));
  const { data: test2Data, error: test2Error } = await clients.client2
    .from('subjects')
    .select('*')
    .eq('id', testData.subject1.id)
    .single();
  
  if (test2Error && test2Error.code === 'PGRST116') {
    console.log(chalk.green('✅ Teste 2 passou: Usuário 2 não pode acessar assunto do usuário 1'));
  } else {
    console.log(chalk.red('❌ Teste 2 falhou: Usuário 2 conseguiu acessar assunto do usuário 1'));
    console.log('Dados acessados:', test2Data);
  }
  
  // Teste 3: Usuário 1 tenta acessar grupo do usuário 2
  console.log(chalk.yellow('Teste 3: Usuário 1 tenta acessar grupo do usuário 2'));
  const { data: test3Data, error: test3Error } = await clients.client1
    .from('study_groups')
    .select('*')
    .eq('id', testData.group2.id)
    .single();
  
  if (test3Error && test3Error.code === 'PGRST116') {
    console.log(chalk.green('✅ Teste 3 passou: Usuário 1 não pode acessar grupo do usuário 2'));
  } else {
    console.log(chalk.red('❌ Teste 3 falhou: Usuário 1 conseguiu acessar grupo do usuário 2'));
    console.log('Dados acessados:', test3Data);
  }
  
  // Teste 4: Usuário 2 tenta acessar grupo do usuário 1
  console.log(chalk.yellow('Teste 4: Usuário 2 tenta acessar grupo do usuário 1'));
  const { data: test4Data, error: test4Error } = await clients.client2
    .from('study_groups')
    .select('*')
    .eq('id', testData.group1.id)
    .single();
  
  if (test4Error && test4Error.code === 'PGRST116') {
    console.log(chalk.green('✅ Teste 4 passou: Usuário 2 não pode acessar grupo do usuário 1'));
  } else {
    console.log(chalk.red('❌ Teste 4 falhou: Usuário 2 conseguiu acessar grupo do usuário 1'));
    console.log('Dados acessados:', test4Data);
  }
}

// Função para limpar dados de teste
async function cleanupTestData(users) {
  console.log(chalk.blue('Limpando dados de teste...'));
  
  // Excluir usuário 1
  const { error: error1 } = await supabaseAdmin.auth.admin.deleteUser(users.user1.id);
  
  if (error1) {
    console.error(chalk.red('Erro ao excluir usuário 1:'), error1);
  }
  
  // Excluir usuário 2
  const { error: error2 } = await supabaseAdmin.auth.admin.deleteUser(users.user2.id);
  
  if (error2) {
    console.error(chalk.red('Erro ao excluir usuário 2:'), error2);
  }
  
  console.log(chalk.green('Dados de teste limpos com sucesso!'));
}

// Função principal
async function main() {
  try {
    console.log(chalk.blue.bold('Iniciando teste de isolamento de dados...'));
    
    // Criar usuários de teste
    const users = await createTestUsers();
    if (!users) {
      console.error(chalk.red('Falha ao criar usuários de teste.'));
      return;
    }
    
    // Autenticar usuários
    const clients = await authenticateUsers(users);
    if (!clients) {
      console.error(chalk.red('Falha ao autenticar usuários.'));
      return;
    }
    
    // Criar dados de teste
    const testData = await createTestData(clients);
    if (!testData) {
      console.error(chalk.red('Falha ao criar dados de teste.'));
      return;
    }
    
    // Testar isolamento de dados
    await testDataIsolation(clients, testData);
    
    // Limpar dados de teste
    await cleanupTestData(users);
    
    console.log(chalk.blue.bold('Teste de isolamento de dados concluído!'));
  } catch (error) {
    console.error(chalk.red('Erro durante o teste:'), error);
  }
}

// Executar a função principal
main();
