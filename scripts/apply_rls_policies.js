/**
 * Script para aplicar as políticas RLS no Supabase
 * Este script deve ser executado para configurar as políticas de segurança no banco de dados
 * 
 * Uso:
 * node scripts/apply_rls_policies.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Carregar variáveis de ambiente
dotenv.config();

// Verificar se as variáveis de ambiente necessárias estão definidas
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
  console.error('Erro: As variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_KEY devem estar definidas.');
  console.error('Adicione-as ao arquivo .env ou defina-as antes de executar o script.');
  process.exit(1);
}

// Criar cliente Supabase com a chave de serviço
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Caminho para o arquivo SQL com as políticas RLS
const rlsPoliciesPath = path.join(__dirname, '..', 'sql', 'optimize_rls_policies.sql');

// Função para executar uma consulta SQL
async function executeQuery(query) {
  try {
    const { data, error } = await supabase.rpc('pgexecute', { query });
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Erro ao executar consulta:', error);
    throw error;
  }
}

// Função para dividir o arquivo SQL em consultas individuais
function splitSqlFile(content) {
  // Dividir por ponto e vírgula, mas ignorar os que estão dentro de strings ou comentários
  const queries = [];
  let currentQuery = '';
  let inString = false;
  let inLineComment = false;
  let inBlockComment = false;
  
  for (let i = 0; i < content.length; i++) {
    const char = content[i];
    const nextChar = content[i + 1] || '';
    
    // Verificar se estamos dentro de uma string
    if (char === "'" && !inLineComment && !inBlockComment) {
      inString = !inString;
    }
    
    // Verificar se estamos dentro de um comentário de linha
    if (char === '-' && nextChar === '-' && !inString && !inBlockComment) {
      inLineComment = true;
    }
    
    // Verificar se estamos dentro de um comentário de bloco
    if (char === '/' && nextChar === '*' && !inString && !inLineComment) {
      inBlockComment = true;
      i++; // Pular o próximo caractere
    }
    
    // Verificar se estamos saindo de um comentário de linha
    if (char === '\n' && inLineComment) {
      inLineComment = false;
    }
    
    // Verificar se estamos saindo de um comentário de bloco
    if (char === '*' && nextChar === '/' && inBlockComment) {
      inBlockComment = false;
      i++; // Pular o próximo caractere
      continue;
    }
    
    // Adicionar o caractere à consulta atual
    currentQuery += char;
    
    // Se encontrarmos um ponto e vírgula fora de uma string ou comentário, finalizar a consulta
    if (char === ';' && !inString && !inLineComment && !inBlockComment) {
      // Remover espaços em branco extras
      const trimmedQuery = currentQuery.trim();
      if (trimmedQuery.length > 1) { // Ignorar consultas vazias
        queries.push(trimmedQuery);
      }
      currentQuery = '';
    }
  }
  
  // Adicionar a última consulta se não terminar com ponto e vírgula
  const trimmedQuery = currentQuery.trim();
  if (trimmedQuery.length > 0 && !trimmedQuery.endsWith(';')) {
    queries.push(trimmedQuery + ';');
  }
  
  return queries;
}

// Função principal
async function applyRlsPolicies() {
  try {
    console.log('Aplicando políticas RLS no Supabase...');
    
    // Ler o arquivo SQL
    const sqlContent = fs.readFileSync(rlsPoliciesPath, 'utf8');
    
    // Dividir o arquivo em consultas individuais
    const queries = splitSqlFile(sqlContent);
    
    console.log(`Encontradas ${queries.length} consultas para executar.`);
    
    // Executar cada consulta
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i];
      console.log(`Executando consulta ${i + 1}/${queries.length}...`);
      
      try {
        await executeQuery(query);
        console.log(`✅ Consulta ${i + 1} executada com sucesso.`);
      } catch (error) {
        console.error(`❌ Erro ao executar consulta ${i + 1}:`, error);
        console.error('Consulta:', query);
        
        // Perguntar se deseja continuar
        const readline = require('readline').createInterface({
          input: process.stdin,
          output: process.stdout
        });
        
        const answer = await new Promise(resolve => {
          readline.question('Deseja continuar com as próximas consultas? (s/n) ', resolve);
        });
        
        readline.close();
        
        if (answer.toLowerCase() !== 's') {
          console.log('Operação cancelada pelo usuário.');
          process.exit(1);
        }
      }
    }
    
    console.log('✅ Políticas RLS aplicadas com sucesso!');
  } catch (error) {
    console.error('Erro ao aplicar políticas RLS:', error);
    process.exit(1);
  }
}

// Executar a função principal
applyRlsPolicies();
