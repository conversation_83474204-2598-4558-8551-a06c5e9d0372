/**
 * Script para executar testes de isolamento de dados
 * 
 * Para executar:
 * npx ts-node scripts/runTests.ts
 */

import { runDataIsolationTests } from '../tests/testDataIsolation';

// Executar os testes
(async () => {
  try {
    console.log('Iniciando testes...');
    await runDataIsolationTests();
    console.log('Testes concluídos.');
  } catch (error) {
    console.error('Erro ao executar testes:', error);
  }
})();
