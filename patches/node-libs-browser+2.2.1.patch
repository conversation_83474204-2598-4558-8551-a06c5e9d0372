diff --git a/node_modules/node-libs-browser/index.js b/node_modules/node-libs-browser/index.js
index 5d1b498..f5d1498 100644
--- a/node_modules/node-libs-browser/index.js
+++ b/node_modules/node-libs-browser/index.js
@@ -29,6 +29,11 @@ module.exports = {
 	zlib: require.resolve("browserify-zlib"),
 	events: require.resolve("events/"),
 	stream: require.resolve("stream-browserify"),
+	// Add a mock for require.resolve
+	resolve: {
+		sync: function(id) { return id; },
+		isCore: function(id) { return false; }
+	},
 	util: require.resolve("util/"),
 	buffer: require.resolve("buffer/"),
 	querystring: require.resolve("querystring-es3"),
