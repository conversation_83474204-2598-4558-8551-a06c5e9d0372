/**
 * Custom entry point for the app
 * This is the first file that gets executed
 */

// Direct polyfill for require.resolve
if (typeof global !== 'undefined' && typeof global.require === 'function' && !global.require.resolve) {
  global.require.resolve = function(moduleName) {
    return moduleName;
  };
  console.log('require.resolve polyfill applied in index.js');
}

// Import the main app entry point
import 'expo-router/entry';
