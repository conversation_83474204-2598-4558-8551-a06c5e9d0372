import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Pressable,
  Alert,
  Modal,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { useUserStore } from "@/store/userStore";
import { useQuizStore } from "@/store/quizStore";
import { Button } from "@/components/Button";
import { Check, X, Bar<PERSON><PERSON>, TrendingUp, MoreVertical, Edit, Trash2 } from "lucide-react-native";
import { QuizAnswer } from "@/types";

export default function QuizScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { quizzes, addQuizAttempt, getQuizStats, deleteQuiz } = useQuizStore();
  const { addXP } = useUserStore();

  const [menuVisible, setMenuVisible] = useState(false);

  // Track time spent on quiz
  const [startTime] = useState<number>(Date.now());
  const [answers, setAnswers] = useState<QuizAnswer[]>([]);
  const [quizStats, setQuizStats] = useState<ReturnType<typeof getQuizStats> | null>(null);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isAnswered, setIsAnswered] = useState(false);
  const [score, setScore] = useState(0);
  const [completed, setCompleted] = useState(false);

  const quiz = quizzes.find((q) => q.id === id);
  const currentQuestion = quiz?.questions?.[currentQuestionIndex];

  useEffect(() => {
    if (id && quiz) {
      setQuizStats(getQuizStats(id as string));
    }
  }, [id, quiz]);

  const handleSelectOption = (optionId: string) => {
    if (isAnswered || !currentQuestion) return;

    const optionIndex = parseInt(optionId);
    setSelectedOption(optionId);
    setIsAnswered(true);

    // Record answer
    const isCorrect = optionIndex === currentQuestion.correctOption;
    const answer: QuizAnswer = {
      questionId: currentQuestion.id,
      selectedOption: optionIndex,
      correct: isCorrect,
    };

    setAnswers([...answers, answer]);

    if (isCorrect) {
      setScore(score + 1);
      addXP(10);
    }
  };

  const handleNextQuestion = () => {
    if (!quiz) return;

    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedOption(null);
      setIsAnswered(false);
    } else {
      setCompleted(true);

      // Calculate time spent
      const timeSpent = Math.floor((Date.now() - startTime) / 1000);

      // Add bonus XP for completing the quiz
      const completionBonus = Math.round((score / quiz.questions.length) * 50);
      addXP(completionBonus);

      // Save quiz attempt
      addQuizAttempt(id as string, {
        date: new Date().toISOString(),
        score,
        totalQuestions: quiz.questions.length,
        timeSpent,
        answers,
      });

      // Update stats
      setQuizStats(getQuizStats(id as string));
    }
  };

  const handleRestartQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedOption(null);
    setIsAnswered(false);
    setScore(0);
    setCompleted(false);
    setAnswers([]);
  };

  const handleViewStats = () => {
    router.push(`/quiz-stats/${id}`);
  };

  // Renderizar mensagem de quiz não encontrado se o quiz não existir
  if (!quiz || !currentQuestion) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Quiz não encontrado" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Quiz não encontrado</Text>
          <Button
            title="Voltar para o início"
            onPress={() => router.push("/")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: quiz.title,
          headerRight: () => (
            <Pressable
              style={styles.menuButton}
              onPress={() => setMenuVisible(true)}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <MoreVertical size={24} color={colors.text} />
            </Pressable>
          )
        }}
      />

      <Modal
        visible={menuVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setMenuVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setMenuVisible(false)}
        >
          <View style={styles.menuContainer}>
            <Pressable
              style={styles.menuItem}
              onPress={() => {
                setMenuVisible(false);
                router.push(`/quiz/edit/${id}`);
              }}
            >
              <Edit size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Editar Quiz</Text>
            </Pressable>

            <Pressable
              style={styles.menuItem}
              onPress={() => {
                setMenuVisible(false);
                Alert.alert(
                  "Excluir Quiz",
                  `Tem certeza que deseja excluir o quiz "${quiz.title}"? Esta ação não pode ser desfeita.`,
                  [
                    { text: "Cancelar", style: "cancel" },
                    {
                      text: "Excluir",
                      style: "destructive",
                      onPress: async () => {
                        try {
                          await deleteQuiz(quiz.id);
                          Alert.alert("Sucesso", "Quiz excluído com sucesso!");
                          router.push('/quizzes');
                        } catch (error) {
                          Alert.alert("Erro", "Não foi possível excluir o quiz. Tente novamente.");
                        }
                      }
                    }
                  ]
                );
              }}
            >
              <Trash2 size={20} color={colors.error} />
              <Text style={[styles.menuItemText, { color: colors.error }]}>Excluir Quiz</Text>
            </Pressable>
          </View>
        </Pressable>
      </Modal>

      {!completed ? (
        <View style={styles.quizContainer}>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              Questão {currentQuestionIndex + 1} de {quiz.questions.length}
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${
                      ((currentQuestionIndex + 1) / quiz.questions.length) * 100
                    }%`,
                  },
                ]}
              />
            </View>
          </View>

          <ScrollView style={styles.questionContainer}>
            <Text style={styles.questionText}>{currentQuestion.question}</Text>

            <View style={styles.optionsContainer}>
              {currentQuestion.options.map((option, index) => (
                <Pressable
                  key={index}
                  style={[
                    styles.optionCard,
                    selectedOption === index.toString() &&
                      styles.selectedOptionCard,
                    isAnswered &&
                      index === currentQuestion.correctOption &&
                      styles.correctOptionCard,
                    isAnswered &&
                      selectedOption === index.toString() &&
                      index !== currentQuestion.correctOption &&
                      styles.incorrectOptionCard,
                  ]}
                  onPress={() => handleSelectOption(index.toString())}
                  disabled={isAnswered}
                >
                  <Text
                    style={[
                      styles.optionText,
                      selectedOption === index.toString() &&
                        styles.selectedOptionText,
                      isAnswered &&
                        index === currentQuestion.correctOption &&
                        styles.correctOptionText,
                      isAnswered &&
                        selectedOption === index.toString() &&
                        index !== currentQuestion.correctOption &&
                        styles.incorrectOptionText,
                    ]}
                  >
                    {option}
                  </Text>

                  {isAnswered && index === currentQuestion.correctOption && (
                    <View style={styles.resultIconContainer}>
                      <Check size={16} color="#fff" />
                    </View>
                  )}

                  {isAnswered &&
                    selectedOption === index.toString() &&
                    index !== currentQuestion.correctOption && (
                      <View style={styles.resultIconContainer}>
                        <X size={16} color="#fff" />
                      </View>
                    )}
                </Pressable>
              ))}
            </View>
          </ScrollView>

          {isAnswered && (
            <View style={styles.nextButtonContainer}>
              <Button
                title="Próxima Questão"
                onPress={handleNextQuestion}
                variant="primary"
                size="large"
                fullWidth
              />
            </View>
          )}
        </View>
      ) : (
        <ScrollView
          style={styles.completedContainer}
          contentContainerStyle={styles.completedContent}
        >
          <Text style={styles.completedTitle}>Quiz Concluído!</Text>
          <Text style={styles.scoreText}>
            Pontuação: {score}/{quiz.questions.length}
          </Text>
          <Text style={styles.percentageText}>
            {Math.round((score / quiz.questions.length) * 100)}%
          </Text>

          <View style={styles.resultCard}>
            <Text style={styles.resultTitle}>Resultado</Text>
            <View style={styles.resultItem}>
              <Text style={styles.resultLabel}>Acertos:</Text>
              <Text style={styles.resultValue}>{score}</Text>
            </View>
            <View style={styles.resultItem}>
              <Text style={styles.resultLabel}>Erros:</Text>
              <Text style={styles.resultValue}>
                {quiz.questions.length - score}
              </Text>
            </View>
            <View style={styles.resultItem}>
              <Text style={styles.resultLabel}>XP ganho:</Text>
              <Text style={styles.resultValue}>
                {score * 10 + Math.round((score / quiz.questions.length) * 50)}
              </Text>
            </View>
          </View>

          {quizStats && quizStats.totalAttempts > 1 && (
            <View style={styles.statsPreviewCard}>
              <Text style={styles.statsPreviewTitle}>Seu progresso</Text>
              <View style={styles.statsPreviewContent}>
                <View style={styles.statsPreviewItem}>
                  <View style={styles.statsPreviewIconContainer}>
                    <BarChart size={20} color={colors.primary} />
                  </View>
                  <Text style={styles.statsPreviewValue}>{quizStats.totalAttempts}</Text>
                  <Text style={styles.statsPreviewLabel}>Tentativas</Text>
                </View>

                <View style={styles.statsPreviewItem}>
                  <View style={styles.statsPreviewIconContainer}>
                    <TrendingUp size={20} color={quizStats.improvement > 0 ? colors.success : colors.error} />
                  </View>
                  <Text style={[styles.statsPreviewValue, {color: quizStats.improvement > 0 ? colors.success : colors.error}]}>
                    {quizStats.improvement > 0 ? '+' : ''}{Math.round(quizStats.improvement)}%
                  </Text>
                  <Text style={styles.statsPreviewLabel}>Melhoria</Text>
                </View>
              </View>
            </View>
          )}

          <View style={styles.buttonsContainer}>
            <Button
              title="Refazer Quiz"
              onPress={handleRestartQuiz}
              variant="primary"
              size="medium"
              fullWidth
            />
            {quizStats && quizStats.totalAttempts > 0 && (
              <Button
                title="Ver Estatísticas"
                onPress={handleViewStats}
                variant="outline"
                size="medium"
                fullWidth
                icon={BarChart}
              />
            )}
            <Button
              title="Voltar para o Início"
              onPress={() => router.push("/")}
              variant="outline"
              size="medium"
              fullWidth
            />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  quizContainer: {
    flex: 1,
    padding: 16,
  },
  progressContainer: {
    marginBottom: 24,
  },
  progressText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
    textAlign: "center",
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.backgroundDark,
    borderRadius: 3,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.primary,
  },
  questionContainer: {
    flex: 1,
  },
  questionText: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 24,
  },
  optionsContainer: {
    marginBottom: 24,
  },
  optionCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  selectedOptionCard: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}10`,
  },
  correctOptionCard: {
    borderColor: colors.success,
    backgroundColor: `${colors.success}10`,
  },
  incorrectOptionCard: {
    borderColor: colors.error,
    backgroundColor: `${colors.error}10`,
  },
  optionText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: "500",
  },
  correctOptionText: {
    color: colors.success,
    fontWeight: "500",
  },
  incorrectOptionText: {
    color: colors.error,
    fontWeight: "500",
  },
  resultIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.success,
    justifyContent: "center",
    alignItems: "center",
  },
  nextButtonContainer: {
    marginTop: 16,
  },
  completedContainer: {
    flex: 1,
  },
  completedContent: {
    padding: 16,
    alignItems: "center",
  },
  completedTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    marginTop: 24,
  },
  scoreText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 8,
  },
  percentageText: {
    fontSize: 48,
    fontWeight: "700",
    color: colors.primary,
    marginBottom: 24,
  },
  resultCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    width: "100%",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  resultItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  resultLabel: {
    fontSize: 16,
    color: colors.textLight,
  },
  resultValue: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  buttonsContainer: {
    width: "100%",
    gap: 12,
  },
  statsPreviewCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    width: "100%",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statsPreviewTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  statsPreviewContent: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statsPreviewItem: {
    alignItems: "center",
  },
  statsPreviewIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  statsPreviewValue: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
  },
  statsPreviewLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 4,
  },
  menuButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    width: '80%',
    maxWidth: 300,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 12,
  },
});