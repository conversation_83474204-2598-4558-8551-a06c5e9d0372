import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Pressable,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { colors } from "@/constants/colors";
import { useQuizStore } from "@/store/quizStore";
import { useStudyStore } from "@/store/studyStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import {
  FileQuestion,
  Plus,
  Trash2,
  Check,
  X,
  Clock,
  Save,
} from "lucide-react-native";
import { Quiz, QuizQuestion } from "@/types";

export default function EditQuizScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getQuizById, updateQuiz, loading } = useQuizStore();
  const { subjects } = useStudyStore();

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [subject, setSubject] = useState("");
  const [timeLimit, setTimeLimit] = useState("");
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (id) {
      const quiz = getQuizById(id as string);
      if (quiz) {
        setTitle(quiz.title);
        setDescription(quiz.description || "");
        setSubject(quiz.subject || "");
        setTimeLimit(quiz.timeLimit ? quiz.timeLimit.toString() : "300");
        setQuestions([...quiz.questions]);
        setIsLoading(false);
      } else {
        Alert.alert("Erro", "Quiz não encontrado");
        router.back();
      }
    }
  }, [id]);

  const handleAddQuestion = () => {
    setQuestions([
      ...questions,
      {
        id: `q_${Date.now()}`,
        question: "",
        options: ["", "", "", ""],
        correctOption: 0,
        explanation: "",
      },
    ]);
  };

  const handleRemoveQuestion = (index: number) => {
    if (questions.length <= 1) {
      Alert.alert("Erro", "O quiz deve ter pelo menos uma pergunta.");
      return;
    }

    const newQuestions = [...questions];
    newQuestions.splice(index, 1);
    setQuestions(newQuestions);
  };

  const handleQuestionChange = (
    questionIndex: number,
    field: keyof QuizQuestion,
    value: string
  ) => {
    const newQuestions = [...questions];
    newQuestions[questionIndex] = {
      ...newQuestions[questionIndex],
      [field]: value,
    };
    setQuestions(newQuestions);
  };

  const handleOptionChange = (
    questionIndex: number,
    optionIndex: number,
    value: string
  ) => {
    const newQuestions = [...questions];
    const options = [...newQuestions[questionIndex].options];
    options[optionIndex] = value;
    newQuestions[questionIndex] = { ...newQuestions[questionIndex], options };
    setQuestions(newQuestions);
  };

  const handleCorrectOptionChange = (
    questionIndex: number,
    optionIndex: number
  ) => {
    const newQuestions = [...questions];
    newQuestions[questionIndex] = {
      ...newQuestions[questionIndex],
      correctOption: optionIndex,
    };
    setQuestions(newQuestions);
  };

  const handleSelectSubject = () => {
    if (subjects.length === 0) {
      Alert.alert(
        "Nenhuma matéria",
        "Você não tem matérias cadastradas. Crie uma matéria primeiro."
      );
      return;
    }

    Alert.alert(
      "Selecione uma matéria",
      "Escolha a matéria para este quiz",
      [
        { text: "Cancelar", style: "cancel" },
        ...subjects.map((s) => ({
          text: s.title,
          onPress: () => setSubject(s.title),
        })),
      ]
    );
  };

  const handleUpdateQuiz = async () => {
    // Validate inputs
    if (!title.trim()) {
      Alert.alert("Erro", "Por favor, insira um título para o quiz.");
      return;
    }

    if (!subject) {
      Alert.alert("Erro", "Por favor, selecione uma matéria para o quiz.");
      return;
    }

    // Validate questions
    for (let i = 0; i < questions.length; i++) {
      const q = questions[i];
      if (!q.question?.trim()) {
        Alert.alert("Erro", `A pergunta ${i + 1} não pode estar vazia.`);
        return;
      }

      for (let j = 0; j < q.options.length; j++) {
        if (!q.options[j].trim()) {
          Alert.alert(
            "Erro",
            `A opção ${j + 1} da pergunta ${i + 1} não pode estar vazia.`
          );
          return;
        }
      }

      if (!q.explanation?.trim()) {
        Alert.alert(
          "Erro",
          `A explicação da pergunta ${i + 1} não pode estar vazia.`
        );
        return;
      }
    }

    try {
      // Update quiz
      const updatedQuiz: Partial<Quiz> = {
        title: title.trim(),
        description: description.trim(),
        subject,
        questions,
        timeLimit: parseInt(timeLimit),
      };

      await updateQuiz(id as string, updatedQuiz);

      Alert.alert("Sucesso", "Quiz atualizado com sucesso!", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      Alert.alert(
        "Erro",
        "Não foi possível atualizar o quiz. Tente novamente."
      );
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <Stack.Screen options={{ title: "Carregando..." }} />
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: "Editar Quiz" }} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.headerCard}>
          <View style={styles.headerContent}>
            <View style={styles.iconContainer}>
              <FileQuestion size={32} color={colors.primary} />
            </View>
            <Text style={styles.headerTitle}>Editar Quiz</Text>
            <Text style={styles.headerDescription}>
              Atualize as informações do quiz e suas perguntas.
            </Text>
          </View>
        </GlassCard>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Informações Básicas</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Título</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Ex: História do Brasil"
              placeholderTextColor={colors.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Ex: Quiz sobre os principais eventos da história brasileira"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Matéria</Text>
            <Pressable
              style={styles.subjectSelector}
              onPress={handleSelectSubject}
            >
              <Text
                style={subject ? styles.subjectText : styles.subjectPlaceholder}
              >
                {subject || "Selecione uma matéria"}
              </Text>
            </Pressable>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Tempo Limite (segundos)</Text>
            <TextInput
              style={styles.input}
              value={timeLimit}
              onChangeText={setTimeLimit}
              placeholder="Ex: 300 (5 minutos)"
              placeholderTextColor={colors.textLight}
              keyboardType="number-pad"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Perguntas</Text>

          {questions.map((question, questionIndex) => (
            <View key={question.id} style={styles.questionCard}>
              <View style={styles.questionHeader}>
                <Text style={styles.questionNumber}>
                  Pergunta {questionIndex + 1}
                </Text>
                <Pressable
                  style={styles.removeButton}
                  onPress={() => handleRemoveQuestion(questionIndex)}
                >
                  <Trash2 size={20} color={colors.error} />
                </Pressable>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Pergunta</Text>
                <TextInput
                  style={styles.input}
                  value={question.question}
                  onChangeText={(value) =>
                    handleQuestionChange(questionIndex, "question", value)
                  }
                  placeholder="Ex: Qual é a capital do Brasil?"
                  placeholderTextColor={colors.textLight}
                />
              </View>

              <Text style={styles.optionsLabel}>Opções</Text>
              {question.options.map((option, optionIndex) => (
                <View key={optionIndex} style={styles.optionContainer}>
                  <Pressable
                    style={[
                      styles.correctOptionButton,
                      question.correctOption === optionIndex &&
                        styles.selectedCorrectOptionButton,
                    ]}
                    onPress={() =>
                      handleCorrectOptionChange(questionIndex, optionIndex)
                    }
                  >
                    {question.correctOption === optionIndex ? (
                      <Check size={16} color="#fff" />
                    ) : null}
                  </Pressable>
                  <TextInput
                    style={styles.optionInput}
                    value={option}
                    onChangeText={(value) =>
                      handleOptionChange(questionIndex, optionIndex, value)
                    }
                    placeholder={`Opção ${optionIndex + 1}`}
                    placeholderTextColor={colors.textLight}
                  />
                </View>
              ))}

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Explicação</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={question.explanation}
                  onChangeText={(value) =>
                    handleQuestionChange(questionIndex, "explanation", value)
                  }
                  placeholder="Ex: Brasília é a capital do Brasil desde 1960."
                  placeholderTextColor={colors.textLight}
                  multiline
                  numberOfLines={3}
                />
              </View>
            </View>
          ))}

          <Button
            title="Adicionar Pergunta"
            onPress={handleAddQuestion}
            variant="outline"
            size="medium"
            icon={Plus}
            style={styles.addQuestionButton}
          />
        </View>

        <View style={styles.buttonsContainer}>
          <Button
            title="Salvar Alterações"
            onPress={handleUpdateQuiz}
            variant="primary"
            size="large"
            icon={Save}
            loading={loading}
            fullWidth
          />
          <Button
            title="Cancelar"
            onPress={() => router.back()}
            variant="outline"
            size="large"
            fullWidth
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    marginBottom: 24,
  },
  headerContent: {
    padding: 16,
    alignItems: "center",
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  headerDescription: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  subjectSelector: {
    backgroundColor: colors.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 12,
  },
  subjectText: {
    fontSize: 16,
    color: colors.text,
  },
  subjectPlaceholder: {
    fontSize: 16,
    color: colors.textLight,
  },
  questionCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  questionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  questionNumber: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  removeButton: {
    padding: 8,
  },
  optionsLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  correctOptionButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedCorrectOptionButton: {
    backgroundColor: colors.success,
    borderColor: colors.success,
  },
  optionInput: {
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  addQuestionButton: {
    marginTop: 8,
  },
  buttonsContainer: {
    gap: 12,
    marginTop: 16,
  },
  aiButton: {
    marginTop: 16,
  },
});
