import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Pressable,
  Alert,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useQuizStore } from "@/store/quizStore";
import { useStudyStore } from "@/store/studyStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import {
  FileQuestion,
  Plus,
  Trash2,
  Check,
  X,
  Clock,
  Sparkles
} from "lucide-react-native";
import { Quiz, QuizQuestion } from "@/types";

export default function CreateQuizScreen() {
  const router = useRouter();
  const { addQuiz } = useQuizStore();
  const { subjects } = useStudyStore();

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [subject, setSubject] = useState("");
  const [timeLimit, setTimeLimit] = useState("300"); // 5 minutes in seconds
  const [questions, setQuestions] = useState<Partial<QuizQuestion>[]>([
    {
      id: `q_${Date.now()}`,
      question: "",
      options: ["", "", "", ""],
      correctOption: 0,
      explanation: "",
    },
  ]);

  const handleAddQuestion = () => {
    setQuestions([
      ...questions,
      {
        id: `q_${Date.now()}`,
        question: "",
        options: ["", "", "", ""],
        correctOption: 0,
        explanation: "",
      },
    ]);
  };

  const handleRemoveQuestion = (index: number) => {
    if (questions.length <= 1) {
      Alert.alert("Erro", "O quiz deve ter pelo menos uma pergunta.");
      return;
    }

    const newQuestions = [...questions];
    newQuestions.splice(index, 1);
    setQuestions(newQuestions);
  };

  const handleQuestionChange = (index: number, field: string, value: string) => {
    const newQuestions = [...questions];
    newQuestions[index] = { ...newQuestions[index], [field]: value };
    setQuestions(newQuestions);
  };

  const handleOptionChange = (questionIndex: number, optionIndex: number, value: string) => {
    const newQuestions = [...questions];
    const options = [...newQuestions[questionIndex].options!];
    options[optionIndex] = value;
    newQuestions[questionIndex] = { ...newQuestions[questionIndex], options };
    setQuestions(newQuestions);
  };

  const handleCorrectOptionChange = (questionIndex: number, optionIndex: number) => {
    const newQuestions = [...questions];
    newQuestions[questionIndex] = { ...newQuestions[questionIndex], correctOption: optionIndex };
    setQuestions(newQuestions);
  };

  const handleCreateQuiz = async () => {
    // Validate inputs
    if (!title.trim()) {
      Alert.alert("Erro", "Por favor, insira um título para o quiz.");
      return;
    }

    if (!subject) {
      Alert.alert("Erro", "Por favor, selecione uma matéria para o quiz.");
      return;
    }

    // Validate questions
    for (let i = 0; i < questions.length; i++) {
      const q = questions[i];
      if (!q.question?.trim()) {
        Alert.alert("Erro", `A pergunta ${i + 1} não pode estar vazia.`);
        return;
      }

      for (let j = 0; j < q.options!.length; j++) {
        if (!q.options![j].trim()) {
          Alert.alert("Erro", `A opção ${j + 1} da pergunta ${i + 1} não pode estar vazia.`);
          return;
        }
      }

      if (!q.explanation?.trim()) {
        Alert.alert("Erro", `A explicação da pergunta ${i + 1} não pode estar vazia.`);
        return;
      }
    }

    try {
      // Create quiz
      const newQuiz: Quiz = {
        id: `quiz_${Date.now()}`,
        title: title.trim(),
        description: description.trim(),
        subject,
        questions: questions as QuizQuestion[],
        timeLimit: parseInt(timeLimit),
        lastAttempt: null,
        bestScore: null,
        createdAt: new Date().toISOString(),
      };

      const savedQuiz = await addQuiz(newQuiz);

      Alert.alert(
        "Sucesso",
        "Quiz criado com sucesso!",
        [
          {
            text: "OK",
            onPress: () => {
              if (savedQuiz && savedQuiz.id) {
                router.push(`/quiz/${savedQuiz.id}`);
              } else {
                router.push('/quizzes');
              }
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert("Erro", "Não foi possível criar o quiz. Tente novamente.");
    }
  };

  const handleGenerateWithAI = () => {
    Alert.alert(
      "Gerar Quiz com IA",
      "Deseja gerar um quiz usando inteligência artificial?",
      [
        {
          text: "Cancelar",
          style: "cancel",
        },
        {
          text: "Gerar",
          onPress: () => {
            // Aqui seria implementada a chamada para a API da OpenAI
            Alert.alert("Funcionalidade em desenvolvimento", "A geração de quiz com IA estará disponível em breve!");
          },
        },
      ]
    );
  };

  const handleSelectSubject = () => {
    Alert.alert(
      "Selecionar Matéria",
      "Escolha a matéria para este quiz",
      [
        ...subjects.map((s) => ({
          text: s.title,
          onPress: () => setSubject(s.title),
        })),
        {
          text: "Geral",
          onPress: () => setSubject("Geral"),
        },
        {
          text: "Cancelar",
          style: "cancel",
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: "Criar Quiz" }} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.headerCard}>
          <View style={styles.headerContent}>
            <View style={styles.iconContainer}>
              <FileQuestion size={32} color={colors.primary} />
            </View>
            <Text style={styles.headerTitle}>Criar Novo Quiz</Text>
            <Text style={styles.headerDescription}>
              Crie um quiz personalizado para testar seus conhecimentos ou gere automaticamente com IA.
            </Text>

            <Button
              title="Gerar com IA"
              onPress={handleGenerateWithAI}
              variant="outline"
              size="medium"
              icon={Sparkles}
              style={styles.aiButton}
            />
          </View>
        </GlassCard>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Informações Básicas</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Título</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Ex: História do Brasil"
              placeholderTextColor={colors.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Ex: Quiz sobre os principais eventos da história brasileira"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Matéria</Text>
            <Pressable
              style={styles.subjectSelector}
              onPress={handleSelectSubject}
            >
              <Text style={subject ? styles.subjectText : styles.subjectPlaceholder}>
                {subject || "Selecione uma matéria"}
              </Text>
            </Pressable>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Tempo Limite (segundos)</Text>
            <TextInput
              style={styles.input}
              value={timeLimit}
              onChangeText={setTimeLimit}
              placeholder="Ex: 300 (5 minutos)"
              placeholderTextColor={colors.textLight}
              keyboardType="number-pad"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Perguntas</Text>

          {questions.map((question, questionIndex) => (
            <View key={question.id} style={styles.questionCard}>
              <View style={styles.questionHeader}>
                <Text style={styles.questionNumber}>Pergunta {questionIndex + 1}</Text>
                <Pressable
                  style={styles.removeButton}
                  onPress={() => handleRemoveQuestion(questionIndex)}
                >
                  <Trash2 size={20} color={colors.error} />
                </Pressable>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Pergunta</Text>
                <TextInput
                  style={styles.input}
                  value={question.question}
                  onChangeText={(value) => handleQuestionChange(questionIndex, "question", value)}
                  placeholder="Ex: Qual é a capital do Brasil?"
                  placeholderTextColor={colors.textLight}
                />
              </View>

              <Text style={styles.inputLabel}>Opções</Text>
              {question.options?.map((option, optionIndex) => (
                <View key={optionIndex} style={styles.optionContainer}>
                  <Pressable
                    style={[
                      styles.correctOptionButton,
                      question.correctOption === optionIndex && styles.correctOptionButtonSelected,
                    ]}
                    onPress={() => handleCorrectOptionChange(questionIndex, optionIndex)}
                  >
                    {question.correctOption === optionIndex ? (
                      <Check size={16} color="#fff" />
                    ) : (
                      <View style={styles.emptyCircle} />
                    )}
                  </Pressable>
                  <TextInput
                    style={[
                      styles.optionInput,
                      question.correctOption === optionIndex && styles.correctOptionInput,
                    ]}
                    value={option}
                    onChangeText={(value) => handleOptionChange(questionIndex, optionIndex, value)}
                    placeholder={`Opção ${optionIndex + 1}`}
                    placeholderTextColor={colors.textLight}
                  />
                </View>
              ))}

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Explicação</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={question.explanation}
                  onChangeText={(value) => handleQuestionChange(questionIndex, "explanation", value)}
                  placeholder="Ex: Brasília é a capital do Brasil desde 1960."
                  placeholderTextColor={colors.textLight}
                  multiline
                  numberOfLines={3}
                />
              </View>
            </View>
          ))}

          <Button
            title="Adicionar Pergunta"
            onPress={handleAddQuestion}
            variant="outline"
            size="medium"
            icon={Plus}
            style={styles.addQuestionButton}
          />
        </View>

        <Button
          title="Criar Quiz"
          onPress={handleCreateQuiz}
          variant="primary"
          size="large"
          fullWidth
          style={styles.createButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    marginBottom: 24,
    padding: 20,
  },
  headerContent: {
    alignItems: "center",
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 16,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  headerDescription: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    marginBottom: 16,
  },
  aiButton: {
    marginTop: 8,
  },
  formSection: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  subjectSelector: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  subjectText: {
    fontSize: 16,
    color: colors.text,
  },
  subjectPlaceholder: {
    fontSize: 16,
    color: colors.textLight,
  },
  questionCard: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  questionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  questionNumber: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  removeButton: {
    padding: 4,
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  correctOptionButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.backgroundDark,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  correctOptionButtonSelected: {
    backgroundColor: colors.success,
  },
  emptyCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.backgroundLight,
  },
  optionInput: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  correctOptionInput: {
    borderColor: colors.success,
    backgroundColor: `${colors.success}10`,
  },
  addQuestionButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
});
