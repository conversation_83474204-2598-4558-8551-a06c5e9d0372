// Import React and necessary components
import React from 'react';

// Import polyfill
import '../require-polyfill';

// Import other initializations
import '../reanimated-init';

import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack, useRouter, useSegments } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useState, useCallback } from "react";
import { Platform, View, Alert, StatusBar as RNStatusBar, AppState, LogBox, InteractionManager } from "react-native";
import { ErrorBoundary } from "./error-boundary";
import { theme } from "@/constants/theme";
import { supabase } from "@/lib/supabase";
import { initDatabase } from "@/lib/initDatabase";
import { useUserStore } from "@/store/userStore";
import { useStudyStore } from "@/store/studyStore";
import { useQuizStore } from "@/store/quizStore";
import { useStudyGroupStore } from "@/store/studyGroupStore";
import { TimerOverlay } from "@/components/TimerOverlay";
import { startTimerTicker } from "@/store/timerStore";
import { requestNotificationPermissions, addNotificationReceivedListener, addNotificationResponseReceivedListener } from "@/services/notificationService";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { StatusBar } from "expo-status-bar";
import { cancelAllActiveRuns, resetAssistantThreads } from "@/services/openaiAssistants";
import { preloadLocalImages, preloadImages, clearImageCache } from "@/utils/imageCache";
import { images } from "@/constants/images";
import { NetworkStatusMonitor } from "@/components/NetworkStatusMonitor";
import { initPerformanceMonitor, startMeasure, endMeasure } from "@/services/performanceMonitor";
import { database, syncDatabase } from "@/lib/database";
import NetInfo from "@react-native-community/netinfo";
import { runAfterInteractions } from "@/utils/performance";
import { useAuthStore } from "@/store/authStore";
import { useAuthInit } from "@/hooks/useAuthInit";
import { ThemeProvider, useTheme } from "@/contexts/ThemeContext";

// Ignore specific warnings to improve performance
LogBox.ignoreLogs([
  'VirtualizedLists should never be nested',
  'Non-serializable values were found in the navigation state',
  'Sending `onAnimatedValueUpdate` with no listeners registered',
  'No route named "login" exists in nested children',
  'No route named "register" exists in nested children',
  '[Layout children]',
  'WARN  [Layout children]: No route named "login"',
  'WARN  [Layout children]: No route named "register"',
]);

// Override console.warn to filter out specific route warnings
const originalWarn = console.warn;
console.warn = (...args) => {
  const message = args.join(' ');
  if (
    message.includes('[Layout children]: No route named "login"') ||
    message.includes('[Layout children]: No route named "register"')
  ) {
    return; // Suppress these specific warnings
  }
  originalWarn.apply(console, args);
};

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "(tabs)",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

/**
 * Root Layout Component
 * This is the main layout component for the app
 * Required by Expo Router as the default export
 */
const RootLayout: React.FC = () => {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });
  const [appReady, setAppReady] = useState(false);
  const [initialLoadTime, setInitialLoadTime] = useState(0);

  // Initialize performance monitoring
  useEffect(() => {
    initPerformanceMonitor().catch(console.error);
  }, []);

  // Handle font loading errors
  useEffect(() => {
    if (error) {
      console.error('Font loading error:', error);
      throw error;
    }
  }, [error]);

  // Prepare app when fonts are loaded
  useEffect(() => {
    if (loaded) {
      const startTime = startMeasure('appInitialization');

      const prepareApp = async () => {
        try {
          // Preload local images in batches to avoid UI jank
          preloadLocalImages(images);

          // Initialize database in the background
          InteractionManager.runAfterInteractions(() => {
            database.adapter.initializingPromise.catch(console.error);
          });

          // Hide splash screen after everything is ready
          await SplashScreen.hideAsync();

          // Record initialization time
          const loadTime = await endMeasure('appInitialization', startTime);
          setInitialLoadTime(loadTime);
          console.log(`App initialized in ${loadTime.toFixed(2)}ms`);

          setAppReady(true);
        } catch (error) {
          console.error('Error preparing app:', error);
          // Still hide splash screen even if there's an error
          SplashScreen.hideAsync().catch(console.error);
          setAppReady(true);
        }
      };

      prepareApp();
    }
  }, [loaded]);

  // Clean up old image cache periodically
  useEffect(() => {
    if (appReady) {
      // Clean image cache in the background after app is ready
      runAfterInteractions(() => {
        clearImageCache().catch(console.error);
      }, 2000);
    }
  }, [appReady]);

  if (!loaded || !appReady) {
    return null;
  }

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <RootLayoutNav initialLoadTime={initialLoadTime} />
      </ThemeProvider>
    </ErrorBoundary>
  );
}

/**
 * Default export for Expo Router
 * This is explicitly required by Expo Router
 */
export default RootLayout;

export function RootLayoutNav({ initialLoadTime = 0 }) {
  // Inicializar autenticação com o novo AuthStore
  useAuthInit();

  // Usar o AuthStore em vez do AuthContext
  const { user, session, loading } = useAuthStore();

  const { fetchSupabaseUser } = useUserStore();
  const { fetchSubjects, fetchActivities } = useStudyStore();
  const { fetchQuizzes } = useQuizStore();
  const { fetchStudyGroups } = useStudyGroupStore();
  const segments = useSegments();
  const router = useRouter();
  const [isOnline, setIsOnline] = useState(true);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [syncStatus, setSyncStatus] = useState({ syncing: false, lastSync: null });

  useEffect(() => {
    if (!loading) {
      const inAuthGroup = segments[0] === '(auth)';

      if (!session && !inAuthGroup && segments[0] !== 'login' && segments[0] !== 'register' && segments[0] !== 'forgot-password') {
        router.replace('/login');
      } else if (session && (inAuthGroup || segments[0] === 'login' || segments[0] === 'register' || segments[0] === 'forgot-password')) {
        router.replace('/(tabs)');
      }
    }
  }, [session, segments, loading]);

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected && state.isInternetReachable);
    });

    return () => unsubscribe();
  }, []);

  // Load data when session is available
  useEffect(() => {
    if (session && !dataLoaded) {
      // Inicializar banco de dados e carregar dados do usuário
      const loadData = async () => {
        const startTime = startMeasure('dataLoading');

        try {
          // Inicializar banco de dados
          await initDatabase();

          // Carregar dados em paralelo para melhor desempenho
          const loadTasks = [
            fetchSupabaseUser(),
            fetchSubjects(),
            fetchActivities(),
          ];

          if (fetchQuizzes) {
            loadTasks.push(fetchQuizzes());
          }

          if (fetchStudyGroups) {
            loadTasks.push(fetchStudyGroups());
          }

          // Executar todas as tarefas em paralelo
          await Promise.all(loadTasks);

          // Iniciar o ticker do temporizador
          startTimerTicker();

          // Marcar dados como carregados
          setDataLoaded(true);

          // Registrar tempo de carregamento
          const loadTime = await endMeasure('dataLoading', startTime);
          console.log(`Data loaded in ${loadTime.toFixed(2)}ms`);

          // Sincronizar com o banco de dados local em segundo plano
          runAfterInteractions(async () => {
            if (isOnline) {
              try {
                setSyncStatus({ syncing: true, lastSync: syncStatus.lastSync });
                await syncDatabase();
                setSyncStatus({
                  syncing: false,
                  lastSync: new Date().toISOString()
                });
              } catch (error) {
                console.error('Error syncing database:', error);
                setSyncStatus({
                  syncing: false,
                  lastSync: syncStatus.lastSync
                });
              }
            }
          }, 1000);

          // Solicitar permissões de notificação em segundo plano
          runAfterInteractions(async () => {
            const hasPermission = await requestNotificationPermissions();
            if (!hasPermission) {
              Alert.alert(
                'Permissões de Notificação',
                'Para receber lembretes sobre seus eventos e tarefas, é necessário permitir notificações. Você pode alterar isso nas configurações do aplicativo.',
                [{ text: 'OK' }]
              );
            }
          }, 2000);

        } catch (error) {
          console.error('Error loading data:', error);
          // Ainda marcar como carregado para evitar loops infinitos
          setDataLoaded(true);
        }
      };

      loadData();
    }
  }, [session, dataLoaded, isOnline]);

  // Configurar listeners de notificação
  useEffect(() => {
    // Listener para notificações recebidas quando o app está em primeiro plano
    const notificationReceivedSubscription = addNotificationReceivedListener(notification => {
      console.log('Notificação recebida:', notification);
    });

    // Listener para quando o usuário interage com uma notificação
    const notificationResponseSubscription = addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      console.log('Notificação respondida:', data);

      // Navegar para a tela apropriada com base no tipo de notificação
      if (data.type === 'event' && data.eventId) {
        router.push(`/calendar?eventId=${data.eventId}`);
      } else if (data.type === 'todo' && data.todoId) {
        router.push(`/calendar?todoId=${data.todoId}`);
      }
    });

    return () => {
      notificationReceivedSubscription.remove();
      notificationResponseSubscription.remove();
    };
  }, []);

  // Configurar o status bar para mostrar ícones em preto
  useEffect(() => {
    // A configuração do StatusBar é feita diretamente no componente StatusBar
  }, []);

  // Monitorar o estado do aplicativo para cancelar processamentos quando o app é fechado
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Quando o app vai para segundo plano ou é fechado, cancelar todos os processamentos
        console.log('Aplicativo indo para segundo plano, cancelando processamentos ativos...');
        cancelAllActiveRuns().catch(error => {
          console.error('Erro ao cancelar processamentos ativos:', error);
          // Em caso de erro, forçar reset de todos os threads
          resetAssistantThreads();
        });
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // Periodic sync with Supabase when online
  useEffect(() => {
    if (!isOnline || !session || !dataLoaded) return;

    // Sync every 5 minutes when the app is active
    const syncInterval = setInterval(async () => {
      if (!syncStatus.syncing) {
        try {
          setSyncStatus({ syncing: true, lastSync: syncStatus.lastSync });
          await syncDatabase();
          setSyncStatus({
            syncing: false,
            lastSync: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error in periodic sync:', error);
          setSyncStatus({
            syncing: false,
            lastSync: syncStatus.lastSync
          });
        }
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(syncInterval);
  }, [isOnline, session, dataLoaded, syncStatus.syncing]);

  // Obter o tema atual
  const { theme: currentTheme, isDark } = useTheme();

  return (
    <GestureHandlerRootView style={{ flex: 1, backgroundColor: currentTheme.colors.background }}>
      <StatusBar style={isDark ? "light" : "dark"} translucent />
      <NetworkStatusMonitor />
      <TimerOverlay />
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: currentTheme.colors.background,
          },
          headerTintColor: currentTheme.colors.text,
          headerTitleStyle: {
            ...currentTheme.typography.heading4,
          },
          headerShadowVisible: false,
          // Improve animation performance
          animation: 'fade_from_bottom',
          animationDuration: 200,
          contentStyle: {
            backgroundColor: currentTheme.colors.background,
          },
          // Ensure content extends to edges of screen
          headerStatusBarHeight: 0,
          fullScreenGestureEnabled: true,
        }}
      >
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="modal" options={{ presentation: "modal" }} />
      <Stack.Screen name="login" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="register" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="forgot-password" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen
        name="flashcards/[id]"
        options={{ title: "Flashcards", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="quiz/[id]"
        options={{ title: "Quiz", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="subject/[id]"
        options={{ title: "Matéria", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-plan/[id]"
        options={{ title: "Plano de Estudos", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="schedules/stats"
        options={{ title: "Estatísticas", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="mind-map/[id]"
        options={{ title: "Mapa Mental", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="notes/[id]"
        options={{ title: "Anotações", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/[id]"
        options={{ title: "Grupo de Estudo", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/create"
        options={{ title: "Criar Grupo", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/invites"
        options={{ title: "Convites", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/timer"
        options={{ title: "Timer de Estudo", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/materials/add"
        options={{ title: "Adicionar Material", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/materials/[id]"
        options={{ title: "Material", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-groups/materials/edit/[id]"
        options={{ title: "Editar Material", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="profile"
        options={{ title: "Perfil", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="settings"
        options={{ title: "Configurações", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="subjects"
        options={{ title: "Suas Matérias", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/[id]/study"
        options={{ title: "Estudar Flashcards", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/[id]/study-enhanced"
        options={{ title: "Estudo Avançado", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/[id]/study-fixed"
        options={{ title: "Estudo Fixo", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/[id]/study-new"
        options={{ title: "Novos Flashcards", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/[id]/study-simple"
        options={{ title: "Estudo Simples", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/review"
        options={{ title: "Revisão de Flashcards", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards/new-set"
        options={{ title: "Novo Conjunto", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="quiz/create"
        options={{ title: "Criar Quiz", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="quiz/edit/[id]"
        options={{ title: "Editar Quiz", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="quiz-stats/[id]"
        options={{ title: "Estatísticas do Quiz", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="events/[id]"
        options={{ title: "Evento", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="events/new"
        options={{ title: "Novo Evento", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="events/edit/[id]"
        options={{ title: "Editar Evento", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="tasks/[id]"
        options={{ title: "Tarefa", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="tasks/new"
        options={{ title: "Nova Tarefa", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="tasks/edit/[id]"
        options={{ title: "Editar Tarefa", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="schedules/create"
        options={{ title: "Criar Cronograma", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="schedules/index"
        options={{ title: "Cronogramas", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="notes/index"
        options={{ title: "Anotações", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="statistics"
        options={{ title: "Estatísticas", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="study-tips"
        options={{ title: "Dicas de Estudo", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="text-generator"
        options={{ title: "Gerador de Texto", animation: "slide_from_right" }}
      />
      <Stack.Screen
        name="flashcards-from-text"
        options={{ title: "Flashcards do Texto", animation: "slide_from_right" }}
      />

    </Stack>
    </GestureHandlerRootView>
  );
}