import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Alert,
  ActivityIndicator,
  FlatList,
  Switch,
  TextInput
} from 'react-native';
import DraggableFlatList, { ScaleDecorator } from 'react-native-draggable-flatlist';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { Header } from '@/components/Header';
import { Button } from '@/components/Button';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassCard } from '@/components/GlassCard';
import { RouteGuard } from '@/components/RouteGuard';
import {
  Calendar,
  Clock,
  BookOpen,
  Plus,
  ArrowLeft,
  Trash2,
  Edit,
  CalendarDays,
  CheckCircle,
  AlertTriangle,
  Calendar as CalendarIcon,
  ClipboardList,
  AlarmClock,
  BarChart3,
  BookMarked,
  Pencil,
  GraduationCap,
  Brain,
  Calculator,
  FileText,
  Lightbulb,
  Microscope,
  Atom,
  Globe,
  Palette,
  Music,
  Code,
  Dumbbell,
  Info,
  <PERSON>,
  Setting<PERSON>,
  Eye
} from 'lucide-react-native';
import { useScheduleStore } from '@/store/scheduleStore';
import { useCalendarStore } from '@/store/calendarStore';
import { format, parseISO, addDays, addWeeks, addMonths, differenceInDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Schedule, ScheduleItem, CalendarEvent, TodoItem } from '@/types';
import { ScheduleItemForm } from '@/components/ScheduleItemForm';
import { ScheduleEventForm } from '@/components/ScheduleEventForm';
import { ScheduleTodoForm } from '@/components/ScheduleTodoForm';
import { ScheduleCalendarView } from '@/components/ScheduleCalendarView';

export default function ScheduleDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const {
    schedules,
    scheduleItems,
    fetchSchedules,
    fetchScheduleItems,
    updateSchedule,
    deleteSchedule,
    addScheduleItem,
    updateScheduleItem,
    deleteScheduleItem,
    applySchedule,
    updateItemsOrder,
    loading,
    set
  } = useScheduleStore();

  const {
    addEvent,
    addTodo
  } = useCalendarStore();

  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [items, setItems] = useState<ScheduleItem[]>([]);
  const [showItemForm, setShowItemForm] = useState(false);
  const [showEventForm, setShowEventForm] = useState(false);
  const [showTodoForm, setShowTodoForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ScheduleItem | undefined>(undefined);
  const [selectedItem, setSelectedItem] = useState<ScheduleItem | undefined>(undefined);
  const [applying, setApplying] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'preview'>('list');
  const [previewEvents, setPreviewEvents] = useState<CalendarEvent[]>([]);
  const [previewTodos, setPreviewTodos] = useState<TodoItem[]>([]);
  const [applyPeriod, setApplyPeriod] = useState<'week' | 'month' | 'custom'>('week');
  const [customDays, setCustomDays] = useState('30');
  const [showPreviewOptions, setShowPreviewOptions] = useState(false);

  useEffect(() => {
    if (!id) {
      router.back();
      return;
    }

    loadSchedule();
  }, [id]);

  const loadSchedule = async () => {
    // Fetch schedules if not already loaded
    if (schedules.length === 0) {
      await fetchSchedules();
    }

    // Find the schedule by id
    const foundSchedule = schedules.find(s => s.id === id);
    if (!foundSchedule) {
      Alert.alert('Erro', 'Cronograma não encontrado');
      router.back();
      return;
    }

    setSchedule(foundSchedule);

    // Fetch schedule items
    const scheduleItems = await fetchScheduleItems(foundSchedule.id);
    setItems(scheduleItems);
  };

  const handleEditSchedule = () => {
    if (!schedule) return;

    Alert.alert(
      'Editar Cronograma',
      'O que você deseja fazer?',
      [
        {
          text: 'Cancelar',
          style: 'cancel'
        },
        {
          text: 'Editar Detalhes',
          onPress: () => {
            router.push(`/schedules/edit/${schedule.id}`);
          }
        },
        {
          text: 'Ver Estatísticas',
          onPress: () => {
            router.push('/schedules/stats');
          }
        },
        {
          text: schedule.active ? 'Desativar' : 'Ativar',
          onPress: async () => {
            if (!schedule) return;

            const updatedSchedule = await updateSchedule(schedule.id, {
              active: !schedule.active
            });

            if (updatedSchedule) {
              setSchedule(updatedSchedule);
              Alert.alert(
                updatedSchedule.active ? 'Cronograma Ativado' : 'Cronograma Desativado',
                updatedSchedule.active
                  ? 'O cronograma foi ativado com sucesso. Agora você pode aplicá-lo ao calendário.'
                  : 'O cronograma foi desativado. Ele não será aplicado automaticamente.'
              );
            }
          }
        },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: handleDeleteSchedule
        }
      ]
    );
  };

  const handleDeleteSchedule = () => {
    if (!schedule) return;

    Alert.alert(
      'Excluir Cronograma',
      `Tem certeza que deseja excluir o cronograma "${schedule.title}"? Esta ação não pode ser desfeita.\n\nTodos os itens associados a este cronograma serão excluídos, mas os eventos e tarefas já criados no calendário permanecerão.`,
      [
        {
          text: 'Cancelar',
          style: 'cancel'
        },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            setDeleting(true);
            try {
              const success = await deleteSchedule(schedule.id);
              if (success) {
                Alert.alert('Cronograma Excluído', 'O cronograma foi excluído com sucesso.');
                router.back();
              } else {
                Alert.alert('Erro', 'Ocorreu um erro ao excluir o cronograma.');
              }
            } catch (error) {
              console.error('Error deleting schedule:', error);
              Alert.alert('Erro', 'Ocorreu um erro ao excluir o cronograma.');
            } finally {
              setDeleting(false);
            }
          }
        }
      ]
    );
  };

  const handleAddItem = () => {
    setEditingItem(undefined);
    setShowItemForm(true);
  };

  const handleEditItem = (item: ScheduleItem) => {
    setEditingItem(item);
    setShowItemForm(true);
  };

  const handleDeleteItem = (item: ScheduleItem) => {
    Alert.alert(
      'Excluir Item',
      'Tem certeza que deseja excluir este item do cronograma?',
      [
        {
          text: 'Cancelar',
          style: 'cancel'
        },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteScheduleItem(item.id);
            if (success) {
              setItems(items.filter(i => i.id !== item.id));
            }
          }
        }
      ]
    );
  };

  const handleSaveItem = async (itemData: Omit<ScheduleItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingItem) {
      // Update existing item
      const updatedItem = await updateScheduleItem(editingItem.id, itemData);
      if (updatedItem) {
        setItems(items.map(item => item.id === editingItem.id ? updatedItem : item));
      }
    } else {
      // Add new item
      const newItem = await addScheduleItem(itemData);
      if (newItem) {
        setItems([...items, newItem]);
      }
    }
  };

  const handleCreateEvent = (item: ScheduleItem) => {
    setSelectedItem(item);
    // Usar requestAnimationFrame para garantir que a UI seja atualizada antes de mostrar o modal
    requestAnimationFrame(() => {
      setShowEventForm(true);
    });
  };

  const handleCreateTodo = (item: ScheduleItem) => {
    setSelectedItem(item);
    // Usar requestAnimationFrame para garantir que a UI seja atualizada antes de mostrar o modal
    requestAnimationFrame(() => {
      setShowTodoForm(true);
    });
  };

  const handleSaveEvent = async (eventData: any) => {
    // Implementação para salvar o evento no calendário
    try {
      // Mostrar indicador de carregamento
      set({ loading: true });

      const savedEvent = await addEvent(eventData);

      if (savedEvent) {
        Alert.alert('Sucesso', 'Evento criado com sucesso no calendário.');
        setShowEventForm(false);
      }

      // Esconder indicador de carregamento
      set({ loading: false });
    } catch (error) {
      console.error('Error creating event:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao criar o evento.');
      set({ loading: false });
    }
  };

  const handleSaveTodo = async (todoData: any) => {
    // Implementação para salvar a tarefa no calendário
    try {
      // Mostrar indicador de carregamento
      set({ loading: true });

      const savedTodo = await addTodo(todoData);

      if (savedTodo) {
        Alert.alert('Sucesso', 'Tarefa de revisão criada com sucesso.');
        setShowTodoForm(false);
      }

      // Esconder indicador de carregamento
      set({ loading: false });
    } catch (error) {
      console.error('Error creating todo:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao criar a tarefa de revisão.');
      set({ loading: false });
    }
  };

  const generatePreview = async () => {
    if (!schedule) return;

    if (items.length === 0) {
      Alert.alert('Erro', 'Adicione pelo menos um item ao cronograma antes de gerar a prévia.');
      return;
    }

    setApplying(true);

    try {
      // Calculate date range based on selected period
      const startDate = new Date();
      let endDate = new Date();

      switch (applyPeriod) {
        case 'week':
          endDate = addDays(startDate, 7);
          break;
        case 'month':
          endDate = addMonths(startDate, 1);
          break;
        case 'custom':
          const days = parseInt(customDays);
          if (isNaN(days) || days <= 0) {
            Alert.alert('Erro', 'O número de dias deve ser um valor positivo.');
            setApplying(false);
            return;
          }
          endDate = addDays(startDate, days);
          break;
      }

      // Generate preview events and todos
      const previewData = await generateSchedulePreview(schedule.id, startDate, endDate);
      setPreviewEvents(previewData.events);
      setPreviewTodos(previewData.todos);

      // Switch to preview mode
      setViewMode('preview');

      Alert.alert(
        'Prévia Gerada',
        `Foram gerados ${previewData.events.length} eventos e ${previewData.todos.length} tarefas para o período selecionado.\n\nEsta é apenas uma prévia. Nada foi salvo no calendário ainda.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error generating preview:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao gerar a prévia do cronograma.');
    } finally {
      setApplying(false);
    }
  };

  const generateSchedulePreview = async (scheduleId: string, startDate: Date, endDate: Date) => {
    // This is a simplified version of the applySchedule function that doesn't save to the database
    const previewEvents: CalendarEvent[] = [];
    const previewTodos: TodoItem[] = [];

    if (!schedule) return { events: previewEvents, todos: previewTodos };

    // Get the schedule items
    const scheduleItems = items;
    if (scheduleItems.length === 0) {
      return { events: previewEvents, todos: previewTodos };
    }

    // Calculate the number of days in the range
    const totalDays = differenceInDays(endDate, startDate) + 1;

    // Generate events for each day in the range
    for (let i = 0; i < totalDays; i++) {
      const currentDate = addDays(startDate, i);
      const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dayOfMonth = currentDate.getDate(); // 1-31

      // Find items that match this day
      for (const item of scheduleItems) {
        let shouldCreateEvent = false;

        // Check if this item applies to this day
        if (schedule.type === 'weekly' && item.dayOfWeek !== undefined && item.dayOfWeek === dayOfWeek) {
          shouldCreateEvent = true;
        } else if (schedule.type === 'monthly' && item.dayOfMonth !== undefined && item.dayOfMonth === dayOfMonth) {
          shouldCreateEvent = true;
        } else if (schedule.type === 'custom' && item.specificDate) {
          const itemDate = parseISO(item.specificDate);
          if (itemDate.getDate() === currentDate.getDate() &&
              itemDate.getMonth() === currentDate.getMonth() &&
              itemDate.getFullYear() === currentDate.getFullYear()) {
            shouldCreateEvent = true;
          }
        }

        if (shouldCreateEvent) {
          // Create a preview event
          if (item.createEvent) {
            try {
              // Parse the time strings
              const startTime = parseISO(item.startTime);
              const endTime = parseISO(item.endTime);

              // Create date objects for this specific day
              const eventStartDate = new Date(currentDate);
              eventStartDate.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);

              const eventEndDate = new Date(currentDate);
              eventEndDate.setHours(endTime.getHours(), endTime.getMinutes(), 0, 0);

              // Create the event object
              const event: CalendarEvent = {
                id: `preview-${i}-${item.id}-event`,
                title: `Estudar ${item.subjectTitle}`,
                description: `Sessão de estudo para ${item.subjectTitle} (Cronograma: ${schedule.title})`,
                startDate: eventStartDate.toISOString(),
                endDate: eventEndDate.toISOString(),
                allDay: false,
                color: item.color || '#4F46E5',
                subject: item.subjectTitle,
                subject_id: item.subjectId,
                type: 'study',
                completed: false,
                reminder: true,
                reminderTime: new Date(eventStartDate.getTime() - 30 * 60000).toISOString(), // 30 minutes before
                recurrence: schedule.repeatWeekly ? 'weekly' : (schedule.repeatMonthly ? 'monthly' : 'none'),
                has_todos: item.createTodo,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };

              previewEvents.push(event);

              // Create a todo if needed
              if (item.createTodo) {
                const todo: TodoItem = {
                  id: `preview-${i}-${item.id}-todo`,
                  title: `Revisar ${item.subjectTitle}`,
                  description: `Tarefa de revisão para ${item.subjectTitle} (Cronograma: ${schedule.title})`,
                  dueDate: addDays(eventEndDate, 1).toISOString(), // Due the next day
                  priority: 'medium',
                  completed: false,
                  subject: item.subjectTitle,
                  subject_id: item.subjectId,
                  event_id: `preview-${i}-${item.id}-event`,
                  tags: ['revisão', 'cronograma'],
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                };

                previewTodos.push(todo);
              }
            } catch (error) {
              console.error('Error creating preview event:', error);
            }
          }
        }
      }
    }

    return { events: previewEvents, todos: previewTodos };
  };

  const handleApplySchedule = async () => {
    if (!schedule) return;

    if (items.length === 0) {
      Alert.alert('Erro', 'Adicione pelo menos um item ao cronograma antes de aplicá-lo.');
      return;
    }

    // Ask for confirmation with period selection
    Alert.alert(
      'Aplicar Cronograma',
      `Você está prestes a aplicar o cronograma "${schedule.title}" ao seu calendário. Isso criará eventos e tarefas com base nos itens configurados.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Aplicar',
          onPress: async () => {
            setApplying(true);

            try {
              // Calculate date range based on selected period
              const startDate = new Date();
              let endDate = new Date();

              switch (applyPeriod) {
                case 'week':
                  endDate = addDays(startDate, 7);
                  break;
                case 'month':
                  endDate = addMonths(startDate, 1);
                  break;
                case 'custom':
                  const days = parseInt(customDays);
                  if (isNaN(days) || days <= 0) {
                    Alert.alert('Erro', 'O número de dias deve ser um valor positivo.');
                    setApplying(false);
                    return;
                  }
                  endDate = addDays(startDate, days);
                  break;
              }

              const result = await applySchedule(schedule.id, startDate, endDate);

              Alert.alert(
                'Cronograma Aplicado',
                `Foram criados ${result.events} eventos e ${result.todos} tarefas com base neste cronograma.\n\nNotificações agendadas: ${result.notifications?.events || 0} para eventos e ${result.notifications?.todos || 0} para tarefas.`,
                [{ text: 'OK' }]
              );
            } catch (error) {
              console.error('Error applying schedule:', error);
              Alert.alert('Erro', 'Ocorreu um erro ao aplicar o cronograma.');
            } finally {
              setApplying(false);
            }
          }
        }
      ]
    );
  };

  const getScheduleTypeText = (type: string) => {
    switch (type) {
      case 'weekly': return 'Semanal';
      case 'monthly': return 'Mensal';
      case '30days': return '30 Dias';
      case 'custom': return 'Personalizado';
      default: return type;
    }
  };

  const getDayText = (item: ScheduleItem) => {
    if (item.specificDate) {
      try {
        const date = parseISO(item.specificDate);

        // Verificar se a data é válida
        if (isNaN(date.getTime())) {
          console.warn('Data específica inválida:', item.specificDate);
          return 'Data inválida';
        }

        return format(date, "dd 'de' MMMM", { locale: ptBR });
      } catch (error) {
        console.error('Erro ao formatar data específica:', error, { specificDate: item.specificDate });
        return 'Data inválida';
      }
    }

    if (item.dayOfWeek !== undefined) {
      const days = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
      // Verificar se o índice é válido
      if (item.dayOfWeek < 0 || item.dayOfWeek > 6) {
        console.warn('Dia da semana inválido:', item.dayOfWeek);
        return 'Dia inválido';
      }
      return days[item.dayOfWeek];
    }

    if (item.dayOfMonth !== undefined) {
      // Verificar se o dia do mês é válido
      if (item.dayOfMonth < 1 || item.dayOfMonth > 31) {
        console.warn('Dia do mês inválido:', item.dayOfMonth);
        return 'Dia inválido';
      }
      return `Dia ${item.dayOfMonth}`;
    }

    return 'Dia não especificado';
  };

  const getTimeText = (item: ScheduleItem) => {
    if (!item.startTime || !item.endTime) return '';

    try {
      // Validar se as strings de data são válidas
      const startDate = parseISO(item.startTime);
      const endDate = parseISO(item.endTime);

      // Verificar se as datas são válidas
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.warn('Data inválida detectada:', { startTime: item.startTime, endTime: item.endTime });
        return 'Horário não especificado';
      }

      const start = format(startDate, 'HH:mm');
      const end = format(endDate, 'HH:mm');

      return `${start} - ${end}`;
    } catch (error) {
      console.error('Erro ao formatar horário:', error, { startTime: item.startTime, endTime: item.endTime });
      return 'Horário não especificado';
    }
  };

  const renderItemIcon = (item: ScheduleItem) => {
    const iconName = item.icon || 'book-open';
    const iconColor = item.color || colors.primary;
    const iconSize = 20;

    const iconMap: Record<string, React.ReactNode> = {
      'book-open': <BookOpen size={iconSize} color={iconColor} />,
      'book-marked': <BookMarked size={iconSize} color={iconColor} />,
      'pencil': <Pencil size={iconSize} color={iconColor} />,
      'graduation-cap': <GraduationCap size={iconSize} color={iconColor} />,
      'brain': <Brain size={iconSize} color={iconColor} />,
      'calculator': <Calculator size={iconSize} color={iconColor} />,
      'file-text': <FileText size={iconSize} color={iconColor} />,
      'lightbulb': <Lightbulb size={iconSize} color={iconColor} />,
      'microscope': <Microscope size={iconSize} color={iconColor} />,
      'atom': <Atom size={iconSize} color={iconColor} />,
      'globe': <Globe size={iconSize} color={iconColor} />,
      'palette': <Palette size={iconSize} color={iconColor} />,
      'music': <Music size={iconSize} color={iconColor} />,
      'code': <Code size={iconSize} color={iconColor} />,
      'dumbbell': <Dumbbell size={iconSize} color={iconColor} />
    };

    return iconMap[iconName] || iconMap['book-open'];
  };

  const handleDragEnd = async ({ data }: { data: ScheduleItem[] }) => {
    setItems(data);

    // Persistir a nova ordem no banco de dados
    try {
      const success = await updateItemsOrder(data);
      if (!success) {
        console.error('Falha ao atualizar a ordem dos itens');
      }
    } catch (error) {
      console.error('Erro ao atualizar a ordem dos itens:', error);
    }
  };

  const renderScheduleItem = ({ item, drag, isActive }: { item: ScheduleItem, drag: () => void, isActive: boolean }) => {
    return (
      <ScaleDecorator>
        <Pressable onLongPress={drag} disabled={isActive}>
          <GlassCard style={[
            styles.itemCard,
            { borderLeftColor: item.color || colors.primary, borderLeftWidth: 4 },
            isActive && styles.itemCardActive
          ]}>
            <View style={styles.itemHeader}>
              <View style={styles.itemTitleContainer}>
                {renderItemIcon(item)}
                <Text style={styles.itemTitle}>{item.subjectTitle}</Text>
              </View>
              <View style={styles.itemActions}>
                <Pressable
                  style={styles.actionButton}
                  onPress={() => handleEditItem(item)}
                >
                  <Edit size={18} color={colors.primary} />
                </Pressable>
                <Pressable
                  style={styles.actionButton}
                  onPress={() => handleDeleteItem(item)}
                >
                  <Trash2 size={18} color={colors.error} />
                </Pressable>
              </View>
            </View>

            <View style={styles.itemDetails}>
              <View style={styles.itemDetail}>
                <Calendar size={16} color={colors.textLight} />
                <Text style={styles.itemDetailText}>{getDayText(item)}</Text>
              </View>

              <View style={styles.itemDetail}>
                <Clock size={16} color={colors.textLight} />
                <Text style={styles.itemDetailText}>{getTimeText(item)}</Text>
              </View>

              {item.createEvent && (
                <View style={styles.itemDetail}>
                  <AlarmClock size={16} color={colors.success} />
                  <Text style={styles.itemDetailText}>Cria evento</Text>
                </View>
              )}

              {item.createTodo && (
                <View style={styles.itemDetail}>
                  <ClipboardList size={16} color={colors.success} />
                  <Text style={styles.itemDetailText}>Cria tarefa</Text>
                </View>
              )}
            </View>

            <View style={styles.itemActions}>
              <Button
                title="Criar Evento"
                onPress={() => handleCreateEvent(item)}
                variant="outline"
                size="small"
                icon={AlarmClock}
                style={styles.itemActionButton}
              />
              <Button
                title="Criar Tarefa"
                onPress={() => handleCreateTodo(item)}
                variant="outline"
                size="small"
                icon={ClipboardList}
                style={styles.itemActionButton}
              />
            </View>
          </GlassCard>
        </Pressable>
      </ScaleDecorator>
    );
  };

  if (!schedule) {
    return (
      <View style={styles.container}>
        <Header
          title="Detalhes do Cronograma"
          leftIcon={ArrowLeft}
          onLeftPress={() => router.back()}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </View>
    );
  }

  return (
    <RouteGuard resourceId={id as string} tableName="schedules">
      <View style={styles.container}>
        <LinearGradient
          colors={['rgba(73, 49, 234, 0.2)', 'rgba(73, 49, 234, 0)']}
          style={styles.gradient}
        />

        <Header
          title="Detalhes do Cronograma"
          leftIcon={ArrowLeft}
          onLeftPress={() => router.back()}
          rightComponent={
          <View style={styles.headerButtons}>
            <Pressable
              style={styles.headerButton}
              onPress={() => router.push(`/schedules/edit/${schedule?.id}`)}
            >
              <Edit size={20} color={colors.text} />
            </Pressable>
            <Pressable
              style={[styles.headerButton, styles.deleteButton]}
              onPress={handleDeleteSchedule}
              disabled={deleting}
            >
              <Trash2 size={20} color={deleting ? colors.textLight : colors.error} />
            </Pressable>
          </View>
        }
      />

      <View style={styles.content}>
        <GlassCard style={styles.scheduleCard}>
          <View style={styles.scheduleHeader}>
            <Text style={styles.scheduleTitle}>{schedule.title}</Text>
            <View style={[styles.scheduleStatusBadge, schedule.active ? styles.activeStatusBadge : styles.inactiveStatusBadge]}>
              <Text style={[styles.scheduleStatusText, schedule.active ? styles.activeStatusText : styles.inactiveStatusText]}>
                {schedule.active ? 'Ativo' : 'Inativo'}
              </Text>
            </View>
          </View>

          {schedule.description && (
            <Text style={styles.scheduleDescription}>{schedule.description}</Text>
          )}

          <View style={styles.scheduleDetails}>
            <View style={styles.scheduleDetail}>
              <CalendarDays size={16} color={colors.textLight} />
              <Text style={styles.scheduleDetailText}>
                {getScheduleTypeText(schedule.type)}
              </Text>
            </View>

            {schedule.startDate && (
              <View style={styles.scheduleDetail}>
                <Calendar size={16} color={colors.textLight} />
                <Text style={styles.scheduleDetailText}>
                  Início: {
                    (() => {
                      try {
                        const date = parseISO(schedule.startDate);
                        if (isNaN(date.getTime())) return 'Data inválida';
                        return format(date, 'dd/MM/yyyy', { locale: ptBR });
                      } catch (error) {
                        console.error('Erro ao formatar data de início:', error);
                        return 'Data inválida';
                      }
                    })()
                  }
                </Text>
              </View>
            )}

            {schedule.endDate && (
              <View style={styles.scheduleDetail}>
                <Calendar size={16} color={colors.textLight} />
                <Text style={styles.scheduleDetailText}>
                  Fim: {
                    (() => {
                      try {
                        const date = parseISO(schedule.endDate);
                        if (isNaN(date.getTime())) return 'Data inválida';
                        return format(date, 'dd/MM/yyyy', { locale: ptBR });
                      } catch (error) {
                        console.error('Erro ao formatar data de fim:', error);
                        return 'Data inválida';
                      }
                    })()
                  }
                </Text>
              </View>
            )}

            {schedule.repeatWeekly && (
              <View style={styles.scheduleDetail}>
                <CheckCircle size={16} color={colors.success} />
                <Text style={styles.scheduleDetailText}>Repete semanalmente</Text>
              </View>
            )}

            {schedule.repeatMonthly && (
              <View style={styles.scheduleDetail}>
                <CheckCircle size={16} color={colors.success} />
                <Text style={styles.scheduleDetailText}>Repete mensalmente</Text>
              </View>
            )}

            <View style={styles.scheduleDetail}>
              <AlertTriangle size={16} color={schedule.active ? colors.success : colors.error} />
              <Text style={styles.scheduleDetailText}>
                {schedule.active ? 'Ativo' : 'Inativo'}
              </Text>
            </View>
          </View>
        </GlassCard>

        <View style={styles.itemsHeader}>
          <View style={styles.headerLeft}>
            <Text style={styles.itemsTitle}>Itens do Cronograma</Text>
            {/* Visualização de lista apenas */}
          </View>
          <View style={styles.headerButtonsRow}>
            <Button
              title="Novo Evento"
              onPress={() => {
                setSelectedItem(undefined);
                requestAnimationFrame(() => {
                  setShowEventForm(true);
                });
              }}
              variant="outline"
              size="small"
              icon={AlarmClock}
              style={styles.headerActionButton}
            />
            <Button
              title="Nova Tarefa"
              onPress={() => {
                setSelectedItem(undefined);
                requestAnimationFrame(() => {
                  setShowTodoForm(true);
                });
              }}
              variant="outline"
              size="small"
              icon={ClipboardList}
              style={styles.headerActionButton}
            />
            <Button
              title="Adicionar Item"
              onPress={handleAddItem}
              variant="primary"
              size="small"
              icon={Plus}
            />
          </View>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : items.length > 0 ? (
          <DraggableFlatList
            data={items}
            renderItem={renderScheduleItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.itemsList}
            showsVerticalScrollIndicator={false}
            onDragEnd={handleDragEnd}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <CalendarDays size={48} color={colors.textLight} />
            <Text style={styles.emptyText}>
              Nenhum item adicionado a este cronograma.
            </Text>
            <Text style={styles.emptySubtext}>
              Adicione itens para definir quando estudar cada matéria.
            </Text>
          </View>
        )}

        <GlassCard style={styles.applyCard}>
          <Text style={styles.applyCardTitle}>Aplicar Cronograma</Text>
          <Text style={styles.applyCardDescription}>
            Defina por quanto tempo deseja aplicar este cronograma ao seu calendário.
            Isso criará eventos e tarefas com base nos itens configurados.
          </Text>

          <View style={styles.periodSelector}>
            <Text style={styles.periodSelectorLabel}>Período:</Text>
            <View style={styles.periodOptions}>
              <Pressable
                style={[styles.periodOption, applyPeriod === 'week' && styles.periodOptionSelected]}
                onPress={() => setApplyPeriod('week')}
              >
                <Text style={[styles.periodOptionText, applyPeriod === 'week' && styles.periodOptionTextSelected]}>
                  1 Semana
                </Text>
              </Pressable>

              <Pressable
                style={[styles.periodOption, applyPeriod === 'month' && styles.periodOptionSelected]}
                onPress={() => setApplyPeriod('month')}
              >
                <Text style={[styles.periodOptionText, applyPeriod === 'month' && styles.periodOptionTextSelected]}>
                  1 Mês
                </Text>
              </Pressable>

              <Pressable
                style={[styles.periodOption, applyPeriod === 'custom' && styles.periodOptionSelected]}
                onPress={() => setApplyPeriod('custom')}
              >
                <Text style={[styles.periodOptionText, applyPeriod === 'custom' && styles.periodOptionTextSelected]}>
                  Personalizado
                </Text>
              </Pressable>
            </View>
          </View>

          {applyPeriod === 'custom' && (
            <View style={styles.customDaysContainer}>
              <Text style={styles.customDaysLabel}>Número de dias:</Text>
              <TextInput
                style={styles.customDaysInput}
                value={customDays}
                onChangeText={setCustomDays}
                keyboardType="number-pad"
                maxLength={3}
              />
            </View>
          )}

          <View style={styles.applyActions}>
            <Button
              title="Gerar Prévia"
              onPress={generatePreview}
              variant="outline"
              size="medium"
              icon={Eye}
              disabled={items.length === 0 || applying || loading}
              loading={applying && viewMode === 'list'}
              style={styles.previewButton}
            />

            <Button
              title={applying ? 'Aplicando...' : 'Aplicar Agora'}
              onPress={handleApplySchedule}
              variant="primary"
              size="medium"
              icon={Play}
              disabled={items.length === 0 || applying || loading}
              loading={applying && viewMode === 'preview'}
            />
          </View>
        </GlassCard>

        {viewMode === 'preview' && previewEvents.length > 0 && (
          <GlassCard style={styles.previewCard}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewTitle}>Prévia do Cronograma</Text>
              <Pressable
                style={styles.closePreviewButton}
                onPress={() => setViewMode('list')}
              >
                <Text style={styles.closePreviewText}>Fechar</Text>
              </Pressable>
            </View>

            <Text style={styles.previewDescription}>
              Esta é uma prévia dos eventos que serão criados ao aplicar o cronograma.
              Nenhum evento foi salvo no calendário ainda.
            </Text>

            <Text style={styles.previewSectionTitle}>Eventos ({previewEvents.length})</Text>
            <ScrollView style={styles.previewList}>
              {previewEvents.map((event, index) => (
                <View key={event.id} style={styles.previewItem}>
                  <View style={[styles.previewItemColor, { backgroundColor: event.color }]} />
                  <View style={styles.previewItemContent}>
                    <Text style={styles.previewItemTitle}>{event.title}</Text>
                    <Text style={styles.previewItemDate}>
                      {format(parseISO(event.startDate), "EEEE, dd 'de' MMMM", { locale: ptBR })}
                    </Text>
                    <Text style={styles.previewItemTime}>
                      {format(parseISO(event.startDate), 'HH:mm')} - {format(parseISO(event.endDate), 'HH:mm')}
                    </Text>
                  </View>
                </View>
              ))}
            </ScrollView>

            {previewTodos.length > 0 && (
              <>
                <Text style={styles.previewSectionTitle}>Tarefas ({previewTodos.length})</Text>
                <ScrollView style={styles.previewList}>
                  {previewTodos.map((todo, index) => (
                    <View key={todo.id} style={styles.previewItem}>
                      <View style={[styles.previewItemColor, { backgroundColor:
                        todo.priority === 'high' ? colors.error :
                        todo.priority === 'medium' ? colors.warning :
                        colors.success
                      }]} />
                      <View style={styles.previewItemContent}>
                        <Text style={styles.previewItemTitle}>{todo.title}</Text>
                        {todo.dueDate && (
                          <Text style={styles.previewItemDate}>
                            Vencimento: {format(parseISO(todo.dueDate), "dd/MM/yyyy", { locale: ptBR })}
                          </Text>
                        )}
                        <Text style={styles.previewItemPriority}>
                          Prioridade: {todo.priority === 'high' ? 'Alta' : todo.priority === 'medium' ? 'Média' : 'Baixa'}
                        </Text>
                      </View>
                    </View>
                  ))}
                </ScrollView>
              </>
            )}

            <Button
              title="Aplicar Cronograma"
              onPress={handleApplySchedule}
              variant="primary"
              size="large"
              icon={Play}
              disabled={applying || loading}
              loading={applying}
              style={styles.applyPreviewButton}
            />
          </GlassCard>
        )}
      </View>

      {schedule && (
        <ScheduleItemForm
          visible={showItemForm}
          onClose={() => setShowItemForm(false)}
          onSave={handleSaveItem}
          initialItem={editingItem}
          schedule={schedule}
        />
      )}

      {showEventForm && (
        <ScheduleEventForm
          visible={true}
          onClose={() => setShowEventForm(false)}
          onSave={handleSaveEvent}
          scheduleTitle={schedule?.title || ''}
          selectedDate={(() => {
            try {
              // Tentar usar a data específica se disponível
              if (selectedItem?.specificDate) {
                const date = parseISO(selectedItem.specificDate);
                if (!isNaN(date.getTime())) {
                  return selectedItem.specificDate;
                }
              }
              // Fallback para a data atual
              return new Date().toISOString();
            } catch (error) {
              console.error('Erro ao processar data para evento:', error);
              return new Date().toISOString();
            }
          })()}
        />
      )}

      {showTodoForm && (
        <ScheduleTodoForm
          visible={true}
          onClose={() => setShowTodoForm(false)}
          onSave={handleSaveTodo}
          scheduleTitle={schedule?.title || ''}
          eventDate={(() => {
            try {
              // Tentar usar a hora de início se disponível
              if (selectedItem?.startTime) {
                const date = parseISO(selectedItem.startTime);
                if (!isNaN(date.getTime())) {
                  return date;
                }
              }
              // Fallback para a data atual
              return new Date();
            } catch (error) {
              console.error('Erro ao processar data para tarefa:', error);
              return new Date();
            }
          })()}
        />
      )}
    </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginLeft: 8,
  },
  deleteButton: {
    backgroundColor: `${colors.error}10`,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scheduleCard: {
    padding: 16,
    marginBottom: 20,
  },
  scheduleTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  scheduleDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 16,
  },
  scheduleDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  scheduleDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  scheduleDetailText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
    marginRight: 10,
  },
  headerButtonsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
  },
  headerActionButton: {
    marginRight: 8,
  },
  viewToggle: {
    flexDirection: 'row',
    marginTop: 8,
  },
  viewToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  viewToggleButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  viewToggleText: {
    fontSize: 12,
    color: colors.text,
    marginLeft: 4,
  },
  viewToggleTextActive: {
    color: colors.white,
  },
  itemsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  itemsList: {
    paddingBottom: 80,
  },
  itemCard: {
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  itemCardActive: {
    opacity: 0.9,
    transform: [{ scale: 1.05 }],
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginLeft: 8,
  },
  itemActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 5,
    marginLeft: 10,
  },
  itemDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  itemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  itemDetailText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 8,
    textAlign: 'center',
    marginBottom: 20,
  },
  applyCard: {
    padding: 16,
    marginTop: 20,
    marginBottom: 100,
  },
  applyCardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  applyCardDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 16,
  },
  periodSelector: {
    marginBottom: 16,
  },
  periodSelectorLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  periodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  periodOption: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  periodOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  periodOptionText: {
    fontSize: 12,
    color: colors.text,
  },
  periodOptionTextSelected: {
    color: colors.white,
    fontWeight: 'bold',
  },
  customDaysContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  customDaysLabel: {
    fontSize: 14,
    color: colors.text,
    marginRight: 8,
  },
  customDaysInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    width: 80,
    fontSize: 14,
    color: colors.text,
  },
  applyActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  previewButton: {
    marginRight: 8,
  },
  previewCard: {
    padding: 16,
    marginTop: 20,
    marginBottom: 100,
  },
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  closePreviewButton: {
    padding: 8,
  },
  closePreviewText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: 'bold',
  },
  previewDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 16,
  },
  previewSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  previewList: {
    maxHeight: 200,
    marginBottom: 16,
  },
  previewItem: {
    flexDirection: 'row',
    marginBottom: 12,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    overflow: 'hidden',
  },
  previewItemColor: {
    width: 6,
    backgroundColor: colors.primary,
  },
  previewItemContent: {
    padding: 12,
    flex: 1,
  },
  previewItemTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  previewItemDate: {
    fontSize: 12,
    color: colors.textLight,
    marginBottom: 2,
  },
  previewItemTime: {
    fontSize: 12,
    color: colors.textLight,
  },
  previewItemPriority: {
    fontSize: 12,
    color: colors.textLight,
  },
  applyPreviewButton: {
    marginTop: 8,
  },
  itemActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  itemActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scheduleStatusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeStatusBadge: {
    backgroundColor: `${colors.success}20`,
  },
  inactiveStatusBadge: {
    backgroundColor: `${colors.error}20`,
  },
  scheduleStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  activeStatusText: {
    color: colors.success,
  },
  inactiveStatusText: {
    color: colors.error,
  },
});
