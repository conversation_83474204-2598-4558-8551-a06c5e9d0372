import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  TextInput,
  Alert,
  Switch,
  Platform
} from 'react-native';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { Header } from '@/components/Header';
import { Button } from '@/components/Button';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassCard } from '@/components/GlassCard';
import { Calendar, ArrowLeft, Save, CalendarDays, Calendar as CalendarIcon, Clock } from 'lucide-react-native';
import { useScheduleStore } from '@/store/scheduleStore';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function CreateScheduleScreen() {
  const router = useRouter();
  const { createSchedule } = useScheduleStore();

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'weekly' | 'monthly' | '30days' | 'custom'>('weekly');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [repeatWeekly, setRepeatWeekly] = useState(false);
  const [repeatMonthly, setRepeatMonthly] = useState(false);
  const [active, setActive] = useState(true);
  const [color, setColor] = useState(colors.primary);
  const [reminderEnabled, setReminderEnabled] = useState(true);
  const [reminderTime, setReminderTime] = useState(30); // minutes before
  const [applyImmediately, setApplyImmediately] = useState(false);
  const [applying, setApplying] = useState(false);

  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título do cronograma é obrigatório.');
      return;
    }

    try {
      const newSchedule = {
        title,
        description,
        type,
        startDate: startDate.toISOString(),
        endDate: endDate ? endDate.toISOString() : undefined,
        repeatWeekly,
        repeatMonthly,
        active,
        settings: {
          color,
          reminderEnabled,
          reminderTime,
          defaultDuration: 60, // Default study session duration in minutes
          createTodosByDefault: true,
          createEventsByDefault: true,
          applyImmediately
        }
      };

      const savedSchedule = await createSchedule(newSchedule);

      if (savedSchedule) {
        if (applyImmediately) {
          setApplying(true);
          try {
            const { applySchedule } = useScheduleStore.getState();
            const result = await applySchedule(savedSchedule.id);

            Alert.alert(
              'Cronograma Criado e Aplicado',
              `O cronograma foi criado e aplicado com sucesso! Foram criados ${result.events} eventos e ${result.todos} tarefas.`,
              [
                {
                  text: 'Ir para Calendário',
                  onPress: () => router.push('/calendar')
                },
                {
                  text: 'Editar Cronograma',
                  onPress: () => router.push(`/schedules/${savedSchedule.id}`)
                }
              ]
            );
          } catch (error) {
            console.error('Error applying schedule:', error);
            Alert.alert(
              'Cronograma Criado',
              'O cronograma foi criado com sucesso, mas ocorreu um erro ao aplicá-lo. Você pode aplicá-lo manualmente mais tarde.',
              [
                {
                  text: 'OK',
                  onPress: () => router.push(`/schedules/${savedSchedule.id}`)
                }
              ]
            );
          } finally {
            setApplying(false);
          }
        } else {
          Alert.alert(
            'Cronograma Criado',
            'O cronograma foi criado com sucesso. Deseja adicionar itens ao cronograma agora?',
            [
              {
                text: 'Mais tarde',
                onPress: () => router.push('/schedules')
              },
              {
                text: 'Adicionar Itens',
                onPress: () => router.push(`/schedules/${savedSchedule.id}`)
              }
            ]
          );
        }
      } else {
        Alert.alert('Erro', 'Ocorreu um erro ao criar o cronograma.');
      }
    } catch (error) {
      console.error('Error creating schedule:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao criar o cronograma.');
    }
  };

  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setStartDate(selectedDate);

      // If end date is before start date, update it
      if (endDate && endDate < selectedDate) {
        setEndDate(selectedDate);
      }
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      // If end date is before start date, don't update
      if (selectedDate < startDate) {
        Alert.alert('Erro', 'A data de término deve ser após a data de início.');
        return;
      }

      setEndDate(selectedDate);
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Criar Cronograma"
        leftComponent={
          <Pressable style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </Pressable>
        }
        rightComponent={
          <Pressable style={styles.saveButton} onPress={handleSave}>
            <Save size={24} color={colors.primary} />
          </Pressable>
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.formCard}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Título</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Título do cronograma"
              placeholderTextColor={colors.textLight}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descrição do cronograma"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Cronograma</Text>
            <View style={styles.typeButtons}>
              <Pressable
                style={[
                  styles.typeButton,
                  type === 'weekly' && styles.typeButtonActive
                ]}
                onPress={() => setType('weekly')}
              >
                <CalendarDays size={20} color={type === 'weekly' ? colors.primary : colors.textLight} />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'weekly' && styles.typeButtonTextActive
                  ]}
                >
                  Semanal
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.typeButton,
                  type === 'monthly' && styles.typeButtonActive
                ]}
                onPress={() => setType('monthly')}
              >
                <CalendarIcon size={20} color={type === 'monthly' ? colors.primary : colors.textLight} />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'monthly' && styles.typeButtonTextActive
                  ]}
                >
                  Mensal
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.typeButton,
                  type === '30days' && styles.typeButtonActive
                ]}
                onPress={() => setType('30days')}
              >
                <Calendar size={20} color={type === '30days' ? colors.primary : colors.textLight} />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === '30days' && styles.typeButtonTextActive
                  ]}
                >
                  30 Dias
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.typeButton,
                  type === 'custom' && styles.typeButtonActive
                ]}
                onPress={() => setType('custom')}
              >
                <Clock size={20} color={type === 'custom' ? colors.primary : colors.textLight} />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'custom' && styles.typeButtonTextActive
                  ]}
                >
                  Personalizado
                </Text>
              </Pressable>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Data de Início</Text>
            <Pressable
              style={styles.datePickerButton}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Calendar size={20} color={colors.primary} />
              <Text style={styles.datePickerButtonText}>
                {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
              </Text>
            </Pressable>
            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display="default"
                onChange={handleStartDateChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Data de Término</Text>
              <Switch
                value={endDate !== null}
                onValueChange={(value) => {
                  if (value) {
                    setEndDate(new Date());
                  } else {
                    setEndDate(null);
                  }
                }}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={endDate !== null ? colors.primary : colors.white}
              />
            </View>
            {endDate && (
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(endDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
            )}
            {showEndDatePicker && endDate && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display="default"
                onChange={handleEndDateChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Repetir Semanalmente</Text>
              <Switch
                value={repeatWeekly}
                onValueChange={setRepeatWeekly}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={repeatWeekly ? colors.primary : colors.white}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Repetir Mensalmente</Text>
              <Switch
                value={repeatMonthly}
                onValueChange={setRepeatMonthly}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={repeatMonthly ? colors.primary : colors.white}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Ativo</Text>
              <Switch
                value={active}
                onValueChange={setActive}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={active ? colors.primary : colors.white}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Cor do Cronograma</Text>
            <View style={styles.colorPickerContainer}>
              {[
                colors.primary, '#F97316', '#10B981', '#8B5CF6',
                '#EC4899', '#F59E0B', '#EF4444', '#3B82F6'
              ].map((colorOption) => (
                <Pressable
                  key={colorOption}
                  style={[
                    styles.colorOption,
                    { backgroundColor: colorOption },
                    color === colorOption && styles.colorOptionSelected
                  ]}
                  onPress={() => setColor(colorOption)}
                />
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Ativar Lembretes</Text>
              <Switch
                value={reminderEnabled}
                onValueChange={setReminderEnabled}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={reminderEnabled ? colors.primary : colors.white}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <View style={styles.labelContainer}>
                <Text style={styles.label}>Aplicar ao criar</Text>
                <Text style={styles.labelDescription}>Cria eventos e tarefas automaticamente</Text>
              </View>
              <Switch
                value={applyImmediately}
                onValueChange={setApplyImmediately}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={applyImmediately ? colors.primary : colors.white}
              />
            </View>
          </View>

          {reminderEnabled && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Tempo de Lembrete (minutos antes)</Text>
              <View style={styles.reminderOptions}>
                {[15, 30, 60, 120].map((minutes) => (
                  <Pressable
                    key={minutes}
                    style={[
                      styles.reminderOption,
                      reminderTime === minutes && styles.reminderOptionSelected
                    ]}
                    onPress={() => setReminderTime(minutes)}
                  >
                    <Text
                      style={[
                        styles.reminderOptionText,
                        reminderTime === minutes && styles.reminderOptionTextSelected
                      ]}
                    >
                      {minutes}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>
          )}
        </GlassCard>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancelar"
            onPress={() => router.back()}
            variant="secondary"
            size="large"
            style={styles.button}
          />
          <Button
            title={applying ? "Criando e Aplicando..." : "Salvar"}
            onPress={handleSave}
            variant="primary"
            size="large"
            style={styles.button}
            icon={Save}
            loading={applying}
            disabled={applying}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  saveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formCard: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  typeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    width: '48%',
    borderWidth: 1,
    borderColor: colors.border,
  },
  typeButtonActive: {
    backgroundColor: `${colors.primary}15`,
    borderColor: colors.primary,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
    marginLeft: 8,
  },
  typeButtonTextActive: {
    color: colors.primary,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
  colorPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  colorOptionSelected: {
    borderWidth: 3,
    borderColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  reminderOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  reminderOption: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  reminderOptionSelected: {
    backgroundColor: `${colors.primary}15`,
    borderColor: colors.primary,
  },
  reminderOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  reminderOptionTextSelected: {
    color: colors.primary,
  },
  labelContainer: {
    flex: 1,
  },
  labelDescription: {
    fontSize: 12,
    color: colors.textLight,
    marginTop: 2,
  },
});
