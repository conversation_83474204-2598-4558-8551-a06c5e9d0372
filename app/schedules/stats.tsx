import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { Header } from '@/components/Header';
import { GlassCard } from '@/components/GlassCard';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, BarChart3, PieChart, Clock, Calendar, CheckCircle, BookOpen } from 'lucide-react-native';
import { useScheduleStore } from '@/store/scheduleStore';
import { useCalendarStore } from '@/store/calendarStore';
import { useStudyStore } from '@/store/studyStore';
import { format, parseISO, differenceInMinutes, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Componente para exibir um gráfico de barras simples
const SimpleBar<PERSON>hart = ({ data, maxValue, title }: { data: { label: string, value: number, color: string }[], maxValue: number, title: string }) => {
  return (
    <View style={styles.chartContainer}>
      <Text style={styles.chartTitle}>{title}</Text>
      {data.map((item, index) => (
        <View key={index} style={styles.barContainer}>
          <Text style={styles.barLabel}>{item.label}</Text>
          <View style={styles.barWrapper}>
            <View 
              style={[
                styles.bar, 
                { 
                  width: `${(item.value / maxValue) * 100}%`,
                  backgroundColor: item.color
                }
              ]}
            />
            <Text style={styles.barValue}>{item.value}</Text>
          </View>
        </View>
      ))}
    </View>
  );
};

// Componente para exibir um gráfico de pizza simples
const SimplePieChart = ({ data, title }: { data: { label: string, value: number, color: string }[], title: string }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let startAngle = 0;
  
  return (
    <View style={styles.pieChartContainer}>
      <Text style={styles.chartTitle}>{title}</Text>
      <View style={styles.pieChartWrapper}>
        <View style={styles.pieChart}>
          {data.map((item, index) => {
            const percentage = item.value / total;
            const angle = percentage * 360;
            const rotation = startAngle;
            startAngle += angle;
            
            return (
              <View 
                key={index}
                style={[
                  styles.pieSlice,
                  {
                    backgroundColor: item.color,
                    transform: [
                      { rotate: `${rotation}deg` },
                      { skewX: `${angle - 90}deg` }
                    ],
                    opacity: percentage > 0 ? 1 : 0
                  }
                ]}
              />
            );
          })}
        </View>
      </View>
      <View style={styles.pieChartLegend}>
        {data.map((item, index) => (
          <View key={index} style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: item.color }]} />
            <Text style={styles.legendLabel}>{item.label}: {Math.round(item.value / total * 100)}%</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

export default function ScheduleStatsScreen() {
  const router = useRouter();
  const { schedules, scheduleItems, fetchSchedules, fetchScheduleItems, loading: scheduleLoading } = useScheduleStore();
  const { events, todos, fetchEvents, fetchTodos, loading: calendarLoading } = useCalendarStore();
  const { subjects, fetchSubjects, loading: subjectsLoading } = useStudyStore();
  
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalSchedules: 0,
    activeSchedules: 0,
    totalItems: 0,
    totalEvents: 0,
    totalTodos: 0,
    completedEvents: 0,
    completedTodos: 0,
    subjectDistribution: [] as { label: string, value: number, color: string }[],
    weekdayDistribution: [] as { label: string, value: number, color: string }[],
    studyTimeByDay: [] as { label: string, value: number, color: string }[]
  });
  
  useEffect(() => {
    loadData();
  }, []);
  
  const loadData = async () => {
    setLoading(true);
    
    try {
      // Carregar dados necessários
      if (schedules.length === 0) await fetchSchedules();
      if (subjects.length === 0) await fetchSubjects();
      
      // Carregar todos os itens de cronograma
      let allItems = [];
      for (const schedule of schedules) {
        const items = await fetchScheduleItems(schedule.id);
        allItems = [...allItems, ...items];
      }
      
      // Carregar eventos e tarefas
      if (events.length === 0) await fetchEvents();
      if (todos.length === 0) await fetchTodos();
      
      // Calcular estatísticas
      calculateStats(schedules, allItems, events, todos, subjects);
    } catch (error) {
      console.error('Erro ao carregar dados para estatísticas:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const calculateStats = (
    schedules: any[], 
    items: any[], 
    events: any[], 
    todos: any[], 
    subjects: any[]
  ) => {
    // Estatísticas básicas
    const activeSchedules = schedules.filter(s => s.active).length;
    const completedEvents = events.filter(e => e.completed).length;
    const completedTodos = todos.filter(t => t.completed).length;
    
    // Distribuição por matéria
    const subjectCounts: Record<string, number> = {};
    items.forEach(item => {
      if (item.subjectTitle) {
        subjectCounts[item.subjectTitle] = (subjectCounts[item.subjectTitle] || 0) + 1;
      }
    });
    
    const subjectDistribution = Object.entries(subjectCounts)
      .map(([label, value]) => {
        const subject = subjects.find(s => s.title === label);
        return {
          label,
          value,
          color: subject?.color || colors.primary
        };
      })
      .sort((a, b) => b.value - a.value)
      .slice(0, 5); // Top 5 matérias
    
    // Distribuição por dia da semana
    const weekdayCounts: Record<number, number> = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0};
    items.forEach(item => {
      if (item.dayOfWeek !== undefined) {
        weekdayCounts[item.dayOfWeek] = (weekdayCounts[item.dayOfWeek] || 0) + 1;
      }
    });
    
    const weekdayNames = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
    const weekdayColors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#C9CBCF'];
    
    const weekdayDistribution = Object.entries(weekdayCounts)
      .map(([day, value]) => ({
        label: weekdayNames[parseInt(day)],
        value,
        color: weekdayColors[parseInt(day)]
      }));
    
    // Tempo de estudo por dia da semana atual
    const now = new Date();
    const weekStart = startOfWeek(now, { weekStartsOn: 0 }); // Domingo
    const weekEnd = endOfWeek(now, { weekStartsOn: 0 }); // Sábado
    const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });
    
    const studyTimeByDay = weekDays.map((day, index) => {
      // Encontrar eventos para este dia
      const dayEvents = events.filter(e => {
        const eventDate = parseISO(e.startDate);
        return isSameDay(eventDate, day) && e.type === 'study';
      });
      
      // Calcular tempo total de estudo em minutos
      const totalMinutes = dayEvents.reduce((total, event) => {
        const startDate = parseISO(event.startDate);
        const endDate = parseISO(event.endDate);
        return total + differenceInMinutes(endDate, startDate);
      }, 0);
      
      // Converter para horas
      const hours = Math.round(totalMinutes / 60 * 10) / 10;
      
      return {
        label: format(day, 'EEE', { locale: ptBR }),
        value: hours,
        color: weekdayColors[index]
      };
    });
    
    setStats({
      totalSchedules: schedules.length,
      activeSchedules,
      totalItems: items.length,
      totalEvents: events.length,
      totalTodos: todos.length,
      completedEvents,
      completedTodos,
      subjectDistribution,
      weekdayDistribution,
      studyTimeByDay
    });
  };
  
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['rgba(73, 49, 234, 0.2)', 'rgba(73, 49, 234, 0)']}
        style={styles.gradient}
      />
      
      <Header
        title="Estatísticas de Cronogramas"
        leftIcon={ArrowLeft}
        onLeftPress={() => router.back()}
      />
      
      {loading || scheduleLoading || calendarLoading || subjectsLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando estatísticas...</Text>
        </View>
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <GlassCard style={styles.summaryCard}>
            <Text style={styles.cardTitle}>Resumo</Text>
            
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <BarChart3 size={24} color={colors.primary} />
                <Text style={styles.statValue}>{stats.totalSchedules}</Text>
                <Text style={styles.statLabel}>Cronogramas</Text>
              </View>
              
              <View style={styles.statItem}>
                <Calendar size={24} color={colors.success} />
                <Text style={styles.statValue}>{stats.activeSchedules}</Text>
                <Text style={styles.statLabel}>Ativos</Text>
              </View>
              
              <View style={styles.statItem}>
                <BookOpen size={24} color={colors.warning} />
                <Text style={styles.statValue}>{stats.totalItems}</Text>
                <Text style={styles.statLabel}>Itens</Text>
              </View>
              
              <View style={styles.statItem}>
                <Clock size={24} color={colors.info} />
                <Text style={styles.statValue}>{stats.totalEvents}</Text>
                <Text style={styles.statLabel}>Eventos</Text>
              </View>
            </View>
          </GlassCard>
          
          <GlassCard style={styles.card}>
            <Text style={styles.cardTitle}>Progresso</Text>
            
            <View style={styles.progressContainer}>
              <View style={styles.progressItem}>
                <Text style={styles.progressLabel}>Eventos Concluídos</Text>
                <View style={styles.progressBarContainer}>
                  <View 
                    style={[
                      styles.progressBar, 
                      { 
                        width: `${stats.totalEvents > 0 ? (stats.completedEvents / stats.totalEvents) * 100 : 0}%`,
                        backgroundColor: colors.success
                      }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {stats.completedEvents} de {stats.totalEvents} ({stats.totalEvents > 0 ? Math.round((stats.completedEvents / stats.totalEvents) * 100) : 0}%)
                </Text>
              </View>
              
              <View style={styles.progressItem}>
                <Text style={styles.progressLabel}>Tarefas Concluídas</Text>
                <View style={styles.progressBarContainer}>
                  <View 
                    style={[
                      styles.progressBar, 
                      { 
                        width: `${stats.totalTodos > 0 ? (stats.completedTodos / stats.totalTodos) * 100 : 0}%`,
                        backgroundColor: colors.primary
                      }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {stats.completedTodos} de {stats.totalTodos} ({stats.totalTodos > 0 ? Math.round((stats.completedTodos / stats.totalTodos) * 100) : 0}%)
                </Text>
              </View>
            </View>
          </GlassCard>
          
          {stats.subjectDistribution.length > 0 && (
            <GlassCard style={styles.card}>
              <Text style={styles.cardTitle}>Distribuição por Matéria</Text>
              <SimplePieChart 
                data={stats.subjectDistribution} 
                title="Top 5 Matérias"
              />
            </GlassCard>
          )}
          
          {stats.weekdayDistribution.length > 0 && (
            <GlassCard style={styles.card}>
              <Text style={styles.cardTitle}>Distribuição por Dia da Semana</Text>
              <SimpleBarChart 
                data={stats.weekdayDistribution} 
                maxValue={Math.max(...stats.weekdayDistribution.map(d => d.value))}
                title="Número de Atividades por Dia"
              />
            </GlassCard>
          )}
          
          {stats.studyTimeByDay.length > 0 && (
            <GlassCard style={styles.card}>
              <Text style={styles.cardTitle}>Tempo de Estudo na Semana Atual</Text>
              <SimpleBarChart 
                data={stats.studyTimeByDay} 
                maxValue={Math.max(...stats.studyTimeByDay.map(d => d.value), 1)}
                title="Horas de Estudo por Dia"
              />
            </GlassCard>
          )}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.text,
  },
  summaryCard: {
    padding: 16,
    marginBottom: 16,
  },
  card: {
    padding: 16,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    marginBottom: 16,
    backgroundColor: colors.white,
    borderRadius: 10,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 8,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 4,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressItem: {
    marginBottom: 16,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  progressBarContainer: {
    height: 12,
    backgroundColor: colors.border,
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    borderRadius: 6,
  },
  progressText: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: 'right',
  },
  chartContainer: {
    marginTop: 8,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  barLabel: {
    width: 80,
    fontSize: 12,
    color: colors.text,
  },
  barWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 24,
  },
  bar: {
    height: '100%',
    borderRadius: 4,
  },
  barValue: {
    marginLeft: 8,
    fontSize: 12,
    color: colors.text,
  },
  pieChartContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  pieChartWrapper: {
    width: 200,
    height: 200,
    marginBottom: 24,
  },
  pieChart: {
    width: '100%',
    height: '100%',
    borderRadius: 100,
    overflow: 'hidden',
    position: 'relative',
  },
  pieSlice: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    transformOrigin: 'center',
  },
  pieChartLegend: {
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendLabel: {
    fontSize: 12,
    color: colors.text,
  },
});
