import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Alert,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { Header } from '@/components/Header';
import { Button } from '@/components/Button';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassCard } from '@/components/GlassCard';
import { Calendar, Clock, BookOpen, Plus, ArrowLeft, Trash2, Edit, CalendarDays, CheckCircle, AlertTriangle, BarChart3 } from 'lucide-react-native';
import { useScheduleStore } from '@/store/scheduleStore';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Schedule } from '@/types';

export default function SchedulesScreen() {
  const router = useRouter();
  const { schedules, fetchSchedules, deleteSchedule, setCurrentSchedule, loading } = useScheduleStore();

  useEffect(() => {
    fetchSchedules();
  }, []);

  const handleCreateSchedule = () => {
    router.push('/schedules/create');
  };

  const handleEditSchedule = (schedule: Schedule) => {
    setCurrentSchedule(schedule);
    router.push(`/schedules/${schedule.id}`);
  };

  const handleDeleteSchedule = (schedule: Schedule) => {
    Alert.alert(
      "Excluir Cronograma",
      `Tem certeza que deseja excluir o cronograma "${schedule.title}"?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            const success = await deleteSchedule(schedule.id);
            if (success) {
              Alert.alert("Cronograma excluído", "O cronograma foi excluído com sucesso.");
            } else {
              Alert.alert("Erro", "Ocorreu um erro ao excluir o cronograma.");
            }
          }
        }
      ]
    );
  };

  const handleApplySchedule = (schedule: Schedule) => {
    setCurrentSchedule(schedule);
    router.push(`/schedules/${schedule.id}`);
  };

  const handleViewStats = () => {
    router.push('/schedules/stats');
  };

  const renderScheduleItem = ({ item }: { item: Schedule }) => {
    return (
      <GlassCard style={styles.scheduleCard}>
        <View style={styles.scheduleHeader}>
          <Text style={styles.scheduleTitle}>{item.title}</Text>
          <View style={styles.scheduleActions}>
            <Pressable
              style={styles.actionButton}
              onPress={() => handleEditSchedule(item)}
            >
              <Edit size={20} color={colors.primary} />
            </Pressable>
            <Pressable
              style={styles.actionButton}
              onPress={() => handleDeleteSchedule(item)}
            >
              <Trash2 size={20} color={colors.error} />
            </Pressable>
          </View>
        </View>

        {item.description && (
          <Text style={styles.scheduleDescription}>{item.description}</Text>
        )}

        <View style={styles.scheduleDetails}>
          <View style={styles.scheduleDetail}>
            <CalendarDays size={16} color={colors.textLight} />
            <Text style={styles.scheduleDetailText}>
              {item.type === 'weekly' ? 'Semanal' :
               item.type === 'monthly' ? 'Mensal' :
               item.type === '30days' ? '30 Dias' : 'Personalizado'}
            </Text>
          </View>

          {item.startDate && (
            <View style={styles.scheduleDetail}>
              <Calendar size={16} color={colors.textLight} />
              <Text style={styles.scheduleDetailText}>
                Início: {format(parseISO(item.startDate), 'dd/MM/yyyy', { locale: ptBR })}
              </Text>
            </View>
          )}

          {item.repeatWeekly && (
            <View style={styles.scheduleDetail}>
              <CheckCircle size={16} color={colors.success} />
              <Text style={styles.scheduleDetailText}>Repete semanalmente</Text>
            </View>
          )}

          {item.repeatMonthly && (
            <View style={styles.scheduleDetail}>
              <CheckCircle size={16} color={colors.success} />
              <Text style={styles.scheduleDetailText}>Repete mensalmente</Text>
            </View>
          )}

          <View style={styles.scheduleDetail}>
            <AlertTriangle size={16} color={item.active ? colors.success : colors.error} />
            <Text style={styles.scheduleDetailText}>
              {item.active ? 'Ativo' : 'Inativo'}
            </Text>
          </View>
        </View>

        <Button
          title="Aplicar Cronograma"
          onPress={() => handleApplySchedule(item)}
          variant="primary"
          size="medium"
          style={styles.applyButton}
        />
      </GlassCard>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Cronogramas de Estudo"
        leftComponent={
          <Pressable style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </Pressable>
        }
        rightComponent={
          <View style={styles.headerButtons}>
            <Pressable style={styles.headerButton} onPress={handleViewStats}>
              <BarChart3 size={24} color={colors.primary} />
            </Pressable>
            <Pressable style={styles.headerButton} onPress={handleCreateSchedule}>
              <Plus size={24} color={colors.primary} />
            </Pressable>
          </View>
        }
      />

      <View style={styles.content}>
        {schedules.length > 0 ? (
          <FlatList
            data={schedules}
            renderItem={renderScheduleItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.schedulesList}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <CalendarDays size={64} color={colors.textLight} />
            <Text style={styles.emptyText}>
              Você ainda não tem cronogramas de estudo.
            </Text>
            <Text style={styles.emptySubtext}>
              Crie um cronograma para organizar seus estudos e gerar eventos no calendário automaticamente.
            </Text>
            <Button
              title="Criar Cronograma"
              onPress={handleCreateSchedule}
              variant="primary"
              size="large"
              icon={Plus}
              style={styles.createButton}
            />
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  schedulesList: {
    paddingBottom: 20,
  },
  scheduleCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 16,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scheduleTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    flex: 1,
  },
  scheduleActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  scheduleDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 12,
  },
  scheduleDetails: {
    marginBottom: 16,
  },
  scheduleDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  scheduleDetailText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
  },
  applyButton: {
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 8,
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    marginTop: 16,
  },
});
