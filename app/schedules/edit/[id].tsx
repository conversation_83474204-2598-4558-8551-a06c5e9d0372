import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Switch,
  Alert,
  ActivityIndicator,
  Platform
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { Header } from '@/components/Header';
import { Button } from '@/components/Button';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassCard } from '@/components/GlassCard';
import { RouteGuard } from '@/components/RouteGuard';
import {
  Calendar,
  Clock,
  ArrowLeft,
  Save,
  CalendarDays,
  Repeat,
  ClipboardList
} from 'lucide-react-native';
import { useScheduleStore } from '@/store/scheduleStore';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import DateTimePicker from '@react-native-community/datetimepicker';

export default function EditScheduleScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { schedules, fetchSchedules, updateSchedule, loading } = useScheduleStore();

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'weekly' | 'monthly' | '30days' | 'custom'>('weekly');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [hasEndDate, setHasEndDate] = useState(false);
  const [repeatWeekly, setRepeatWeekly] = useState(false);
  const [repeatMonthly, setRepeatMonthly] = useState(false);
  const [active, setActive] = useState(true);

  // Date picker states
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  useEffect(() => {
    if (!id) {
      router.back();
      return;
    }

    loadSchedule();
  }, [id]);

  const loadSchedule = async () => {
    // Fetch schedules if not already loaded
    if (schedules.length === 0) {
      await fetchSchedules();
    }

    // Find the schedule by id
    const schedule = schedules.find(s => s.id === id);
    if (!schedule) {
      Alert.alert('Erro', 'Cronograma não encontrado');
      router.back();
      return;
    }

    // Initialize form with schedule data
    setTitle(schedule.title);
    setDescription(schedule.description || '');
    setType(schedule.type);
    setStartDate(parseISO(schedule.startDate));

    if (schedule.endDate) {
      setEndDate(parseISO(schedule.endDate));
      setHasEndDate(true);
    } else {
      setEndDate(null);
      setHasEndDate(false);
    }

    setRepeatWeekly(schedule.repeatWeekly);
    setRepeatMonthly(schedule.repeatMonthly);
    setActive(schedule.active);
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título do cronograma é obrigatório.');
      return;
    }

    try {
      const updatedSchedule = await updateSchedule(id as string, {
        title,
        description: description.trim() || undefined,
        type,
        startDate: startDate.toISOString(),
        endDate: hasEndDate && endDate ? endDate.toISOString() : undefined,
        repeatWeekly,
        repeatMonthly,
        active
      });

      if (updatedSchedule) {
        Alert.alert(
          'Sucesso',
          'Cronograma atualizado com sucesso!',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro ao atualizar o cronograma.');
      }
    } catch (error) {
      console.error('Error updating schedule:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao atualizar o cronograma.');
    }
  };

  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setStartDate(selectedDate);

      // If end date is before start date, update it
      if (endDate && endDate < selectedDate) {
        setEndDate(selectedDate);
      }
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      // Ensure end date is not before start date
      if (selectedDate >= startDate) {
        setEndDate(selectedDate);
      } else {
        Alert.alert('Erro', 'A data de término deve ser após a data de início.');
      }
    }
  };

  const getTypeText = (scheduleType: string) => {
    switch (scheduleType) {
      case 'weekly': return 'Semanal';
      case 'monthly': return 'Mensal';
      case '30days': return '30 Dias';
      case 'custom': return 'Personalizado';
      default: return scheduleType;
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Header
          title="Editar Cronograma"
          leftIcon={ArrowLeft}
          onLeftPress={() => router.back()}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </View>
    );
  }

  return (
    <RouteGuard resourceId={id as string} tableName="schedules">
      <View style={styles.container}>
        <LinearGradient
          colors={['rgba(73, 49, 234, 0.2)', 'rgba(73, 49, 234, 0)']}
          style={styles.gradient}
        />

        <Header
          title="Editar Cronograma"
          leftIcon={ArrowLeft}
          onLeftPress={() => router.back()}
          rightIcon={Save}
          onRightPress={handleSave}
        />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <GlassCard style={styles.formCard}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Título</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Título do cronograma"
              placeholderTextColor={colors.textLight}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descrição do cronograma (opcional)"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Cronograma</Text>
            <View style={styles.typeContainer}>
              {[
                { value: 'weekly', label: 'Semanal', icon: CalendarDays },
                { value: 'monthly', label: 'Mensal', icon: Calendar },
                { value: '30days', label: '30 Dias', icon: Calendar },
                { value: 'custom', label: 'Personalizado', icon: ClipboardList }
              ].map((item) => (
                <Pressable
                  key={item.value}
                  style={[
                    styles.typeItem,
                    type === item.value && styles.typeItemActive
                  ]}
                  onPress={() => setType(item.value as any)}
                >
                  <item.icon
                    size={16}
                    color={type === item.value ? colors.white : colors.text}
                  />
                  <Text
                    style={[
                      styles.typeItemText,
                      type === item.value && styles.typeItemTextActive
                    ]}
                  >
                    {item.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Data de Início</Text>
            <Pressable
              style={styles.datePickerButton}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Calendar size={20} color={colors.primary} />
              <Text style={styles.datePickerButtonText}>
                {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
              </Text>
            </Pressable>

            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleStartDateChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Data de Término</Text>
              <Switch
                value={hasEndDate}
                onValueChange={setHasEndDate}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={hasEndDate ? colors.primary : colors.white}
              />
            </View>

            {hasEndDate && (
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {endDate ? format(endDate, 'dd/MM/yyyy', { locale: ptBR }) : 'Selecionar data'}
                </Text>
              </Pressable>
            )}

            {showEndDatePicker && hasEndDate && (
              <DateTimePicker
                value={endDate || startDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleEndDateChange}
                minimumDate={startDate}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Opções de Repetição</Text>
            <GlassCard style={styles.optionsCard}>
              <View style={styles.optionItem}>
                <View style={styles.optionTextContainer}>
                  <Repeat size={16} color={colors.text} />
                  <Text style={styles.optionText}>Repetir semanalmente</Text>
                </View>
                <Switch
                  value={repeatWeekly}
                  onValueChange={setRepeatWeekly}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={repeatWeekly ? colors.primary : colors.white}
                />
              </View>

              <View style={styles.optionItem}>
                <View style={styles.optionTextContainer}>
                  <Repeat size={16} color={colors.text} />
                  <Text style={styles.optionText}>Repetir mensalmente</Text>
                </View>
                <Switch
                  value={repeatMonthly}
                  onValueChange={setRepeatMonthly}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={repeatMonthly ? colors.primary : colors.white}
                />
              </View>

              <View style={styles.optionItem}>
                <View style={styles.optionTextContainer}>
                  <CalendarDays size={16} color={colors.text} />
                  <Text style={styles.optionText}>Cronograma ativo</Text>
                </View>
                <Switch
                  value={active}
                  onValueChange={setActive}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={active ? colors.primary : colors.white}
                />
              </View>
            </GlassCard>
          </View>

          <Button
            title="Salvar Alterações"
            onPress={handleSave}
            variant="primary"
            size="large"
            icon={Save}
            style={styles.saveButton}
            loading={loading}
          />
        </GlassCard>
      </ScrollView>
    </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formCard: {
    padding: 16,
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  typeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 8,
  },
  typeItemActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeItemText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
  },
  typeItemTextActive: {
    color: colors.white,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    marginTop: 8,
  },
  datePickerButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.text,
  },
  optionsCard: {
    padding: 0,
    overflow: 'hidden',
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  optionTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
  },
  saveButton: {
    marginTop: 24,
    marginBottom: 16,
  },
});
