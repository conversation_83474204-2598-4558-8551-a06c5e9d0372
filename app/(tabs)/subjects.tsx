import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Pressable, SafeAreaView, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { Header } from '@/components/Header';
import { SubjectCard } from '@/components/SubjectCard';
import { SwipeableSubjectCard } from '@/components/SwipeableSubjectCard';
import { SubjectSeparator } from '@/components/SubjectSeparator';
import { SwipeableSubjectSeparator } from '@/components/SwipeableSubjectSeparator';
import { CreateSubjectModal } from '@/components/CreateSubjectModal';
import { CreateSeparatorModal } from '@/components/CreateSeparatorModal';
import { AssignSeparatorModal } from '@/components/AssignSeparatorModal';
import { EditSeparatorModal } from '@/components/EditSeparatorModal';
import { EditSubjectModal } from '@/components/EditSubjectModal';
import { useStudyStore } from '@/store/studyStore';
import { useSeparatorStore } from '@/store/separatorStore';
import { Subject } from '@/types';
import { colors } from '@/constants/colors';
import { LinearGradient } from 'expo-linear-gradient';
import { Plus, FolderPlus } from 'lucide-react-native';

export default function SubjectsScreen() {
  const router = useRouter();
  const { subjects, addSubject, updateSubject, removeSubject, fetchSubjects } = useStudyStore();
  const { separators, fetchSeparators, addSeparator, updateSeparator, deleteSeparator } = useSeparatorStore();
  const [showCreateSubjectModal, setShowCreateSubjectModal] = useState(false);
  const [showCreateSeparatorModal, setShowCreateSeparatorModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [editingSeparator, setEditingSeparator] = useState<string | null>(null);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await fetchSubjects();
        await fetchSeparators();
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubjectPress = (subject) => {
    router.push(`/subject/${subject.id}`);
  };

  const handleSubjectLongPress = (subject) => {
    setSelectedSubject(subject);
    setShowAssignModal(true);
  };

  const handleCreateSubject = async (subject) => {
    await addSubject(subject);
  };

  const handleCreateSeparator = async (separator) => {
    await addSeparator(separator);
  };

  const handleEditSeparator = (separator) => {
    setEditingSeparator(separator.id);
  };

  const handleUpdateSeparator = async (id, updates) => {
    await updateSeparator(id, updates);
    setEditingSeparator(null);
  };

  const handleDeleteSeparator = async (separator) => {
    await deleteSeparator(separator.id);
  };

  const handleAssignSeparator = async (separatorId) => {
    if (selectedSubject) {
      await updateSubject(selectedSubject.id, { separator_id: separatorId });
      setShowAssignModal(false);
      setSelectedSubject(null);
    }
  };

  const handleEditSubject = (subject) => {
    setEditingSubject(subject);
  };

  const handleUpdateSubject = async (id, updates) => {
    await updateSubject(id, updates);
    setEditingSubject(null);
  };

  const handleDeleteSubject = async (subject) => {
    await removeSubject(subject.id);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header title="Suas Matérias" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollContent, {paddingBottom: Platform.OS === 'ios' ? 120 : 100}]}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.subjectsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Matérias</Text>
            <View style={styles.addButtonsContainer}>
              <Pressable
                style={styles.addButton}
                onPress={() => setShowCreateSeparatorModal(true)}
              >
                <FolderPlus size={20} color={colors.primary} />
                <Text style={styles.addButtonText}>Novo Separador</Text>
              </Pressable>
              <Pressable
                style={styles.addButton}
                onPress={() => setShowCreateSubjectModal(true)}
              >
                <Plus size={20} color={colors.primary} />
                <Text style={styles.addButtonText}>Nova Matéria</Text>
              </Pressable>
            </View>
          </View>

          {subjects.length > 0 || separators.length > 0 ? (
            <>
              {/* Mostrar separadores com suas matérias */}
              {separators.map((separator) => (
                <SwipeableSubjectSeparator
                  key={separator.id}
                  separator={separator}
                  subjects={subjects.filter(s => s.separator_id === separator.id)}
                  onEditSeparator={handleEditSeparator}
                  onDeleteSeparator={handleDeleteSeparator}
                  onSubjectPress={handleSubjectPress}
                  onEditSubject={handleEditSubject}
                  onDeleteSubject={handleDeleteSubject}
                />
              ))}

              {/* Mostrar matérias sem separador */}
              {subjects.filter(s => !s.separator_id).map((subject) => (
                <SwipeableSubjectCard
                  key={subject.id}
                  subject={subject}
                  onPress={handleSubjectPress}
                  onEdit={handleEditSubject}
                  onDelete={handleDeleteSubject}
                />
              ))}
            </>
          ) : (
            <View style={styles.emptySubjectsContainer}>
              <Text style={styles.emptySubjectsText}>
                Você ainda não tem matérias. Crie sua primeira matéria para começar a organizar seus estudos.
              </Text>
              <View style={styles.emptyButtonsContainer}>
                <Pressable
                  style={styles.createFirstButton}
                  onPress={() => setShowCreateSeparatorModal(true)}
                >
                  <Text style={styles.createFirstButtonText}>Criar Separador</Text>
                </Pressable>
                <Pressable
                  style={styles.createFirstButton}
                  onPress={() => setShowCreateSubjectModal(true)}
                >
                  <Text style={styles.createFirstButtonText}>Criar Matéria</Text>
                </Pressable>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Modals */}
      <CreateSubjectModal
        visible={showCreateSubjectModal}
        onClose={() => setShowCreateSubjectModal(false)}
        onCreateSubject={handleCreateSubject}
        separators={separators}
      />

      <CreateSeparatorModal
        visible={showCreateSeparatorModal}
        onClose={() => setShowCreateSeparatorModal(false)}
        onCreateSeparator={handleCreateSeparator}
      />

      {editingSeparator && (
        <EditSeparatorModal
          visible={!!editingSeparator}
          onClose={() => setEditingSeparator(null)}
          onUpdateSeparator={handleUpdateSeparator}
          separator={separators.find(s => s.id === editingSeparator)!}
        />
      )}

      <AssignSeparatorModal
        visible={showAssignModal}
        onClose={() => {
          setShowAssignModal(false);
          setSelectedSubject(null);
        }}
        onAssign={handleAssignSeparator}
        separators={separators}
        subject={selectedSubject}
      />

      {editingSubject && (
        <EditSubjectModal
          visible={!!editingSubject}
          onClose={() => setEditingSubject(null)}
          onUpdateSubject={handleUpdateSubject}
          subject={editingSubject}
          separators={separators}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subjectsSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
  },
  addButtonsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
    marginLeft: 4,
  },
  emptySubjectsContainer: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  emptySubjectsText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButtonsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  createFirstButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  createFirstButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
