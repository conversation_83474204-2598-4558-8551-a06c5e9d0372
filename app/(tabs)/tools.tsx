import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Pressable,
} from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { FeatureCard } from "@/components/FeatureCard";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";

export default function ToolsScreen() {
  const router = useRouter();

  const handleFeaturePress = (feature: string) => {
    switch (feature) {
      case "mind-maps":
        router.push("/mind-maps");
        break;
      case "notes":
        router.push("/notes");
        break;
      case "study-plans":
        router.push("/study-plans");
        break;
      case "text-generator":
        router.push("/text-generator");
        break;
      default:
        break;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header title="Ferramentas de Estudo" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerSection}>
          <GlassCard style={styles.headerCard} gradient>
            <Text style={styles.headerTitle}>Ferramentas Inteligentes</Text>
            <Text style={styles.headerText}>
              Utilize nossas ferramentas de estudo potencializadas por IA para maximizar seu aprendizado
            </Text>
          </GlassCard>
        </View>

        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Organização</Text>
          <FeatureCard
            title="Mapas Mentais"
            description="Crie mapas mentais interativos para visualizar conceitos e suas conexões."
            icon="Network"
            gradientColors={[colors.accent1, `${colors.accent1}80`]}
            onPress={() => handleFeaturePress("mind-maps")}
          />
          <FeatureCard
            title="Anotações"
            description="Faça anotações organizadas e transforme-as em flashcards."
            icon="FileText"
            gradientColors={colors.secondaryGradient}
            onPress={() => handleFeaturePress("notes")}
          />
          <FeatureCard
            title="Planos de Estudo"
            description="Organize seu tempo com planos de estudo personalizados."
            icon="Calendar"
            gradientColors={colors.primaryGradient}
            onPress={() => handleFeaturePress("study-plans")}
          />
        </View>

        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Geração de Conteúdo</Text>
          <FeatureCard
            title="Gerador de Textos"
            description="Gere textos de estudo sobre qualquer assunto com IA."
            icon="FileText"
            gradientColors={["#8B5CF6", "#6366F1"]}
            onPress={() => handleFeaturePress("text-generator")}
            new
          />
          <FeatureCard
            title="Conversor de Anotações"
            description="Transforme suas anotações em flashcards automaticamente."
            icon="Repeat"
            gradientColors={["#F59E0B", "#F97316"]}
            onPress={() => handleFeaturePress("notes")}
            new
          />
        </View>

        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Dicas</Text>
          <GlassCard style={styles.tipCard} gradient>
            <Text style={styles.tipTitle}>Estudo Eficiente</Text>
            <Text style={styles.tipText}>
              Combine diferentes ferramentas para maximizar seu aprendizado. Use mapas mentais para entender conceitos, flashcards para memorização e planos de estudo para organizar seu tempo.
            </Text>
          </GlassCard>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  headerSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerCard: {
    padding: 20,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
  },
  headerText: {
    fontSize: 16,
    color: colors.textLight,
    lineHeight: 22,
  },
  featuresSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  tipsSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  tipCard: {
    padding: 16,
    marginBottom: 16,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: colors.textLight,
    lineHeight: 20,
  },
});
