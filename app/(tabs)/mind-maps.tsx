import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, SafeAreaView, Pressable, TextInput, Alert, ActivityIndicator } from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { FeatureCard } from "@/components/FeatureCard";
import { MindMapCard } from "@/components/MindMapCard";
import { useMindMapStore } from "@/store/mindMapStore";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import {
  Network,
  Plus,
  BrainCircuit,
  GitBranch,
  Search,
  Circle,
  Square,
  Triangle,
  Hexagon,
  Palette,
  Filter,
  SlidersHorizontal,
  X,
  Check,
  Sparkles,
} from "lucide-react-native";
import { MindMap, MindMapNode, MindMapConnection } from "@/types";
import { AIGenerationModal } from "@/components/AIGenerationModal";
import { generateMindMap } from "@/services/openai";

export default function MindMapsScreen() {
  const router = useRouter();
  const { mindMaps, addMindMap, fetchMindMaps, loading } = useMindMapStore();

  useEffect(() => {
    fetchMindMaps();
  }, []);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    types: {
      concept: false,
      brain: false,
      flow: false,
    },
    shapes: {
      circle: false,
      square: false,
      triangle: false,
      hexagon: false,
    },
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [newMapTitle, setNewMapTitle] = useState("");
  const [newMapSubject, setNewMapSubject] = useState("");
  const [newMapType, setNewMapType] = useState<string>("brain");
  const [newMapColor, setNewMapColor] = useState(colors.primary);

  const handleMindMapPress = (mindMap: MindMap) => {
    router.push(`/mind-map/${mindMap.id}`);
  };

  const handleCreateMindMap = (type: string = "general") => {
    let title = newMapTitle || "Novo Mapa Mental";
    let subject = newMapSubject || "Geral";
    let color = newMapColor;

    if (!newMapTitle) {
      switch (type) {
        case "concept":
          title = "Novo Mapa Conceitual";
          color = colors.accent1;
          break;
        case "flow":
          title = "Novo Fluxograma";
          color = colors.secondary;
          break;
        case "brain":
          title = "Novo Mapa Mental Livre";
          color = colors.primary;
          break;
      }
    }

    const newMindMap: MindMap = {
      id: `mindmap_${Date.now()}`,
      title,
      subject,
      description: "",
      lastEdited: new Date().toISOString(),
      nodes: [],
      connections: [],
      type: type,
      color: color,
    };

    addMindMap(newMindMap);
    setShowCreateModal(false);
    setNewMapTitle("");
    setNewMapSubject("");
    setNewMapType("brain");
    setNewMapColor(colors.primary);
    router.push(`/mind-map/${newMindMap.id}`);
  };

  const handleGenerateMindMap = async (topic: string) => {
    try {
      setIsGenerating(true);

      // Generate mind map using OpenAI
      const result = await generateMindMap(topic, 7);

      if (!result || !result.nodes || !result.connections) {
        Alert.alert("Erro", "Não foi possível gerar o mapa mental. Por favor, tente novamente.");
        return;
      }

      // Transform the generated data to our app's format
      // Organize nodes in a tree structure for better visualization
      const nodes: MindMapNode[] = result.nodes.map((node, index) => {
        // Calculate position based on level for a tree-like structure
        const level = node.level || 0;

        // Position nodes in a hierarchical tree layout
        // Root node at center, children spread horizontally based on level
        const x = 400; // Center X position
        const y = 100 + (level * 150); // Y position based on level (top to bottom)

        // Assign different colors based on level
        let color;
        switch (level) {
          case 0:
            color = "#333333"; // Dark color for root node
            break;
          case 1:
            color = colors.primary;
            break;
          case 2:
            color = colors.accent1;
            break;
          case 3:
            color = colors.secondary;
            break;
          default:
            color = colors.accent2 || "#6366F1";
        }

        return {
          id: node.id,
          text: node.text,
          x,
          y,
          color,
          shape: (level === 0 ? "rectangle" : level === 1 ? "rectangle" : "rectangle") as any,
          style: level === 0 ? "filled" : "outline",
          size: level === 0 ? "large" : level === 1 ? "medium" : "small",
        };
      });

      // Create connections
      const connections: MindMapConnection[] = result.connections.map((conn, index) => ({
        id: `conn_${index}`,
        source: conn.source,
        target: conn.target,
        color: colors.text,
        width: 2,
        style: "solid",
      }));

      // Create the mind map
      const newMindMap: MindMap = {
        id: `mindmap_${Date.now()}`,
        title: `${topic}`,
        subject: "Gerado por IA",
        description: `Mapa mental sobre ${topic} gerado por inteligência artificial`,
        lastEdited: new Date().toISOString(),
        nodes,
        connections,
        type: "concept",
        color: colors.accent1,
        aiGenerated: true,
      };

      addMindMap(newMindMap);
      setShowAIModal(false);
      router.push(`/mind-map/${newMindMap.id}`);

    } catch (error) {
      console.error("Error generating mind map:", error);
      Alert.alert("Erro", "Ocorreu um erro ao gerar o mapa mental. Por favor, verifique sua conexão e tente novamente.");
    } finally {
      setIsGenerating(false);
    }
  };

  const toggleFilter = (category: 'types' | 'shapes', key: string) => {
    setFilters(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: !prev[category][key],
      }
    }));
  };

  const clearFilters = () => {
    setFilters({
      types: {
        concept: false,
        brain: false,
        flow: false,
      },
      shapes: {
        circle: false,
        square: false,
        triangle: false,
        hexagon: false,
      },
    });
    setSearchQuery("");
  };

  const isAnyFilterActive = () => {
    return (
      Object.values(filters.types).some(value => value) ||
      Object.values(filters.shapes).some(value => value) ||
      searchQuery.trim() !== ""
    );
  };

  // Filter mind maps based on search and filters
  const filteredMindMaps = mindMaps.filter(mindMap => {
    // Search filter
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      const matchesTitle = mindMap.title.toLowerCase().includes(query);
      const matchesSubject = mindMap.subject.toLowerCase().includes(query);
      const matchesDescription = mindMap.description?.toLowerCase().includes(query);

      if (!matchesTitle && !matchesSubject && !matchesDescription) {
        return false;
      }
    }

    // Type filters
    const typeFiltersActive = Object.values(filters.types).some(value => value);
    if (typeFiltersActive) {
      if (!mindMap.type || !filters.types[mindMap.type]) {
        return false;
      }
    }

    // Shape filters
    const shapeFiltersActive = Object.values(filters.shapes).some(value => value);
    if (shapeFiltersActive) {
      // Check if any node has the selected shape
      const hasMatchingShape = mindMap.nodes?.some(node =>
        node.shape && filters.shapes[node.shape]
      );

      if (!hasMatchingShape) {
        return false;
      }
    }

    return true;
  });

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Mapas e Fluxogramas"
        rightComponent={
          <Pressable
            style={styles.aiButton}
            onPress={() => setShowAIModal(true)}
          >
            <Sparkles size={24} color={colors.primary} />
          </Pressable>
        }
      />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerSection}>
          <Text style={styles.headerText}>
            Organize suas ideias e conecte conceitos com mapas mentais e fluxogramas interativos
          </Text>
          <Button
            title="Criar novo mapa"
            onPress={() => setShowCreateModal(true)}
            variant="primary"
            size="large"
            fullWidth
            icon={Plus}
          />
        </View>

        <View style={styles.searchSection}>
          <View style={styles.searchInputContainer}>
            <Search size={20} color={colors.textLight} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar mapas mentais"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <Pressable onPress={() => setSearchQuery("")} style={styles.clearSearch}>
                <X size={18} color={colors.textLight} />
              </Pressable>
            )}
          </View>
          <Pressable
            style={[
              styles.filterButton,
              showFilters && styles.filterButtonActive
            ]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal size={20} color={showFilters ? colors.white : colors.text} />
          </Pressable>
        </View>

        {showFilters && (
          <GlassCard style={styles.filtersContainer}>
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Tipo de Mapa</Text>
              <View style={styles.filterOptions}>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.types.concept && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('types', 'concept')}
                >
                  <Network size={18} color={filters.types.concept ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.types.concept && styles.filterOptionTextActive
                  ]}>Conceitual</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.types.brain && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('types', 'brain')}
                >
                  <BrainCircuit size={18} color={filters.types.brain ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.types.brain && styles.filterOptionTextActive
                  ]}>Mental</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.types.flow && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('types', 'flow')}
                >
                  <GitBranch size={18} color={filters.types.flow ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.types.flow && styles.filterOptionTextActive
                  ]}>Fluxo</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Formas</Text>
              <View style={styles.filterOptions}>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.shapes.circle && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('shapes', 'circle')}
                >
                  <Circle size={18} color={filters.shapes.circle ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.shapes.circle && styles.filterOptionTextActive
                  ]}>Círculo</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.shapes.square && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('shapes', 'square')}
                >
                  <Square size={18} color={filters.shapes.square ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.shapes.square && styles.filterOptionTextActive
                  ]}>Quadrado</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.shapes.triangle && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('shapes', 'triangle')}
                >
                  <Triangle size={18} color={filters.shapes.triangle ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.shapes.triangle && styles.filterOptionTextActive
                  ]}>Triângulo</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.filterOption,
                    filters.shapes.hexagon && styles.filterOptionActive
                  ]}
                  onPress={() => toggleFilter('shapes', 'hexagon')}
                >
                  <Hexagon size={18} color={filters.shapes.hexagon ? colors.white : colors.text} />
                  <Text style={[
                    styles.filterOptionText,
                    filters.shapes.hexagon && styles.filterOptionTextActive
                  ]}>Hexágono</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.filterActions}>
              <Button
                title="Limpar filtros"
                onPress={clearFilters}
                variant="outline"
                size="medium"
                icon={X}
              />
            </View>
          </GlassCard>
        )}

        {isAnyFilterActive() && (
          <View style={styles.activeFiltersContainer}>
            <Text style={styles.activeFiltersText}>
              {filteredMindMaps.length} {filteredMindMaps.length === 1 ? 'resultado' : 'resultados'} encontrados
            </Text>
            <Pressable style={styles.clearFiltersButton} onPress={clearFilters}>
              <Text style={styles.clearFiltersText}>Limpar</Text>
              <X size={14} color={colors.primary} />
            </Pressable>
          </View>
        )}

        <View style={styles.statsSection}>
          <GlassCard style={styles.statCard} gradient>
            <Text style={styles.statValue}>{mindMaps.length}</Text>
            <Text style={styles.statLabel}>Mapas criados</Text>
          </GlassCard>
          <GlassCard style={styles.statCard} gradient>
            <Text style={styles.statValue}>
              {mindMaps.reduce((total, map) => total + (map.nodes?.length || 0), 0)}
            </Text>
            <Text style={styles.statLabel}>Conceitos</Text>
          </GlassCard>
          <GlassCard style={styles.statCard} gradient>
            <Text style={styles.statValue}>
              {mindMaps.reduce((total, map) => total + (map.connections?.length || 0), 0)}
            </Text>
            <Text style={styles.statLabel}>Conexões</Text>
          </GlassCard>
        </View>

        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Ferramentas</Text>
          <FeatureCard
            title="Mapa Conceitual"
            description="Crie mapas com conceitos e suas relações hierárquicas."
            icon="Network"
            gradientColors={[colors.accent1, `${colors.accent1}80`]}
            onPress={() => {
              setNewMapType("concept");
              setNewMapColor(colors.accent1);
              setShowCreateModal(true);
            }}
          />
          <FeatureCard
            title="Mapa Mental"
            description="Organize ideias de forma livre e criativa."
            icon="BrainCircuit"
            gradientColors={colors.primaryGradient}
            onPress={() => {
              setNewMapType("brain");
              setNewMapColor(colors.primary);
              setShowCreateModal(true);
            }}
          />
          <FeatureCard
            title="Fluxograma"
            description="Crie diagramas para processos e procedimentos."
            icon="GitBranch"
            gradientColors={colors.secondaryGradient}
            onPress={() => {
              setNewMapType("flow");
              setNewMapColor(colors.secondary);
              setShowCreateModal(true);
            }}
          />
        </View>

        <View style={styles.mapsSection}>
          <Text style={styles.sectionTitle}>Seus Mapas</Text>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.loadingText}>Carregando mapas mentais...</Text>
            </View>
          ) : filteredMindMaps.length > 0 ? (
            filteredMindMaps.map((mindMap) => (
              <MindMapCard
                key={mindMap.id}
                mindMap={mindMap}
                onPress={() => handleMindMapPress(mindMap)}
              />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Network size={48} color={colors.textLight} />
              <Text style={styles.emptyText}>
                {isAnyFilterActive()
                  ? "Nenhum mapa encontrado com os filtros atuais"
                  : "Você ainda não tem mapas mentais"}
              </Text>
              {isAnyFilterActive() && (
                <Button
                  title="Limpar filtros"
                  onPress={clearFilters}
                  variant="outline"
                  size="medium"
                  icon={X}
                />
              )}
            </View>
          )}

          <Pressable
            style={styles.createMapCard}
            onPress={() => setShowCreateModal(true)}
          >
            <View style={styles.createMapContent}>
              <View style={styles.createMapIconContainer}>
                <Plus size={24} color={colors.primary} />
              </View>
              <Text style={styles.createMapText}>Criar novo mapa ou fluxograma</Text>
            </View>
          </Pressable>
        </View>

        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Dicas</Text>
          <GlassCard style={styles.tipCard} gradient>
            <Text style={styles.tipTitle}>Como criar mapas eficientes</Text>
            <Text style={styles.tipText}>
              Comece com um conceito central e expanda com ideias relacionadas. Use cores diferentes para categorias distintas e mantenha o mapa organizado.
            </Text>
          </GlassCard>
          <GlassCard style={styles.tipCard} gradient>
            <Text style={styles.tipTitle}>Mapas para revisão</Text>
            <Text style={styles.tipText}>
              Utilize mapas mentais para revisar conteúdos complexos. Eles ajudam a visualizar conexões entre conceitos e facilitam a memorização.
            </Text>
          </GlassCard>
        </View>
      </ScrollView>

      {/* Create Mind Map Modal */}
      {showCreateModal && (
        <View style={styles.modalOverlay}>
          <GlassCard style={styles.createModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Criar Novo Mapa</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => setShowCreateModal(false)}
              >
                <X size={24} color={colors.text} />
              </Pressable>
            </View>

            <TextInput
              style={styles.modalInput}
              placeholder="Título do mapa"
              value={newMapTitle}
              onChangeText={setNewMapTitle}
            />

            <TextInput
              style={styles.modalInput}
              placeholder="Assunto (opcional)"
              value={newMapSubject}
              onChangeText={setNewMapSubject}
            />

            <Text style={styles.modalSectionTitle}>Tipo de Mapa</Text>
            <View style={styles.typeOptions}>
              <Pressable
                style={[
                  styles.typeOption,
                  newMapType === "concept" && styles.typeOptionActive,
                  { borderColor: colors.accent1 }
                ]}
                onPress={() => {
                  setNewMapType("concept");
                  setNewMapColor(colors.accent1);
                }}
              >
                <View style={[styles.typeIconContainer, { backgroundColor: colors.accent1 }]}>
                  <Network size={24} color="#fff" />
                </View>
                <Text style={styles.typeOptionText}>Conceitual</Text>
                {newMapType === "concept" && (
                  <View style={styles.typeSelectedIndicator}>
                    <Check size={16} color={colors.accent1} />
                  </View>
                )}
              </Pressable>

              <Pressable
                style={[
                  styles.typeOption,
                  newMapType === "brain" && styles.typeOptionActive,
                  { borderColor: colors.primary }
                ]}
                onPress={() => {
                  setNewMapType("brain");
                  setNewMapColor(colors.primary);
                }}
              >
                <View style={[styles.typeIconContainer, { backgroundColor: colors.primary }]}>
                  <BrainCircuit size={24} color="#fff" />
                </View>
                <Text style={styles.typeOptionText}>Mental</Text>
                {newMapType === "brain" && (
                  <View style={styles.typeSelectedIndicator}>
                    <Check size={16} color={colors.primary} />
                  </View>
                )}
              </Pressable>

              <Pressable
                style={[
                  styles.typeOption,
                  newMapType === "flow" && styles.typeOptionActive,
                  { borderColor: colors.secondary }
                ]}
                onPress={() => {
                  setNewMapType("flow");
                  setNewMapColor(colors.secondary);
                }}
              >
                <View style={[styles.typeIconContainer, { backgroundColor: colors.secondary }]}>
                  <GitBranch size={24} color="#fff" />
                </View>
                <Text style={styles.typeOptionText}>Fluxo</Text>
                {newMapType === "flow" && (
                  <View style={styles.typeSelectedIndicator}>
                    <Check size={16} color={colors.secondary} />
                  </View>
                )}
              </Pressable>
            </View>

            <Text style={styles.modalSectionTitle}>Cor Principal</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.colorPicker}
            >
              {[
                colors.primary,
                colors.secondary,
                colors.accent1,
                colors.accent2,
                colors.accent3,
                colors.success,
                "#F472B6", // Pink
                "#34D399", // Green
                "#A78BFA", // Purple
                "#F97316", // Orange
                "#0EA5E9", // Sky blue
                "#64748B", // Slate
              ].map((color) => (
                <Pressable
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    newMapColor === color && styles.selectedColorOption,
                  ]}
                  onPress={() => setNewMapColor(color)}
                />
              ))}
            </ScrollView>

            <View style={styles.modalActions}>
              <Button
                title="Cancelar"
                onPress={() => setShowCreateModal(false)}
                variant="outline"
                size="medium"
              />
              <Button
                title="Criar Mapa"
                onPress={() => handleCreateMindMap(newMapType)}
                variant="primary"
                size="medium"
              />
            </View>
          </GlassCard>
        </View>
      )}

      {/* AI Generation Modal */}
      <AIGenerationModal
        visible={showAIModal}
        onClose={() => setShowAIModal(false)}
        title="Gerar Mapa Mental com IA"
        placeholder="Digite um tópico ou assunto para gerar um mapa mental..."
        onGenerate={handleGenerateMindMap}
        loading={isGenerating}
        type="mindmap"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Extra padding for floating tab bar
  },
  headerSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
    gap: 16,
  },
  headerText: {
    fontSize: 16,
    color: colors.textLight,
    lineHeight: 22,
  },
  searchSection: {
    flexDirection: "row",
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 12,
    alignItems: "center",
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 50,
    borderWidth: 1,
    borderColor: colors.border,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: colors.text,
  },
  clearSearch: {
    padding: 8,
  },
  filterButton: {
    width: 50,
    height: 50,
    borderRadius: 12,
    backgroundColor: colors.card,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
  },
  filtersContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  filterOptions: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  filterOption: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.border,
    gap: 8,
  },
  filterOptionActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  filterOptionTextActive: {
    color: colors.white,
  },
  filterActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  activeFiltersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: `${colors.primary}10`,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
  },
  activeFiltersText: {
    fontSize: 14,
    color: colors.text,
  },
  clearFiltersButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  clearFiltersText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  statsSection: {
    flexDirection: "row",
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    alignItems: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  featuresSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  mapsSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    backgroundColor: colors.backgroundLight,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    marginTop: 16,
    marginBottom: 16,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    backgroundColor: colors.backgroundLight,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 16,
  },
  createMapCard: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 20,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.primary,
    padding: 20,
    marginBottom: 16,
  },
  createMapContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  createMapIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  createMapText: {
    fontSize: 18,
    fontWeight: "500",
    color: colors.primary,
  },
  tipsSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  tipCard: {
    padding: 16,
    marginBottom: 16,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: colors.textLight,
    lineHeight: 20,
  },
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  createModal: {
    width: "90%",
    maxWidth: 500,
    padding: 20,
    borderRadius: 20,
    backgroundColor: colors.card,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
  },
  modalInput: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  typeOptions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  typeOption: {
    flex: 1,
    marginHorizontal: 4,
    padding: 12,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
  },
  typeOptionActive: {
    backgroundColor: `${colors.primary}10`,
  },
  typeIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  typeOptionText: {
    fontSize: 14,
    color: colors.text,
    fontWeight: "500",
  },
  typeSelectedIndicator: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
  },
  colorPicker: {
    flexDirection: "row",
    marginBottom: 20,
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: colors.text,
    transform: [{ scale: 1.1 }],
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
  },
  aiButton: {
    padding: 8,
  },
});