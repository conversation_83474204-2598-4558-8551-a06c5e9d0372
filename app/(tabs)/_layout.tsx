import React from "react";
import { Tabs } from "expo-router";
import { View, StyleSheet, Pressable, Image } from "react-native";
import { colors } from "@/constants/colors";
import { images } from "@/constants/images";
import { BlurView } from "expo-blur";
import { Platform } from "react-native";
import {
  Home,
  BookOpen,
  MessageSquare,
  Layers,
  Menu,
  Network,
  FileText,
  Calendar,
  Users,
  LucideIcon,
  GraduationCap,
} from "lucide-react-native";

interface TabBarIconProps {
  Icon: LucideIcon;
  color: string;
  size: number;
}

const TabBarIcon = ({ Icon, color, size }: TabBarIconProps) => {
  return <Icon size={size} color={color} />;
};

export default function TabsLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: colors.white,
        tabBarInactiveTintColor: colors.white,
        tabBarShowLabel: false,
        tabBarBackground: () => (
          <View style={[StyleSheet.absoluteFill, styles.tabBarBackground]} />
        ),
      }}
      tabBar={(props) => (
        <View style={styles.tabBarContainer}>
          <View style={styles.tabBarContent}>
            {props.state.routes
              .filter(route => [
                "index", "subjects", "study-groups", "chat", "calendar", "more"
              ].includes(route.name))
              .sort((a, b) => {
                const order = {"index": 1, "subjects": 2, "study-groups": 3, "chat": 4, "calendar": 5, "more": 6};
                return order[a.name] - order[b.name];
              })
              .map((route, index) => {
              const { options } = props.descriptors[route.key];
              const isFocused = props.state.index === index;

              const onPress = () => {
                const event = props.navigation.emit({
                  type: "tabPress",
                  target: route.key,
                  canPreventDefault: true,
                });

                if (!isFocused && !event.defaultPrevented) {
                  props.navigation.navigate(route.name);
                }
              };

              let iconName;
              if (route.name === "index") {
                iconName = "Home";
              } else if (route.name === "subjects") {
                iconName = "GraduationCap";
              } else if (route.name === "study-groups") {
                iconName = "Users";
              } else if (route.name === "chat") {
                iconName = "MessageSquare";
              } else if (route.name === "calendar") {
                iconName = "Calendar";
              } else if (route.name === "more") {
                iconName = "Menu";
              } else if (route.name === "mind-maps") {
                iconName = "Network";
              } else if (route.name === "notes") {
                iconName = "FileText";
              }

              let Icon;
              switch (iconName) {
                case "Home":
                  Icon = Home;
                  break;
                case "GraduationCap":
                  Icon = GraduationCap;
                  break;
                case "Users":
                  Icon = Users;
                  break;
                case "MessageSquare":
                  Icon = MessageSquare;
                  break;
                case "Layers":
                  Icon = Layers;
                  break;
                case "Menu":
                  Icon = Menu;
                  break;
                case "Network":
                  Icon = Network;
                  break;
                case "FileText":
                  Icon = FileText;
                  break;
                case "Calendar":
                  Icon = Calendar;
                  break;
                default:
                  Icon = Home;
              }

              return (
                <Pressable
                  key={route.key}
                  accessibilityRole="button"
                  accessibilityState={isFocused ? { selected: true } : {}}
                  onPress={onPress}
                  style={styles.tabButton}
                >
                  <View style={[styles.tabIconContainer, isFocused && styles.tabIconContainerActive]}>
                    {route.name === "chat" ? (
                      <Image
                        source={images.logoLiaBranco}
                        style={{ width: 24, height: 24 }}
                        resizeMode="contain"
                      />
                    ) : (
                      <TabBarIcon
                        Icon={Icon}
                        color={colors.white}
                        size={24}
                      />
                    )}
                  </View>
                </Pressable>
              );
            })}
          </View>
        </View>
      )}
    >
      <Tabs.Screen name="index" />
      <Tabs.Screen name="subjects" />
      <Tabs.Screen name="study-groups" />
      <Tabs.Screen name="chat" />
      <Tabs.Screen name="calendar" />
      <Tabs.Screen name="more" />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    elevation: 0,
    height: 0,
    backgroundColor: "transparent",
    borderTopWidth: 0,
  },
  tabBarBackground: {
    backgroundColor: colors.primary,
  },
  tabBarContainer: {
    position: "absolute",
    bottom: 25, // Adjusted to be closer to the bottom of the screen
    left: 15,
    right: 15,
    height: 65,
    borderRadius: 30,
    backgroundColor: colors.primary,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: `${colors.primary}80`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  tabBarContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around",
    height: "100%",
    paddingBottom: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  tabIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    opacity: 0.7,
  },
  tabIconContainerActive: {
    backgroundColor: `${colors.primaryDark}`,
    opacity: 1,
  },
});