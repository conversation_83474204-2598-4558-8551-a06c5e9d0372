import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  ActivityIndicator,
  ScrollView,
  Platform,
  Dimensions,
  Animated
} from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { Header } from "@/components/Header";
import { RouteGuard } from "@/components/RouteGuard";
import { LinearGradient } from "expo-linear-gradient";
import {
  Users,
  Search,
  Plus,
  UserPlus,
  Clock,
  BookOpen,
  Trophy,
  MessageCircle,
  TrendingUp,
  Sparkles,
  Star,
  Zap,
  ChevronRight,
  Brain
} from "lucide-react-native";
const { width } = Dimensions.get("window");

// Componente de Card de Estatística para Grupos
const GroupStatCard = ({ icon, label, value, gradient, change }: {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  gradient: string[];
  change?: { value: number; type: 'increase' | 'decrease' };
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[styles.statCard, { transform: [{ scale: scaleAnim }] }]}>
      <Pressable
        style={styles.statCardPressable}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <LinearGradient
          colors={gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.statCardGradient}
        >
          <View style={styles.statCardContent}>
            <View style={styles.statIconContainer}>
              {icon}
            </View>
            <Text style={styles.statValue}>{value}</Text>
            <Text style={styles.statLabel}>{label}</Text>
            {change && (
              <View style={styles.statChange}>
                <Text style={[
                  styles.statChangeText,
                  { color: change.type === 'increase' ? colors.success : colors.error }
                ]}>
                  {change.type === 'increase' ? '+' : '-'}{change.value}%
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

// Componente de Card de Grupo Moderno
const StudyGroupCard = ({ group, onPress }: {
  group: any;
  onPress: (group: any) => void;
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[styles.groupCard, { transform: [{ scale: scaleAnim }] }]}>
      <Pressable
        style={styles.groupCardPressable}
        onPress={() => onPress(group)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <LinearGradient
          colors={colors.cardGradient1}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.groupCardGradient}
        >
          <View style={styles.groupCardContent}>
            <View style={styles.groupCardHeader}>
              <View style={styles.groupIconContainer}>
                <Users size={theme.sizes.icon.md} color="#fff" />
              </View>
              <View style={styles.groupInfo}>
                <Text style={styles.groupName}>{group.name}</Text>
                <Text style={styles.groupDescription} numberOfLines={2}>
                  {group.description || 'Grupo de estudos colaborativo'}
                </Text>
              </View>
            </View>

            <View style={styles.groupStats}>
              <View style={styles.groupStat}>
                <UserPlus size={theme.sizes.icon.xs} color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.groupStatText}>{group.memberCount || 0} membros</Text>
              </View>
              <View style={styles.groupStat}>
                <Clock size={theme.sizes.icon.xs} color="rgba(255, 255, 255, 0.8)" />
                <Text style={styles.groupStatText}>Ativo</Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

export default function StudyGroupsScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [studyGroups] = useState([
    { id: '1', name: 'Matemática Avançada', description: 'Grupo focado em cálculo e álgebra linear', memberCount: 12 },
    { id: '2', name: 'Programação Web', description: 'Desenvolvimento frontend e backend', memberCount: 8 },
    { id: '3', name: 'Física Quântica', description: 'Estudos avançados em mecânica quântica', memberCount: 6 },
  ]);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Animações de entrada
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleCreateGroup = () => {
    router.push('/study-groups/create');
  };

  const handleJoinGroup = () => {
    router.push('/study-groups/join');
  };

  const handleGroupPress = (group: any) => {
    router.push(`/study-groups/${group.id}`);
  };

  if (isLoading) {
    return (
      <RouteGuard>
        <View style={[styles.container, styles.loadingContainer]}>
          <LinearGradient
            colors={["#F8FAFC", "#F1F5F9", "#E2E8F0"]}
            style={styles.backgroundGradient}
          />
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando grupos...</Text>
        </View>
      </RouteGuard>
    );
  }

  return (
    <RouteGuard>
      <View style={styles.container}>
        <LinearGradient
          colors={["#F8FAFC", "#F1F5F9", "#E2E8F0"]}
          style={styles.backgroundGradient}
        />
        <Header />

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.scrollContent, { paddingBottom: Platform.OS === 'ios' ? 120 : 100 }]}
          showsVerticalScrollIndicator={false}
          bounces={true}
          scrollEventThrottle={16}
        >
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
          >
            {/* Hero Section - Azul Predominante */}
            <View style={styles.heroSection}>
              <LinearGradient
                colors={colors.heroGradient4}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.heroGradient}
              >
                <View style={styles.heroContent}>
                  <View style={styles.heroHeader}>
                    <View style={styles.heroTextContainer}>
                      <Text style={styles.heroGreeting}>
                        👥 Grupos de Estudo
                      </Text>
                      <Text style={styles.heroSubtitle}>
                        Conecte-se, colabore e conquiste seus objetivos juntos
                      </Text>
                    </View>
                    <View style={styles.heroIconContainer}>
                      <Users size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                    </View>
                  </View>

                  <View style={styles.heroActionsContainer}>
                    <Pressable
                      style={({ pressed }) => [
                        styles.heroActionButton,
                        pressed && { transform: [{ scale: 0.95 }] }
                      ]}
                      onPress={handleCreateGroup}
                    >
                      <Plus size={theme.sizes.icon.sm} color="#fff" />
                      <Text style={styles.heroActionText}>Criar Grupo</Text>
                    </Pressable>

                    <Pressable
                      style={({ pressed }) => [
                        styles.heroActionButton,
                        pressed && { transform: [{ scale: 0.95 }] }
                      ]}
                      onPress={handleJoinGroup}
                    >
                      <UserPlus size={theme.sizes.icon.sm} color="#fff" />
                      <Text style={styles.heroActionText}>Entrar</Text>
                    </Pressable>
                  </View>
                </View>
              </LinearGradient>
            </View>

            {/* Estatísticas Compactas */}
            <View style={styles.statsSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Estatísticas dos Grupos</Text>
                <Text style={styles.sectionSubtitle}>Acompanhe sua participação</Text>
              </View>

              <View style={styles.statsGrid}>
                <GroupStatCard
                  icon={<Users size={theme.sizes.icon.sm} color="#fff" />}
                  label="Grupos"
                  value={studyGroups.length}
                  gradient={colors.cardGradient1}
                  change={{ value: 15, type: 'increase' }}
                />

                <GroupStatCard
                  icon={<MessageCircle size={theme.sizes.icon.sm} color="#fff" />}
                  label="Chat Ativo"
                  value="Online"
                  gradient={colors.cardGradient2}
                />

                <GroupStatCard
                  icon={<BookOpen size={theme.sizes.icon.sm} color="#fff" />}
                  label="Recursos"
                  value="Compartilhados"
                  gradient={colors.cardGradient3}
                  change={{ value: 8, type: 'increase' }}
                />

                <GroupStatCard
                  icon={<Trophy size={theme.sizes.icon.sm} color="#fff" />}
                  label="XP Ranking"
                  value="Top 10"
                  gradient={colors.cardGradient5}
                  change={{ value: 3, type: 'increase' }}
                />
              </View>
            </View>

            {/* Lista de Grupos */}
            <View style={styles.groupsSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Seus Grupos</Text>
                <Text style={styles.sectionSubtitle}>Participe e colabore</Text>
              </View>

              <View style={styles.groupsList}>
                {studyGroups.map((group) => (
                  <StudyGroupCard
                    key={group.id}
                    group={group}
                    onPress={handleGroupPress}
                  />
                ))}
              </View>
            </View>

            {/* Banner de Convite */}
            <View style={styles.inviteSection}>
              <Pressable
                style={({ pressed }) => [
                  styles.inviteCard,
                  pressed && { transform: [{ scale: 0.98 }] }
                ]}
                onPress={handleJoinGroup}
              >
                <LinearGradient
                  colors={colors.heroGradient2}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.inviteGradient}
                >
                  <View style={styles.inviteContent}>
                    <View style={styles.inviteTextContainer}>
                      <View style={styles.inviteBadgeContainer}>
                        <Sparkles size={theme.sizes.icon.sm} color="#fff" />
                        <Text style={styles.inviteBadge}>NOVO</Text>
                      </View>
                      <Text style={styles.inviteTitle}>Tem um código de convite?</Text>
                      <Text style={styles.inviteSubtitle}>
                        Entre em um grupo existente usando um código de convite
                      </Text>
                    </View>
                    <View style={styles.inviteIconContainer}>
                      <UserPlus size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                    </View>
                  </View>
                </LinearGradient>
              </Pressable>
            </View>
          </Animated.View>
        </ScrollView>
      </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.size.md,
    color: colors.textLight,
    fontWeight: '500',
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: theme.spacing.sm,
    paddingBottom: 100,
    flexGrow: 1,
  },

  // Hero Section Styles
  heroSection: {
    marginHorizontal: theme.spacing.md,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.lg,
    borderRadius: theme.borderRadius.hero,
    overflow: 'hidden',
    ...theme.shadows.xl,
    shadowColor: colors.primary,
  },
  heroGradient: {
    borderRadius: theme.borderRadius.hero,
  },
  heroContent: {
    padding: theme.spacing.cardPadding,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  heroTextContainer: {
    flex: 1,
    paddingRight: theme.spacing.componentGap,
  },
  heroGreeting: {
    fontSize: theme.typography.size.xl,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    marginBottom: theme.spacing.xs,
    lineHeight: 28,
  },
  heroSubtitle: {
    fontSize: theme.typography.size.sm,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: theme.typography.weight.medium,
    lineHeight: 20,
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.lg,
    height: theme.sizes.iconContainer.lg,
    borderRadius: theme.sizes.iconContainer.lg / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  heroActionsContainer: {
    flexDirection: 'row',
    gap: theme.spacing.componentGap,
    marginTop: theme.spacing.sm,
  },
  heroActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: theme.spacing.componentGap,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.button,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    gap: theme.spacing.xs,
    minHeight: theme.sizes.button.height.sm,
  },
  heroActionText: {
    color: colors.white,
    fontSize: theme.typography.size.sm,
    fontWeight: theme.typography.weight.semibold,
  },

  // Section Headers
  sectionHeader: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  sectionTitle: {
    fontSize: theme.typography.size.lg,
    fontWeight: theme.typography.weight.bold,
    color: colors.textDark,
    lineHeight: 24,
  },
  sectionSubtitle: {
    fontSize: theme.typography.size.sm,
    color: colors.textMedium,
    fontWeight: theme.typography.weight.medium,
    marginTop: theme.spacing.xxs,
  },

  // Stats Section
  statsSection: {
    marginBottom: theme.spacing.sectionSpacing,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: theme.spacing.md,
    gap: theme.spacing.componentGap,
  },
  statCard: {
    width: (width - theme.spacing.md * 2 - theme.spacing.componentGap) / 2,
    aspectRatio: 1.2,
  },
  statCardPressable: {
    flex: 1,
  },
  statCardGradient: {
    flex: 1,
    borderRadius: theme.borderRadius.card,
    ...theme.shadows.md,
  },
  statCardContent: {
    flex: 1,
    padding: theme.spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statIconContainer: {
    width: theme.sizes.iconContainer.sm,
    height: theme.sizes.iconContainer.sm,
    borderRadius: theme.sizes.iconContainer.sm / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValue: {
    fontSize: theme.typography.size.xl,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: theme.typography.size.xs,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: theme.typography.weight.medium,
    textAlign: 'center',
  },
  statChange: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: theme.spacing.xxs,
    borderRadius: theme.borderRadius.xs,
  },
  statChangeText: {
    fontSize: theme.typography.size.xs,
    fontWeight: theme.typography.weight.semibold,
  },

  // Groups Section
  groupsSection: {
    marginBottom: theme.spacing.sectionSpacing,
  },
  groupsList: {
    paddingHorizontal: theme.spacing.md,
    gap: theme.spacing.componentGap,
  },
  groupCard: {
    marginBottom: theme.spacing.componentGap,
  },
  groupCardPressable: {
    borderRadius: theme.borderRadius.card,
    overflow: 'hidden',
  },
  groupCardGradient: {
    borderRadius: theme.borderRadius.card,
    ...theme.shadows.md,
  },
  groupCardContent: {
    padding: theme.spacing.md,
  },
  groupCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  groupIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: theme.typography.size.md,
    fontWeight: theme.typography.weight.semibold,
    color: colors.white,
    marginBottom: theme.spacing.xxs,
  },
  groupDescription: {
    fontSize: theme.typography.size.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: theme.typography.weight.medium,
  },
  groupStats: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  groupStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  groupStatText: {
    fontSize: theme.typography.size.xs,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: theme.typography.weight.medium,
  },

  // Invite Section
  inviteSection: {
    marginBottom: theme.spacing.sectionSpacing,
    paddingHorizontal: theme.spacing.md,
  },
  inviteCard: {
    borderRadius: theme.borderRadius.card,
    overflow: 'hidden',
    ...theme.shadows.lg,
  },
  inviteGradient: {
    borderRadius: theme.borderRadius.card,
  },
  inviteContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.cardPadding,
    minHeight: 100,
  },
  inviteTextContainer: {
    flex: 1,
    paddingRight: theme.spacing.md,
  },
  inviteBadgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
    gap: theme.spacing.xs,
  },
  inviteBadge: {
    fontSize: theme.typography.size.xs,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xxs,
    borderRadius: theme.borderRadius.xs,
    overflow: 'hidden',
  },
  inviteTitle: {
    fontSize: theme.typography.size.md,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    marginBottom: theme.spacing.xxs,
  },
  inviteSubtitle: {
    fontSize: theme.typography.size.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: theme.typography.weight.medium,
  },
  inviteIconContainer: {
    width: theme.sizes.iconContainer.lg,
    height: theme.sizes.iconContainer.lg,
    borderRadius: theme.sizes.iconContainer.lg / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
