import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Pressable,
  Image,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";
import { useUserStore } from "@/store/userStore";
import { useAuthStore } from "@/store/authStore";
import {
  Settings,
  User,
  BookOpen,
  Compass,
  HelpCircle,
  Star,
  Bell,
  Shield,
  LogOut,
  ChevronRight,
  GraduationCap,
} from "lucide-react-native";

export default function MoreScreen() {
  const router = useRouter();
  const { user, supabaseUser } = useUserStore();
  const { signOut } = useAuthStore();

  const menuItems = [
    {
      title: "Meu Perfil",
      icon: User,
      onPress: () => router.push("/profile"),
      color: colors.primary,
    },
    {
      title: "<PERSON><PERSON>",
      icon: GraduationCap,
      onPress: () => router.push("/subjects"),
      color: "#3B82F6",
    },
    {
      title: "Explorar",
      icon: Compass,
      onPress: () => router.push("/explore"),
      color: colors.accent1,
    },
    {
      title: "Configurações",
      icon: Settings,
      onPress: () => router.push("/settings"),
      color: colors.text,
    },
    {
      title: "Ajuda e Suporte",
      icon: HelpCircle,
      onPress: () => router.push("/help"),
      color: colors.secondary,
    },
    {
      title: "Avalie o App",
      icon: Star,
      onPress: () => alert("Funcionalidade em desenvolvimento"),
      color: "#F59E0B",
    },
    {
      title: "Notificações",
      icon: Bell,
      onPress: () => router.push("/notifications"),
      color: "#8B5CF6",
    },
    {
      title: "Privacidade",
      icon: Shield,
      onPress: () => router.push("/privacy"),
      color: "#10B981",
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header title="Mais" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.profileSection}>
          <GlassCard style={styles.profileCard} gradient>
            <View style={styles.profileContent}>
              <View style={styles.profileImageContainer}>
                <LinearGradient
                  colors={colors.primaryGradient}
                  style={styles.profileImageGradient}
                >
                  <Text style={styles.profileInitials}>
                    {user?.name
                      ? user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()
                          .substring(0, 2)
                      : "U"}
                  </Text>
                </LinearGradient>
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>{user?.name || "Usuário"}</Text>
                <Text style={styles.profileEmail}>
                  {supabaseUser?.email || user?.email || "<EMAIL>"}
                </Text>
                <View style={styles.levelContainer}>
                  <Text style={styles.levelText}>Nível {user?.level || 1}</Text>
                  <View style={styles.xpContainer}>
                    <View
                      style={[
                        styles.xpBar,
                        { width: `${(user?.xp || 0) % 100}%` },
                      ]}
                    />
                  </View>
                </View>
              </View>
            </View>
            <Pressable
              style={styles.editProfileButton}
              onPress={() => router.push("/profile")}
            >
              <Text style={styles.editProfileText}>Editar Perfil</Text>
            </Pressable>
          </GlassCard>
        </View>

        <View style={styles.menuSection}>
          {menuItems.map((item, index) => (
            <Pressable
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <View style={styles.menuItemContent}>
                <View
                  style={[
                    styles.menuItemIconContainer,
                    { backgroundColor: `${item.color}15` },
                  ]}
                >
                  <item.icon size={20} color={item.color} />
                </View>
                <Text style={styles.menuItemText}>{item.title}</Text>
              </View>
              <ChevronRight size={20} color={colors.textLight} />
            </Pressable>
          ))}
        </View>

        <View style={styles.versionSection}>
          <Text style={styles.versionText}>Versão 1.0.0</Text>
          <Pressable
            style={styles.logoutButton}
            onPress={() => {
              Alert.alert(
                "Sair da conta",
                "Tem certeza que deseja sair da sua conta?",
                [
                  {
                    text: "Cancelar",
                    style: "cancel",
                  },
                  {
                    text: "Sair",
                    style: "destructive",
                    onPress: signOut,
                  },
                ]
              );
            }}
          >
            <LogOut size={16} color={colors.danger} />
            <Text style={styles.logoutText}>Sair</Text>
          </Pressable>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  profileSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  profileCard: {
    padding: 16,
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  profileImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: "hidden",
    marginRight: 16,
  },
  profileImageGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  profileInitials: {
    fontSize: 24,
    fontWeight: "bold",
    color: colors.white,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
  levelContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  levelText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.primary,
    marginRight: 8,
  },
  xpContainer: {
    flex: 1,
    height: 6,
    backgroundColor: `${colors.primary}20`,
    borderRadius: 3,
    overflow: "hidden",
  },
  xpBar: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  editProfileButton: {
    marginTop: 16,
    alignSelf: "flex-end",
  },
  editProfileText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.primary,
  },
  menuSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  menuItemContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  menuItemIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text,
  },
  versionSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    alignItems: "center",
  },
  versionText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 16,
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
  },
  logoutText: {
    fontSize: 16,
    color: colors.danger,
    marginLeft: 8,
  },
});
