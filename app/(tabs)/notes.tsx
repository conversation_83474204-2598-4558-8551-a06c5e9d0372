import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, TextInput, Alert, Animated } from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { NoteCard } from "@/components/NoteCard";
import { useNoteStore } from "@/store/noteStore";
import { useStudyStore } from "@/store/studyStore";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import { Search, Plus, FileText, Tag, Clock, BookOpen, Sparkles, Star } from "lucide-react-native";
import { Note } from "@/types";

export default function NotesScreen() {
  const router = useRouter();
  const { notes, addNote, fetchNotes, loading } = useNoteStore();
  const { subjects } = useStudyStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTag, setSelectedTag] = useState<string | null>(null);

  useEffect(() => {
    fetchNotes();
  }, []);

  // Get all unique tags from notes
  const allTags = Array.from(
    new Set(notes.flatMap((note) => note.tags))
  ).sort();

  // Filter notes based on search query and selected tag
  const filteredNotes = notes.filter((note) => {
    const matchesSearch = searchQuery
      ? note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    const matchesTag = selectedTag
      ? note.tags.includes(selectedTag)
      : true;

    return matchesSearch && matchesTag;
  });

  const handleNotePress = (note: Note) => {
    // Use a rota completa para evitar problemas de navegação
    router.push({
      pathname: "/notes/[id]",
      params: { id: note.id }
    });
  };

  const handleConvertToFlashcards = (note: Note) => {
    if (!note.content.trim()) {
      Alert.alert("Erro", "Esta anotação está vazia. Adicione conteúdo antes de converter para flashcards.");
      return;
    }

    router.push({
      pathname: "/flashcards-from-text",
      params: { text: note.content }
    });
  };

  const handleCreateNote = () => {
    // Mostrar modal para selecionar a matéria
    Alert.alert(
      "Selecionar Matéria",
      "Escolha a matéria para esta anotação",
      [
        ...subjects.map(subject => ({
          text: subject.title,
          onPress: () => createNoteForSubject(subject.title)
        })),
        {
          text: "Geral",
          onPress: () => createNoteForSubject("Geral")
        },
        {
          text: "Cancelar",
          style: "cancel"
        }
      ]
    );
  };

  const createNoteForSubject = (subjectName: string) => {
    // Criar uma nota vazia com um ID temporário
    const newNote: Note = {
      id: `note_${Date.now()}`,
      title: `Nova Anotação - ${subjectName}`,
      content: "",
      subject: subjectName,
      tags: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Adicionar a nota ao store
    addNote(newNote).then(savedNote => {
      if (savedNote) {
        // Navegar para a página de edição da nota
        router.push({
          pathname: "/notes/[id]",
          params: { id: savedNote.id }
        });
      }
    });
  };

  const handleTagPress = (tag: string) => {
    setSelectedTag(selectedTag === tag ? null : tag);
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Anotações"
        rightComponent={
          <View style={styles.headerButtonsContainer}>
            <Pressable
              style={styles.headerButton}
              onPress={() => {
                Alert.alert(
                  "Gerar Anotação com IA",
                  "Escolha a matéria para gerar uma anotação com IA",
                  [
                    ...subjects.map(subject => ({
                      text: subject.title,
                      onPress: () => {
                        Alert.alert(
                          "Gerar Anotação",
                          `Deseja gerar uma anotação sobre ${subject.title} usando inteligência artificial?`,
                          [
                            { text: "Cancelar", style: "cancel" },
                            {
                              text: "Gerar",
                              onPress: () => {
                                // Aqui seria implementada a chamada para a API da OpenAI
                                Alert.alert("Funcionalidade em desenvolvimento", "A geração de anotações com IA estará disponível em breve!");
                              }
                            }
                          ]
                        );
                      }
                    })),
                    {
                      text: "Cancelar",
                      style: "cancel"
                    }
                  ]
                );
              }}
            >
              <Sparkles size={24} color={colors.primary} />
            </Pressable>
            <Pressable
              style={styles.headerButton}
              onPress={() => {
                if (notes.length > 0) {
                  router.push("/flashcards-from-text");
                } else {
                  Alert.alert("Sem anotações", "Crie uma anotação primeiro para poder convertê-la em flashcards.");
                }
              }}
            >
              <BookOpen size={24} color={colors.primary} />
            </Pressable>
          </View>
        }
      />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <LinearGradient
            colors={['#3399FF', '#66B2FF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.heroGradient}
          >
            <View style={styles.heroContent}>
              <View style={styles.heroHeader}>
                <View style={styles.heroTextContainer}>
                  <Text style={styles.heroTitle}>Suas Anotações</Text>
                  <Text style={styles.heroSubtitle}>
                    Organize e acesse suas anotações de estudo facilmente
                  </Text>
                </View>
                <View style={styles.heroIconContainer}>
                  <FileText size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                </View>
              </View>
              <View style={styles.quickActionsContainer}>
                <Pressable
                  style={styles.quickActionButton}
                  onPress={handleCreateNote}
                >
                  <Plus size={theme.sizes.icon.sm} color="#fff" />
                  <Text style={styles.quickActionText}>Criar</Text>
                </Pressable>
                <Pressable
                  style={styles.quickActionButton}
                  onPress={() => {
                    Alert.alert(
                      "Gerar Anotação com IA",
                      "Escolha a matéria para gerar uma anotação com IA",
                      [
                        ...subjects.map(subject => ({
                          text: subject.title,
                          onPress: () => {
                            Alert.alert(
                              "Gerar Anotação",
                              `Deseja gerar uma anotação sobre ${subject.title} usando inteligência artificial?`,
                              [
                                { text: "Cancelar", style: "cancel" },
                                {
                                  text: "Gerar",
                                  onPress: () => {
                                    Alert.alert("Funcionalidade em desenvolvimento", "A geração de anotações com IA estará disponível em breve!");
                                  }
                                }
                              ]
                            );
                          }
                        })),
                        {
                          text: "Cancelar",
                          style: "cancel"
                        }
                      ]
                    );
                  }}
                >
                  <Sparkles size={theme.sizes.icon.sm} color="#fff" />
                  <Text style={styles.quickActionText}>IA</Text>
                </Pressable>
              </View>
            </View>
          </LinearGradient>
        </View>

        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <Search size={20} color={colors.primary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Pesquisar anotações..."
              placeholderTextColor={colors.textLight}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        {allTags.length > 0 && (
          <View style={styles.tagsSection}>
            <Text style={styles.sectionTitle}>Tags</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.tagsContainer}
            >
              {allTags.map((tag) => (
                <Pressable
                  key={tag}
                  style={[
                    styles.tagChip,
                    selectedTag === tag && styles.selectedTagChip,
                  ]}
                  onPress={() => handleTagPress(tag)}
                >
                  <Text
                    style={[
                      styles.tagText,
                      selectedTag === tag && styles.selectedTagText,
                    ]}
                  >
                    {tag}
                  </Text>
                </Pressable>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Statistics Section */}
        <View style={styles.statsSection}>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#3399FF', '#66B2FF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <FileText size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>{notes.length}</Text>
                <Text style={styles.statLabel}>Anotações</Text>
              </LinearGradient>
            </View>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#F59E0B', '#FBBF24']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <Tag size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>{allTags.length}</Text>
                <Text style={styles.statLabel}>Tags</Text>
              </LinearGradient>
            </View>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#10B981', '#34D399']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <BookOpen size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>
                  {notes.reduce(
                    (total, note) =>
                      total + Math.ceil(note.content.length / 500),
                    0
                  )}
                </Text>
                <Text style={styles.statLabel}>Páginas</Text>
              </LinearGradient>
            </View>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#8B5CF6', '#A78BFA']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <Clock size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>
                  {notes.filter(note => {
                    const noteDate = new Date(note.updatedAt);
                    const today = new Date();
                    const diffTime = Math.abs(today.getTime() - noteDate.getTime());
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays <= 7;
                  }).length}
                </Text>
                <Text style={styles.statLabel}>Esta Semana</Text>
              </LinearGradient>
            </View>
          </View>
        </View>

        <View style={styles.notesSection}>
          <Text style={styles.sectionTitle}>
            {searchQuery || selectedTag
              ? `Resultados (${filteredNotes.length})`
              : "Suas Anotações"}
          </Text>

          {filteredNotes.length > 0 ? (
            filteredNotes.map((note) => (
              <NoteCard
                key={note.id}
                note={note}
                onPress={() => handleNotePress(note)}
                onConvert={() => handleConvertToFlashcards(note)}
              />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <FileText size={48} color={colors.textLight} />
              <Text style={styles.emptyText}>
                {searchQuery || selectedTag
                  ? "Nenhuma anotação encontrada"
                  : "Você ainda não tem anotações"}
              </Text>
              <Button
                title="Criar anotação"
                onPress={handleCreateNote}
                variant="primary"
                size="large"
              />
            </View>
          )}

          <Pressable
            style={styles.createNoteCard}
            onPress={handleCreateNote}
          >
            <View style={styles.createNoteContent}>
              <View style={styles.createNoteIconContainer}>
                <Plus size={24} color={colors.primary} />
              </View>
              <Text style={styles.createNoteText}>Criar nova anotação</Text>
            </View>
          </Pressable>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Extra padding for floating tab bar
  },
  // Hero Section Styles
  heroSection: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  heroGradient: {
    borderRadius: 20,
  },
  heroContent: {
    padding: 20,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 18,
  },
  heroTextContainer: {
    flex: 1,
    paddingRight: 12,
  },
  heroTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#fff",
    marginBottom: 4,
    lineHeight: 28,
  },
  heroSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: '500',
    lineHeight: 20,
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  quickActionsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    gap: 6,
  },
  quickActionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  searchSection: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    height: 60,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: colors.text,
    fontWeight: "500",
  },
  tagsSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  tagsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  tagChip: {
    backgroundColor: `${colors.primary}15`,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
  },
  selectedTagChip: {
    backgroundColor: colors.primary,
  },
  tagText: {
    fontSize: 14,
    color: colors.primary,
  },
  selectedTagText: {
    color: "#fff",
  },
  // Stats Section Styles
  statsSection: {
    marginHorizontal: 16,
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: '48%',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  statCardGradient: {
    padding: 16,
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.9)',
    minHeight: 90,
    justifyContent: 'center',
  },
  statIconContainer: {
    width: theme.sizes.iconContainer.sm,
    height: theme.sizes.iconContainer.sm,
    borderRadius: theme.sizes.iconContainer.sm / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "700",
    color: colors.white,
    marginBottom: 2,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 11,
    fontWeight: "600",
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: "center",
    lineHeight: 14,
  },
  notesSection: {
    paddingHorizontal: 16,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    backgroundColor: colors.backgroundLight,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    marginVertical: 16,
  },
  createNoteCard: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 20,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.primary,
    padding: 20,
    marginBottom: 16,
  },
  createNoteContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  createNoteIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  createNoteText: {
    fontSize: 18,
    fontWeight: "500",
    color: colors.primary,
  },
  headerButtonsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
});