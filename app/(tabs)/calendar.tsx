import React, { useState, useEffect, useRef } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, ActivityIndicator, Platform, Animated, TouchableOpacity, StatusBar as RNStatusBar } from "react-native";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import { Calendar as CalendarIcon, Clock, BookOpen, Plus, CheckCircle, Circle, ChevronLeft, ChevronRight, List, Edit, ClipboardList, Grid, Columns, LayoutList } from "lucide-react-native";
import { useCalendarStore } from "@/store/calendarStore";
import { StatusBar } from "expo-status-bar";
import { CalendarFAB } from "@/components/CalendarFAB";
import { TaskFAB } from "@/components/TaskFAB";

// import { useScheduleStore } from "@/store/scheduleStore";
import { CalendarEvent, TodoItem } from "@/types";
import { Calendar as RNCalendar } from "react-native-calendars";
import { format, parseISO, isToday, isTomorrow, isThisWeek, isAfter, isBefore, addDays, startOfWeek, endOfWeek, eachDayOfInterval } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useRouter } from "expo-router";
import { TodoForm } from "@/components/TodoForm";


type ViewType = 'calendar' | 'todo';
type CalendarViewMode = 'month' | 'week' | 'day';

export default function CalendarScreen() {
  const router = useRouter();
  const { events, todos, fetchEvents, fetchTodos, addTodo, toggleTodoCompleted, loading: calendarLoading } = useCalendarStore();
  // const { schedules, fetchSchedules, fetchScheduleItems, loading: scheduleLoading } = useScheduleStore();

  const [activeView, setActiveView] = useState<ViewType>('todo'); // Começamos com a aba de tarefas
  const fadeAnim = useRef(new Animated.Value(1)).current;

  const handleViewChange = (view: ViewType) => {
    // Animação de fade out
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      // Muda a visualização
      setActiveView(view);

      // Animação de fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    });
  };
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [calendarViewMode, setCalendarViewMode] = useState<CalendarViewMode>('month');
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  const [showTodoForm, setShowTodoForm] = useState(false);
  const [currentTodo, setCurrentTodo] = useState<Partial<TodoItem> | null>(null);
  const [todoFilter, setTodoFilter] = useState<'all' | 'pending' | 'completed'>('all');

  useEffect(() => {
    const loadData = async () => {
      await fetchEvents();
      await fetchTodos();
      // await fetchSchedules();
    };

    loadData();
  }, []);

  // Gerar dias da semana quando o modo de visualização ou data selecionada mudar
  useEffect(() => {
    if (calendarViewMode === 'week') {
      try {
        console.log('Gerando dias da semana para a data:', selectedDate);

        // Garantir que a data selecionada seja válida
        let dateToUse;
        try {
          dateToUse = new Date(selectedDate);
          // Verificar se a data é válida
          if (isNaN(dateToUse.getTime())) {
            throw new Error('Data inválida');
          }
        } catch (e) {
          console.warn('Data selecionada inválida, usando data atual');
          dateToUse = new Date();
        }

        // Calcular início e fim da semana
        const start = startOfWeek(dateToUse, { weekStartsOn: 0 }); // Domingo
        const end = endOfWeek(dateToUse, { weekStartsOn: 0 }); // Sábado

        console.log('Início da semana:', format(start, 'yyyy-MM-dd'));
        console.log('Fim da semana:', format(end, 'yyyy-MM-dd'));

        // Gerar array com todos os dias da semana
        const days = eachDayOfInterval({ start, end });
        console.log('Dias da semana gerados:', days.map(d => format(d, 'yyyy-MM-dd')));

        // Atualizar estado
        setWeekDays(days);
      } catch (error) {
        console.error('Erro ao gerar dias da semana:', error);
        // Fallback para evitar problemas
        const today = new Date();
        const start = startOfWeek(today, { weekStartsOn: 0 });
        const end = endOfWeek(today, { weekStartsOn: 0 });
        const days = eachDayOfInterval({ start, end });
        setWeekDays(days);
      }
    }
  }, [calendarViewMode, selectedDate]);

  // Funções de navegação do calendário
  const handlePreviousMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentMonth(newDate);
  };

  const handleNextMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentMonth(newDate);
  };

  const handlePreviousWeek = () => {
    try {
      console.log('Navegando para semana anterior. Data atual:', selectedDate);

      // Garantir que a data selecionada seja válida
      let dateToUse;
      try {
        dateToUse = new Date(selectedDate);
        if (isNaN(dateToUse.getTime())) {
          throw new Error('Data inválida');
        }
      } catch (e) {
        console.warn('Data selecionada inválida, usando data atual');
        dateToUse = new Date();
      }

      // Subtrair 7 dias diretamente da data atual
      const previousWeekDate = addDays(dateToUse, -7);
      console.log('Data da semana anterior:', format(previousWeekDate, 'yyyy-MM-dd'));

      // Formatar a data corretamente
      const formattedDate = format(previousWeekDate, 'yyyy-MM-dd');
      console.log('Data formatada para semana anterior:', formattedDate);

      // Atualizar a data selecionada
      setSelectedDate(formattedDate);
    } catch (error) {
      console.error('Erro ao navegar para semana anterior:', error);
      // Fallback em caso de erro
      const today = new Date();
      const previousWeek = addDays(today, -7);
      setSelectedDate(format(previousWeek, 'yyyy-MM-dd'));
    }
  };

  const handleNextWeek = () => {
    try {
      console.log('Navegando para próxima semana. Data atual:', selectedDate);

      // Garantir que a data selecionada seja válida
      let dateToUse;
      try {
        dateToUse = new Date(selectedDate);
        if (isNaN(dateToUse.getTime())) {
          throw new Error('Data inválida');
        }
      } catch (e) {
        console.warn('Data selecionada inválida, usando data atual');
        dateToUse = new Date();
      }

      // Adicionar 7 dias diretamente à data atual
      const nextWeekDate = addDays(dateToUse, 7);
      console.log('Data da próxima semana:', format(nextWeekDate, 'yyyy-MM-dd'));

      // Formatar a data corretamente
      const formattedDate = format(nextWeekDate, 'yyyy-MM-dd');
      console.log('Data formatada para próxima semana:', formattedDate);

      // Atualizar a data selecionada
      setSelectedDate(formattedDate);
    } catch (error) {
      console.error('Erro ao navegar para próxima semana:', error);
      // Fallback em caso de erro
      const today = new Date();
      const nextWeek = addDays(today, 7);
      setSelectedDate(format(nextWeek, 'yyyy-MM-dd'));
    }
  };

  const handlePreviousDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() - 1);
    setSelectedDate(newDate.toISOString().split('T')[0]);
  };

  const handleNextDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + 1);
    setSelectedDate(newDate.toISOString().split('T')[0]);
  };

  // Format calendar events for the calendar component
  const markedDates = events.reduce((acc, event) => {
    const dateStr = event.startDate.split('T')[0];
    if (!acc[dateStr]) {
      acc[dateStr] = { marked: true, dotColor: event.color };
    }
    return acc;
  }, {} as Record<string, any>);

  // Add today's marker
  markedDates[new Date().toISOString().split('T')[0]] = {
    ...markedDates[new Date().toISOString().split('T')[0]],
    selected: selectedDate === new Date().toISOString().split('T')[0],
    selectedColor: colors.primary,
  };

  // Add selected date marker
  if (selectedDate !== new Date().toISOString().split('T')[0]) {
    markedDates[selectedDate] = {
      ...markedDates[selectedDate],
      selected: true,
      selectedColor: colors.primary,
    };
  }

  // Filter events for the selected date
  const eventsForSelectedDate = events.filter(event => {
    const eventDate = event.startDate.split('T')[0];
    return eventDate === selectedDate;
  });

  // Filter todos based on the selected filter
  const filteredTodos = todos.filter(todo => {
    if (todoFilter === 'all') return true;
    if (todoFilter === 'pending') return !todo.completed;
    if (todoFilter === 'completed') return todo.completed;
    return true;
  });

  // Group todos by due date
  const groupedTodos = filteredTodos.reduce((acc, todo) => {
    let group = 'future';

    if (!todo.dueDate) {
      group = 'no-date';
    } else {
      const dueDate = parseISO(todo.dueDate);
      if (isToday(dueDate)) {
        group = 'today';
      } else if (isTomorrow(dueDate)) {
        group = 'tomorrow';
      } else if (isThisWeek(dueDate) && isAfter(dueDate, new Date())) {
        group = 'this-week';
      } else if (isBefore(dueDate, new Date())) {
        group = 'overdue';
      }
    }

    if (!acc[group]) {
      acc[group] = [];
    }

    acc[group].push(todo);
    return acc;
  }, {} as Record<string, TodoItem[]>);

  const handleAddEvent = () => {
    console.log('Navegando para nova página de evento');
    router.push('/events/new' as any);
  };

  const handleEventPress = (event: CalendarEvent) => {
    router.push({
      pathname: '/events/[id]',
      params: { id: event.id }
    } as any);
  };

  // Removida a função handleSaveEvent pois agora usamos páginas separadas

  const handleAddTodo = () => {
    console.log('Navegando para nova página de tarefa');
    router.push('/tasks/new' as any);
  };

  // const handleAddSchedule = () => {
  //   // Navegar para a tela de criação de cronograma
  //   router.push('/schedules/create');
  // };





  const handleSaveTodo = (todo: Omit<TodoItem, "id" | "createdAt" | "updatedAt">) => {
    if (currentTodo?.id) {
      // Update existing todo
      useCalendarStore.getState().updateTodo(currentTodo.id, todo).then(updatedTodo => {
        if (updatedTodo) {
          Alert.alert("Tarefa atualizada", "Tarefa atualizada com sucesso!");
          fetchTodos();
        }
      });
    } else {
      // Add new todo
      addTodo(todo).then(savedTodo => {
        if (savedTodo) {
          Alert.alert("Tarefa criada", "Tarefa adicionada com sucesso!");
        }
      });
    }
  };

  const handleToggleTodo = (todo: TodoItem) => {
    toggleTodoCompleted(todo.id);
  };

  const renderTodoItem = (todo: TodoItem) => {
    // Determinar a cor da borda com base na prioridade
    let borderColor = colors.border;
    if (todo.priority === 'high') borderColor = colors.error;
    else if (todo.priority === 'medium') borderColor = colors.secondary;
    else if (todo.priority === 'low') borderColor = colors.success;

    // Verificar se a tarefa está atrasada
    const isOverdue = todo.dueDate && !todo.completed && isBefore(parseISO(todo.dueDate), new Date());

    // Calcular tempo restante ou atrasado
    let timeStatus = '';
    if (todo.dueDate) {
      const dueDate = parseISO(todo.dueDate);
      if (isToday(dueDate)) {
        timeStatus = 'Hoje';
      } else if (isTomorrow(dueDate)) {
        timeStatus = 'Amanhã';
      } else if (isOverdue) {
        const days = Math.abs(Math.ceil((new Date().getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)));
        timeStatus = `Atrasada ${days} dia${days > 1 ? 's' : ''}`;
      }
    }

    return (
      <Pressable
        key={todo.id}
        style={[styles.todoItem, { borderLeftColor: borderColor }]}
        onPress={() => router.push({
          pathname: '/tasks/[id]',
          params: { id: todo.id }
        } as any)}
      >
        <View style={styles.todoCheckbox}>
          <Pressable
            style={styles.checkboxButton}
            onPress={() => handleToggleTodo(todo)}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            {todo.completed ? (
              <CheckCircle size={24} color={colors.primary} />
            ) : (
              <Circle size={24} color={isOverdue ? colors.error : colors.textLight} />
            )}
          </Pressable>
        </View>
        <View style={styles.todoContent}>
          <View style={styles.todoHeader}>
            <Text style={[
              styles.todoTitle,
              todo.completed && styles.todoTitleCompleted,
              isOverdue && !todo.completed && { color: colors.error }
            ]} numberOfLines={1}>
              {todo.title}
            </Text>
            {timeStatus && (
              <View style={[styles.todoTimeStatus, isOverdue && styles.todoTimeStatusOverdue]}>
                <Text style={[styles.todoTimeStatusText, isOverdue && { color: colors.white }]}>
                  {timeStatus}
                </Text>
              </View>
            )}
          </View>

          {todo.description ? (
            <Text style={styles.todoDescription} numberOfLines={1}>
              {todo.description}
            </Text>
          ) : null}

          <View style={styles.todoMeta}>
            {todo.dueDate ? (
              <View style={styles.todoDueDate}>
                <Clock size={14} color={isOverdue && !todo.completed ? colors.error : colors.textLight} />
                <Text style={[styles.todoDueDateText, isOverdue && !todo.completed && { color: colors.error }]}>
                  {format(parseISO(todo.dueDate), "dd/MM/yyyy", { locale: ptBR })}
                </Text>
              </View>
            ) : null}
            {todo.priority ? (
              <View style={[
                styles.todoPriority,
                todo.priority === 'high' && styles.todoPriorityHigh,
                todo.priority === 'medium' && styles.todoPriorityMedium,
                todo.priority === 'low' && styles.todoPriorityLow
              ]}>
                <Text style={[styles.todoPriorityText, todo.priority === 'high' && { color: colors.error }]}>
                  {todo.priority === 'high' ? 'Alta' : todo.priority === 'medium' ? 'Média' : 'Baixa'}
                </Text>
              </View>
            ) : null}
            {todo.event_id && (
              <View style={styles.todoEventLink}>
                <CalendarIcon size={14} color={colors.primary} />
              </View>
            )}
          </View>
        </View>
      </Pressable>
    );
  };

  const renderEventItem = (event: CalendarEvent) => {
    const startTime = format(parseISO(event.startDate), "HH:mm");
    const endTime = event.endDate ? format(parseISO(event.endDate), "HH:mm") : null;
    const eventTodos = useCalendarStore.getState().getEventTodos(event.id);
    const hasTodos = eventTodos.length > 0;
    const completedTodos = eventTodos.filter(todo => todo.completed).length;
    const todoProgress = hasTodos ? (completedTodos / eventTodos.length) * 100 : 0;

    // Determine background color with opacity
    const bgColor = `${event.color}15`; // 15% opacity
    const borderColor = event.color;

    return (
      <Pressable
        key={event.id}
        style={[styles.eventItem, { borderLeftColor: borderColor, backgroundColor: bgColor }]}
        onPress={() => handleEventPress(event)}
      >
        <View style={styles.eventHeader}>
          <View style={[styles.eventTypeIndicator, { backgroundColor: event.color }]}>
            {event.type === 'study' && <BookOpen size={14} color="white" />}
            {event.type === 'exam' && <Edit size={14} color="white" />}
            {event.type === 'assignment' && <ClipboardList size={14} color="white" />}
            {event.type === 'meeting' && <CalendarIcon size={14} color="white" />}
            {event.type === 'other' && <CalendarIcon size={14} color="white" />}
          </View>
          <Text style={styles.eventTitle} numberOfLines={1}>{event.title}</Text>
          {event.completed && (
            <View style={styles.completedIndicator}>
              <CheckCircle size={16} color={colors.success} />
            </View>
          )}
        </View>

        <View style={styles.eventDetails}>
          <View style={styles.eventTimeContainer}>
            <Clock size={14} color={colors.textMedium} />
            <Text style={styles.eventTime}>
              {event.allDay ? "Dia todo" : `${startTime}${endTime ? ` - ${endTime}` : ""}`}
            </Text>
          </View>

          {event.subject && (
            <View style={styles.eventSubject}>
              <BookOpen size={14} color={colors.textMedium} />
              <Text style={styles.eventSubjectText} numberOfLines={1}>{event.subject}</Text>
            </View>
          )}
        </View>

        {hasTodos && (
          <View style={styles.todoProgressContainer}>
            <View style={styles.todoProgressBar}>
              <View
                style={[styles.todoProgressFill, { width: `${todoProgress}%`, backgroundColor: event.color }]}
              />
            </View>
            <Text style={styles.todoProgressText}>
              {completedTodos}/{eventTodos.length}
            </Text>
          </View>
        )}
      </Pressable>
    );
  };

  // Cronogramas foram removidos

  if (calendarLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <RNStatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title={
          activeView === 'todo'
            ? "Tarefas"
            : "Calendário"
        }
      />

      <GlassCard style={styles.tabsContainer}>
        <Pressable
          style={[
            styles.tabButton,
            activeView === 'todo' && styles.activeTabButton
          ]}
          onPress={() => handleViewChange('todo')}
        >
          <View style={styles.tabButtonContent}>
            <ClipboardList
              size={22}
              color={activeView === 'todo' ? colors.primary : colors.textLight}
            />
            <Text
              style={[
                styles.tabButtonText,
                activeView === 'todo' && styles.activeTabButtonText
              ]}
            >
              Tarefas
            </Text>
          </View>
          {activeView === 'todo' && <View style={styles.activeTabIndicator} />}
        </Pressable>

        <Pressable
          style={[
            styles.tabButton,
            activeView === 'calendar' && styles.activeTabButton
          ]}
          onPress={() => handleViewChange('calendar')}
        >
          <View style={styles.tabButtonContent}>
            <CalendarIcon
              size={22}
              color={activeView === 'calendar' ? colors.primary : colors.textLight}
            />
            <Text
              style={[
                styles.tabButtonText,
                activeView === 'calendar' && styles.activeTabButtonText
              ]}
            >
              Calendário
            </Text>
          </View>
          {activeView === 'calendar' && <View style={styles.activeTabIndicator} />}
        </Pressable>
      </GlassCard>

      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[
            styles.scrollContent,
            {paddingBottom: Platform.OS === 'ios' ? 120 : 100}
          ]}
          showsVerticalScrollIndicator={false}
        >
          {/* Resumo de Estatísticas */}
          <GlassCard style={styles.statsContainer}>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <View style={[styles.statIcon, { backgroundColor: `${colors.primary}20` }]}>
                  <CalendarIcon size={18} color={colors.primary} />
                </View>
                <View style={styles.statContent}>
                  <Text style={styles.statValue}>{events.length}</Text>
                  <Text style={styles.statLabel}>Eventos</Text>
                </View>
              </View>

              <View style={styles.statItem}>
                <View style={[styles.statIcon, { backgroundColor: `${colors.accent1}20` }]}>
                  <ClipboardList size={18} color={colors.accent1} />
                </View>
                <View style={styles.statContent}>
                  <Text style={styles.statValue}>{todos.length}</Text>
                  <Text style={styles.statLabel}>Tarefas</Text>
                </View>
              </View>

              <View style={styles.statItem}>
                <View style={[styles.statIcon, { backgroundColor: `${colors.success}20` }]}>
                  <CheckCircle size={18} color={colors.success} />
                </View>
                <View style={styles.statContent}>
                  <Text style={styles.statValue}>{todos.filter(todo => todo.completed).length}</Text>
                  <Text style={styles.statLabel}>Concluídas</Text>
                </View>
              </View>
            </View>
          </GlassCard>
        {activeView === 'calendar' ? (
          // Calendar Tab Content
          <View style={styles.calendarContainer}>
            <GlassCard style={styles.calendarViewSelector}>
              <Pressable
                style={[
                  styles.viewModeButton,
                  calendarViewMode === 'month' && styles.viewModeButtonActive
                ]}
                onPress={() => setCalendarViewMode('month')}
              >
                <Grid size={18} color={calendarViewMode === 'month' ? colors.primary : colors.textLight} />
                <Text style={[
                  styles.viewModeButtonText,
                  calendarViewMode === 'month' && styles.viewModeButtonTextActive
                ]}>Mensal</Text>
              </Pressable>

              <Pressable
                style={[
                  styles.viewModeButton,
                  calendarViewMode === 'week' && styles.viewModeButtonActive
                ]}
                onPress={() => setCalendarViewMode('week')}
              >
                <Columns size={18} color={calendarViewMode === 'week' ? colors.primary : colors.textLight} />
                <Text style={[
                  styles.viewModeButtonText,
                  calendarViewMode === 'week' && styles.viewModeButtonTextActive
                ]}>Semanal</Text>
              </Pressable>

              <Pressable
                style={[
                  styles.viewModeButton,
                  calendarViewMode === 'day' && styles.viewModeButtonActive
                ]}
                onPress={() => setCalendarViewMode('day')}
              >
                <LayoutList size={18} color={calendarViewMode === 'day' ? colors.primary : colors.textLight} />
                <Text style={[
                  styles.viewModeButtonText,
                  calendarViewMode === 'day' && styles.viewModeButtonTextActive
                ]}>Diário</Text>
              </Pressable>
            </GlassCard>

            {calendarViewMode === 'month' && (
              <GlassCard style={styles.calendarCard}>
                <View style={styles.calendarNavigation}>
                  <Pressable onPress={handlePreviousMonth} style={styles.calendarNavButton}>
                    <ChevronLeft size={24} color={colors.primary} />
                  </Pressable>
                  <Text style={styles.calendarMonthTitle}>
                    {format(currentMonth, 'MMMM yyyy', { locale: ptBR })}
                  </Text>
                  <Pressable onPress={handleNextMonth} style={styles.calendarNavButton}>
                    <ChevronRight size={24} color={colors.primary} />
                  </Pressable>
                </View>
                <RNCalendar
                  current={format(currentMonth, 'yyyy-MM-dd')}
                  initialDate={format(currentMonth, 'yyyy-MM-dd')}
                  onDayPress={(day: any) => setSelectedDate(day.dateString)}
                  markedDates={markedDates}
                  hideArrows={true}
                  hideExtraDays={true}
                  hideDayNames={false}
                  hideMonthNames={true}
                  renderHeader={() => null}
                  theme={{
                    backgroundColor: 'transparent',
                    calendarBackground: 'transparent',
                    textSectionTitleColor: colors.text,
                    selectedDayBackgroundColor: colors.primary,
                    selectedDayTextColor: colors.white,
                    todayTextColor: colors.primary,
                    dayTextColor: colors.text,
                    textDisabledColor: colors.textLight,
                    dotColor: colors.primary,
                    selectedDotColor: colors.white,
                    arrowColor: colors.primary,
                    monthTextColor: colors.text,
                    indicatorColor: colors.primary,
                    textDayFontWeight: '400',
                    textMonthFontWeight: '700',
                    textDayHeaderFontWeight: '600',
                    textDayFontSize: 16,
                    textMonthFontSize: 18,
                    textDayHeaderFontSize: 14
                  }}
                />
              </GlassCard>
            )}

            {calendarViewMode === 'week' && (
              <GlassCard style={styles.calendarCard}>
                <View style={styles.calendarNavigation}>
                  <Pressable
                    onPress={handlePreviousWeek}
                    style={styles.calendarNavButton}
                    testID="previous-week-button"
                  >
                    <ChevronLeft size={24} color={colors.primary} />
                  </Pressable>
                  <Text style={styles.calendarMonthTitle}>
                    {weekDays.length > 0 ? (
                      `${format(weekDays[0], 'd', { locale: ptBR })} - ${format(weekDays[6], 'd', { locale: ptBR })} ${format(weekDays[0], 'MMMM yyyy', { locale: ptBR })}`
                    ) : (
                      'Semana atual'
                    )}
                  </Text>
                  <Pressable
                    onPress={handleNextWeek}
                    style={styles.calendarNavButton}
                    testID="next-week-button"
                  >
                    <ChevronRight size={24} color={colors.primary} />
                  </Pressable>
                </View>

                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.weekDaysContainer}>
                    {weekDays.map((day) => {
                      const formattedDate = format(day, 'yyyy-MM-dd');
                      const isSelected = formattedDate === selectedDate;
                      const isTodayDay = isToday(day);

                      // Verificar se há eventos neste dia
                      const hasEvents = events.some(event => {
                        const eventDate = event.startDate.split('T')[0];
                        return eventDate === formattedDate;
                      });

                      return (
                        <TouchableOpacity
                          key={formattedDate}
                          style={[
                            styles.weekDayItem,
                            isTodayDay && styles.todayItem,
                            isSelected && styles.selectedDayItem
                          ]}
                          onPress={() => {
                            setSelectedDate(formattedDate);
                          }}
                          activeOpacity={0.7}
                        >
                          <Text style={[
                            styles.weekDayName,
                            isSelected && styles.selectedDayText
                          ]}>
                            {format(day, 'EEE', { locale: ptBR })}
                          </Text>
                          <Text style={[
                            styles.weekDayNumber,
                            isSelected && styles.selectedDayText
                          ]}>
                            {format(day, 'd')}
                          </Text>
                          {hasEvents && (
                            <View style={[
                              styles.eventDot,
                              isSelected && styles.eventDotSelected
                            ]} />
                          )}
                        </TouchableOpacity>
                      );
                    })}
                  </View>
                </ScrollView>

                {/* Eventos do dia selecionado na visualização semanal */}
                <View style={styles.weekViewEvents}>
                  {eventsForSelectedDate.length > 0 ? (
                    eventsForSelectedDate.map((event) => (
                      <Pressable
                        key={`week-event-${event.id}`}
                        style={[
                          styles.weekViewEventItem,
                          { backgroundColor: `${event.color}15`, borderLeftColor: event.color }
                        ]}
                        onPress={() => handleEventPress(event)}
                      >
                        <View style={styles.weekViewEventTime}>
                          <Clock size={12} color={colors.textMedium} />
                          <Text style={styles.weekViewEventTimeText}>
                            {format(parseISO(event.startDate), 'HH:mm')}
                          </Text>
                        </View>
                        <Text style={styles.weekViewEventTitle} numberOfLines={1}>
                          {event.title}
                        </Text>
                      </Pressable>
                    ))
                  ) : (
                    <View style={styles.weekViewEmptyEvents}>
                      <Text style={styles.weekViewEmptyText}>Nenhum evento para este dia</Text>
                      <Button
                        title="Adicionar evento"
                        onPress={handleAddEvent}
                        variant="outline"
                        size="small"
                        icon={Plus}
                        style={styles.weekViewAddButton}
                      />
                    </View>
                  )}
                </View>
              </GlassCard>
            )}

            {calendarViewMode === 'day' && (
              <GlassCard style={styles.calendarCard}>
                <View style={styles.calendarNavigation}>
                  <Pressable onPress={handlePreviousDay} style={styles.calendarNavButton}>
                    <ChevronLeft size={24} color={colors.primary} />
                  </Pressable>
                  <Text style={styles.calendarMonthTitle}>
                    {format(new Date(selectedDate), "EEEE, d 'de' MMMM", { locale: ptBR })}
                  </Text>
                  <Pressable onPress={handleNextDay} style={styles.calendarNavButton}>
                    <ChevronRight size={24} color={colors.primary} />
                  </Pressable>
                </View>

                <View style={styles.dayTimelineContainer}>
                  {Array.from({ length: 24 }).map((_, hour) => (
                    <View key={hour} style={styles.hourRow}>
                      <Text style={styles.hourText}>{`${hour.toString().padStart(2, '0')}:00`}</Text>
                      <View style={styles.hourLine} />
                    </View>
                  ))}

                  {eventsForSelectedDate.map(event => {
                    const startHour = new Date(event.startDate).getHours();
                    const startMinute = new Date(event.startDate).getMinutes();
                    const endHour = event.endDate ? new Date(event.endDate).getHours() : startHour + 1;
                    const endMinute = event.endDate ? new Date(event.endDate).getMinutes() : startMinute;

                    const top = (startHour + startMinute / 60) * 60;
                    const height = ((endHour + endMinute / 60) - (startHour + startMinute / 60)) * 60;

                    return (
                      <Pressable
                        key={event.id}
                        style={[
                          styles.dayEventItem,
                          {
                            top,
                            height: Math.max(height, 30),
                            backgroundColor: `${event.color}20`,
                            borderLeftColor: event.color
                          }
                        ]}
                        onPress={() => handleEventPress(event)}
                      >
                        <Text style={styles.dayEventTitle} numberOfLines={1}>{event.title}</Text>
                        <Text style={styles.dayEventTime} numberOfLines={1}>
                          {format(new Date(event.startDate), 'HH:mm')} -
                          {event.endDate ? format(new Date(event.endDate), 'HH:mm') : ''}
                        </Text>
                      </Pressable>
                    );
                  })}
                </View>
              </GlassCard>
            )}

            <View style={styles.eventsSection}>
              <Text style={styles.sectionTitle}>
                Eventos para {format(parseISO(selectedDate), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
              </Text>

              {eventsForSelectedDate.length > 0 ? (
                eventsForSelectedDate.map(event => renderEventItem(event))
              ) : (
                <View style={styles.emptyContainer}>
                  <CalendarIcon size={48} color={colors.textLight} />
                  <Text style={styles.emptyText}>
                    Nenhum evento para esta data
                  </Text>
                  <Button
                    title="Adicionar evento"
                    onPress={handleAddEvent}
                    variant="primary"
                    size="large"
                    icon={Plus}
                  />
                </View>
              )}
            </View>
          </View>
        ) : (
          // Todo View Content
          <View style={styles.todoContainer}>
            <View style={styles.todoFilterContainer}>
              <Pressable
                style={[
                  styles.todoFilterButton,
                  todoFilter === 'all' && styles.todoFilterButtonActive
                ]}
                onPress={() => setTodoFilter('all')}
              >
                <Text
                  style={[
                    styles.todoFilterButtonText,
                    todoFilter === 'all' && styles.todoFilterButtonTextActive
                  ]}
                >
                  Todas
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.todoFilterButton,
                  todoFilter === 'pending' && styles.todoFilterButtonActive
                ]}
                onPress={() => setTodoFilter('pending')}
              >
                <Text
                  style={[
                    styles.todoFilterButtonText,
                    todoFilter === 'pending' && styles.todoFilterButtonTextActive
                  ]}
                >
                  Pendentes
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.todoFilterButton,
                  todoFilter === 'completed' && styles.todoFilterButtonActive
                ]}
                onPress={() => setTodoFilter('completed')}
              >
                <Text
                  style={[
                    styles.todoFilterButtonText,
                    todoFilter === 'completed' && styles.todoFilterButtonTextActive
                  ]}
                >
                  Concluídas
                </Text>
              </Pressable>
            </View>

            {filteredTodos.length > 0 ? (
              <View style={styles.todoListContainer}>
                {groupedTodos['overdue'] && groupedTodos['overdue'].length > 0 && (
                  <View style={styles.todoGroup}>
                    <Text style={[styles.todoGroupTitle, styles.todoGroupTitleOverdue]}>
                      Atrasadas
                    </Text>
                    {groupedTodos['overdue'].map(todo => renderTodoItem(todo))}
                  </View>
                )}


                {groupedTodos['today'] && groupedTodos['today'].length > 0 && (
                  <View style={styles.todoGroup}>
                    <Text style={styles.todoGroupTitle}>
                      Hoje
                    </Text>
                    {groupedTodos['today'].map(todo => renderTodoItem(todo))}
                  </View>
                )}

                {groupedTodos['tomorrow'] && groupedTodos['tomorrow'].length > 0 && (
                  <View style={styles.todoGroup}>
                    <Text style={styles.todoGroupTitle}>
                      Amanhã
                    </Text>
                    {groupedTodos['tomorrow'].map(todo => renderTodoItem(todo))}
                  </View>
                )}

                {groupedTodos['this-week'] && groupedTodos['this-week'].length > 0 && (
                  <View style={styles.todoGroup}>
                    <Text style={styles.todoGroupTitle}>
                      Esta Semana
                    </Text>
                    {groupedTodos['this-week'].map(todo => renderTodoItem(todo))}
                  </View>
                )}

                {groupedTodos['future'] && groupedTodos['future'].length > 0 && (
                  <View style={styles.todoGroup}>
                    <Text style={styles.todoGroupTitle}>
                      Futuras
                    </Text>
                    {groupedTodos['future'].map(todo => renderTodoItem(todo))}
                  </View>
                )}

                {groupedTodos['no-date'] && groupedTodos['no-date'].length > 0 && (
                  <View style={styles.todoGroup}>
                    <Text style={styles.todoGroupTitle}>
                      Sem Data
                    </Text>
                    {groupedTodos['no-date'].map(todo => renderTodoItem(todo))}
                  </View>
                )}
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <List size={48} color={colors.textLight} />
                <Text style={styles.emptyText}>
                  {todoFilter === 'all'
                    ? 'Nenhuma tarefa encontrada'
                    : todoFilter === 'pending'
                    ? 'Nenhuma tarefa pendente'
                    : 'Nenhuma tarefa concluída'}
                </Text>
                <Button
                  title="Adicionar tarefa"
                  onPress={handleAddTodo}
                  variant="primary"
                  size="large"
                  icon={Plus}
                />
              </View>
            )}
          </View>
        )}
        </ScrollView>
      </Animated.View>

      {/* Removido os botões flutuantes que ficavam embaixo do menu */}

      {/* Removido o modal de evento, agora usando página separada */}

      {/* Todo Form Modal */}
      <TodoForm
        visible={showTodoForm}
        onClose={() => {
          console.log('Fechando modal de tarefa');
          setShowTodoForm(false);
          setCurrentTodo(null);
        }}
        onSave={handleSaveTodo}
        initialTodo={currentTodo || undefined}
      />

      {/* Floating Action Button */}
      {activeView === 'calendar' ? (
        <CalendarFAB
          onAddEvent={handleAddEvent}
          onAddTask={handleAddTodo}
        />
      ) : (
        <TaskFAB
          onAddTask={handleAddTodo}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  // Estilos para a visualização semanal
  eventDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 4,
  },
  eventDotSelected: {
    backgroundColor: colors.white,
  },
  weekViewEvents: {
    padding: 10,
    maxHeight: 200,
  },
  weekViewEventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
  },
  weekViewEventTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  weekViewEventTimeText: {
    fontSize: 12,
    color: colors.textMedium,
    marginLeft: 4,
  },
  weekViewEventTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    flex: 1,
  },
  weekViewEmptyEvents: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  weekViewEmptyText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 10,
  },
  weekViewAddButton: {
    minWidth: 150,
  },
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Extra padding for floating tab bar
  },
  tabsContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 16,
    padding: 6,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 12,
    position: 'relative',
  },
  tabButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabIndicator: {
    position: 'absolute',
    bottom: 0,
    height: 3,
    width: '50%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  scheduleContainer: {
    padding: 16,
  },
  scheduleSelector: {
    marginBottom: 16,
  },
  scheduleSelectorItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scheduleSelectorItemActive: {
    backgroundColor: colors.primary,
  },
  scheduleSelectorText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  scheduleSelectorTextActive: {
    color: colors.white,
  },
  scheduleDetailsCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 16,
  },
  scheduleTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 8,
  },
  scheduleDescription: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 16,
  },
  scheduleInfo: {
    marginBottom: 16,
  },
  scheduleInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  scheduleInfoText: {
    fontSize: 14,
    color: colors.textMedium,
    marginLeft: 8,
  },
  scheduleActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scheduleActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  activeTabButton: {
    backgroundColor: `${colors.primary}30`,
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
    marginLeft: 4,
  },
  activeTabButtonText: {
    color: colors.primary,
    fontWeight: '700',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  statsContainer: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 16,
    padding: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statContent: {
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  calendarContainer: {
    padding: 16,
  },
  calendarCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
  },
  calendarViewSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    padding: 4,
    borderRadius: 12,
  },
  viewModeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
  },
  viewModeButtonActive: {
    backgroundColor: `${colors.primary}20`,
  },
  viewModeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginLeft: 4,
  },
  viewModeButtonTextActive: {
    color: colors.primary,
    fontWeight: '600',
  },
  calendarNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  calendarNavButton: {
    padding: 8,
  },
  calendarMonthTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    textTransform: 'capitalize',
  },
  weekDaysContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 16,
  },
  weekDayItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 12,
    marginHorizontal: 6,
    minWidth: 65,
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  todayItem: {
    backgroundColor: `${colors.primary}15`,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  selectedDayItem: {
    backgroundColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  weekDayName: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textMedium,
    textTransform: 'uppercase',
    marginBottom: 6,
  },
  weekDayNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  selectedDayText: {
    color: colors.white,
  },
  dayTimelineContainer: {
    position: 'relative',
    paddingLeft: 50,
    paddingRight: 16,
    height: 1440, // 24 hours * 60px
  },
  hourRow: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
  },
  hourText: {
    position: 'absolute',
    left: -45,
    fontSize: 12,
    color: colors.textLight,
    width: 40,
    textAlign: 'right',
  },
  hourLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.border,
  },
  dayEventItem: {
    position: 'absolute',
    left: 60,
    right: 16,
    borderRadius: 6,
    padding: 8,
    borderLeftWidth: 4,
  },
  dayEventTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  dayEventTime: {
    fontSize: 12,
    color: colors.textLight,
  },
  eventsSection: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 16,
  },
  eventItem: {
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: 12,
    padding: 12,
    borderLeftWidth: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
  },
  eventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  eventTypeIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  completedIndicator: {
    marginLeft: 'auto',
  },
  eventDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  eventTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  eventTime: {
    fontSize: 12,
    color: colors.textMedium,
    marginLeft: 4,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    flex: 1,
  },
  eventDescription: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 10,
    lineHeight: 20,
    backgroundColor: `${colors.backgroundLight}50`,
    padding: 10,
    borderRadius: 8,
    borderLeftWidth: 2,
    borderLeftColor: colors.border,
  },
  eventSubject: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 6,
  },
  eventSubjectText: {
    fontSize: 14,
    color: colors.textMedium,
    marginLeft: 6,
    flex: 1,
  },
  todoProgressContainer: {
    marginVertical: 10,
  },
  todoProgressBar: {
    height: 8,
    backgroundColor: `${colors.border}30`,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 6,
  },
  todoProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  todoProgressText: {
    fontSize: 12,
    color: colors.textMedium,
    textAlign: 'right',
    fontWeight: '500',
  },
  eventActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: `${colors.border}30`,
    paddingTop: 10,
  },
  eventActionButton: {
    padding: 10,
    marginLeft: 12,
    backgroundColor: `${colors.border}15`,
    borderRadius: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
    marginVertical: 16,
  },
  todoContainer: {
    padding: 16,
  },
  todoFilterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  todoFilterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  todoFilterButtonActive: {
    backgroundColor: `${colors.primary}15`,
  },
  todoFilterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  todoFilterButtonTextActive: {
    color: colors.primary,
  },
  applyButton: {
    marginTop: 16,
    marginHorizontal: 16,
  },
  todoListContainer: {
    marginTop: 8,
  },
  todoGroup: {
    marginBottom: 24,
  },
  todoGroupTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 12,
  },
  todoGroupTitleOverdue: {
    color: colors.error,
  },
  todoItem: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 12,
    padding: 14,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
    borderLeftWidth: 3,
    borderLeftColor: colors.border,
  },
  todoCheckbox: {
    marginRight: 12,
    justifyContent: 'center',
  },
  checkboxButton: {
    padding: 4,
  },
  todoContent: {
    flex: 1,
  },
  todoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  todoTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    flex: 1,
  },
  todoTitleCompleted: {
    textDecorationLine: 'line-through',
    color: colors.textLight,
  },
  todoTimeStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: `${colors.border}30`,
    marginLeft: 8,
  },
  todoTimeStatusOverdue: {
    backgroundColor: colors.error,
  },
  todoTimeStatusText: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.textLight,
  },
  todoDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
    lineHeight: 20,
  },
  todoEventLink: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: `${colors.border}50`,
  },
  todoEventLinkText: {
    fontSize: 12,
    color: colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  todoMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  todoSubject: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  todoSubjectText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  todoDueDate: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  todoDueDateText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  todoPriority: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: colors.backgroundLight,
  },
  todoPriorityHigh: {
    backgroundColor: `${colors.error}20`,
  },
  todoPriorityMedium: {
    backgroundColor: `${colors.secondary}20`,
  },
  todoPriorityLow: {
    backgroundColor: `${colors.success}20`,
  },
  todoPriorityText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textLight,
  },
  // Estilos dos botões flutuantes removidos
});

