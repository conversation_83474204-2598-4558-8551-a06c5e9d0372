import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  ActivityIndicator,
  Platform,
  Dimensions,
  Animated
} from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { useUserStore } from "@/store/userStore";

import { Header } from "@/components/Header";
import { RouteGuard } from "@/components/RouteGuard";
import { LinearGradient } from "expo-linear-gradient";
import {
  <PERSON>rkles,
  BookOpen,
  Brain,
  Flame,
  Star,
  Award,
  Users,
  Crown,
  ChevronRight,
  Play,
  Target,
  TrendingUp,
  FileQuestion,
  Zap,
  Calendar,
  Clock,
  LightbulbIcon,
  BarChart
} from "lucide-react-native";

const { width } = Dimensions.get("window");

// Componente de Card de Estatística Moderno
const StatCard = ({ icon, label, value, gradient, change }: {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  gradient: string[];
  change?: { value: number; type: 'increase' | 'decrease' };
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[styles.statCard, { transform: [{ scale: scaleAnim }] }]}>
      <Pressable
        style={styles.statCardPressable}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <LinearGradient
          colors={gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.statCardGradient}
        >
          <View style={styles.statCardContent}>
            <View style={styles.statIconContainer}>
              {icon}
            </View>
            <Text style={styles.statValue}>{value}</Text>
            <Text style={styles.statLabel}>{label}</Text>
            {change && (
              <View style={styles.statChange}>
                <Text style={[
                  styles.statChangeText,
                  { color: change.type === 'increase' ? colors.success : colors.error }
                ]}>
                  {change.type === 'increase' ? '+' : '-'}{change.value}%
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

// Componente de Card de Ação Rápida
const QuickActionCard = ({ icon, title, subtitle, onPress, gradient }: {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  onPress: () => void;
  gradient: string[];
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[styles.quickActionCard, { transform: [{ scale: scaleAnim }] }]}>
      <Pressable
        style={styles.quickActionPressable}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <LinearGradient
          colors={gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.quickActionGradient}
        >
          <View style={styles.quickActionContent}>
            <View style={styles.quickActionIconContainer}>
              {icon}
            </View>
            <View style={styles.quickActionTextContainer}>
              <Text style={styles.quickActionTitle}>{title}</Text>
              <Text style={styles.quickActionSubtitle}>{subtitle}</Text>
            </View>
            <ChevronRight size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.8)" />
          </View>
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

export default function HomeScreen() {
  const router = useRouter();
  const { user, supabaseUser, loading: userLoading } = useUserStore();
  const [isLoading, setIsLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Animações de entrada
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading || userLoading) {
    return (
      <RouteGuard>
        <View style={[styles.container, styles.loadingContainer]}>
          <LinearGradient
            colors={["#F8FAFC", "#F1F5F9", "#E2E8F0"]}
            style={styles.backgroundGradient}
          />
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando sua jornada...</Text>
        </View>
      </RouteGuard>
    );
  }

  return (
    <RouteGuard>
      <View style={styles.container}>
        <LinearGradient
          colors={["#F8FAFC", "#F1F5F9", "#E2E8F0"]}
          style={styles.backgroundGradient}
        />
        <Header />

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.scrollContent, { paddingBottom: Platform.OS === 'ios' ? 120 : 100 }]}
          showsVerticalScrollIndicator={false}
          bounces={true}
          scrollEventThrottle={16}
        >
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
          >
            {/* Hero Section - Azul Predominante */}
            <View style={styles.heroSection}>
              <LinearGradient
                colors={colors.heroGradient1}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.heroGradient}
              >
                <View style={styles.heroContent}>
                  <View style={styles.heroHeader}>
                    <View style={styles.heroTextContainer}>
                      <Text style={styles.heroGreeting}>
                        Olá, {supabaseUser?.name || user?.name || 'Estudante'}! 👋
                      </Text>
                      <Text style={styles.heroSubtitle}>
                        Pronto para conquistar seus objetivos hoje?
                      </Text>
                    </View>
                    <View style={styles.heroIconContainer}>
                      <Brain size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                    </View>
                  </View>

                  <View style={styles.heroActionsContainer}>
                    <Pressable
                      style={({ pressed }) => [
                        styles.heroActionButton,
                        pressed && { transform: [{ scale: 0.95 }] }
                      ]}
                      onPress={() => router.push('/chat')}
                    >
                      <Sparkles size={theme.sizes.icon.sm} color="#fff" />
                      <Text style={styles.heroActionText}>Chat IA</Text>
                    </Pressable>

                    <Pressable
                      style={({ pressed }) => [
                        styles.heroActionButton,
                        pressed && { transform: [{ scale: 0.95 }] }
                      ]}
                      onPress={() => router.push('/flashcards')}
                    >
                      <Play size={theme.sizes.icon.sm} color="#fff" />
                      <Text style={styles.heroActionText}>Estudar</Text>
                    </Pressable>
                  </View>
                </View>
              </LinearGradient>
            </View>

            {/* Estatísticas Compactas */}
            <View style={styles.statsSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Seu Progresso</Text>
                <Text style={styles.sectionSubtitle}>Acompanhe seu desempenho</Text>
              </View>

              <View style={styles.statsGrid}>
                <StatCard
                  icon={<Flame size={theme.sizes.icon.sm} color="#fff" />}
                  label="Sequência"
                  value={user?.streak || 0}
                  gradient={colors.cardGradient5}
                  change={{ value: 12, type: 'increase' }}
                />

                <StatCard
                  icon={<Star size={theme.sizes.icon.sm} color="#fff" />}
                  label="XP Total"
                  value={supabaseUser?.xp || user?.xp || 0}
                  gradient={colors.cardGradient1}
                  change={{ value: 8, type: 'increase' }}
                />

                <StatCard
                  icon={<Award size={theme.sizes.icon.sm} color="#fff" />}
                  label="Nível"
                  value={supabaseUser?.level || user?.level || 1}
                  gradient={colors.cardGradient3}
                />

                <StatCard
                  icon={<BookOpen size={theme.sizes.icon.sm} color="#fff" />}
                  label="Estudos"
                  value={12}
                  gradient={colors.cardGradient4}
                  change={{ value: 5, type: 'increase' }}
                />
              </View>
            </View>

            {/* Ações Rápidas */}
            <View style={styles.quickActionsSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Ações Rápidas</Text>
                <Text style={styles.sectionSubtitle}>Acesse suas ferramentas favoritas</Text>
              </View>

              <View style={styles.quickActionsGrid}>
                <QuickActionCard
                  icon={<BookOpen size={theme.sizes.icon.md} color="#fff" />}
                  title="Flashcards"
                  subtitle="Memorize conceitos"
                  onPress={() => router.push('/flashcards')}
                  gradient={colors.cardGradient1}
                />

                <QuickActionCard
                  icon={<FileQuestion size={theme.sizes.icon.md} color="#fff" />}
                  title="Quizzes"
                  subtitle="Teste conhecimentos"
                  onPress={() => router.push('/quizzes')}
                  gradient={colors.cardGradient2}
                />

                <QuickActionCard
                  icon={<Brain size={theme.sizes.icon.md} color="#fff" />}
                  title="Mapas Mentais"
                  subtitle="Organize ideias"
                  onPress={() => router.push('/mind-maps')}
                  gradient={colors.cardGradient3}
                />

                <QuickActionCard
                  icon={<Users size={theme.sizes.icon.md} color="#fff" />}
                  title="Grupos"
                  subtitle="Estude em equipe"
                  onPress={() => router.push('/study-groups')}
                  gradient={colors.cardGradient4}
                />
              </View>
            </View>

            {/* Banner Premium */}
            <View style={styles.premiumSection}>
              <Pressable
                style={({ pressed }) => [
                  styles.premiumCard,
                  pressed && { transform: [{ scale: 0.98 }] }
                ]}
                onPress={() => router.push('/premium')}
              >
                <LinearGradient
                  colors={colors.heroGradient2}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.premiumGradient}
                >
                  <View style={styles.premiumContent}>
                    <View style={styles.premiumTextContainer}>
                      <View style={styles.premiumBadgeContainer}>
                        <Crown size={theme.sizes.icon.sm} color="#fff" />
                        <Text style={styles.premiumBadge}>PREMIUM</Text>
                      </View>
                      <Text style={styles.premiumTitle}>Desbloqueie Todo o Potencial</Text>
                      <Text style={styles.premiumSubtitle}>
                        IA avançada • Recursos ilimitados • Suporte prioritário
                      </Text>
                    </View>
                    <View style={styles.premiumIconContainer}>
                      <Sparkles size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                    </View>
                  </View>
                </LinearGradient>
              </Pressable>
            </View>

            {/* Dica do Dia */}
            <View style={styles.tipSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>💡 Dica do Dia</Text>
                <Pressable onPress={() => router.push('/study-tips')}>
                  <Text style={styles.viewAllText}>Ver mais</Text>
                </Pressable>
              </View>

              <View style={styles.tipCard}>
                <LinearGradient
                  colors={['#667eea', '#764ba2']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.tipGradient}
                >
                  <View style={styles.tipContent}>
                    <View style={styles.tipIconContainer}>
                      <LightbulbIcon size={theme.sizes.icon.md} color="#fff" />
                    </View>
                    <View style={styles.tipTextContainer}>
                      <Text style={styles.tipTitle}>Técnica Pomodoro</Text>
                      <Text style={styles.tipDescription}>
                        Estude por 25 minutos e descanse por 5. Mantenha o foco e aumente a produtividade.
                      </Text>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            </View>
          </Animated.View>
        </ScrollView>
      </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.size.md,
    color: colors.textLight,
    fontWeight: '500',
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: theme.spacing.sm,
    paddingBottom: 100,
    flexGrow: 1,
  },

  // Hero Section Styles
  heroSection: {
    marginHorizontal: theme.spacing.md,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.lg,
    borderRadius: theme.borderRadius.hero,
    overflow: 'hidden',
    ...theme.shadows.xl,
    shadowColor: colors.primary,
  },
  heroGradient: {
    borderRadius: theme.borderRadius.hero,
  },
  heroContent: {
    padding: theme.spacing.cardPadding,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  heroTextContainer: {
    flex: 1,
    paddingRight: theme.spacing.componentGap,
  },
  heroGreeting: {
    fontSize: theme.typography.size.xl,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    marginBottom: theme.spacing.xs,
    lineHeight: 28,
  },
  heroSubtitle: {
    fontSize: theme.typography.size.sm,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: theme.typography.weight.medium,
    lineHeight: 20,
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.lg,
    height: theme.sizes.iconContainer.lg,
    borderRadius: theme.sizes.iconContainer.lg / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  heroActionsContainer: {
    flexDirection: 'row',
    gap: theme.spacing.componentGap,
    marginTop: theme.spacing.sm,
  },
  heroActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: theme.spacing.componentGap,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.button,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    gap: theme.spacing.xs,
    minHeight: theme.sizes.button.height.sm,
  },
  heroActionText: {
    color: colors.white,
    fontSize: theme.typography.size.sm,
    fontWeight: theme.typography.weight.semibold,
  },

  // Section Headers
  sectionHeader: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  sectionTitle: {
    fontSize: theme.typography.size.lg,
    fontWeight: theme.typography.weight.bold,
    color: colors.textDark,
    lineHeight: 24,
  },
  sectionSubtitle: {
    fontSize: theme.typography.size.sm,
    color: colors.textMedium,
    fontWeight: theme.typography.weight.medium,
    marginTop: theme.spacing.xxs,
  },
  viewAllText: {
    fontSize: theme.typography.size.sm,
    color: colors.primary,
    fontWeight: theme.typography.weight.semibold,
  },

  // Stats Section
  statsSection: {
    marginBottom: theme.spacing.sectionSpacing,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: theme.spacing.md,
    gap: theme.spacing.componentGap,
  },
  statCard: {
    width: (width - theme.spacing.md * 2 - theme.spacing.componentGap) / 2,
    aspectRatio: 1.2,
  },
  statCardPressable: {
    flex: 1,
  },
  statCardGradient: {
    flex: 1,
    borderRadius: theme.borderRadius.card,
    ...theme.shadows.md,
  },
  statCardContent: {
    flex: 1,
    padding: theme.spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statIconContainer: {
    width: theme.sizes.iconContainer.sm,
    height: theme.sizes.iconContainer.sm,
    borderRadius: theme.sizes.iconContainer.sm / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValue: {
    fontSize: theme.typography.size.xl,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: theme.typography.size.xs,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: theme.typography.weight.medium,
    textAlign: 'center',
  },
  statChange: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: theme.spacing.xxs,
    borderRadius: theme.borderRadius.xs,
  },
  statChangeText: {
    fontSize: theme.typography.size.xs,
    fontWeight: theme.typography.weight.semibold,
  },

  // Quick Actions Section
  quickActionsSection: {
    marginBottom: theme.spacing.sectionSpacing,
  },
  quickActionsGrid: {
    paddingHorizontal: theme.spacing.md,
    gap: theme.spacing.componentGap,
  },
  quickActionCard: {
    marginBottom: theme.spacing.componentGap,
  },
  quickActionPressable: {
    borderRadius: theme.borderRadius.card,
    overflow: 'hidden',
  },
  quickActionGradient: {
    borderRadius: theme.borderRadius.card,
    ...theme.shadows.md,
  },
  quickActionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    minHeight: theme.sizes.button.height.lg,
  },
  quickActionIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  quickActionTextContainer: {
    flex: 1,
  },
  quickActionTitle: {
    fontSize: theme.typography.size.md,
    fontWeight: theme.typography.weight.semibold,
    color: colors.white,
    marginBottom: theme.spacing.xxs,
  },
  quickActionSubtitle: {
    fontSize: theme.typography.size.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: theme.typography.weight.medium,
  },

  // Premium Section
  premiumSection: {
    marginBottom: theme.spacing.sectionSpacing,
    paddingHorizontal: theme.spacing.md,
  },
  premiumCard: {
    borderRadius: theme.borderRadius.card,
    overflow: 'hidden',
    ...theme.shadows.lg,
  },
  premiumGradient: {
    borderRadius: theme.borderRadius.card,
  },
  premiumContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.cardPadding,
    minHeight: 100,
  },
  premiumTextContainer: {
    flex: 1,
    paddingRight: theme.spacing.md,
  },
  premiumBadgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
    gap: theme.spacing.xs,
  },
  premiumBadge: {
    fontSize: theme.typography.size.xs,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xxs,
    borderRadius: theme.borderRadius.xs,
    overflow: 'hidden',
  },
  premiumTitle: {
    fontSize: theme.typography.size.md,
    fontWeight: theme.typography.weight.bold,
    color: colors.white,
    marginBottom: theme.spacing.xxs,
  },
  premiumSubtitle: {
    fontSize: theme.typography.size.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: theme.typography.weight.medium,
  },
  premiumIconContainer: {
    width: theme.sizes.iconContainer.lg,
    height: theme.sizes.iconContainer.lg,
    borderRadius: theme.sizes.iconContainer.lg / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Tip Section
  tipSection: {
    marginBottom: theme.spacing.sectionSpacing,
  },
  tipCard: {
    marginHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.card,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  tipGradient: {
    borderRadius: theme.borderRadius.card,
  },
  tipContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  tipIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  tipTextContainer: {
    flex: 1,
  },
  tipTitle: {
    fontSize: theme.typography.size.md,
    fontWeight: theme.typography.weight.semibold,
    color: colors.white,
    marginBottom: theme.spacing.xs,
  },
  tipDescription: {
    fontSize: theme.typography.size.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: theme.typography.weight.medium,
    lineHeight: 20,
  },
});