import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  Dimensions,
  Alert,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  Image,
  KeyboardEvent,
} from "react-native";
import { FlashList } from "@shopify/flash-list";
import { colors } from "@/constants/colors";
import { images } from "@/constants/images";
import { useAssistantChatStore } from "@/store/assistantChatStore";
import { Header } from "@/components/Header";
import { ChatMessage } from "@/components/ChatMessage";
import { TypingIndicator } from "@/components/TypingIndicator";
import { Send, Settings, MessageSquare, Plus, AlertCircle, BarChart2 } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import { hasApiKey, initializeOpenAIAssistants, resetAssistantThreads } from "@/services/openaiAssistants";
import { APIKeyModal } from "@/components/APIKeyModal";
import { ChatHistoryModal } from "@/components/ChatHistoryModal";
import { LargeConversationWarning } from "@/components/LargeConversationWarning";
import { startMeasure, endMeasure } from "@/services/performanceMonitor";
import { PerformanceStats } from "@/components/PerformanceStats";

const { width, height } = Dimensions.get("window");

export default function ChatScreen() {
  const [message, setMessage] = useState("");
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [showPerformanceStats, setShowPerformanceStats] = useState(false);
  const [apiKeyConfigured, setApiKeyConfigured] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const flashListRef = useRef<FlashList<any>>(null);

  // Medir o tempo de renderização inicial da tela
  const screenLoadStartTime = useRef(startMeasure('chatScreenLoad'));

  const {
    currentConversationId,
    conversations,
    messages,
    isLoading,
    error,
    isTyping,
    showLargeConversationWarning,
    createNewConversation,
    sendUserMessage,
    fetchMessages,
    fetchConversations,
    setCurrentConversation,
    checkConversationSize
  } = useAssistantChatStore();

  const currentMessages = currentConversationId && messages[currentConversationId]
    ? messages[currentConversationId]
    : [];

  // Function to ensure scrolling works - corrigida para evitar saltos
  const scrollToBottom = (delay = 0) => {
    if (currentMessages.length === 0) return;

    const scrollAction = () => {
      if (flashListRef.current) {
        try {
          // Usar uma abordagem mais estável para o scroll
          flashListRef.current.scrollToEnd({
            animated: false // Desativar animação para evitar saltos
          });
        } catch (error) {
          console.log('Erro no scroll:', error);
        }
      }
    };

    if (delay === 0) {
      // Executar imediatamente
      scrollAction();
    } else {
      // Usar setTimeout para o delay especificado
      setTimeout(scrollAction, delay);
    }
  };

  // Function to handle layout changes - simplificada para evitar problemas
  const handleLayout = () => {
    // Usar um delay maior para garantir que o layout foi completamente renderizado
    setTimeout(() => scrollToBottom(0), 100);
  };

  // Removida função handleContentSizeChange que não é mais necessária

  useEffect(() => {
    checkApiKey();
    fetchConversations();

    // Add keyboard listeners with improved handling
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event: KeyboardEvent) => {
        const keyboardHeight = event.endCoordinates.height;
        setKeyboardVisible(true);
        setKeyboardHeight(keyboardHeight);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        setKeyboardHeight(0);
      }
    );

    // Registrar o tempo de carregamento da tela
    endMeasure('chatScreenLoad', screenLoadStartTime.current)
      .then(loadTime => {
        console.log(`Tela de chat carregada em ${loadTime.toFixed(2)}ms`);
      });

    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Fetch messages when conversation changes
  useEffect(() => {
    if (currentConversationId) {
      fetchMessages(currentConversationId);

      // Verificar se a conversa está muito grande
      if (messages[currentConversationId]?.length > 0) {
        checkConversationSize(currentConversationId);
      }
    }
  }, [currentConversationId]);

  // Verificar se há conversas disponíveis e criar uma nova se necessário
  useEffect(() => {
    const checkAndCreateConversation = async () => {
      // Se não há conversas e não estamos carregando, criar uma nova conversa automaticamente
      if (conversations.length === 0 && !isLoading && apiKeyConfigured) {
        console.log('Nenhuma conversa disponível, criando uma nova automaticamente...');
        try {
          // Mostrar indicador de carregamento
          set({ isLoading: true });

          // Criar nova conversa
          const newConversationId = await createNewConversation();

          // Definir como conversa atual
          setCurrentConversation(newConversationId);

          // Atualizar estado
          set({
            currentConversationId: newConversationId,
            isLoading: false
          });

          console.log(`Nova conversa criada automaticamente: ${newConversationId}`);
        } catch (error) {
          console.error('Erro ao criar conversa automaticamente:', error);
          set({ isLoading: false });
        }
      }
    };

    // Executar a verificação após um pequeno delay para garantir que o estado foi carregado
    const timer = setTimeout(checkAndCreateConversation, 500);

    return () => clearTimeout(timer);
  }, [conversations.length, isLoading, apiKeyConfigured]);

  // Scroll to bottom when messages change - corrigido para evitar saltos
  useEffect(() => {
    if (currentMessages.length > 0) {
      // Usar um único scroll com delay suficiente para garantir que o layout foi atualizado
      setTimeout(() => {
        scrollToBottom(0);
      }, 150);
    }
  }, [currentMessages.length]);  // Observar apenas o comprimento para evitar loops desnecessários

  // Scroll to bottom when typing state changes
  useEffect(() => {
    if (isTyping && currentMessages.length > 0) {
      // Usar um delay maior para garantir que o indicador de digitação foi renderizado
      setTimeout(() => {
        scrollToBottom(0);
      }, 100);
    }
  }, [isTyping]);

  const checkApiKey = async () => {
    const hasKey = await hasApiKey();
    setApiKeyConfigured(hasKey);
    if (hasKey) {
      await initializeOpenAIAssistants();
    }
  };

  const handleSend = async () => {
    if (!message.trim()) return;

    // Verificar se a API key está configurada
    if (!apiKeyConfigured) {
      setShowApiKeyModal(true);
      return;
    }

    // Store message text and clear input immediately for better UX
    const messageText = message.trim();
    setMessage("");

    // Dismiss keyboard on send for better UX
    Keyboard.dismiss();

    try {
      // Se não houver conversa ativa, criar uma nova automaticamente
      let activeConversationId = currentConversationId;

      if (!activeConversationId) {
        console.log('Nenhuma conversa ativa, criando uma nova...');

        // Mostrar indicador de carregamento
        set({ isLoading: true });

        // Criar nova conversa instantaneamente
        activeConversationId = await createNewConversation();
        console.log(`Nova conversa criada: ${activeConversationId}`);

        // Forçar a definição da conversa atual para a nova conversa
        setCurrentConversation(activeConversationId);

        // Forçar atualização do estado para garantir que a UI reflita a nova conversa
        set({
          currentConversationId: activeConversationId,
          isLoading: false
        });

        // Aguardar um momento para garantir que a mudança de conversa seja processada
        await new Promise(resolve => setTimeout(resolve, 100));

        // Verificar se a conversa atual foi realmente atualizada
        if (useAssistantChatStore.getState().currentConversationId !== activeConversationId) {
          console.log(`Alerta: A conversa atual não foi atualizada corretamente. Esperado: ${activeConversationId}, Atual: ${useAssistantChatStore.getState().currentConversationId}`);
          // Forçar novamente a atualização
          useAssistantChatStore.setState({
            currentConversationId: activeConversationId,
            isLoading: false
          });
        }
      }

      console.log(`Enviando mensagem para a conversa: ${activeConversationId}`);
      // Enviar a mensagem imediatamente
      await sendUserMessage(activeConversationId, messageText);

      // Scroll to bottom after sending - corrigido para evitar saltos
      // Usar um único scroll com delay suficiente
      setTimeout(() => {
        scrollToBottom(0);
      }, 200);
    } catch (error) {
      console.error('Error sending message:', error);
      set({ isLoading: false });
      Alert.alert(
        "Erro",
        "Ocorreu um erro ao enviar sua mensagem. Por favor, tente novamente."
      );
    }
  };

  const handleApiKeySuccess = () => {
    setApiKeyConfigured(true);
    setShowApiKeyModal(false);
    // Try sending the message again
    if (message.trim()) {
      handleSend();
    }
  };

  const handleNewChat = async () => {
    try {
      // Limpar mensagem atual
      setMessage("");

      // Mostrar indicador de carregamento
      set({ isLoading: true });

      // Criar nova conversa e obter o ID
      const newConversationId = await createNewConversation();

      // Garantir que a nova conversa seja definida como a conversa atual
      setCurrentConversation(newConversationId);

      // Forçar atualização do estado para garantir que a UI reflita a nova conversa
      set({
        currentConversationId: newConversationId,
        isLoading: false
      });

      // Mostrar feedback visual
      Alert.alert(
        "Nova Conversa",
        "Uma nova conversa foi criada com sucesso!",
        [{ text: "OK" }],
        { cancelable: true }
      );
    } catch (error) {
      console.error('Error creating new chat:', error);
      set({ isLoading: false });
      Alert.alert(
        "Erro",
        "Ocorreu um erro ao criar uma nova conversa. Por favor, tente novamente."
      );
    }
  };

  // Função auxiliar para acessar o setState do store diretamente
  const set = useAssistantChatStore.setState;

  // Render empty chat view
  const renderEmptyChat = () => (
    <View style={styles.emptyChat}>
      <Image
        source={images.logo}
        style={styles.emptyChatLogo}
        resizeMode="contain"
        fadeDuration={0}
      />
      <Text style={styles.emptyChatTitle}>
        Olá! Sou a Lia
      </Text>
      <Text style={styles.emptyChatText}>
        Sua assistente de estudos inteligente. Como posso ajudar você hoje?
      </Text>
      <Text style={styles.emptyChatText}>
        Você pode me perguntar sobre qualquer assunto ou pedir ajuda com seus estudos.
      </Text>

      {!apiKeyConfigured && (
        <Pressable
          style={styles.apiKeyButton}
          onPress={() => setShowApiKeyModal(true)}
        >
          <Text style={styles.apiKeyButtonText}>Configurar API Key da OpenAI</Text>
        </Pressable>
      )}

      <Pressable
        style={[styles.apiKeyButton, { backgroundColor: colors.success, marginTop: 10 }]}
        onPress={handleNewChat}
      >
        <Text style={styles.apiKeyButtonText}>Iniciar Nova Conversa</Text>
      </Pressable>
    </View>
  );

  // Render error view
  const renderErrorView = () => (
    <View style={styles.errorContainer}>
      <AlertCircle size={40} color={colors.danger} />
      <Text style={styles.errorTitle}>Ops! Algo deu errado</Text>
      <Text style={styles.errorText}>{error}</Text>
      <View style={styles.errorButtonsContainer}>
        <Pressable
          style={styles.retryButton}
          onPress={() => currentConversationId && fetchMessages(currentConversationId)}
        >
          <Text style={styles.retryButtonText}>Tentar novamente</Text>
        </Pressable>

        <Pressable
          style={[styles.retryButton, styles.resetButton]}
          onPress={() => {
            try {
              // Resetar todos os threads ativos
              resetAssistantThreads();
              // Limpar o erro usando o store
              useAssistantChatStore.setState({ error: null, isTyping: false });
              // Mostrar mensagem de sucesso
              Alert.alert("Sucesso", "Todas as conexões foram resetadas. Tente enviar sua mensagem novamente.");
            } catch (error) {
              console.error('Erro ao resetar threads:', error);
              // Fallback: usar o store para limpar o erro
              useAssistantChatStore.setState({ error: null, isTyping: false });

              // Tentar inicializar novamente o cliente OpenAI
              initializeOpenAIAssistants().then(() => {
                Alert.alert("Aviso", "Conexões reiniciadas. Tente enviar sua mensagem novamente.");
              }).catch(() => {
                Alert.alert("Aviso", "Algumas conexões podem não ter sido resetadas completamente. Tente reiniciar o aplicativo.");
              });
            }
          }}
        >
          <Text style={styles.retryButtonText}>Resetar conexões</Text>
        </Pressable>
      </View>
    </View>
  );

  // Render a message item - memoizado para melhor desempenho
  const renderMessageItem = useCallback(({ item }: { item: any }) => {
    // Usar startMeasure/endMeasure para medir o desempenho de renderização
    const startTime = startMeasure('renderChatMessage');
    const result = <ChatMessage message={item} />;
    endMeasure('renderChatMessage', startTime);
    return result;
  }, []);

  // Render loading indicator or typing indicator - memoizado para melhor desempenho
  const renderFooter = useCallback(() => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Processando...</Text>
        </View>
      );
    }

    if (isTyping) {
      // Usar um pequeno delay para garantir que o scroll funcione corretamente
      setTimeout(() => scrollToBottom(0), 50);
      return <TypingIndicator />;
    }

    return null;
  }, [isLoading, isTyping]);

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Pressable
            style={styles.headerConversationsButton}
            onPress={() => setShowHistoryModal(true)}
          >
            <MessageSquare size={22} color={colors.primary} />
            <Text style={styles.headerButtonText}>Conversas</Text>
          </Pressable>

          <Text style={styles.headerTitle}>Chat com a Lia</Text>

          <View style={styles.headerRightButtons}>
            <Pressable
              style={styles.headerStatsButton}
              onPress={() => setShowPerformanceStats(true)}
            >
              <BarChart2 size={22} color={colors.primary} />
            </Pressable>

            <Pressable
              style={styles.headerNewChatButton}
              onPress={handleNewChat}
            >
              <Plus size={22} color={colors.primary} />
              <Text style={styles.headerButtonText}>Nova</Text>
            </Pressable>
          </View>
        </View>
      </View>

      {/* Main content with keyboard avoiding */}
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
        enabled={false} // Desativamos o KeyboardAvoidingView padrão e gerenciamos manualmente
      >
        {/* Messages list */}
        {error ? (
          renderErrorView()
        ) : currentMessages.length === 0 && !isLoading ? (
          renderEmptyChat()
        ) : (
          <View style={{ flex: 1 }}>
            {showLargeConversationWarning && (
              <LargeConversationWarning onNewChat={handleNewChat} />
            )}

            <FlashList
              ref={flashListRef}
              data={currentMessages}
              renderItem={renderMessageItem}
              keyExtractor={(item: any, index: number) => `${item.id}_${index}`}
              contentContainerStyle={styles.messagesContent}
              showsVerticalScrollIndicator={true}
              ListFooterComponent={renderFooter}
              onLayout={handleLayout}
              keyboardShouldPersistTaps="handled"
              estimatedItemSize={100} // Estimativa do tamanho médio de cada mensagem
              extraData={isTyping} // Atualizar quando o estado de digitação mudar
              drawDistance={500} // Aumentar a distância de desenho para melhor desempenho
              overrideItemLayout={(layout, item) => {
                // Otimização opcional para tamanhos de itens conhecidos
                if (item.role === 'assistant') {
                  layout.size = 120; // Mensagens do assistente geralmente são maiores
                } else {
                  layout.size = 80; // Mensagens do usuário geralmente são menores
                }
              }}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0, // Manter a posição de rolagem quando novos itens são adicionados
                autoscrollToTopThreshold: 10 // Distância do topo para acionar rolagem automática
              }}
              onMomentumScrollEnd={() => {
                // Verificar se estamos perto do final e, se sim, garantir que o scroll vá até o final
                if (isTyping) {
                  setTimeout(() => scrollToBottom(0), 50);
                }
              }}
              onEndReachedThreshold={0.1} // Distância do final para acionar onEndReached
            />
          </View>
        )}

        {/* Input area */}
        <View style={[styles.inputWrapper, keyboardVisible && { bottom: Platform.OS === 'ios' ? keyboardHeight + 10 : 10 }]}>
          <View style={styles.inputGlow} />
          <GlassCard style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Digite sua mensagem..."
              placeholderTextColor={colors.textLight}
              value={message}
              onChangeText={setMessage}
              multiline
              maxLength={500}
              autoCorrect={false}
              keyboardType="default"
              returnKeyType="send"
              onSubmitEditing={message.trim() ? handleSend : undefined}
              // Removido blurOnSubmit que está depreciado
            />
            <Pressable
              style={({ pressed }) => [
                styles.sendButton,
                pressed && styles.sendButtonPressed,
                !message.trim() && styles.sendButtonDisabled,
              ]}
              onPress={handleSend}
              disabled={!message.trim()}
            >
              <LinearGradient
                colors={message.trim() ? ["#3399FF", "#3399FF"] : ["#D1D5DB", "#9CA3AF"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                style={styles.sendButtonGradient}
              >
                <Send size={18} color="#fff" />
              </LinearGradient>
            </Pressable>
          </GlassCard>
        </View>
      </KeyboardAvoidingView>

      {/* Modals */}
      <APIKeyModal
        visible={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        onSuccess={handleApiKeySuccess}
      />

      <ChatHistoryModal
        visible={showHistoryModal}
        onClose={() => setShowHistoryModal(false)}
      />

      <PerformanceStats
        visible={showPerformanceStats}
        onClose={() => setShowPerformanceStats(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  errorButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 20,
  },
  resetButton: {
    backgroundColor: colors.danger,
    marginLeft: 10,
  },
  apiKeyButton: {
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  apiKeyButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.danger,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 22,
  },
  retryButton: {
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  keyboardAvoidingView: {
    flex: 1,
    position: 'relative',
  },
  messagesContent: {
    paddingVertical: 16,
    paddingHorizontal: 8,
    paddingBottom: 170, // Ajustado para garantir que as mensagens não fiquem escondidas
  },
  emptyChat: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyChatTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyChatText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 22,
  },
  emptyChatLogo: {
    width: 100,
    height: 100,
    marginBottom: 16,
  },
  inputWrapper: {
    paddingHorizontal: 16,
    paddingVertical: 6, // Reduzido de 8 para 6
    backgroundColor: 'transparent',
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 95 : 85, // Ajustado para não ficar atrás do menu flutuante
    left: 0,
    right: 0,
    zIndex: 100,
  },
  inputGlow: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 20,
    backgroundColor: 'transparent',
    transform: [{ scale: 1.05 }],
    // Removed shadow for cleaner look
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10, // Reduzido de 14 para 10
    backgroundColor: "rgba(255, 255, 255, 0.98)",
    borderWidth: 1,
    borderColor: `${colors.primary}60`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 4,
    borderRadius: 16,
  },
  input: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingHorizontal: 14, // Reduzido de 16 para 14
    paddingVertical: 8, // Reduzido de 12 para 8
    maxHeight: 80, // Reduzido de 100 para 80
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    borderWidth: 1,
    borderColor: `${colors.primary}70`,
  },
  sendButton: {
    width: 44, // Reduzido de 52 para 44
    height: 44, // Reduzido de 52 para 44
    borderRadius: 22, // Reduzido de 26 para 22
    overflow: "hidden",
    marginLeft: 10, // Reduzido de 12 para 10
    backgroundColor: colors.primary,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sendButtonGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonPressed: {
    opacity: 0.8,
    transform: [{ scale: 0.95 }],
  },
  sendButtonDisabled: {
    opacity: 0.7,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    marginLeft: 16,
    marginBottom: 8,
  },
  loadingText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
    fontStyle: "italic",
  },
  headerContainer: {
    backgroundColor: colors.background,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: `${colors.primary}20`,
    zIndex: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerConversationsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  headerNewChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  headerButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
    marginLeft: 6,
  },
  headerRightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerStatsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
});
