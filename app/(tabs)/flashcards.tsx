import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, SafeAreaView, Pressable, Alert, TextInput, Modal, ActivityIndicator, Animated } from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { useStudyStore } from "@/store/studyStore";
import { Header } from "@/components/Header";
import { FlashcardSetCard } from "@/components/FlashcardSetCard";
import { Button } from "@/components/Button";
import { BookOpen, Search, Sparkles, X, Plus, Calendar, Clock, Zap, Target, Star } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import { AIGenerationModal } from "@/components/AIGenerationModal";
import { SubjectSelector } from "@/components/SubjectSelector";
import { MultipleFlashcardsForm } from "@/components/MultipleFlashcardsForm";
import { generateFlashcards } from "@/services/openai";
import { FlashcardFilters, SortOption, FilterOption } from "@/components/FlashcardFilters";
import { RouteGuard } from "@/components/RouteGuard";

export default function FlashcardsScreen() {
  const router = useRouter();
  const { flashcardSets, addFlashcardSet, addFlashcard, fetchSubjects, fetchFlashcardSets, deleteFlashcardSet, getFlashcardsForReview } = useStudyStore();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showMultipleFlashcardsForm, setShowMultipleFlashcardsForm] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Filtros e ordenação
  const [sortOption, setSortOption] = useState<SortOption>("date");
  const [filterOption, setFilterOption] = useState<FilterOption>("all");

  // Estado para armazenar o número de flashcards para revisão
  const [reviewCount, setReviewCount] = useState(0);

  // Carregar matérias e conjuntos de flashcards quando o componente for montado
  useEffect(() => {
    fetchSubjects();
    fetchFlashcardSets();

    // Verificar quantos flashcards precisam ser revisados
    const cardsForReview = getFlashcardsForReview();
    setReviewCount(cardsForReview.length);
  }, []);

  const handleFlashcardSetPress = (set) => {
    router.push(`/flashcards/${set.id}`);
  };

  const handleEditFlashcardSet = (set) => {
    // Implementar a edição do conjunto de flashcards
    router.push(`/flashcards/edit/${set.id}`);
  };

  const handleDeleteFlashcardSet = async (set) => {
    try {
      await deleteFlashcardSet(set.id);
      // Atualizar a lista após a exclusão
      fetchFlashcardSets();
    } catch (error) {
      console.error("Erro ao excluir conjunto de flashcards:", error);
      Alert.alert("Erro", "Não foi possível excluir o conjunto de flashcards.");
    }
  };

  // Função para filtrar e ordenar os conjuntos de flashcards
  const getFilteredAndSortedSets = () => {
    // Primeiro aplicar o filtro
    let filteredSets = [...flashcardSets];

    switch (filterOption) {
      case "mastered":
        filteredSets = filteredSets.filter(set => {
          const masteredPercentage = Math.round((set.mastered / set.count) * 100);
          return masteredPercentage >= 90; // Consideramos dominado quando 90% ou mais estão dominados
        });
        break;
      case "learning":
        filteredSets = filteredSets.filter(set => {
          const masteredPercentage = Math.round((set.mastered / set.count) * 100);
          return masteredPercentage > 0 && masteredPercentage < 90;
        });
        break;
      case "new":
        filteredSets = filteredSets.filter(set => {
          return set.mastered === 0 || set.count === 0;
        });
        break;
      default: // "all"
        // Não aplicar filtro
        break;
    }

    // Depois aplicar a ordenação
    switch (sortOption) {
      case "title":
        filteredSets.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case "date":
        filteredSets.sort((a, b) => {
          const dateA = a.lastReviewed ? new Date(a.lastReviewed).getTime() : 0;
          const dateB = b.lastReviewed ? new Date(b.lastReviewed).getTime() : 0;
          return dateB - dateA; // Ordem decrescente (mais recente primeiro)
        });
        break;
      case "progress":
        filteredSets.sort((a, b) => {
          const progressA = a.count > 0 ? (a.mastered / a.count) : 0;
          const progressB = b.count > 0 ? (b.mastered / b.count) : 0;
          return progressB - progressA; // Ordem decrescente (maior progresso primeiro)
        });
        break;
      default:
        // Não aplicar ordenação
        break;
    }

    return filteredSets;
  };

  const handleCreateFlashcardSet = () => {
    router.push("/flashcards/new-set");
  };

  const handleSaveMultipleFlashcards = async (title: string, description: string, subject: string, subjectId: string | undefined, flashcards: any[]) => {
    try {
      setIsSaving(true);

      // Criar um novo conjunto sem especificar o ID (será gerado pelo Supabase)
      const newSet = {
        title,
        description: description || "Conjunto de flashcards para estudo",
        subject: subject || "Geral",
      };

      // Adicionar o conjunto e obter o ID gerado
      const savedSet = await addFlashcardSet(newSet);

      if (!savedSet || !savedSet.id) {
        throw new Error("Falha ao criar conjunto de flashcards");
      }

      // Adicionar cada flashcard ao conjunto
      for (const card of flashcards) {
        try {
          const flashcard = {
            setId: savedSet.id,
            front: card.front,
            back: card.back,
            difficulty: 0,
            nextReview: null,
            reviewCount: 0,
            imageUrl: null,
            subject_id: subjectId
          };

          await addFlashcard(flashcard);
        } catch (cardError) {
          console.error("Erro ao adicionar flashcard individual:", cardError);
          // Continuar com o próximo card mesmo se um falhar
        }
      }

      // Fechar modal
      setShowMultipleFlashcardsForm(false);

      // Mostrar mensagem de sucesso
      Alert.alert(
        "Sucesso",
        `Conjunto de flashcards criado com ${flashcards.length} cartões.`,
        [
          {
            text: "Ver Conjunto",
            onPress: () => router.push(`/flashcards/${savedSet.id}`)
          }
        ]
      );

      // Atualizar a lista de conjuntos
      fetchFlashcardSets();

    } catch (error) {
      console.error("Erro ao criar conjunto de flashcards:", error);
      Alert.alert("Erro", "Não foi possível criar o conjunto de flashcards. Por favor, tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleGenerateFlashcards = async (topic: string) => {
    try {
      setIsGenerating(true);

      // Generate flashcards using OpenAI
      const generatedFlashcards = await generateFlashcards(topic, 5);

      if (!generatedFlashcards || generatedFlashcards.length === 0) {
        Alert.alert("Erro", "Não foi possível gerar flashcards. Por favor, tente novamente.");
        return;
      }

      // Criar um novo conjunto sem especificar o ID (será gerado pelo Supabase)
      const newSet = {
        title: topic,
        description: `Flashcards gerados por IA sobre ${topic}`,
        subject: "Gerado por IA",
      };

      // Adicionar o conjunto e obter o ID gerado
      const savedSet = await addFlashcardSet(newSet);

      if (!savedSet || !savedSet.id) {
        throw new Error("Falha ao criar conjunto de flashcards");
      }

      // Adicionar cada flashcard ao conjunto
      for (const card of generatedFlashcards) {
        try {
          const flashcard = {
            setId: savedSet.id,
            front: card.question,
            back: card.answer,
            difficulty: 0,
            nextReview: null,
            reviewCount: 0,
            imageUrl: null
          };

          await addFlashcard(flashcard);
        } catch (cardError) {
          console.error("Erro ao adicionar flashcard individual:", cardError);
          // Continuar com o próximo card mesmo se um falhar
        }
      }

      // Fechar modal e navegar para o novo conjunto
      setShowAIModal(false);
      router.push(`/flashcards/${savedSet.id}`);

    } catch (error) {
      console.error("Erro ao gerar flashcards:", error);
      Alert.alert("Erro", "Ocorreu um erro ao gerar os flashcards. Por favor, verifique sua conexão e tente novamente.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <RouteGuard>
      <View style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Header
        title="Flashcards"
        rightComponent={
          <Pressable
            style={styles.aiButton}
            onPress={() => setShowAIModal(true)}
          >
            <Sparkles size={24} color={colors.primary} />
          </Pressable>
        }
      />

      <View style={styles.searchContainer}>
        <Pressable style={styles.searchBar}>
          <Search size={20} color={colors.textMedium} />
          <Text style={styles.searchPlaceholder}>Buscar conjuntos...</Text>
        </Pressable>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <LinearGradient
            colors={['#3399FF', '#66B2FF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.heroGradient}
          >
            <View style={styles.heroContent}>
              <View style={styles.heroHeader}>
                <View style={styles.heroTextContainer}>
                  <Text style={styles.heroTitle}>Flashcards</Text>
                  <Text style={styles.heroSubtitle}>
                    Memorização espaçada para melhor retenção
                  </Text>
                </View>
                <View style={styles.heroIconContainer}>
                  <Zap size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                </View>
              </View>
              <View style={styles.quickActionsContainer}>
                <Pressable
                  style={styles.quickActionButton}
                  onPress={handleCreateFlashcardSet}
                >
                  <Plus size={theme.sizes.icon.sm} color="#fff" />
                  <Text style={styles.quickActionText}>Criar</Text>
                </Pressable>
                <Pressable
                  style={styles.quickActionButton}
                  onPress={() => setShowAIModal(true)}
                >
                  <Sparkles size={theme.sizes.icon.sm} color="#fff" />
                  <Text style={styles.quickActionText}>IA</Text>
                </Pressable>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Statistics Section */}
        <View style={styles.statsSection}>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#3399FF', '#66B2FF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <BookOpen size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>{flashcardSets.length}</Text>
                <Text style={styles.statLabel}>Conjuntos</Text>
              </LinearGradient>
            </View>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#F59E0B', '#FBBF24']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <Zap size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>
                  {flashcardSets.reduce((total, set) => total + set.count, 0)}
                </Text>
                <Text style={styles.statLabel}>Cartões</Text>
              </LinearGradient>
            </View>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#10B981', '#34D399']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <Target size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>
                  {flashcardSets.reduce((total, set) => total + set.mastered, 0)}
                </Text>
                <Text style={styles.statLabel}>Dominados</Text>
              </LinearGradient>
            </View>
            <View style={styles.statCard}>
              <LinearGradient
                colors={['#8B5CF6', '#A78BFA']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.statCardGradient}
              >
                <View style={styles.statIconContainer}>
                  <Clock size={theme.sizes.icon.sm} color="rgba(255, 255, 255, 0.9)" />
                </View>
                <Text style={styles.statValue}>{reviewCount}</Text>
                <Text style={styles.statLabel}>Para Revisar</Text>
              </LinearGradient>
            </View>
          </View>
        </View>

        {/* Seção de revisão diária */}
        <View style={styles.reviewSection}>
          <GlassCard style={styles.reviewCard} gradient>
            <View style={styles.reviewCardContent}>
              <View style={styles.reviewIconContainer}>
                <Clock size={28} color="#fff" />
              </View>
              <View style={styles.reviewTextContainer}>
                <Text style={styles.reviewTitle}>Revisão Diária</Text>
                <Text style={styles.reviewText}>
                  {reviewCount > 0
                    ? `Você tem ${reviewCount} cartões para revisar hoje`
                    : "Você não tem cartões para revisar hoje"}
                </Text>
              </View>
            </View>
            <Button
              title={reviewCount > 0 ? "Iniciar revisão" : "Verificar novamente"}
              onPress={() => router.push("/flashcards/review")}
              variant={reviewCount > 0 ? "primary" : "secondary"}
              size="medium"
              fullWidth
              icon={Calendar}
            />
          </GlassCard>
        </View>

        <View style={styles.setsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Seus Conjuntos</Text>
            <Button
              title="Novo conjunto"
              onPress={handleCreateFlashcardSet}
              variant="primary"
              size="small"
              style={styles.newSetButton}
            />
          </View>

          <FlashcardFilters
            currentSort={sortOption}
            currentFilter={filterOption}
            onSortChange={setSortOption}
            onFilterChange={setFilterOption}
          />

          {flashcardSets.length > 0 ? (
            getFilteredAndSortedSets().map((set) => (
              <FlashcardSetCard
                key={set.id}
                set={set}
                onPress={handleFlashcardSetPress}
                onEdit={handleEditFlashcardSet}
                onDelete={handleDeleteFlashcardSet}
              />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <BookOpen size={48} color={`${colors.primary}50`} />
              <Text style={styles.emptyText}>
                Você ainda não tem conjuntos de flashcards. Crie seu primeiro conjunto para começar a estudar.
              </Text>
              <Button
                title="Criar Primeiro Conjunto"
                onPress={handleCreateFlashcardSet}
                variant="primary"
                size="medium"
                icon={Plus}
                style={styles.emptyButton}
              />
            </View>
          )}
        </View>
      </ScrollView>

      {/* Removido o modal de formulário de flashcards múltiplos, agora usando uma página dedicada */}

      {/* AI Generation Modal */}
      <AIGenerationModal
        visible={showAIModal}
        onClose={() => setShowAIModal(false)}
        title="Gerar Flashcards com IA"
        placeholder="Digite um tópico ou assunto para gerar flashcards..."
        onGenerate={handleGenerateFlashcards}
        loading={isGenerating}
        type="flashcards"
      />
      </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  searchContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchPlaceholder: {
    marginLeft: 8,
    color: colors.textMedium,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Aumentado para evitar que os cards fiquem embaixo do menu
  },
  // Hero Section Styles
  heroSection: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  heroGradient: {
    borderRadius: 20,
  },
  heroContent: {
    padding: 20,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 18,
  },
  heroTextContainer: {
    flex: 1,
    paddingRight: 12,
  },
  heroTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#fff",
    marginBottom: 4,
    lineHeight: 28,
  },
  heroSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: '500',
    lineHeight: 20,
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  quickActionsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    gap: 6,
  },
  quickActionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  // Stats Section Styles
  statsSection: {
    marginHorizontal: 16,
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: '48%',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  statCardGradient: {
    padding: 16,
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.9)',
    minHeight: 90,
    justifyContent: 'center',
  },
  statIconContainer: {
    width: theme.sizes.iconContainer.sm,
    height: theme.sizes.iconContainer.sm,
    borderRadius: theme.sizes.iconContainer.sm / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  reviewSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  reviewCard: {
    padding: 16,
  },
  reviewCardContent: {
    flexDirection: "row",
    marginBottom: 16,
  },
  reviewIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: colors.secondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  reviewTextContainer: {
    flex: 1,
  },
  reviewTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  reviewText: {
    fontSize: 14,
    color: colors.textLight,
    lineHeight: 20,
  },
  statCard: {
    flex: 1,
    padding: 16,
    alignItems: "center",
  },
  statValue: {
    fontSize: 16,
    fontWeight: "700",
    color: colors.white,
    marginBottom: 2,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 11,
    fontWeight: "600",
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: "center",
    lineHeight: 14,
  },
  setsSection: {
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
  },
  newSetButton: {
    minWidth: 120,
  },
  aiButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalContainer: {
    width: "100%",
    maxWidth: 500,
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  modalFooter: {
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  modalButton: {
    minWidth: 100,
    marginLeft: 12,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  buttonGroupItem: {
    flex: 1,
    marginHorizontal: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 16,
    marginVertical: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
    marginVertical: 16,
    lineHeight: 24,
  },
  emptyButton: {
    marginTop: 8,
  },
});