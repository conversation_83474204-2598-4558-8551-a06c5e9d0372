import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TextInput,
  Pressable,
  ActivityIndicator,
  Alert,
  Share,
  Keyboard,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";
import { Button } from "@/components/Button";
import { useUserStore } from "@/store/userStore";
import { generateChatCompletion, hasApiKey } from "@/services/openai";
import { APIKeyModal } from "@/components/APIKeyModal";
import {
  Sparkles,
  FileText,
  BookOpen,
  Share2,
  Copy,
  Save,
  ArrowLeft,
  Sliders,
} from "lucide-react-native";
import * as Clipboard from 'expo-clipboard';

export default function TextGeneratorScreen() {
  const router = useRouter();
  const { addXP } = useUserStore();

  const [topic, setTopic] = useState("");
  const [textType, setTextType] = useState("resumo");
  const [generatedText, setGeneratedText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [textLength, setTextLength] = useState("medium");
  const [complexity, setComplexity] = useState("medium");

  const textTypes = [
    { id: "resumo", label: "Resumo", icon: FileText },
    { id: "explicacao", label: "Explicação", icon: BookOpen },
    { id: "topicos", label: "Tópicos", icon: FileText },
  ];

  const handleGenerate = async () => {
    if (!topic.trim()) {
      Alert.alert("Erro", "Por favor, insira um tópico para gerar o texto.");
      return;
    }

    const hasKey = await hasApiKey();
    if (!hasKey) {
      setShowApiKeyModal(true);
      return;
    }

    setIsLoading(true);
    try {
      // Prepare the prompt based on selected options
      let lengthText = "";
      switch (textLength) {
        case "short":
          lengthText = "curto (cerca de 150 palavras)";
          break;
        case "medium":
          lengthText = "médio (cerca de 300 palavras)";
          break;
        case "long":
          lengthText = "longo (cerca de 500 palavras)";
          break;
      }

      let complexityText = "";
      switch (complexity) {
        case "basic":
          complexityText = "básico, adequado para iniciantes";
          break;
        case "medium":
          complexityText = "intermediário, com conceitos moderadamente avançados";
          break;
        case "advanced":
          complexityText = "avançado, com conceitos complexos e terminologia específica";
          break;
      }

      let typeText = "";
      switch (textType) {
        case "resumo":
          typeText = "um resumo conciso e informativo";
          break;
        case "explicacao":
          typeText = "uma explicação detalhada e didática";
          break;
        case "topicos":
          typeText = "uma lista de tópicos importantes com breves explicações";
          break;
      }

      const prompt = `Gere ${typeText} sobre "${topic}". O texto deve ser ${lengthText} e com nível de complexidade ${complexityText}. Formate o texto de forma clara e organizada, utilizando parágrafos, marcadores ou subtítulos quando apropriado.`;

      const messages = [
        {
          role: "system",
          content:
            "Você é um assistente educacional especializado em criar materiais de estudo de alta qualidade. Seus textos são claros, informativos e bem estruturados.",
        },
        { role: "user", content: prompt },
      ];

      const response = await generateChatCompletion(messages);
      setGeneratedText(response || "Não foi possível gerar o texto. Por favor, tente novamente.");

      // Add XP for generating text
      addXP(15);
    } catch (error) {
      console.error("Error generating text:", error);
      Alert.alert(
        "Erro",
        "Ocorreu um erro ao gerar o texto. Por favor, verifique sua conexão e tente novamente."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyText = async () => {
    if (!generatedText) return;

    try {
      await Clipboard.setStringAsync(generatedText);
      Alert.alert("Sucesso", "Texto copiado para a área de transferência!");
    } catch (error) {
      Alert.alert("Erro", "Não foi possível copiar o texto.");
    }
  };

  const handleShareText = async () => {
    if (!generatedText) return;

    try {
      await Share.share({
        message: generatedText,
        title: `Texto sobre ${topic}`,
      });
    } catch (error) {
      Alert.alert("Erro", "Não foi possível compartilhar o texto.");
    }
  };

  const handleSaveAsNote = () => {
    if (!generatedText) return;

    // In a real app, this would save to the notes system
    Alert.alert("Funcionalidade em desenvolvimento", "Em breve você poderá salvar textos como anotações!");
  };

  const handleApiKeySuccess = () => {
    setShowApiKeyModal(false);
    handleGenerate();
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Stack.Screen
          options={{
            title: "Gerador de Textos",
            headerShown: true,
            headerRight: () => (
              <Pressable
                style={styles.optionsButton}
                onPress={() => setShowOptions(!showOptions)}
              >
                <Sliders size={24} color={colors.text} />
              </Pressable>
            ),
          }}
        />
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{flex: 1}}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
        <View style={styles.headerSection}>
          <GlassCard style={styles.headerCard} gradient>
            <View style={styles.headerIconContainer}>
              <Sparkles size={24} color={colors.primary} />
            </View>
            <Text style={styles.headerTitle}>Gerador de Textos de Estudo</Text>
            <Text style={styles.headerText}>
              Gere textos de estudo personalizados sobre qualquer assunto com a ajuda da IA.
            </Text>
          </GlassCard>
        </View>

        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>Tópico ou Assunto</Text>
          <TextInput
            style={styles.input}
            placeholder="Ex: Fotossíntese, Segunda Guerra Mundial, Equações Quadráticas..."
            value={topic}
            onChangeText={setTopic}
            multiline
          />

          <Text style={styles.inputLabel}>Tipo de Texto</Text>
          <View style={styles.typeSelector}>
            {textTypes.map((type) => (
              <Pressable
                key={type.id}
                style={[
                  styles.typeOption,
                  textType === type.id && styles.typeOptionSelected,
                ]}
                onPress={() => setTextType(type.id)}
              >
                <type.icon
                  size={20}
                  color={textType === type.id ? colors.white : colors.text}
                />
                <Text
                  style={[
                    styles.typeText,
                    textType === type.id && styles.typeTextSelected,
                  ]}
                >
                  {type.label}
                </Text>
              </Pressable>
            ))}
          </View>

          {showOptions && (
            <View style={styles.advancedOptions}>
              <Text style={styles.optionsTitle}>Opções Avançadas</Text>

              <Text style={styles.optionLabel}>Tamanho do Texto</Text>
              <View style={styles.optionSelector}>
                <Pressable
                  style={[
                    styles.optionButton,
                    textLength === "short" && styles.optionButtonSelected,
                  ]}
                  onPress={() => setTextLength("short")}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      textLength === "short" && styles.optionButtonTextSelected,
                    ]}
                  >
                    Curto
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.optionButton,
                    textLength === "medium" && styles.optionButtonSelected,
                  ]}
                  onPress={() => setTextLength("medium")}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      textLength === "medium" && styles.optionButtonTextSelected,
                    ]}
                  >
                    Médio
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.optionButton,
                    textLength === "long" && styles.optionButtonSelected,
                  ]}
                  onPress={() => setTextLength("long")}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      textLength === "long" && styles.optionButtonTextSelected,
                    ]}
                  >
                    Longo
                  </Text>
                </Pressable>
              </View>

              <Text style={styles.optionLabel}>Nível de Complexidade</Text>
              <View style={styles.optionSelector}>
                <Pressable
                  style={[
                    styles.optionButton,
                    complexity === "basic" && styles.optionButtonSelected,
                  ]}
                  onPress={() => setComplexity("basic")}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      complexity === "basic" && styles.optionButtonTextSelected,
                    ]}
                  >
                    Básico
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.optionButton,
                    complexity === "medium" && styles.optionButtonSelected,
                  ]}
                  onPress={() => setComplexity("medium")}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      complexity === "medium" && styles.optionButtonTextSelected,
                    ]}
                  >
                    Intermediário
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.optionButton,
                    complexity === "advanced" && styles.optionButtonSelected,
                  ]}
                  onPress={() => setComplexity("advanced")}
                >
                  <Text
                    style={[
                      styles.optionButtonText,
                      complexity === "advanced" && styles.optionButtonTextSelected,
                    ]}
                  >
                    Avançado
                  </Text>
                </Pressable>
              </View>
            </View>
          )}

          <Button
            title={isLoading ? "Gerando..." : "Gerar Texto"}
            onPress={handleGenerate}
            variant="primary"
            disabled={isLoading || !topic.trim()}
            icon={isLoading ? undefined : Sparkles}
            style={styles.generateButton}
          />
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Gerando texto de estudo...</Text>
          </View>
        ) : generatedText ? (
          <View style={styles.resultSection}>
            <GlassCard style={styles.resultCard}>
              <View style={styles.resultHeader}>
                <Text style={styles.resultTitle}>
                  {textType === "resumo"
                    ? "Resumo"
                    : textType === "explicacao"
                    ? "Explicação"
                    : "Tópicos"}{" "}
                  sobre {topic}
                </Text>
                <View style={styles.resultActions}>
                  <Pressable
                    style={styles.actionButton}
                    onPress={handleCopyText}
                  >
                    <Copy size={20} color={colors.text} />
                  </Pressable>
                  <Pressable
                    style={styles.actionButton}
                    onPress={handleShareText}
                  >
                    <Share2 size={20} color={colors.text} />
                  </Pressable>
                  <Pressable
                    style={styles.actionButton}
                    onPress={handleSaveAsNote}
                  >
                    <Save size={20} color={colors.text} />
                  </Pressable>
                </View>
              </View>
              <ScrollView style={styles.textContainer}>
                <Text style={styles.generatedText}>{generatedText}</Text>
              </ScrollView>
            </GlassCard>

            <View style={styles.actionButtons}>
              <Button
                title="Gerar Flashcards"
                onPress={() => {
                  router.push({
                    pathname: "/flashcards-from-text",
                    params: { text: generatedText },
                  });
                }}
                variant="outline"
                icon={BookOpen}
                style={styles.actionFullButton}
              />
            </View>
          </View>
        ) : null}
          </ScrollView>
        </KeyboardAvoidingView>

        <APIKeyModal
          visible={showApiKeyModal}
          onClose={() => setShowApiKeyModal(false)}
          onSuccess={handleApiKeySuccess}
        />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  headerSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerCard: {
    padding: 20,
  },
  headerIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}20`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
  },
  headerText: {
    fontSize: 16,
    color: colors.textLight,
    lineHeight: 22,
  },
  inputSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.text,
    minHeight: 100,
    textAlignVertical: "top",
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  typeSelector: {
    flexDirection: "row",
    marginBottom: 20,
  },
  typeOption: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  typeOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
    marginLeft: 6,
  },
  typeTextSelected: {
    color: colors.white,
  },
  generateButton: {
    marginTop: 8,
  },
  loadingContainer: {
    padding: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
  },
  resultSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  resultCard: {
    padding: 16,
  },
  resultHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    flex: 1,
  },
  resultActions: {
    flexDirection: "row",
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  textContainer: {
    maxHeight: 400,
  },
  generatedText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  actionButtons: {
    marginTop: 16,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionFullButton: {
    flex: 1,
  },
  optionsButton: {
    padding: 8,
  },
  advancedOptions: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  optionLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  optionSelector: {
    flexDirection: "row",
    marginBottom: 16,
  },
  optionButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    borderRadius: 8,
    marginRight: 8,
    alignItems: "center",
  },
  optionButtonSelected: {
    backgroundColor: colors.primary,
  },
  optionButtonText: {
    fontSize: 14,
    color: colors.text,
  },
  optionButtonTextSelected: {
    color: colors.white,
    fontWeight: "500",
  },
});
