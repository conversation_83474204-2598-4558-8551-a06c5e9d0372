import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Alert,
  ActivityIndicator,
  Switch
} from 'react-native';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { Header } from '@/components/Header';
import { Button } from '@/components/Button';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassCard } from '@/components/GlassCard';
import { Calendar, Clock, BookOpen, Plus, Save, ArrowLeft, Trash2 } from 'lucide-react-native';
import { useCalendarStore } from '@/store/calendarStore';
import { useStudyStore } from '@/store/studyStore';
import { CalendarEvent, TodoItem } from '@/types';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, parseISO, isSameDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ScheduleItem {
  day: Date;
  subjects: {
    id: string;
    title: string;
    selected: boolean;
    startTime: Date;
    endTime: Date;
    createTodo: boolean;
  }[];
}

export default function ScheduleScreen() {
  const router = useRouter();
  const { addEvent, addTodo } = useCalendarStore();
  const { subjects, loading: loadingSubjects } = useStudyStore();
  const [schedule, setSchedule] = useState<ScheduleItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (subjects.length > 0) {
      initializeSchedule();
    }
  }, [subjects]);

  const initializeSchedule = () => {
    const today = new Date();
    const weekStart = startOfWeek(today, { weekStartsOn: 0 }); // Sunday
    const weekEnd = endOfWeek(today, { weekStartsOn: 0 }); // Saturday
    
    const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
    
    const newSchedule = days.map(day => {
      return {
        day,
        subjects: subjects.map(subject => {
          // Default start time: 9 AM
          const startTime = new Date(day);
          startTime.setHours(9, 0, 0, 0);
          
          // Default end time: 10 AM
          const endTime = new Date(day);
          endTime.setHours(10, 0, 0, 0);
          
          return {
            id: subject.id,
            title: subject.title,
            selected: false,
            startTime,
            endTime,
            createTodo: true
          };
        })
      };
    });
    
    setSchedule(newSchedule);
  };

  const handleSubjectToggle = (dayIndex: number, subjectIndex: number) => {
    const newSchedule = [...schedule];
    newSchedule[dayIndex].subjects[subjectIndex].selected = 
      !newSchedule[dayIndex].subjects[subjectIndex].selected;
    setSchedule(newSchedule);
  };

  const handleStartTimeChange = (dayIndex: number, subjectIndex: number, hours: number, minutes: number) => {
    const newSchedule = [...schedule];
    const startTime = new Date(newSchedule[dayIndex].subjects[subjectIndex].startTime);
    startTime.setHours(hours, minutes, 0, 0);
    
    // Ensure end time is after start time
    const endTime = new Date(newSchedule[dayIndex].subjects[subjectIndex].endTime);
    if (endTime <= startTime) {
      endTime.setTime(startTime.getTime() + 60 * 60 * 1000); // Add 1 hour
    }
    
    newSchedule[dayIndex].subjects[subjectIndex].startTime = startTime;
    newSchedule[dayIndex].subjects[subjectIndex].endTime = endTime;
    setSchedule(newSchedule);
  };

  const handleEndTimeChange = (dayIndex: number, subjectIndex: number, hours: number, minutes: number) => {
    const newSchedule = [...schedule];
    const endTime = new Date(newSchedule[dayIndex].subjects[subjectIndex].endTime);
    endTime.setHours(hours, minutes, 0, 0);
    
    const startTime = newSchedule[dayIndex].subjects[subjectIndex].startTime;
    if (endTime <= startTime) {
      Alert.alert('Erro', 'O horário de término deve ser após o horário de início.');
      return;
    }
    
    newSchedule[dayIndex].subjects[subjectIndex].endTime = endTime;
    setSchedule(newSchedule);
  };

  const handleTodoToggle = (dayIndex: number, subjectIndex: number) => {
    const newSchedule = [...schedule];
    newSchedule[dayIndex].subjects[subjectIndex].createTodo = 
      !newSchedule[dayIndex].subjects[subjectIndex].createTodo;
    setSchedule(newSchedule);
  };

  const handleSaveSchedule = async () => {
    try {
      setSaving(true);
      
      // Collect all selected subjects across all days
      const eventsToCreate: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>[] = [];
      const todosToCreate: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>[] = [];
      
      schedule.forEach(day => {
        day.subjects.forEach(subject => {
          if (subject.selected) {
            // Create calendar event
            const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
              title: `Estudar ${subject.title}`,
              description: `Sessão de estudo para ${subject.title}`,
              startDate: subject.startTime.toISOString(),
              endDate: subject.endTime.toISOString(),
              allDay: false,
              color: colors.primary,
              subject: subject.title,
              subject_id: subject.id,
              type: 'study',
              completed: false,
              reminder: true,
              reminderTime: new Date(subject.startTime.getTime() - 30 * 60000).toISOString(), // 30 minutes before
              recurrence: 'weekly' // Make it recurring weekly
            };
            
            eventsToCreate.push(event);
            
            // Create todo if selected
            if (subject.createTodo) {
              const todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'> = {
                title: `Revisar ${subject.title}`,
                description: `Revisar o conteúdo estudado em ${subject.title}`,
                dueDate: new Date(subject.endTime.getTime() + 24 * 60 * 60 * 1000).toISOString(), // Next day
                priority: 'medium',
                completed: false,
                subject: subject.title,
                subject_id: subject.id,
                tags: ['revisão', 'cronograma']
              };
              
              todosToCreate.push(todo);
            }
          }
        });
      });
      
      // Save all events and todos
      for (const event of eventsToCreate) {
        await addEvent(event);
      }
      
      for (const todo of todosToCreate) {
        await addTodo(todo);
      }
      
      Alert.alert(
        'Cronograma Salvo',
        `Foram criados ${eventsToCreate.length} eventos e ${todosToCreate.length} tarefas no seu calendário.`,
        [
          {
            text: 'Ver Calendário',
            onPress: () => router.push('/(tabs)/calendar')
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
      
      setSaving(false);
    } catch (error) {
      console.error('Error saving schedule:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao salvar o cronograma.');
      setSaving(false);
    }
  };

  const handleClearSchedule = () => {
    Alert.alert(
      'Limpar Cronograma',
      'Tem certeza que deseja limpar todo o cronograma? Esta ação não pode ser desfeita.',
      [
        {
          text: 'Cancelar',
          style: 'cancel'
        },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: initializeSchedule
        }
      ]
    );
  };

  const renderTimeSelector = (
    dayIndex: number,
    subjectIndex: number,
    isStartTime: boolean
  ) => {
    const subject = schedule[dayIndex].subjects[subjectIndex];
    const time = isStartTime ? subject.startTime : subject.endTime;
    const hours = time.getHours();
    const minutes = time.getMinutes();
    
    return (
      <View style={styles.timeSelector}>
        <Text style={styles.timeLabel}>
          {isStartTime ? 'Início:' : 'Fim:'}
        </Text>
        <View style={styles.timePickerContainer}>
          <Pressable
            style={styles.timePickerButton}
            onPress={() => {
              const options = [];
              for (let i = 0; i < 24; i++) {
                options.push({
                  text: i < 10 ? `0${i}:00` : `${i}:00`,
                  onPress: () => {
                    if (isStartTime) {
                      handleStartTimeChange(dayIndex, subjectIndex, i, minutes);
                    } else {
                      handleEndTimeChange(dayIndex, subjectIndex, i, minutes);
                    }
                  }
                });
              }
              
              Alert.alert(
                isStartTime ? 'Selecionar Hora de Início' : 'Selecionar Hora de Término',
                '',
                [...options, { text: 'Cancelar', style: 'cancel' }]
              );
            }}
          >
            <Text style={styles.timeText}>
              {hours < 10 ? `0${hours}` : hours}
            </Text>
          </Pressable>
          <Text style={styles.timeSeparator}>:</Text>
          <Pressable
            style={styles.timePickerButton}
            onPress={() => {
              const options = [
                {
                  text: '00',
                  onPress: () => {
                    if (isStartTime) {
                      handleStartTimeChange(dayIndex, subjectIndex, hours, 0);
                    } else {
                      handleEndTimeChange(dayIndex, subjectIndex, hours, 0);
                    }
                  }
                },
                {
                  text: '15',
                  onPress: () => {
                    if (isStartTime) {
                      handleStartTimeChange(dayIndex, subjectIndex, hours, 15);
                    } else {
                      handleEndTimeChange(dayIndex, subjectIndex, hours, 15);
                    }
                  }
                },
                {
                  text: '30',
                  onPress: () => {
                    if (isStartTime) {
                      handleStartTimeChange(dayIndex, subjectIndex, hours, 30);
                    } else {
                      handleEndTimeChange(dayIndex, subjectIndex, hours, 30);
                    }
                  }
                },
                {
                  text: '45',
                  onPress: () => {
                    if (isStartTime) {
                      handleStartTimeChange(dayIndex, subjectIndex, hours, 45);
                    } else {
                      handleEndTimeChange(dayIndex, subjectIndex, hours, 45);
                    }
                  }
                },
                {
                  text: 'Cancelar',
                  style: 'cancel'
                }
              ];
              
              Alert.alert(
                isStartTime ? 'Selecionar Minutos de Início' : 'Selecionar Minutos de Término',
                '',
                options
              );
            }}
          >
            <Text style={styles.timeText}>
              {minutes < 10 ? `0${minutes}` : minutes}
            </Text>
          </Pressable>
        </View>
      </View>
    );
  };

  if (loadingSubjects || loading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Cronograma Semanal"
        leftComponent={
          <Pressable style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </Pressable>
        }
        rightComponent={
          <View style={styles.headerButtons}>
            <Pressable style={styles.headerButton} onPress={handleClearSchedule}>
              <Trash2 size={24} color={colors.error} />
            </Pressable>
            <Pressable style={styles.headerButton} onPress={handleSaveSchedule}>
              <Save size={24} color={colors.primary} />
            </Pressable>
          </View>
        }
      />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.description}>
          Selecione as matérias que você deseja estudar em cada dia da semana e defina os horários.
          Ao salvar, serão criados eventos no calendário e tarefas de revisão.
        </Text>
        
        {schedule.map((day, dayIndex) => (
          <GlassCard key={dayIndex} style={styles.dayCard}>
            <Text style={styles.dayTitle}>
              {format(day.day, "EEEE, dd 'de' MMMM", { locale: ptBR })}
            </Text>
            
            {day.subjects.map((subject, subjectIndex) => (
              <View key={subjectIndex} style={styles.subjectItem}>
                <View style={styles.subjectHeader}>
                  <Pressable
                    style={[
                      styles.subjectCheckbox,
                      subject.selected && styles.subjectCheckboxSelected
                    ]}
                    onPress={() => handleSubjectToggle(dayIndex, subjectIndex)}
                  >
                    {subject.selected && (
                      <View style={styles.subjectCheckboxInner} />
                    )}
                  </Pressable>
                  <Text style={styles.subjectTitle}>{subject.title}</Text>
                </View>
                
                {subject.selected && (
                  <View style={styles.subjectDetails}>
                    <View style={styles.timeSelectors}>
                      {renderTimeSelector(dayIndex, subjectIndex, true)}
                      {renderTimeSelector(dayIndex, subjectIndex, false)}
                    </View>
                    
                    <View style={styles.todoOption}>
                      <Text style={styles.todoOptionText}>Criar tarefa de revisão</Text>
                      <Switch
                        value={subject.createTodo}
                        onValueChange={() => handleTodoToggle(dayIndex, subjectIndex)}
                        trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                        thumbColor={subject.createTodo ? colors.primary : colors.white}
                      />
                    </View>
                  </View>
                )}
              </View>
            ))}
            
            {day.subjects.every(subject => !subject.selected) && (
              <Text style={styles.emptyDayText}>
                Nenhuma matéria selecionada para este dia.
              </Text>
            )}
          </GlassCard>
        ))}
        
        <View style={styles.buttonContainer}>
          <Button
            title="Cancelar"
            onPress={() => router.back()}
            variant="secondary"
            size="large"
            style={styles.button}
          />
          <Button
            title={saving ? "Salvando..." : "Salvar Cronograma"}
            onPress={handleSaveSchedule}
            variant="primary"
            size="large"
            style={styles.button}
            disabled={saving || schedule.every(day => day.subjects.every(subject => !subject.selected))}
            icon={Save}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  description: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
    lineHeight: 24,
  },
  dayCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 16,
  },
  dayTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 16,
    textTransform: 'capitalize',
  },
  subjectItem: {
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingBottom: 12,
  },
  subjectHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.primary,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subjectCheckboxSelected: {
    backgroundColor: colors.white,
  },
  subjectCheckboxInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
  },
  subjectTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  subjectDetails: {
    marginTop: 12,
    marginLeft: 36,
  },
  timeSelectors: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  timeSelector: {
    flex: 1,
    marginRight: 8,
  },
  timeLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 4,
  },
  timePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timePickerButton: {
    backgroundColor: colors.card,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: colors.border,
    minWidth: 50,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    color: colors.text,
    fontWeight: '600',
  },
  timeSeparator: {
    fontSize: 16,
    color: colors.text,
    fontWeight: '600',
    marginHorizontal: 4,
  },
  todoOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  todoOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  emptyDayText: {
    fontSize: 14,
    color: colors.textLight,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});
