import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Dimensions,
  Pressable,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useQuizStore } from "@/store/quizStore";
import { useUserStore } from "@/store/userStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import { ProgressBar } from "@/components/ProgressBar";
import {
  BarChart,
  TrendingUp,
  Clock,
  Calendar,
  Award,
  BookOpen,
  Brain,
  CheckCircle,
  XCircle,
  ArrowRight,
} from "lucide-react-native";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

const { width } = Dimensions.get("window");

export default function DashboardScreen() {
  const router = useRouter();
  const { quizzes, getRecentQuizAttempts } = useQuizStore();
  const { user } = useUserStore();
  
  const recentAttempts = getRecentQuizAttempts(5);
  
  // Calculate overall stats
  const totalQuizzes = quizzes.length;
  const completedQuizzes = quizzes.filter(q => q.lastAttempt).length;
  const totalAttempts = recentAttempts.length;
  
  // Calculate average score
  const averageScore = totalAttempts > 0
    ? recentAttempts.reduce((sum, a) => sum + (a.score / a.totalQuestions) * 100, 0) / totalAttempts
    : 0;
  
  // Calculate best quiz
  const bestQuiz = quizzes.length > 0
    ? [...quizzes].sort((a, b) => (b.bestScore || 0) - (a.bestScore || 0))[0]
    : null;
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: "Dashboard" }} />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.welcomeCard} gradient>
          <Text style={styles.welcomeTitle}>
            Olá, {user?.name || "Estudante"}!
          </Text>
          <Text style={styles.welcomeSubtitle}>
            Aqui está o resumo do seu desempenho
          </Text>
        </GlassCard>
        
        <View style={styles.statsOverviewSection}>
          <Text style={styles.sectionTitle}>Visão Geral</Text>
          
          <View style={styles.statsCardsContainer}>
            <GlassCard style={styles.statCard} gradient>
              <View style={styles.statIconContainer}>
                <BookOpen size={24} color={colors.primary} />
              </View>
              <Text style={styles.statValue}>{completedQuizzes}/{totalQuizzes}</Text>
              <Text style={styles.statLabel}>Quizzes Completados</Text>
            </GlassCard>
            
            <GlassCard style={styles.statCard} gradient>
              <View style={styles.statIconContainer}>
                <Award size={24} color={colors.secondary} />
              </View>
              <Text style={styles.statValue}>{Math.round(averageScore)}%</Text>
              <Text style={styles.statLabel}>Pontuação Média</Text>
            </GlassCard>
            
            <GlassCard style={styles.statCard} gradient>
              <View style={styles.statIconContainer}>
                <Brain size={24} color={colors.accent1} />
              </View>
              <Text style={styles.statValue}>{totalAttempts}</Text>
              <Text style={styles.statLabel}>Total de Tentativas</Text>
            </GlassCard>
          </View>
        </View>
        
        {bestQuiz && bestQuiz.bestScore && (
          <View style={styles.bestQuizSection}>
            <Text style={styles.sectionTitle}>Seu Melhor Quiz</Text>
            
            <GlassCard style={styles.bestQuizCard} gradient>
              <View style={styles.bestQuizHeader}>
                <Text style={styles.bestQuizTitle}>{bestQuiz.title}</Text>
                <View style={styles.bestQuizScoreContainer}>
                  <Text style={styles.bestQuizScoreValue}>
                    {Math.round(bestQuiz.bestScore)}%
                  </Text>
                </View>
              </View>
              
              <Text style={styles.bestQuizSubject}>{bestQuiz.subject}</Text>
              
              <View style={styles.bestQuizProgressContainer}>
                <ProgressBar
                  progress={bestQuiz.bestScore}
                  gradientColors={colors.primaryGradient}
                  height={12}
                  backgroundColor={`${colors.primary}20`}
                  borderRadius={6}
                />
              </View>
              
              <Pressable
                style={styles.bestQuizViewButton}
                onPress={() => router.push(`/quiz-stats/${bestQuiz.id}`)}
              >
                <Text style={styles.bestQuizViewButtonText}>Ver Estatísticas</Text>
                <ArrowRight size={16} color={colors.primary} />
              </Pressable>
            </GlassCard>
          </View>
        )}
        
        {recentAttempts.length > 0 && (
          <View style={styles.recentAttemptsSection}>
            <Text style={styles.sectionTitle}>Atividades Recentes</Text>
            
            {recentAttempts.map((attempt) => {
              const quiz = quizzes.find(q => q.id === attempt.quizId);
              if (!quiz) return null;
              
              return (
                <GlassCard key={attempt.id} style={styles.attemptCard} gradient>
                  <View style={styles.attemptHeader}>
                    <Text style={styles.attemptQuizTitle}>{quiz.title}</Text>
                    <Text style={styles.attemptDate}>
                      {formatDistanceToNow(new Date(attempt.date), { 
                        addSuffix: true,
                        locale: ptBR
                      })}
                    </Text>
                  </View>
                  
                  <View style={styles.attemptProgressContainer}>
                    <ProgressBar
                      progress={(attempt.score / attempt.totalQuestions) * 100}
                      gradientColors={colors.primaryGradient}
                      height={8}
                      backgroundColor={`${colors.primary}20`}
                      borderRadius={4}
                    />
                    <Text style={styles.attemptScoreText}>
                      {Math.round((attempt.score / attempt.totalQuestions) * 100)}%
                    </Text>
                  </View>
                  
                  <View style={styles.attemptStatsContainer}>
                    <View style={styles.attemptStatItem}>
                      <CheckCircle size={16} color={colors.success} />
                      <Text style={styles.attemptStatText}>
                        {attempt.score} acertos
                      </Text>
                    </View>
                    
                    <View style={styles.attemptStatItem}>
                      <XCircle size={16} color={colors.error} />
                      <Text style={styles.attemptStatText}>
                        {attempt.totalQuestions - attempt.score} erros
                      </Text>
                    </View>
                    
                    <View style={styles.attemptStatItem}>
                      <Clock size={16} color={colors.textLight} />
                      <Text style={styles.attemptStatText}>
                        {Math.floor(attempt.timeSpent / 60)}m {attempt.timeSpent % 60}s
                      </Text>
                    </View>
                  </View>
                  
                  <Pressable
                    style={styles.attemptViewButton}
                    onPress={() => router.push(`/quiz-stats/${quiz.id}`)}
                  >
                    <Text style={styles.attemptViewButtonText}>Ver Detalhes</Text>
                    <ArrowRight size={16} color={colors.primary} />
                  </Pressable>
                </GlassCard>
              );
            })}
          </View>
        )}
        
        <Button
          title="Ver Todos os Quizzes"
          onPress={() => router.push("/quizzes")}
          variant="primary"
          size="large"
          fullWidth
          style={styles.viewAllButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  welcomeCard: {
    padding: 20,
    marginBottom: 24,
    alignItems: "center",
  },
  welcomeTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  statsOverviewSection: {
    marginBottom: 24,
  },
  statsCardsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
  },
  statCard: {
    width: (width - 48) / 3,
    padding: 12,
    alignItems: "center",
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  bestQuizSection: {
    marginBottom: 24,
  },
  bestQuizCard: {
    padding: 16,
  },
  bestQuizHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  bestQuizTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    flex: 1,
  },
  bestQuizScoreContainer: {
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  bestQuizScoreValue: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
  },
  bestQuizSubject: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 12,
  },
  bestQuizProgressContainer: {
    marginBottom: 16,
  },
  bestQuizViewButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
  },
  bestQuizViewButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
    marginRight: 4,
  },
  recentAttemptsSection: {
    marginBottom: 24,
  },
  attemptCard: {
    padding: 16,
    marginBottom: 12,
  },
  attemptHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  attemptQuizTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    flex: 1,
  },
  attemptDate: {
    fontSize: 12,
    color: colors.textLight,
  },
  attemptProgressContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  attemptScoreText: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
    marginLeft: 8,
    width: 40,
    textAlign: "right",
  },
  attemptStatsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  attemptStatItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  attemptStatText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 4,
  },
  attemptViewButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
  },
  attemptViewButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
    marginRight: 4,
  },
  viewAllButton: {
    marginTop: 8,
  },
});
