import React from "react";
import { View, Text, StyleSheet, ScrollView, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { GlassCard } from "@/components/GlassCard";
// import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { Network, Construction } from "lucide-react-native";

export default function MindMapsScreen() {
  const router = useRouter();
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header title="Mapas Mentais" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerSection}>
          <GlassCard style={styles.headerCard} gradient>
            <View style={styles.headerCardContent}>
              <View style={styles.headerIconContainer}>
                <Network size={28} color="#fff" />
              </View>
              <View style={styles.headerTextContainer}>
                <Text style={styles.headerTitle}>Mapas Mentais</Text>
                <Text style={styles.headerText}>
                  Visualize conceitos e suas conexões para melhor compreensão
                </Text>
              </View>
            </View>
          </GlassCard>
        </View>

        <View style={styles.comingSoonContainer}>
          <View style={styles.comingSoonIcon}>
            <Construction size={60} color={colors.textLight} />
          </View>
          <Text style={styles.comingSoonTitle}>Em Desenvolvimento</Text>
          <Text style={styles.comingSoonText}>
            A funcionalidade de Mapas Mentais está sendo desenvolvida e estará disponível em breve.
          </Text>
          <Pressable
            style={styles.backButtonContainer}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Voltar para o início</Text>
          </Pressable>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Extra padding for floating tab bar
  },
  headerSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  headerCard: {
    padding: 16,
  },
  headerCardContent: {
    flexDirection: "row",
    marginBottom: 16,
  },
  headerIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: colors.accent1,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  headerText: {
    fontSize: 14,
    color: colors.textLight,
    lineHeight: 20,
  },
  comingSoonContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    marginHorizontal: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comingSoonIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
  },
  comingSoonTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  comingSoonText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 24,
  },
  backButtonContainer: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 200,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
});
