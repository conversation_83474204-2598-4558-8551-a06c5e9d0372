import React from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { ProgressBar } from "@/components/ProgressBar";
import { Button } from "@/components/Button";
import { Calendar, Clock, BookOpen } from "lucide-react-native";
import { RouteGuard } from '@/components/RouteGuard';

export default function StudyPlanScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { studyPlans } = useStudyStore();

  const plan = studyPlans.find((p) => p.id === id);

  const handleStartSession = () => {
    // In a real app, this would start a study session
    alert("Esta funcionalidade estará disponível em breve!");
  };

  const handleEditPlan = () => {
    // In a real app, this would navigate to an edit screen
    alert("Esta funcionalidade estará disponível em breve!");
  };

  if (!plan) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Plano não encontrado" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Plano de estudos não encontrado</Text>
          <Button
            title="Voltar para planos"
            onPress={() => router.push("/study-plans")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <RouteGuard resourceId={id} tableName="study_plans">
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: plan.title }} />
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
        <View style={styles.headerSection}>
          <Text style={styles.title}>{plan.title}</Text>
          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Calendar size={16} color={colors.textLight} />
              <Text style={styles.infoText}>{plan.duration}</Text>
            </View>
            <View style={styles.infoItem}>
              <Clock size={16} color={colors.textLight} />
              <Text style={styles.infoText}>
                {plan.sessions.reduce(
                  (total, session) =>
                    total +
                    session.activities.reduce(
                      (sessionTotal, activity) => sessionTotal + activity.duration,
                      0
                    ),
                  0
                )}{" "}
                min/semana
              </Text>
            </View>
          </View>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              Progresso: {plan.progress}%
            </Text>
            <ProgressBar
              progress={plan.progress}
              color={colors.primary}
              height={8}
            />
          </View>
        </View>

        <View style={styles.subjectsSection}>
          <Text style={styles.sectionTitle}>Matérias</Text>
          <View style={styles.subjectsContainer}>
            {plan.subjects.map((subject, index) => (
              <View key={index} style={styles.subjectTag}>
                <Text style={styles.subjectText}>{subject}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.scheduleSection}>
          <Text style={styles.sectionTitle}>Cronograma Semanal</Text>
          {plan.sessions.map((session, index) => (
            <View key={index} style={styles.sessionCard}>
              <Text style={styles.sessionDay}>{session.day}</Text>
              {session.activities.map((activity, actIndex) => (
                <View key={actIndex} style={styles.activityItem}>
                  <View style={styles.activityIconContainer}>
                    <BookOpen size={16} color={colors.primary} />
                  </View>
                  <Text style={styles.activitySubject}>
                    {activity.subject}
                  </Text>
                  <Text style={styles.activityDuration}>
                    {activity.duration} min
                  </Text>
                </View>
              ))}
              <View style={styles.sessionTotal}>
                <Text style={styles.sessionTotalText}>
                  Total:{" "}
                  {session.activities.reduce(
                    (total, activity) => total + activity.duration,
                    0
                  )}{" "}
                  min
                </Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.buttonsContainer}>
          <Button
            title="Iniciar Sessão de Estudo"
            onPress={handleStartSession}
            variant="primary"
            size="large"
            fullWidth
          />
          <Button
            title="Editar Plano"
            onPress={handleEditPlan}
            variant="outline"
            size="medium"
            fullWidth
          />
        </View>
      </ScrollView>
    </SafeAreaView>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  headerSection: {
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  infoContainer: {
    flexDirection: "row",
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  infoText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 4,
  },
  progressContainer: {
    width: "100%",
  },
  progressText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
  subjectsSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  subjectsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  subjectTag: {
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  subjectText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  scheduleSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sessionCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sessionDay: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  activityItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  activityIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  activitySubject: {
    flex: 1,
    fontSize: 14,
    color: colors.text,
  },
  activityDuration: {
    fontSize: 14,
    color: colors.textLight,
  },
  sessionTotal: {
    marginTop: 12,
    alignItems: "flex-end",
  },
  sessionTotalText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textLight,
  },
  buttonsContainer: {
    paddingHorizontal: 16,
    gap: 12,
  },
});