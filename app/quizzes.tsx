import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { QuizCard } from "@/components/QuizCard";
import { useQuizStore } from "@/store/quizStore";
import { useStudyStore } from "@/store/studyStore";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import { Search, Plus, FileQuestion, Tag, Clock, BookOpen, Sparkles } from "lucide-react-native";
import { Quiz } from "@/types";

export default function QuizzesScreen() {
  const router = useRouter();
  const { quizzes, fetchQuizzes, loading, deleteQuiz } = useQuizStore();
  const { subjects } = useStudyStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null);

  useEffect(() => {
    fetchQuizzes();
  }, []);

  // Get all unique subjects from quizzes
  const uniqueSubjects = Array.from(
    new Set(quizzes.map((quiz) => quiz.subject))
  ).filter(Boolean);

  const handleQuizPress = (quiz: Quiz) => {
    router.push(`/quiz/${quiz.id}`);
  };

  const handleEditQuiz = (quiz: Quiz) => {
    router.push(`/quiz/edit/${quiz.id}`);
  };

  const handleDeleteQuiz = async (quiz: Quiz) => {
    try {
      await deleteQuiz(quiz.id);
      Alert.alert("Sucesso", "Quiz excluído com sucesso!");
      fetchQuizzes();
    } catch (error) {
      Alert.alert("Erro", "Não foi possível excluir o quiz. Tente novamente.");
    }
  };

  const handleCreateQuiz = () => {
    router.push("/quiz/create");
  };

  const handleGenerateQuiz = () => {
    // Mostrar modal para selecionar a matéria
    Alert.alert(
      "Gerar Quiz com IA",
      "Escolha a matéria para gerar um quiz com IA",
      [
        ...subjects.map(subject => ({
          text: subject.title,
          onPress: () => {
            Alert.alert(
              "Gerar Quiz",
              `Deseja gerar um quiz sobre ${subject.title} usando inteligência artificial?`,
              [
                { text: "Cancelar", style: "cancel" },
                {
                  text: "Gerar",
                  onPress: () => {
                    // Aqui seria implementada a chamada para a API da OpenAI
                    Alert.alert("Funcionalidade em desenvolvimento", "A geração de quiz com IA estará disponível em breve!");
                  }
                }
              ]
            );
          }
        })),
        {
          text: "Cancelar",
          style: "cancel"
        }
      ]
    );
  };

  const handleSubjectPress = (subject: string) => {
    setSelectedSubject(selectedSubject === subject ? null : subject);
  };

  // Filter quizzes based on search query and selected subject
  const filteredQuizzes = quizzes.filter((quiz) => {
    const matchesSearch =
      quiz.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      quiz.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesSubject = selectedSubject ? quiz.subject === selectedSubject : true;

    return matchesSearch && matchesSubject;
  });

  return (
    <View style={styles.container}>
      <Header title="Quizzes" />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.searchCard}>
          <View style={styles.searchContainer}>
            <Search size={20} color={colors.textLight} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar quizzes..."
              placeholderTextColor={colors.textLight}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </GlassCard>

        <View style={styles.actionsContainer}>
          <Button
            title="Criar Quiz"
            onPress={handleCreateQuiz}
            variant="primary"
            size="medium"
            icon={Plus}
            style={styles.actionButton}
          />
          <Button
            title="Gerar com IA"
            onPress={handleGenerateQuiz}
            variant="outline"
            size="medium"
            icon={Sparkles}
            style={styles.actionButton}
          />
        </View>

        {uniqueSubjects.length > 0 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.subjectsContainer}
          >
            {uniqueSubjects.map((subject) => (
              <Pressable
                key={subject}
                style={[
                  styles.subjectTag,
                  selectedSubject === subject && styles.selectedSubjectTag,
                ]}
                onPress={() => handleSubjectPress(subject)}
              >
                <BookOpen
                  size={16}
                  color={
                    selectedSubject === subject ? "#fff" : colors.primary
                  }
                />
                <Text
                  style={[
                    styles.subjectTagText,
                    selectedSubject === subject && styles.selectedSubjectTagText,
                  ]}
                >
                  {subject}
                </Text>
              </Pressable>
            ))}
          </ScrollView>
        )}

        <View style={styles.quizzesContainer}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Carregando quizzes...</Text>
            </View>
          ) : (
            <>
              <Text style={styles.sectionTitle}>
                {filteredQuizzes.length > 0
                  ? `${filteredQuizzes.length} ${
                      filteredQuizzes.length === 1 ? "Quiz" : "Quizzes"
                    }`
                  : "Nenhum quiz encontrado"}
              </Text>

              {filteredQuizzes.map((quiz) => (
                <QuizCard
                  key={quiz.id}
                  quiz={quiz}
                  onPress={handleQuizPress}
                  onEdit={handleEditQuiz}
                  onDelete={handleDeleteQuiz}
                />
              ))}

              {filteredQuizzes.length === 0 && (
                <View style={styles.emptyContainer}>
                  <FileQuestion size={48} color={`${colors.primary}50`} />
                  <Text style={styles.emptyText}>
                    Nenhum quiz encontrado. Crie um novo quiz ou gere um com IA.
                  </Text>
                  <Button
                    title="Criar Primeiro Quiz"
                    onPress={handleCreateQuiz}
                    variant="primary"
                    size="medium"
                    icon={Plus}
                    style={styles.emptyButton}
                  />
                </View>
              )}
            </>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Extra padding for tab bar
  },
  searchCard: {
    marginBottom: 16,
    padding: 4,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.card,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
    paddingVertical: 8,
  },
  actionsContainer: {
    flexDirection: "row",
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  subjectsContainer: {
    paddingBottom: 16,
  },
  subjectTag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  selectedSubjectTag: {
    backgroundColor: colors.primary,
  },
  subjectTagText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 4,
  },
  selectedSubjectTagText: {
    color: "#fff",
  },
  quizzesContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 32,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: "dashed",
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    marginVertical: 16,
    lineHeight: 22,
  },
  emptyButton: {
    marginTop: 8,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
  },
});
