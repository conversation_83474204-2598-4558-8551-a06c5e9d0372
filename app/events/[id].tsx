import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  ScrollView,
  Alert,
  ActivityIndicator,
  SafeAreaView
} from 'react-native';
import { colors } from '@/constants/colors';
import { CalendarEvent, TodoItem } from '@/types';
import { Calendar, Clock, Bell, BookOpen, FileText, GraduationCap, Users, Tag, Edit, Trash2, CheckCircle, Plus } from 'lucide-react-native';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useCalendarStore } from '@/store/calendarStore';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { GlassCard } from '@/components/GlassCard';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Button } from '@/components/Button';

export default function EventDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { events, todos, fetchEvents, fetchTodos, deleteEvent } = useCalendarStore();
  
  const [event, setEvent] = useState<CalendarEvent | null>(null);
  const [relatedTodos, setRelatedTodos] = useState<TodoItem[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Fetch event details
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchEvents();
      await fetchTodos();
      setLoading(false);
    };
    
    loadData();
  }, []);
  
  // Find event and related todos
  useEffect(() => {
    if (events.length > 0 && id) {
      const foundEvent = events.find(e => e.id === id);
      if (foundEvent) {
        setEvent(foundEvent);
        
        // Find related todos
        const eventTodos = todos.filter(todo => todo.event_id === id);
        setRelatedTodos(eventTodos);
      } else {
        Alert.alert('Erro', 'Evento não encontrado');
        router.back();
      }
    }
  }, [events, todos, id]);
  
  const handleEditEvent = () => {
    if (event) {
      router.push(`/events/edit/${event.id}`);
    }
  };
  
  const handleDeleteEvent = () => {
    if (event) {
      Alert.alert(
        'Excluir Evento',
        'Tem certeza que deseja excluir este evento? Esta ação não pode ser desfeita.',
        [
          {
            text: 'Cancelar',
            style: 'cancel'
          },
          {
            text: 'Excluir',
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteEvent(event.id);
                Alert.alert('Sucesso', 'Evento excluído com sucesso');
                router.back();
              } catch (error) {
                console.error('Erro ao excluir evento:', error);
                Alert.alert('Erro', 'Ocorreu um erro ao excluir o evento');
              }
            }
          }
        ]
      );
    }
  };
  
  const handleAddTask = () => {
    if (event) {
      router.push({
        pathname: '/tasks/new',
        params: { event_id: event.id, event_title: event.title }
      });
    }
  };
  
  const handleToggleCompleted = () => {
    if (event) {
      const updatedEvent = {
        ...event,
        completed: !event.completed
      };
      
      useCalendarStore.getState().updateEvent(event.id, updatedEvent).then(() => {
        fetchEvents();
      });
    }
  };
  
  const getEventTypeIcon = () => {
    switch (event?.type) {
      case 'study':
        return <BookOpen size={20} color="#4F46E5" />;
      case 'exam':
        return <FileText size={20} color="#EF4444" />;
      case 'assignment':
        return <GraduationCap size={20} color="#10B981" />;
      case 'meeting':
        return <Users size={20} color="#F59E0B" />;
      default:
        return <Tag size={20} color="#8B5CF6" />;
    }
  };
  
  const getEventTypeName = () => {
    switch (event?.type) {
      case 'study':
        return 'Estudo';
      case 'exam':
        return 'Prova';
      case 'assignment':
        return 'Tarefa';
      case 'meeting':
        return 'Reunião';
      default:
        return 'Outro';
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="dark" />
        <Stack.Screen options={{ title: "Detalhes do Evento" }} />
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }
  
  if (!event) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="dark" />
        <Stack.Screen options={{ title: "Detalhes do Evento" }} />
        <Text style={styles.errorText}>Evento não encontrado</Text>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <Stack.Screen 
        options={{ 
          title: "Detalhes do Evento",
          headerRight: () => (
            <View style={styles.headerButtons}>
              <Pressable 
                style={styles.headerButton} 
                onPress={handleEditEvent}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Edit size={24} color={colors.primary} />
              </Pressable>
              <Pressable 
                style={styles.headerButton} 
                onPress={handleDeleteEvent}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Trash2 size={24} color={colors.error} />
              </Pressable>
            </View>
          )
        }} 
      />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      
      <ScrollView style={styles.scrollView}>
        <GlassCard style={[styles.eventCard, { borderLeftColor: event.color, borderLeftWidth: 5 }]}>
          <View style={styles.eventHeader}>
            <View style={styles.titleContainer}>
              <Text style={styles.eventTitle}>{event.title}</Text>
              <View style={styles.typeContainer}>
                {getEventTypeIcon()}
                <Text style={styles.typeText}>{getEventTypeName()}</Text>
              </View>
            </View>
            <Pressable
              style={styles.completedButton}
              onPress={handleToggleCompleted}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              {event.completed ? (
                <CheckCircle size={28} color={colors.success} />
              ) : (
                <CheckCircle size={28} color={colors.border} />
              )}
            </Pressable>
          </View>
          
          {event.description ? (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Descrição</Text>
              <Text style={styles.description}>{event.description}</Text>
            </View>
          ) : null}
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Data e Hora</Text>
            <View style={styles.dateTimeContainer}>
              <View style={styles.dateTimeItem}>
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.dateTimeText}>
                  {format(parseISO(event.startDate), 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </View>
              
              {!event.allDay && (
                <View style={styles.dateTimeItem}>
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.dateTimeText}>
                    {format(parseISO(event.startDate), 'HH:mm', { locale: ptBR })} - 
                    {format(parseISO(event.endDate), 'HH:mm', { locale: ptBR })}
                  </Text>
                </View>
              )}
              
              {event.allDay && (
                <View style={styles.dateTimeItem}>
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.dateTimeText}>Dia inteiro</Text>
                </View>
              )}
            </View>
          </View>
          
          {event.reminder && event.reminderTime && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Lembrete</Text>
              <View style={styles.dateTimeItem}>
                <Bell size={20} color={colors.primary} />
                <Text style={styles.dateTimeText}>
                  {format(parseISO(event.reminderTime), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                </Text>
              </View>
            </View>
          )}
          
          {event.subject && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Matéria</Text>
              <Text style={styles.subjectText}>{event.subject}</Text>
            </View>
          )}
        </GlassCard>
        
        <View style={styles.tasksSection}>
          <View style={styles.tasksSectionHeader}>
            <Text style={styles.tasksSectionTitle}>Tarefas Relacionadas</Text>
            <Button
              title="Nova Tarefa"
              onPress={handleAddTask}
              variant="outline"
              size="small"
              icon={Plus}
            />
          </View>
          
          {relatedTodos.length === 0 ? (
            <GlassCard style={styles.emptyTasksCard}>
              <Text style={styles.emptyTasksText}>
                Nenhuma tarefa relacionada a este evento.
              </Text>
              <Text style={styles.emptyTasksSubtext}>
                Adicione tarefas para acompanhar seu progresso.
              </Text>
            </GlassCard>
          ) : (
            relatedTodos.map(todo => (
              <GlassCard 
                key={todo.id} 
                style={[
                  styles.todoCard,
                  todo.completed && styles.completedTodoCard
                ]}
              >
                <View style={styles.todoHeader}>
                  <Text style={[
                    styles.todoTitle,
                    todo.completed && styles.completedTodoTitle
                  ]}>
                    {todo.title}
                  </Text>
                  <Pressable
                    onPress={() => {
                      useCalendarStore.getState().toggleTodoCompleted(todo.id);
                      fetchTodos();
                    }}
                  >
                    {todo.completed ? (
                      <CheckCircle size={24} color={colors.success} />
                    ) : (
                      <CheckCircle size={24} color={colors.border} />
                    )}
                  </Pressable>
                </View>
                
                {todo.description ? (
                  <Text style={[
                    styles.todoDescription,
                    todo.completed && styles.completedTodoText
                  ]}>
                    {todo.description}
                  </Text>
                ) : null}
                
                {todo.dueDate && (
                  <View style={styles.todoDueDate}>
                    <Calendar size={16} color={todo.completed ? colors.textLight : colors.primary} />
                    <Text style={[
                      styles.todoDueDateText,
                      todo.completed && styles.completedTodoText
                    ]}>
                      {format(parseISO(todo.dueDate), 'dd/MM/yyyy', { locale: ptBR })}
                    </Text>
                  </View>
                )}
                
                <View style={styles.todoPriority}>
                  <View style={[
                    styles.priorityDot,
                    { backgroundColor: getPriorityColor(todo.priority) }
                  ]} />
                  <Text style={[
                    styles.priorityText,
                    todo.completed && styles.completedTodoText
                  ]}>
                    {getPriorityLabel(todo.priority)}
                  </Text>
                </View>
              </GlassCard>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

function getPriorityColor(priority: string) {
  switch (priority) {
    case 'high':
      return '#EF4444';
    case 'medium':
      return '#F59E0B';
    case 'low':
      return '#10B981';
    default:
      return '#8B5CF6';
  }
}

function getPriorityLabel(priority: string) {
  switch (priority) {
    case 'high':
      return 'Alta';
    case 'medium':
      return 'Média';
    case 'low':
      return 'Baixa';
    default:
      return 'Média';
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 16,
  },
  eventCard: {
    padding: 16,
    marginBottom: 16,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeText: {
    fontSize: 16,
    color: colors.textLight,
    marginLeft: 8,
  },
  completedButton: {
    marginLeft: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  dateTimeContainer: {
    marginTop: 8,
  },
  dateTimeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  dateTimeText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  subjectText: {
    fontSize: 16,
    color: colors.text,
  },
  tasksSection: {
    marginBottom: 24,
  },
  tasksSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tasksSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  emptyTasksCard: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTasksText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyTasksSubtext: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
  },
  todoCard: {
    padding: 16,
    marginBottom: 12,
  },
  completedTodoCard: {
    opacity: 0.7,
  },
  todoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  todoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
    marginRight: 8,
  },
  completedTodoTitle: {
    textDecorationLine: 'line-through',
    color: colors.textLight,
  },
  todoDescription: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 8,
  },
  completedTodoText: {
    color: colors.textLight,
  },
  todoDueDate: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  todoDueDateText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
  },
  todoPriority: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 14,
    color: colors.textLight,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
  },
});
