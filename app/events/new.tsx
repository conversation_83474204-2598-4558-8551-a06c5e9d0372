import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  ScrollView,
  Alert,
  Platform,
  Switch,
  TouchableOpacity,
  SafeAreaView
} from 'react-native';
import { colors } from '@/constants/colors';
import { CalendarEvent } from '@/types';
import { Calendar, Clock, Bell, BookOpen, FileText, GraduationCap, Users, Tag, ArrowLeft } from 'lucide-react-native';
import { RecurrenceSelector, RecurrenceSettings } from '@/components/RecurrenceSelector';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, addHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useCalendarStore } from '@/store/calendarStore';
import { useStudyStore } from '@/store/studyStore';
import { useRouter, Stack } from 'expo-router';
import { GlassCard } from '@/components/GlassCard';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';

export default function NewEventScreen() {
  const router = useRouter();
  const { addEvent } = useCalendarStore();
  const { subjects } = useStudyStore();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setHours(10, 0, 0, 0);
    return date;
  });
  const [endDate, setEndDate] = useState(() => {
    const date = new Date();
    date.setHours(11, 0, 0, 0);
    return date;
  });
  const [allDay, setAllDay] = useState(false);
  const [color, setColor] = useState(colors.primary);
  const [reminder, setReminder] = useState(false);
  const [reminderTime, setReminderTime] = useState(() => {
    const date = new Date();
    date.setHours(9, 30, 0, 0);
    return date;
  });
  const [type, setType] = useState<'study' | 'exam' | 'assignment' | 'meeting' | 'other'>('study');
  const [subject, setSubject] = useState<string | null>(null);
  const [subjectId, setSubjectId] = useState<string | null>(null);
  const [recurrenceSettings, setRecurrenceSettings] = useState<RecurrenceSettings>({
    type: 'none',
    endType: 'never',
    interval: 1,
    weekdays: [new Date().getDay()],
    endDate: (() => {
      const date = new Date();
      date.setMonth(date.getMonth() + 1); // Default: 1 month from now
      return date;
    })()
  });

  // Date picker visibility
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  // Form validation
  const [titleError, setTitleError] = useState(false);
  const [dateError, setDateError] = useState(false);

  // Color options
  const colorOptions = [
    colors.primary, '#F97316', '#10B981', '#8B5CF6',
    '#EC4899', '#F59E0B', '#EF4444', '#3B82F6'
  ];

  // Event type options
  const eventTypes = [
    { value: 'study', label: 'Estudo', icon: BookOpen, color: '#4F46E5' },
    { value: 'exam', label: 'Prova', icon: FileText, color: '#EF4444' },
    { value: 'assignment', label: 'Tarefa', icon: GraduationCap, color: '#10B981' },
    { value: 'meeting', label: 'Reunião', icon: Users, color: '#F59E0B' },
    { value: 'other', label: 'Outro', icon: Tag, color: '#8B5CF6' }
  ];

  // Validate dates when they change
  useEffect(() => {
    if (endDate < startDate) {
      setDateError(true);
    } else {
      setDateError(false);
    }
  }, [startDate, endDate]);

  // Validate form before saving
  const validateForm = () => {
    let isValid = true;

    // Title validation
    if (!title.trim()) {
      setTitleError(true);
      isValid = false;
    } else {
      setTitleError(false);
    }

    // Date validation
    if (endDate < startDate) {
      setDateError(true);
      isValid = false;
    } else {
      setDateError(false);
    }

    return isValid;
  };

  const handleSave = () => {
    if (!validateForm()) {
      Alert.alert('Erro', 'Por favor, corrija os erros no formulário antes de salvar.');
      return;
    }

    const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      allDay,
      color: color,
      type: type,
      completed: false,
      reminder: reminder,
      reminderTime: reminder ? reminderTime.toISOString() : undefined,
      subject: subject || undefined,
      subject_id: subjectId || undefined,
      recurrence: recurrenceSettings.type !== 'none' ? recurrenceSettings.type : undefined,
      recurrenceEndDate: recurrenceSettings.type !== 'none' && recurrenceSettings.endType === 'on_date' && recurrenceSettings.endDate
        ? recurrenceSettings.endDate.toISOString()
        : undefined,
      recurrenceSettings: recurrenceSettings.type !== 'none' ? {
        interval: recurrenceSettings.interval,
        weekdays: recurrenceSettings.weekdays,
        monthDay: recurrenceSettings.monthDay,
        endType: recurrenceSettings.endType,
        occurrences: recurrenceSettings.occurrences
      } : undefined
    };

    console.log('NewEventScreen: Salvando evento:', event);
    addEvent(event);
    Alert.alert('Sucesso', 'Evento criado com sucesso!', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      try {
        const newDate = new Date(selectedDate);

        // Validate the date
        if (isNaN(newDate.getTime())) {
          console.error('Invalid date selected');
          return;
        }

        newDate.setHours(
          startDate.getHours(),
          startDate.getMinutes(),
          startDate.getSeconds(),
          startDate.getMilliseconds()
        );
        setStartDate(newDate);

        // If end date is before new start date, update end date
        if (endDate < newDate) {
          setEndDate(addHours(newDate, 1));
        }
      } catch (error) {
        console.error('Error handling start date change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar a data. Por favor, tente novamente.');
      }
    }
  };

  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
    setShowStartTimePicker(false);
    if (selectedTime) {
      try {
        const newDate = new Date(startDate);

        // Validate the time
        if (isNaN(selectedTime.getTime())) {
          console.error('Invalid time selected');
          return;
        }

        newDate.setHours(
          selectedTime.getHours(),
          selectedTime.getMinutes(),
          selectedTime.getSeconds(),
          selectedTime.getMilliseconds()
        );
        setStartDate(newDate);

        // If end date is before new start date, update end date
        if (endDate < newDate) {
          setEndDate(addHours(newDate, 1));
        }
      } catch (error) {
        console.error('Error handling start time change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar o horário. Por favor, tente novamente.');
      }
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      try {
        const newDate = new Date(selectedDate);

        // Validate the date
        if (isNaN(newDate.getTime())) {
          console.error('Invalid date selected');
          return;
        }

        newDate.setHours(
          endDate.getHours(),
          endDate.getMinutes(),
          endDate.getSeconds(),
          endDate.getMilliseconds()
        );
        setEndDate(newDate);

        // If end date is before start date, show error
        if (newDate < startDate) {
          setDateError(true);
        } else {
          setDateError(false);
        }
      } catch (error) {
        console.error('Error handling end date change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar a data. Por favor, tente novamente.');
      }
    }
  };

  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
    setShowEndTimePicker(false);
    if (selectedTime) {
      try {
        const newDate = new Date(endDate);

        // Validate the time
        if (isNaN(selectedTime.getTime())) {
          console.error('Invalid time selected');
          return;
        }

        newDate.setHours(
          selectedTime.getHours(),
          selectedTime.getMinutes(),
          selectedTime.getSeconds(),
          selectedTime.getMilliseconds()
        );
        setEndDate(newDate);

        // If end date is before start date, show error
        if (newDate < startDate) {
          setDateError(true);
        } else {
          setDateError(false);
        }
      } catch (error) {
        console.error('Error handling end time change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar o horário. Por favor, tente novamente.');
      }
    }
  };

  const handleReminderTimeChange = (event: any, selectedTime?: Date) => {
    setShowReminderTimePicker(false);
    if (selectedTime) {
      try {
        // Validate the time
        if (isNaN(selectedTime.getTime())) {
          console.error('Invalid reminder time selected');
          return;
        }

        setReminderTime(selectedTime);
      } catch (error) {
        console.error('Error handling reminder time change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar o horário do lembrete. Por favor, tente novamente.');
      }
    }
  };



  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <Stack.Screen options={{ title: "Novo Evento" }} />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      <ScrollView style={styles.scrollView}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Título <Text style={styles.requiredMark}>*</Text></Text>
            <TextInput
              style={[styles.input, titleError && styles.inputError]}
              value={title}
              onChangeText={(text) => {
                setTitle(text);
                if (text.trim()) setTitleError(false);
              }}
              placeholder="Título do evento"
              placeholderTextColor={colors.textLight}
            />
            {titleError && (
              <Text style={styles.errorText}>O título é obrigatório</Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descrição do evento"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Dia inteiro</Text>
            <Switch
              value={allDay}
              onValueChange={setAllDay}
              trackColor={{ false: colors.border, true: `${colors.primary}80` }}
              thumbColor={allDay ? colors.primary : colors.white}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Data de início <Text style={styles.requiredMark}>*</Text></Text>
            <Pressable
              style={styles.datePickerButton}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Calendar size={20} color={colors.primary} />
              <Text style={styles.datePickerButtonText}>
                {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
              </Text>
            </Pressable>
            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display="default"
                onChange={handleStartDateChange}
              />
            )}
          </View>

          {!allDay && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Hora de início</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowStartTimePicker(true)}
              >
                <Clock size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(startDate, 'HH:mm', { locale: ptBR })}
                </Text>
              </Pressable>
              {showStartTimePicker && (
                <DateTimePicker
                  value={startDate}
                  mode="time"
                  display="default"
                  onChange={handleStartTimeChange}
                />
              )}
            </View>
          )}

          <View style={styles.formGroup}>
            <Text style={styles.label}>Data de término <Text style={styles.requiredMark}>*</Text></Text>
            <Pressable
              style={[styles.datePickerButton, dateError && styles.inputError]}
              onPress={() => setShowEndDatePicker(true)}
            >
              <Calendar size={20} color={colors.primary} />
              <Text style={styles.datePickerButtonText}>
                {format(endDate, 'dd/MM/yyyy', { locale: ptBR })}
              </Text>
            </Pressable>
            {showEndDatePicker && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display="default"
                onChange={handleEndDateChange}
              />
            )}
            {dateError && (
              <Text style={styles.errorText}>A data de término deve ser posterior à data de início</Text>
            )}
          </View>

          {!allDay && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Hora de término</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowEndTimePicker(true)}
              >
                <Clock size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(endDate, 'HH:mm', { locale: ptBR })}
                </Text>
              </Pressable>
              {showEndTimePicker && (
                <DateTimePicker
                  value={endDate}
                  mode="time"
                  display="default"
                  onChange={handleEndTimeChange}
                />
              )}
            </View>
          )}

          <View style={styles.formGroup}>
            <Text style={styles.label}>Lembrete</Text>
            <Switch
              value={reminder}
              onValueChange={setReminder}
              trackColor={{ false: colors.border, true: `${colors.primary}80` }}
              thumbColor={reminder ? colors.primary : colors.white}
            />
          </View>

          {reminder && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Horário do lembrete</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowReminderTimePicker(true)}
              >
                <Bell size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(reminderTime, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                </Text>
              </Pressable>
              {showReminderTimePicker && (
                <DateTimePicker
                  value={reminderTime}
                  mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                  display="default"
                  onChange={handleReminderTimeChange}
                />
              )}
            </View>
          )}

          <RecurrenceSelector
            value={recurrenceSettings}
            onChange={setRecurrenceSettings}
          />

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Evento</Text>
            <View style={styles.eventTypeContainer}>
              {eventTypes.map((eventType) => {
                const Icon = eventType.icon;
                return (
                  <TouchableOpacity
                    key={eventType.value}
                    style={[
                      styles.eventTypeButton,
                      type === eventType.value && styles.eventTypeButtonActive,
                      type === eventType.value && { borderColor: eventType.color }
                    ]}
                    onPress={() => setType(eventType.value as any)}
                  >
                    <Icon size={16} color={type === eventType.value ? eventType.color : colors.textLight} />
                    <Text
                      style={[
                        styles.eventTypeText,
                        type === eventType.value && styles.eventTypeTextActive,
                        type === eventType.value && { color: eventType.color }
                      ]}
                    >
                      {eventType.label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Cor</Text>
            <View style={styles.colorGrid}>
              {colorOptions.map((colorOption) => (
                <Pressable
                  key={colorOption}
                  style={[
                    styles.colorOption,
                    { backgroundColor: colorOption },
                    color === colorOption && styles.colorOptionSelected
                  ]}
                  onPress={() => setColor(colorOption)}
                />
              ))}
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Pressable
              style={[styles.button, styles.cancelButton]}
              onPress={() => router.back()}
            >
              <Text style={styles.buttonText}>Cancelar</Text>
            </Pressable>
            <Pressable
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
            >
              <Text style={[styles.buttonText, styles.saveButtonText]}>Salvar</Text>
            </Pressable>
          </View>
        </GlassCard>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  requiredMark: {
    color: colors.error,
  },
  input: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  inputError: {
    borderColor: colors.error,
    borderWidth: 1.5,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
  textArea: {
    minHeight: 100,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  saveButtonText: {
    color: colors.white,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  colorOptionSelected: {
    borderWidth: 3,
    borderColor: colors.text,
  },
  eventTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  eventTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 8,
    minWidth: '48%',
    backgroundColor: colors.background,
  },
  eventTypeButtonActive: {
    backgroundColor: `${colors.backgroundLight}80`,
    borderWidth: 1.5,
  },
  eventTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginLeft: 6,
  },
  eventTypeTextActive: {
    fontWeight: '700',
  },

});
