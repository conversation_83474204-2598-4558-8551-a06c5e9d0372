import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Dimensions,
  Pressable,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useQuizStore } from "@/store/quizStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import { ProgressBar } from "@/components/ProgressBar";
import { LinearGradient } from "expo-linear-gradient";
import {
  BarChart,
  TrendingUp,
  Clock,
  Calendar,
  Award,
  ArrowLeft,
  BarChart2,
  CheckCircle,
  XCircle,
} from "lucide-react-native";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

const { width } = Dimensions.get("window");

export default function QuizStatsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getQuizById, getQuizAttempts, getQuizStats } = useQuizStore();

  const quiz = getQuizById(id as string);
  const attempts = getQuizAttempts(id as string);
  const stats = getQuizStats(id as string);

  if (!quiz) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Estatísticas" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Quiz não encontrado</Text>
          <Button
            title="Voltar"
            onPress={() => router.back()}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  // Calculate average time per question
  const avgTimePerQuestion = attempts.length > 0
    ? attempts.reduce((sum, a) => sum + (a.timeSpent / a.totalQuestions), 0) / attempts.length
    : 0;

  // Calculate correct answer percentage per attempt
  const correctPercentages = attempts.map(attempt => 
    (attempt.score / attempt.totalQuestions) * 100
  );

  // Calculate most difficult questions
  const questionStats: Record<string, { total: number, correct: number }> = {};
  
  attempts.forEach(attempt => {
    attempt.answers.forEach(answer => {
      if (!questionStats[answer.questionId]) {
        questionStats[answer.questionId] = { total: 0, correct: 0 };
      }
      
      questionStats[answer.questionId].total += 1;
      if (answer.correct) {
        questionStats[answer.questionId].correct += 1;
      }
    });
  });
  
  const difficultQuestions = Object.entries(questionStats)
    .map(([id, stats]) => ({
      id,
      correctRate: stats.correct / stats.total,
      question: quiz.questions.find(q => q.id === id)?.question || "Questão desconhecida"
    }))
    .sort((a, b) => a.correctRate - b.correctRate)
    .slice(0, 3);

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: "Estatísticas do Quiz",
          headerLeft: () => (
            <Pressable style={styles.backButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.text} />
            </Pressable>
          )
        }} 
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.quizInfoCard} gradient>
          <Text style={styles.quizTitle}>{quiz.title}</Text>
          <Text style={styles.quizSubject}>{quiz.subject}</Text>
          <Text style={styles.quizQuestions}>{quiz.questions.length} questões</Text>
        </GlassCard>

        <View style={styles.statsOverviewSection}>
          <Text style={styles.sectionTitle}>Visão Geral</Text>
          
          <View style={styles.statsCardsContainer}>
            <GlassCard style={styles.statCard} gradient>
              <View style={styles.statIconContainer}>
                <BarChart size={24} color={colors.primary} />
              </View>
              <Text style={styles.statValue}>{stats.totalAttempts}</Text>
              <Text style={styles.statLabel}>Tentativas</Text>
            </GlassCard>
            
            <GlassCard style={styles.statCard} gradient>
              <View style={styles.statIconContainer}>
                <Award size={24} color={colors.secondary} />
              </View>
              <Text style={styles.statValue}>{Math.round(stats.bestScore)}%</Text>
              <Text style={styles.statLabel}>Melhor Pontuação</Text>
            </GlassCard>
            
            <GlassCard style={styles.statCard} gradient>
              <View style={styles.statIconContainer}>
                <Clock size={24} color={colors.accent1} />
              </View>
              <Text style={styles.statValue}>{Math.round(avgTimePerQuestion)}s</Text>
              <Text style={styles.statLabel}>Tempo Médio/Questão</Text>
            </GlassCard>
          </View>
        </View>

        {stats.totalAttempts > 1 && (
          <View style={styles.progressSection}>
            <Text style={styles.sectionTitle}>Seu Progresso</Text>
            
            <GlassCard style={styles.progressCard} gradient>
              <View style={styles.progressHeader}>
                <Text style={styles.progressTitle}>Melhoria</Text>
                <Text style={[
                  styles.progressValue,
                  {color: stats.improvement > 0 ? colors.success : colors.error}
                ]}>
                  {stats.improvement > 0 ? '+' : ''}{Math.round(stats.improvement)}%
                </Text>
              </View>
              
              <View style={styles.progressBarContainer}>
                <Text style={styles.progressBarLabel}>Primeira tentativa</Text>
                <ProgressBar
                  progress={correctPercentages[correctPercentages.length - 1]}
                  gradientColors={["#CBD5E1", "#94A3B8"]}
                  height={12}
                  backgroundColor={`${colors.primary}20`}
                  borderRadius={6}
                />
                <Text style={styles.progressBarValue}>
                  {Math.round(correctPercentages[correctPercentages.length - 1])}%
                </Text>
              </View>
              
              <View style={styles.progressBarContainer}>
                <Text style={styles.progressBarLabel}>Última tentativa</Text>
                <ProgressBar
                  progress={correctPercentages[0]}
                  gradientColors={colors.primaryGradient}
                  height={12}
                  backgroundColor={`${colors.primary}20`}
                  borderRadius={6}
                />
                <Text style={styles.progressBarValue}>
                  {Math.round(correctPercentages[0])}%
                </Text>
              </View>
            </GlassCard>
          </View>
        )}

        {difficultQuestions.length > 0 && (
          <View style={styles.difficultQuestionsSection}>
            <Text style={styles.sectionTitle}>Questões Mais Difíceis</Text>
            
            {difficultQuestions.map((q, index) => (
              <GlassCard key={q.id} style={styles.questionCard} gradient>
                <View style={styles.questionHeader}>
                  <View style={[
                    styles.questionDifficultyIndicator,
                    {backgroundColor: q.correctRate < 0.5 ? colors.error : colors.secondary}
                  ]} />
                  <Text style={styles.questionNumber}>Questão {index + 1}</Text>
                  <Text style={styles.questionCorrectRate}>
                    {Math.round(q.correctRate * 100)}% de acerto
                  </Text>
                </View>
                <Text style={styles.questionText}>{q.question}</Text>
              </GlassCard>
            ))}
          </View>
        )}

        <View style={styles.attemptsHistorySection}>
          <Text style={styles.sectionTitle}>Histórico de Tentativas</Text>
          
          {attempts.map((attempt, index) => (
            <GlassCard key={attempt.id} style={styles.attemptCard} gradient>
              <View style={styles.attemptHeader}>
                <Text style={styles.attemptDate}>
                  {formatDistanceToNow(new Date(attempt.date), { 
                    addSuffix: true,
                    locale: ptBR
                  })}
                </Text>
                <View style={styles.attemptScoreContainer}>
                  <Text style={styles.attemptScoreValue}>
                    {Math.round((attempt.score / attempt.totalQuestions) * 100)}%
                  </Text>
                </View>
              </View>
              
              <View style={styles.attemptStatsContainer}>
                <View style={styles.attemptStatItem}>
                  <CheckCircle size={16} color={colors.success} />
                  <Text style={styles.attemptStatText}>
                    {attempt.score} acertos
                  </Text>
                </View>
                
                <View style={styles.attemptStatItem}>
                  <XCircle size={16} color={colors.error} />
                  <Text style={styles.attemptStatText}>
                    {attempt.totalQuestions - attempt.score} erros
                  </Text>
                </View>
                
                <View style={styles.attemptStatItem}>
                  <Clock size={16} color={colors.textLight} />
                  <Text style={styles.attemptStatText}>
                    {Math.floor(attempt.timeSpent / 60)}m {attempt.timeSpent % 60}s
                  </Text>
                </View>
              </View>
            </GlassCard>
          ))}
        </View>

        <Button
          title="Refazer Quiz"
          onPress={() => router.push(`/quiz/${id}`)}
          variant="primary"
          size="large"
          fullWidth
          style={styles.retakeButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  backButton: {
    padding: 8,
  },
  quizInfoCard: {
    padding: 20,
    marginBottom: 24,
    alignItems: "center",
  },
  quizTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  quizSubject: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: "600",
    marginBottom: 8,
  },
  quizQuestions: {
    fontSize: 14,
    color: colors.textLight,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  statsOverviewSection: {
    marginBottom: 24,
  },
  statsCardsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
  },
  statCard: {
    width: (width - 48) / 3,
    padding: 12,
    alignItems: "center",
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  progressSection: {
    marginBottom: 24,
  },
  progressCard: {
    padding: 16,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  progressValue: {
    fontSize: 18,
    fontWeight: "700",
  },
  progressBarContainer: {
    marginBottom: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  progressBarLabel: {
    width: 120,
    fontSize: 14,
    color: colors.textLight,
  },
  progressBarValue: {
    width: 40,
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
    marginLeft: 8,
    textAlign: "right",
  },
  difficultQuestionsSection: {
    marginBottom: 24,
  },
  questionCard: {
    padding: 16,
    marginBottom: 12,
  },
  questionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  questionDifficultyIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  questionNumber: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
  },
  questionCorrectRate: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: "auto",
  },
  questionText: {
    fontSize: 16,
    color: colors.text,
  },
  attemptsHistorySection: {
    marginBottom: 24,
  },
  attemptCard: {
    padding: 16,
    marginBottom: 12,
  },
  attemptHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  attemptDate: {
    fontSize: 14,
    color: colors.textLight,
  },
  attemptScoreContainer: {
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  attemptScoreValue: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
  },
  attemptStatsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  attemptStatItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  attemptStatText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 4,
  },
  retakeButton: {
    marginTop: 8,
  },
});
