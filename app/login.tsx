import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Pressable,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Link } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { theme } from '@/constants/theme';
import { colors } from '@/constants/colors';
import { images } from '@/constants/images';
import { Mail, Lock, LogIn, UserPlus } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';

import { ModernCard } from '@/components/ModernCard';
import { HeroSection } from '@/components/HeroSection';
import { AuthGuard } from '@/components/AuthGuard';
import { validators } from '@/utils/validation';
import SafeAreaWrapper from '@/components/SafeAreaWrapper';
import { LinearGradient } from 'expo-linear-gradient';
import SocialLoginButtons from '@/components/SocialLoginButtons';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState<string | undefined>(undefined);
  const [passwordError, setPasswordError] = useState<string | undefined>(undefined);
  const [rememberMe, setRememberMe] = useState(false);
  const { signIn, signInWithGoogle, signInWithApple, loading } = useAuthStore();

  // Validar email
  const validateEmail = (value: string) => {
    if (!value.trim()) {
      setEmailError('O email é obrigatório');
      return false;
    }

    if (!validators.isValidEmail(value)) {
      setEmailError('Digite um email válido');
      return false;
    }

    setEmailError(undefined);
    return true;
  };

  // Validar senha
  const validatePassword = (value: string) => {
    if (!value.trim()) {
      setPasswordError('A senha é obrigatória');
      return false;
    }

    setPasswordError(undefined);
    return true;
  };

  // Função para fazer login
  const handleLogin = async () => {
    // Validar campos
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);

    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    // Sanitizar dados
    const sanitizedEmail = validators.sanitizeText(email.trim().toLowerCase());

    // Tentar fazer login
    try {
      await signIn(sanitizedEmail, password);
    } catch (error) {
      Alert.alert('Erro no login', 'Não foi possível fazer login. Verifique suas credenciais e tente novamente.');
    }
  };

  return (
    <AuthGuard requireAuth={false}>
      <SafeAreaWrapper>
        <StatusBar style="light" />
        <View style={styles.container}>
          <LinearGradient
            colors={['#F9FAFB', '#F3F4F6']}
            style={styles.backgroundGradient}
          />

          <KeyboardAvoidingView
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {/* Hero Section - Modernizado */}
              <HeroSection
                title="Lia"
                subtitle="Seu assistente de estudos inteligente"
                description="Faça login para continuar sua jornada de aprendizado"
                icon="LogIn"
                gradientColors={[...colors.heroGradient1]}
                animated={true}
                variant="large"
                leftComponent={
                  <Image
                    source={images.logoLiaBranco}
                    style={styles.logo}
                    resizeMode="contain"
                  />
                }
              />

              {/* Login Form - Modernizado */}
              <ModernCard
                title="Bem-vindo de volta!"
                subtitle="Faça login para continuar seus estudos"
                variant="glass"
                shadow="lg"
                style={styles.formCard}
              >

                {/* Campo de email */}
                <Input
                  label="E-mail"
                  placeholder="Digite seu e-mail"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) validateEmail(text);
                  }}
                  error={emailError}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  icon={<Mail size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validateEmail(email)}
                  required
                />

                {/* Campo de senha */}
                <Input
                  label="Senha"
                  placeholder="Digite sua senha"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (passwordError) validatePassword(text);
                  }}
                  error={passwordError}
                  secureTextEntry={true}
                  icon={<Lock size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validatePassword(password)}
                  required
                />

                {/* Remember Me and Forgot Password */}
                <View style={styles.optionsContainer}>
                  <Pressable
                    style={styles.rememberMeContainer}
                    onPress={() => setRememberMe(!rememberMe)}
                  >
                    <View style={[styles.checkbox, rememberMe && styles.checkboxChecked]}>
                      {rememberMe && <View style={styles.checkmark} />}
                    </View>
                    <Text style={styles.rememberMeText}>Lembrar de mim</Text>
                  </Pressable>

                  <Link href="/forgot-password" asChild>
                    <TouchableOpacity>
                      <Text style={styles.forgotPasswordText}>Esqueceu a senha?</Text>
                    </TouchableOpacity>
                  </Link>
                </View>

                {/* Botão de login */}
                <Button
                  title="Entrar"
                  onPress={handleLogin}
                  variant="primary"
                  size="large"
                  loading={loading}
                  disabled={loading}
                  fullWidth
                  style={styles.loginButton}
                  icon={LogIn}
                />

                {/* Botões de login social */}
                <SocialLoginButtons
                  onGooglePress={signInWithGoogle}
                  onApplePress={signInWithApple}
                  loading={loading}
                  disabled={loading}
                />
              </ModernCard>

              {/* Link para cadastro */}
              <View style={styles.signupContainer}>
                <Text style={styles.signupText}>Não tem uma conta?</Text>
                <Link href="/register" asChild>
                  <TouchableOpacity style={styles.signupButton}>
                    <UserPlus size={theme.sizes.icon.sm} color={colors.primary} />
                    <Text style={styles.signupLink}>Cadastre-se</Text>
                  </TouchableOpacity>
                </Link>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </View>
      </SafeAreaWrapper>
    </AuthGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 24,
  },
  // Hero Section Styles
  heroSection: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  heroGradient: {
    borderRadius: 20,
  },
  heroContent: {
    padding: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 8,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
  },
  tagline: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.85)',
    fontWeight: '500',
  },
  heroActions: {
    alignItems: 'center',
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  // Form Card Styles
  formCard: {
    marginHorizontal: 16,
    padding: 24,
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textDark,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  // Options Container (Remember Me + Forgot Password)
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.border,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  checkmark: {
    width: 8,
    height: 8,
    borderRadius: 2,
    backgroundColor: colors.white,
  },
  rememberMeText: {
    fontSize: 14,
    color: colors.textMedium,
    fontWeight: '500',
  },
  forgotPasswordText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
  },
  loginButton: {
    marginBottom: 24,
  },
  // Signup Section Styles
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  signupText: {
    fontSize: 15,
    color: colors.textMedium,
    marginRight: 8,
    fontWeight: '500',
  },
  signupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  signupLink: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.primary,
  },
});
