import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  Pressable,
  Alert,
  TouchableOpacity,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { theme } from "@/constants/theme";
import { colors } from "@/constants/colors";
import { Button } from "@/components/Button";
import { Card } from "@/components/Card";
import { ThemeSettings } from "@/components/ThemeSettings";
import { useTheme } from "@/contexts/ThemeContext";
import { useAuthStore } from "@/store/authStore";
import { useUserStore } from "@/store/userStore";
import { supabase } from "@/lib/supabase";
import SafeAreaWrapper from "@/components/SafeAreaWrapper";
import {
  Bell,
  Lock,
  HelpCircle,
  Info,
  LogOut,
  Trash2,
  Smartphone,
  ArrowLeft,
  ChevronRight,
} from "lucide-react-native";

export default function SettingsScreen() {
  const router = useRouter();
  const [notifications, setNotifications] = useState(true);
  const [biometrics, setBiometrics] = useState(false);
  const { theme: currentTheme, isDark } = useTheme();
  const { signOut } = useAuthStore();
  const { supabaseUser } = useUserStore();

  const handleLogout = () => {
    Alert.alert(
      "Sair da conta",
      "Tem certeza que deseja sair da sua conta?",
      [
        {
          text: "Cancelar",
          style: "cancel",
        },
        {
          text: "Sair",
          style: "destructive",
          onPress: async () => {
            try {
              await signOut();
            } catch (error) {
              Alert.alert("Erro", "Não foi possível sair da conta. Tente novamente.");
            }
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      "Excluir conta",
      "Tem certeza que deseja excluir sua conta? Esta ação não pode ser desfeita e todos os seus dados serão perdidos.",
      [
        {
          text: "Cancelar",
          style: "cancel",
        },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            try {
              if (!supabaseUser?.id) {
                Alert.alert('Erro', 'Usuário não autenticado');
                return;
              }

              // Delete user data from Supabase
              const { error } = await supabase
                .from('users')
                .delete()
                .eq('id', supabaseUser.id);

              if (error) {
                console.error('Error deleting user data:', error);
                Alert.alert('Erro', 'Não foi possível excluir a conta');
                return;
              }

              // Sign out
              await signOut();
              router.replace('/login');
            } catch (error) {
              console.error('Error in handleDeleteAccount:', error);
              Alert.alert('Erro', 'Ocorreu um erro ao excluir a conta');
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaWrapper>
      <Stack.Screen
        options={{
          title: "Configurações",
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={styles.headerButton}
            >
              <ArrowLeft size={22} color={colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferências</Text>

          {/* Configurações de notificações */}
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={[styles.settingIconContainer, { backgroundColor: colors.backgroundLight }]}>
                <Bell size={20} color={colors.text} />
              </View>
              <Text style={[styles.settingText, { color: colors.text }]}>Notificações</Text>
            </View>
            <Switch
              value={notifications}
              onValueChange={setNotifications}
              trackColor={{
                false: colors.backgroundDark,
                true: `${colors.primary}80`,
              }}
              thumbColor={notifications ? colors.primary : "#f4f3f4"}
            />
          </View>

          {/* Configurações de autenticação biométrica */}
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={[styles.settingIconContainer, { backgroundColor: colors.backgroundLight }]}>
                <Lock size={20} color={colors.text} />
              </View>
              <Text style={[styles.settingText, { color: colors.text }]}>Autenticação biométrica</Text>
            </View>
            <Switch
              value={biometrics}
              onValueChange={setBiometrics}
              trackColor={{
                false: colors.backgroundDark,
                true: `${colors.primary}80`,
              }}
              thumbColor={biometrics ? colors.primary : "#f4f3f4"}
            />
          </View>
        </View>

        {/* Configurações de tema */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Aparência</Text>
          <ThemeSettings showTitle={false} />
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Suporte</Text>
          <Pressable style={[styles.menuItem, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
            <View style={[styles.menuIconContainer, { backgroundColor: colors.backgroundLight }]}>
              <HelpCircle size={20} color={colors.text} />
            </View>
            <Text style={[styles.menuText, { color: colors.text }]}>Ajuda e Suporte</Text>
          </Pressable>
          <Pressable style={[styles.menuItem, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
            <View style={[styles.menuIconContainer, { backgroundColor: colors.backgroundLight }]}>
              <Info size={20} color={colors.text} />
            </View>
            <Text style={[styles.menuText, { color: colors.text }]}>Sobre o LIA</Text>
          </Pressable>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Conta</Text>
          <Pressable
            style={[styles.menuItem, { backgroundColor: colors.card, borderBottomColor: colors.border }]}
            onPress={handleLogout}
          >
            <View style={[styles.menuIconContainer, { backgroundColor: colors.backgroundLight }]}>
              <LogOut size={20} color={colors.error} />
            </View>
            <Text style={[styles.menuText, { color: colors.error }]}>
              Sair da conta
            </Text>
          </Pressable>
          <Pressable
            style={[styles.menuItem, { backgroundColor: colors.card, borderBottomColor: colors.border }]}
            onPress={handleDeleteAccount}
          >
            <View style={[styles.menuIconContainer, { backgroundColor: colors.backgroundLight }]}>
              <Trash2 size={20} color={colors.error} />
            </View>
            <Text style={[styles.menuText, { color: colors.error }]}>
              Excluir conta
            </Text>
          </Pressable>
        </View>

        <View style={styles.versionContainer}>
          <Text style={[styles.versionText, { color: colors.textLight }]}>Versão 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
}

const styles = StyleSheet.create({
  headerButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    ...theme.typography.heading3,
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: theme.spacing.sm,
  },
  settingText: {
    ...theme.typography.body1,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  },
  menuIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: theme.spacing.sm,
  },
  menuText: {
    ...theme.typography.body1,
  },
  versionContainer: {
    alignItems: "center",
    marginTop: theme.spacing.xl,
  },
  versionText: {
    ...theme.typography.caption,
  },
});