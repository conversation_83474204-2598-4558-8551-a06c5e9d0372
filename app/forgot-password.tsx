import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { theme } from '@/constants/theme';
import { colors } from '@/constants/colors';
import { images } from '@/constants/images';
import { Mail, ArrowLeft, Shield, Key, CheckCircle } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import { AuthGuard } from '@/components/AuthGuard';
import { validators } from '@/utils/validation';
import SafeAreaWrapper from '@/components/SafeAreaWrapper';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const { resetPassword, loading } = useAuthStore();

  // Validar email
  const validateEmail = (value: string) => {
    if (!value.trim()) {
      setEmailError('O e-mail é obrigatório');
      return false;
    }

    if (!validators.isValidEmail(value)) {
      setEmailError('Por favor, insira um e-mail válido');
      return false;
    }

    setEmailError(null);
    return true;
  };

  const handleResetPassword = async () => {
    if (!validateEmail(email)) {
      return;
    }

    try {
      await resetPassword(email);
      setIsEmailSent(true);
      Alert.alert(
        'E-mail enviado!',
        'Verifique sua caixa de entrada e siga as instruções para redefinir sua senha.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/login')
          }
        ]
      );
    } catch (error) {
      Alert.alert(
        'Erro',
        'Não foi possível enviar o e-mail de recuperação. Verifique se o e-mail está correto e tente novamente.'
      );
    }
  };

  return (
    <AuthGuard requireAuth={false}>
      <SafeAreaWrapper>
        <StatusBar style="light" />
        <View style={styles.container}>
          <LinearGradient
            colors={['#F9FAFB', '#F3F4F6']}
            style={styles.backgroundGradient}
          />

          <KeyboardAvoidingView
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {/* Back Button */}
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <ArrowLeft size={theme.sizes.icon.md} color={colors.textDark} />
              </TouchableOpacity>

              {/* Hero Section */}
              <View style={styles.heroSection}>
                <LinearGradient
                  colors={['#3399FF', '#66B2FF']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.heroGradient}
                >
                  <View style={styles.heroContent}>
                    <View style={styles.logoContainer}>
                      <Image
                        source={images.logo}
                        style={styles.logo}
                        resizeMode="contain"
                      />
                      <Text style={styles.appName}>Lia</Text>
                      <Text style={styles.tagline}>Recuperação de senha</Text>
                    </View>
                    <View style={styles.heroActions}>
                      <View style={styles.heroIconContainer}>
                        <Shield size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                      </View>
                    </View>
                  </View>
                </LinearGradient>
              </View>

              {/* Password Recovery Form */}
              <GlassCard style={styles.formCard} gradient>
                <Text style={styles.welcomeText}>Esqueceu sua senha?</Text>
                <Text style={styles.subtitle}>
                  Não se preocupe! Informe seu e-mail e enviaremos instruções para redefinir sua senha.
                </Text>

                {/* Campo de email */}
                <Input
                  label="E-mail"
                  placeholder="Digite seu e-mail"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) validateEmail(text);
                  }}
                  error={emailError}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  icon={<Mail size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validateEmail(email)}
                  required
                />

                {/* Botão de envio */}
                <Button
                  title="Enviar instruções"
                  onPress={handleResetPassword}
                  variant="primary"
                  size="large"
                  loading={loading}
                  disabled={loading || isEmailSent}
                  fullWidth
                  style={styles.resetButton}
                  icon={Key}
                />

                {isEmailSent && (
                  <View style={styles.successContainer}>
                    <CheckCircle size={theme.sizes.icon.md} color={colors.success} />
                    <Text style={styles.successText}>
                      E-mail enviado! Verifique sua caixa de entrada.
                    </Text>
                  </View>
                )}
              </GlassCard>

              {/* Back to Login */}
              <View style={styles.backToLoginContainer}>
                <Text style={styles.backToLoginText}>Lembrou da senha?</Text>
                <TouchableOpacity
                  style={styles.backToLoginButton}
                  onPress={() => router.replace('/login')}
                >
                  <ArrowLeft size={theme.sizes.icon.sm} color={colors.primary} />
                  <Text style={styles.backToLoginLink}>Voltar para o login</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </View>
      </SafeAreaWrapper>
    </AuthGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // Hero Section Styles
  heroSection: {
    marginHorizontal: 16,
    marginTop: 60,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  heroGradient: {
    borderRadius: 20,
  },
  heroContent: {
    padding: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 8,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
  },
  tagline: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.85)',
    fontWeight: '500',
  },
  heroActions: {
    alignItems: 'center',
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  // Form Card Styles
  formCard: {
    marginHorizontal: 16,
    padding: 24,
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textDark,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  resetButton: {
    marginBottom: 16,
  },
  // Success Container Styles
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.successLight,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: colors.success,
  },
  successText: {
    fontSize: 14,
    color: colors.success,
    fontWeight: '600',
    marginLeft: 8,
    textAlign: 'center',
    flex: 1,
  },
  // Back to Login Section Styles
  backToLoginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  backToLoginText: {
    fontSize: 15,
    color: colors.textMedium,
    marginRight: 8,
    fontWeight: '500',
  },
  backToLoginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  backToLoginLink: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.primary,
  },
});
