/**
 * Rota para o dashboard de desempenho
 *
 * Esta rota exibe o dashboard de desempenho para monitorar o desempenho das consultas ao banco de dados.
 * Apenas administradores têm acesso a esta rota.
 */

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import RouteGuard from '@/components/RouteGuard';
import PerformanceDashboard from '@/components/PerformanceDashboard';
import { useAuthStore } from '@/store/authStore';
import { supabase } from '@/lib/supabase';

// Componente principal
export default function PerformancePage() {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  // Verificar se o usuário é administrador
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      try {
        // Verificar se o usuário é administrador
        const { data, error } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Erro ao verificar status de administrador:', error);
          setIsAdmin(false);
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (error) {
        console.error('Erro ao verificar status de administrador:', error);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Dashboard de Desempenho',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.white,
        }}
      />

      <RouteGuard
        redirectTo="/profile"
        showErrorMessage={true}
        errorMessage="Apenas administradores têm acesso a esta página."
        cachePermission={false}
        onPermissionDenied={() => {
          Alert.alert(
            'Acesso Negado',
            'Apenas administradores têm acesso a esta página.'
          );
        }}
      >
        {isAdmin ? (
          <PerformanceDashboard />
        ) : (
          <View style={styles.container} />
        )}
      </RouteGuard>
    </View>
  );
}

// Estilos
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
});
