import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useAuthStore } from '@/store/authStore';
import { theme } from '@/constants/theme';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { Input } from '@/components/Input';
import { GlassCard } from '@/components/GlassCard';
import SafeAreaWrapper from '@/components/SafeAreaWrapper';
import { Camera, User, Mail, Phone, Calendar, MapPin, Edit, Save, ArrowLeft } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { RouteGuard } from '@/components/RouteGuard';

export default function ProfileDetailsScreen() {
  const router = useRouter();
  const { user, supabaseUser, fetchSupabaseUser, updateUserProfile } = useUserStore();
  const { session } = useAuthStore();

  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [location, setLocation] = useState('');
  const [bio, setBio] = useState('');

  // Load user data
  useEffect(() => {
    if (!supabaseUser) {
      fetchSupabaseUser();
    } else {
      setName(supabaseUser.name || '');
      setEmail(supabaseUser.email || '');
      setPhone(supabaseUser.phone || '');
      setLocation(supabaseUser.location || '');
      setBio(supabaseUser.bio || '');
      setProfileImage(supabaseUser.avatar_url || null);
    }
  }, [supabaseUser]);

  // Pick image from gallery
  const pickImage = async () => {
    if (!editMode) return;

    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permissão necessária', 'Precisamos de permissão para acessar sua galeria de fotos.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setProfileImage(result.assets[0].uri);
    }
  };

  // Take photo with camera
  const takePhoto = async () => {
    if (!editMode) return;

    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permissão necessária', 'Precisamos de permissão para acessar sua câmera.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setProfileImage(result.assets[0].uri);
    }
  };

  // Upload profile image to Supabase Storage
  const uploadProfileImage = async (uri: string): Promise<string | null> => {
    try {
      if (!session?.user.id) return null;

      const response = await fetch(uri);
      const blob = await response.blob();
      const fileExt = uri.split('.').pop();
      const fileName = `${session.user.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error } = await supabase.storage
        .from('profiles')
        .upload(filePath, blob);

      if (error) {
        console.error('Error uploading image:', error);
        return null;
      }

      const { data } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Error in uploadProfileImage:', error);
      return null;
    }
  };

  // Save profile changes
  const handleSave = async () => {
    try {
      setLoading(true);

      if (!session?.user.id) {
        Alert.alert('Erro', 'Usuário não autenticado');
        setLoading(false);
        return;
      }

      let avatarUrl = supabaseUser?.avatar_url || null;

      // Upload new profile image if changed
      if (profileImage && profileImage !== supabaseUser?.avatar_url) {
        const uploadedUrl = await uploadProfileImage(profileImage);
        if (uploadedUrl) {
          avatarUrl = uploadedUrl;
        }
      }

      // Update profile in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          name,
          phone,
          location,
          bio,
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', session.user.id);

      if (error) {
        console.error('Error updating profile:', error);
        Alert.alert('Erro', 'Não foi possível atualizar o perfil');
        setLoading(false);
        return;
      }

      // Refresh user data
      await fetchSupabaseUser();

      setEditMode(false);
      setLoading(false);
      Alert.alert('Sucesso', 'Perfil atualizado com sucesso');
    } catch (error) {
      console.error('Error in handleSave:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao salvar o perfil');
      setLoading(false);
    }
  };

  return (
    <RouteGuard>
      <SafeAreaWrapper>
        <Stack.Screen
          options={{
            title: 'Detalhes do Perfil',
            headerRight: () => (
              <TouchableOpacity
                onPress={() => setEditMode(!editMode)}
                style={styles.headerButton}
              >
                {editMode ? (
                  <Save size={22} color={theme.colors.primary} />
                ) : (
                  <Edit size={22} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ),
            headerLeft: () => (
              <TouchableOpacity
                onPress={() => router.back()}
                style={styles.headerButton}
              >
                <ArrowLeft size={22} color={theme.colors.primary} />
              </TouchableOpacity>
            ),
          }}
        />

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Atualizando perfil...</Text>
          </View>
        ) : (
          <>
            <View style={styles.profileImageContainer}>
              <TouchableOpacity
                style={styles.profileImageWrapper}
                onPress={pickImage}
                disabled={!editMode}
              >
                {profileImage ? (
                  <Image
                    source={{ uri: profileImage }}
                    style={styles.profileImage}
                  />
                ) : (
                  <View style={styles.profileImagePlaceholder}>
                    <User size={60} color={theme.colors.textLight} />
                  </View>
                )}

                {editMode && (
                  <View style={styles.cameraButton}>
                    <Camera size={20} color="#fff" />
                  </View>
                )}
              </TouchableOpacity>
            </View>

            <Card style={styles.infoCard}>
              <Text style={styles.sectionTitle}>Informações Pessoais</Text>

              {editMode ? (
                <>
                  <Input
                    label="Nome"
                    value={name}
                    onChangeText={setName}
                    placeholder="Seu nome completo"
                    icon={<User size={20} color={theme.colors.textMedium} />}
                  />

                  <Input
                    label="E-mail"
                    value={email}
                    editable={false}
                    placeholder="Seu e-mail"
                    icon={<Mail size={20} color={theme.colors.textMedium} />}
                    helperText="O e-mail não pode ser alterado"
                  />

                  <Input
                    label="Telefone"
                    value={phone}
                    onChangeText={setPhone}
                    placeholder="Seu telefone"
                    keyboardType="phone-pad"
                    icon={<Phone size={20} color={theme.colors.textMedium} />}
                  />

                  <Input
                    label="Localização"
                    value={location}
                    onChangeText={setLocation}
                    placeholder="Sua cidade/estado"
                    icon={<MapPin size={20} color={theme.colors.textMedium} />}
                  />

                  <Input
                    label="Sobre mim"
                    value={bio}
                    onChangeText={setBio}
                    placeholder="Conte um pouco sobre você"
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                    style={styles.bioInput}
                  />

                  <Button
                    title="Salvar Alterações"
                    onPress={handleSave}
                    variant="primary"
                    loading={loading}
                    disabled={loading}
                    style={styles.saveButton}
                  />
                </>
              ) : (
                <>
                  <View style={styles.infoRow}>
                    <User size={20} color={theme.colors.primary} />
                    <View style={styles.infoTextContainer}>
                      <Text style={styles.infoLabel}>Nome</Text>
                      <Text style={styles.infoValue}>{name || 'Não informado'}</Text>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Mail size={20} color={theme.colors.primary} />
                    <View style={styles.infoTextContainer}>
                      <Text style={styles.infoLabel}>E-mail</Text>
                      <Text style={styles.infoValue}>{email || 'Não informado'}</Text>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <Phone size={20} color={theme.colors.primary} />
                    <View style={styles.infoTextContainer}>
                      <Text style={styles.infoLabel}>Telefone</Text>
                      <Text style={styles.infoValue}>{phone || 'Não informado'}</Text>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <MapPin size={20} color={theme.colors.primary} />
                    <View style={styles.infoTextContainer}>
                      <Text style={styles.infoLabel}>Localização</Text>
                      <Text style={styles.infoValue}>{location || 'Não informado'}</Text>
                    </View>
                  </View>

                  {bio ? (
                    <View style={styles.bioContainer}>
                      <Text style={styles.bioLabel}>Sobre mim</Text>
                      <Text style={styles.bioText}>{bio}</Text>
                    </View>
                  ) : null}
                </>
              )}
            </Card>

            <GlassCard style={styles.statsCard} gradient>
              <Text style={styles.statsTitle}>Estatísticas de Estudo</Text>

              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{user.streak}</Text>
                  <Text style={styles.statLabel}>Dias seguidos</Text>
                </View>

                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{Math.floor(user.totalStudyTime / 60)}</Text>
                  <Text style={styles.statLabel}>Horas estudadas</Text>
                </View>

                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{user.level}</Text>
                  <Text style={styles.statLabel}>Nível</Text>
                </View>
              </View>

              <View style={styles.progressContainer}>
                <Text style={styles.progressLabel}>
                  XP para o próximo nível: {user.xp}/{user.xpToNextLevel}
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${(user.xp / user.xpToNextLevel) * 100}%` }
                    ]}
                  />
                </View>
              </View>
            </GlassCard>
          </>
        )}
      </ScrollView>
    </SafeAreaWrapper>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    padding: theme.spacing.lg,
    paddingBottom: theme.spacing.xxl,
  },
  headerButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.textMedium,
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  profileImageWrapper: {
    position: 'relative',
    width: 120,
    height: 120,
    borderRadius: 60,
    overflow: 'hidden',
    backgroundColor: theme.colors.backgroundLight,
    borderWidth: 3,
    borderColor: theme.colors.primary,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  profileImagePlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.backgroundLight,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: theme.colors.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoCard: {
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.heading3,
    marginBottom: theme.spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  infoTextContainer: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: theme.colors.textMedium,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: theme.colors.text,
  },
  bioContainer: {
    marginTop: theme.spacing.sm,
  },
  bioLabel: {
    fontSize: 14,
    color: theme.colors.textMedium,
    marginBottom: theme.spacing.xs,
  },
  bioText: {
    fontSize: 16,
    color: theme.colors.text,
    lineHeight: 22,
  },
  bioInput: {
    height: 100,
    paddingTop: 12,
  },
  saveButton: {
    marginTop: theme.spacing.md,
  },
  statsCard: {
    padding: theme.spacing.lg,
  },
  statsTitle: {
    ...theme.typography.heading3,
    marginBottom: theme.spacing.md,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.lg,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: theme.colors.textMedium,
  },
  progressContainer: {
    marginTop: theme.spacing.sm,
  },
  progressLabel: {
    fontSize: 14,
    color: theme.colors.textMedium,
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: `${theme.colors.primary}30`,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 4,
  },
});
