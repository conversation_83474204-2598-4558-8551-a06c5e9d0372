import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Pressable,
  Alert,
  TouchableOpacity,
  Image,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { theme } from "@/constants/theme";
import { colors } from "@/constants/colors";
import { useUserStore } from "@/store/userStore";
import { useStudyStore } from "@/store/studyStore";
import { ProgressBar } from "@/components/ProgressBar";
import { Button } from "@/components/Button";
import { Card } from "@/components/Card";
import { GlassCard } from "@/components/GlassCard";
import SafeAreaWrapper from "@/components/SafeAreaWrapper";
import { User, Award, Clock, Calendar, Edit2, ChevronRight, Settings, LogOut } from "lucide-react-native";
import { useAuthStore } from "@/store/authStore";

export default function ProfileScreen() {
  const router = useRouter();
  const { user, setUser, supabaseUser, fetchSupabaseUser } = useUserStore();
  const { subjects, activities } = useStudyStore();
  const { signOut } = useAuthStore();

  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(user.name);

  const totalStudyHours = Math.floor(user.totalStudyTime / 60);
  const totalStudyMinutes = user.totalStudyTime % 60;

  // Fetch user data from Supabase
  useEffect(() => {
    if (!supabaseUser) {
      fetchSupabaseUser();
    }
  }, []);

  const handleSaveProfile = () => {
    if (editName.trim()) {
      setUser({ name: editName.trim() });
      setIsEditing(false);
    } else {
      Alert.alert("Nome inválido", "Por favor, insira um nome válido.");
    }
  };

  // Handle logout
  const handleLogout = async () => {
    Alert.alert(
      "Sair da conta",
      "Tem certeza que deseja sair da sua conta?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Sair",
          style: "destructive",
          onPress: async () => {
            try {
              await signOut();
              router.replace("/login");
            } catch (error) {
              console.error("Error signing out:", error);
              Alert.alert("Erro", "Não foi possível sair da conta. Tente novamente.");
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaWrapper>
      <Stack.Screen options={{ title: "Perfil" }} />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.headerCard} gradient>
          <View style={styles.headerSection}>
            <TouchableOpacity
              style={styles.avatarContainer}
              onPress={() => router.push('/profile/details')}
            >
              {supabaseUser?.avatar_url ? (
                <Image
                  source={{ uri: supabaseUser.avatar_url }}
                  style={styles.avatarImage}
                />
              ) : (
                <User size={40} color={colors.primary} />
              )}
            </TouchableOpacity>

            {isEditing ? (
              <View style={styles.editNameContainer}>
                <TextInput
                  style={styles.nameInput}
                  value={editName}
                  onChangeText={setEditName}
                  placeholder="Seu nome"
                  maxLength={30}
                />
                <View style={styles.editButtons}>
                  <Button
                    title="Cancelar"
                    onPress={() => {
                      setIsEditing(false);
                      setEditName(user.name);
                    }}
                    variant="outline"
                    size="small"
                  />
                  <Button
                    title="Salvar"
                    onPress={handleSaveProfile}
                    variant="primary"
                    size="small"
                  />
                </View>
              </View>
            ) : (
              <View style={styles.nameContainer}>
                <Text style={styles.name}>{supabaseUser?.name || user.name}</Text>
                <Pressable
                  style={styles.editButton}
                  onPress={() => setIsEditing(true)}
                >
                  <Edit2 size={16} color={colors.textLight} />
                </Pressable>
              </View>
            )}
          </View>
        </GlassCard>

        <View style={styles.statsSection}>
          <View style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Award size={24} color={colors.primary} />
            </View>
            <Text style={styles.statValue}>{user.level}</Text>
            <Text style={styles.statLabel}>Nível</Text>
          </View>
          <View style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Calendar size={24} color={colors.secondary} />
            </View>
            <Text style={styles.statValue}>{user.streak}</Text>
            <Text style={styles.statLabel}>Dias seguidos</Text>
          </View>
          <View style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Clock size={24} color={colors.success} />
            </View>
            <Text style={styles.statValue}>{totalStudyHours}</Text>
            <Text style={styles.statLabel}>Horas estudadas</Text>
          </View>
        </View>

        <View style={styles.progressSection}>
          <Text style={styles.sectionTitle}>Progresso</Text>
          <View style={styles.levelCard}>
            <View style={styles.levelHeader}>
              <Text style={styles.levelTitle}>Nível {user.level}</Text>
              <Text style={styles.xpText}>
                {user.xp}/{user.xpToNextLevel} XP
              </Text>
            </View>
            <ProgressBar
              progress={(user.xp / user.xpToNextLevel) * 100}
              color={colors.primary}
              height={8}
            />
          </View>
        </View>

        <View style={styles.subjectsSection}>
          <Text style={styles.sectionTitle}>Suas Matérias</Text>
          {subjects.map((subject) => (
            <Pressable
              key={subject.id}
              style={styles.subjectCard}
              onPress={() => router.push(`/subject/${subject.id}`)}
            >
              <Text style={styles.subjectTitle}>{subject.title}</Text>
              <View style={styles.subjectProgressContainer}>
                <ProgressBar
                  progress={subject.progress}
                  color={subject.color}
                  height={6}
                />
                <Text style={styles.subjectProgressText}>
                  {subject.progress}%
                </Text>
              </View>
            </Pressable>
          ))}
        </View>

        <View style={styles.activitiesSection}>
          <Text style={styles.sectionTitle}>Estatísticas</Text>
          <View style={styles.activityStatsCard}>
            <View style={styles.activityStat}>
              <Text style={styles.activityStatValue}>
                {activities.length}
              </Text>
              <Text style={styles.activityStatLabel}>
                Atividades realizadas
              </Text>
            </View>
            <View style={styles.activityStat}>
              <Text style={styles.activityStatValue}>
                {activities.filter((a) => a.progress === 100).length}
              </Text>
              <Text style={styles.activityStatLabel}>
                Atividades concluídas
              </Text>
            </View>
          </View>
        </View>

        <Card style={styles.menuCard}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => router.push('/profile/details')}
          >
            <View style={styles.menuItemContent}>
              <User size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Detalhes do Perfil</Text>
            </View>
            <ChevronRight size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => router.push('/settings')}
          >
            <View style={styles.menuItemContent}>
              <Settings size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Configurações</Text>
            </View>
            <ChevronRight size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, styles.logoutItem]}
            onPress={handleLogout}
          >
            <View style={styles.menuItemContent}>
              <LogOut size={20} color={colors.error} />
              <Text style={[styles.menuItemText, styles.logoutText]}>Sair da Conta</Text>
            </View>
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </SafeAreaWrapper>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 48,
  },
  headerCard: {
    marginBottom: 24,
    padding: 16,
  },
  headerSection: {
    alignItems: "center",
    paddingVertical: 16,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: `${colors.primary}20`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 2,
    borderColor: colors.primary,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  name: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
  },
  editButton: {
    padding: 8,
    marginLeft: 8,
  },
  editNameContainer: {
    width: "100%",
    alignItems: "center",
  },
  nameInput: {
    width: "100%",
    height: 40,
    backgroundColor: colors.card,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    fontSize: 16,
  },
  editButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    width: "100%",
    gap: 8,
  },
  statsSection: {
    flexDirection: "row",
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuCard: {
    marginTop: 24,
    padding: 0,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 16,
  },
  logoutItem: {
    borderBottomWidth: 0,
  },
  logoutText: {
    color: colors.error,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  progressSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  levelCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  levelHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  levelTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  xpText: {
    fontSize: 14,
    color: colors.textLight,
  },
  subjectsSection: {
    marginBottom: 24,
  },
  subjectCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  subjectTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  subjectProgressContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  subjectProgressText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
    width: 40,
    textAlign: "right",
  },
  activitiesSection: {
    marginBottom: 24,
  },
  activityStatsCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  activityStat: {
    marginBottom: 16,
  },
  activityStatValue: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.primary,
    marginBottom: 4,
  },
  activityStatLabel: {
    fontSize: 14,
    color: colors.textLight,
  },
});