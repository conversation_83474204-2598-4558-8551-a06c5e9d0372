import React, { useState, useRef, useCallback, useEffect, useMemo, memo } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Pressable,
  TextInput,
  Dimensions,
  Platform,
  Alert,
  ScrollView,
  PanResponder,
  Animated,
  Modal,
  TouchableOpacity,
  InteractionManager,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useMindMapStore } from "@/store/mindMapStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import { MindMapTreeView } from "@/components/MindMapTreeView";
import { MobileMindMapCanvas } from "@/components/MobileMindMapCanvas";
import RouteGuard from '@/components/RouteGuard';
import {
  Plus,
  Trash2,
  Edit2,
  Save,
  X,
  Move,
  GitBranch,
  BrainCircuit,
  Circle,
  Square,
  Triangle,
  Hexagon,
  Palette,
  Type,
  ArrowRight,
  ZoomIn,
  <PERSON>mOut,
  RotateCcw,
  Undo,
  Redo,
  Copy,
  Layers,
  Image,
  Link,
  Maximize,
  Minimize,
  PanelLeft,
  PanelRight,
  Sliders,
  Settings,
  Check,
  ChevronDown,
  ChevronUp,
  Info,
  HelpCircle,
  AlertCircle,
} from "lucide-react-native";
import { MindMap, MindMapNode, MindMapConnection, NodeShape, NodeStyle } from "@/types";
import { LinearGradient } from "expo-linear-gradient";
import { debounce, throttle } from "@/utils/performance";

// Web-only imports
// Define empty components for non-web platforms
const EmptyComponent = () => null;

// Initialize variables with default values to prevent undefined errors
let ReactFlow = EmptyComponent;
let Background = EmptyComponent;
let Controls = EmptyComponent;
let MiniMap = EmptyComponent;
let Panel = EmptyComponent;
let useNodesState = () => [[], () => {}];
let useEdgesState = () => [[], () => {}];
let addEdge = () => {};
let MarkerType = { ArrowClosed: 'arrow-closed' };

// React Flow support detection - declare at module level
let isReactFlowSupported = false;

// Only import React Flow on web
if (Platform.OS === 'web') {
  try {
    // Use dynamic import to avoid bundling issues on native
    const ReactFlowImport = require('reactflow');

    // Check if the import was successful and has a default export
    if (ReactFlowImport && ReactFlowImport.default) {
      ReactFlow = ReactFlowImport.default;
      Background = ReactFlowImport.Background || EmptyComponent;
      Controls = ReactFlowImport.Controls || EmptyComponent;
      MiniMap = ReactFlowImport.MiniMap || EmptyComponent;
      Panel = ReactFlowImport.Panel || EmptyComponent;
      useNodesState = ReactFlowImport.useNodesState || (() => [[], () => {}]);
      useEdgesState = ReactFlowImport.useEdgesState || (() => [[], () => {}]);
      addEdge = ReactFlowImport.addEdge || (() => {});
      MarkerType = ReactFlowImport.MarkerType || { ArrowClosed: 'arrow-closed' };

      // Mark React Flow as supported if all components loaded successfully
      isReactFlowSupported = true;
      console.log("React Flow loaded successfully for web platform");
    } else {
      console.warn("ReactFlow import succeeded but default export is missing");
      isReactFlowSupported = false;
    }

    // Import CSS for React Flow
    try {
      require('reactflow/dist/style.css');
    } catch (cssError) {
      console.warn("Failed to import ReactFlow CSS:", cssError);
    }
  } catch (error) {
    console.error("Failed to import React Flow:", error);
    isReactFlowSupported = false;
  }
} else {
  // React Flow is not supported on native platforms
  isReactFlowSupported = false;
  console.log("Using mobile canvas implementation for native platform");
}

const { width, height } = Dimensions.get("window");

// Memoized Node shape components for better performance
const NodeShapeComponent = memo(({ shape, size, color, style }) => {
  const shapeSize = size || 24;

  switch (shape) {
    case 'circle':
      return <Circle size={shapeSize} color={color} style={style} />;
    case 'square':
      return <Square size={shapeSize} color={color} style={style} />;
    case 'triangle':
      return <Triangle size={shapeSize} color={color} style={style} />;
    case 'hexagon':
      return <Hexagon size={shapeSize} color={color} style={style} />;
    default:
      return <Circle size={shapeSize} color={color} style={style} />;
  }
});

// Color palette for nodes
const nodeColors = [
  colors.primary,
  colors.secondary,
  colors.accent1,
  colors.accent2,
  colors.accent3,
  colors.success,
  "#F472B6", // Pink
  "#34D399", // Green
  "#A78BFA", // Purple
  "#F97316", // Orange
  "#0EA5E9", // Sky blue
  "#64748B", // Slate
];

// Node shapes
const nodeShapes: NodeShape[] = ['circle', 'square', 'triangle', 'hexagon'];

// Node styles
const nodeStyles: NodeStyle[] = ['filled', 'outline', 'minimal'];

// Performance optimized MindMapScreen component
const MindMapScreen = memo(() => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { mindMaps, updateMindMap } = useMindMapStore();

  // Memoize mind map lookup to prevent unnecessary re-renders
  const mindMap = useMemo(() => mindMaps.find((m) => m.id === id), [mindMaps, id]);

  // Consolidated state for better performance
  const [uiState, setUiState] = useState({
    editMode: false,
    isAddingNode: false,
    isAddingConnection: false,
    isEditingDetails: false,
    showNodeStyler: false,
    showColorPicker: false,
    showShapePicker: false,
    showStylePicker: false,
    showConnectionStyler: false,
    showSettings: false,
    showTutorial: false,
    nodePlacementMode: false,
    showNodePreview: false,
    isMovingNode: false,
  });

  // Core data state
  const [nodes, setNodes] = useState<MindMapNode[]>(mindMap?.nodes || []);
  const [connections, setConnections] = useState<MindMapConnection[]>(mindMap?.connections || []);

  // Selection and editing state
  const [selectedNode, setSelectedNode] = useState<MindMapNode | null>(null);
  const [connectionStart, setConnectionStart] = useState<MindMapNode | null>(null);
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | null>(null);

  // Form state
  const [newNodeText, setNewNodeText] = useState("");
  const [mapTitle, setMapTitle] = useState(mindMap?.title || "");
  const [mapDescription, setMapDescription] = useState(mindMap?.description || "");

  // Style state
  const [selectedColor, setSelectedColor] = useState(colors.primary);
  const [selectedShape, setSelectedShape] = useState<NodeShape>('circle');
  const [selectedStyle, setSelectedStyle] = useState<NodeStyle>('filled');
  const [nodeSize, setNodeSize] = useState('medium');
  const [connectionType, setConnectionType] = useState('straight');
  const [selectedConnectionColor, setSelectedConnectionColor] = useState(colors.primary);
  const [selectedConnectionWidth, setSelectedConnectionWidth] = useState(2);
  const [selectedConnectionStyle, setSelectedConnectionStyle] = useState('solid');

  // View state
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [tempNodePosition, setTempNodePosition] = useState({ x: 0, y: 0 });
  const [connectionPreview, setConnectionPreview] = useState<{start: {x: number, y: number}, end: {x: number, y: number}} | null>(null);

  // History state
  const [undoStack, setUndoStack] = useState<any[]>([]);
  const [redoStack, setRedoStack] = useState<any[]>([]);

  // Settings state
  const [mapSettings, setMapSettings] = useState({
    gridEnabled: true,
    snapToGrid: true,
    autoLayout: false,
    showMinimap: true,
  });

  // React Flow specific states (web only)
  const reactFlowWrapper = useRef<any>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

  // Refs for pan and zoom
  const panRef = useRef({ x: 0, y: 0 });
  const lastPanRef = useRef({ x: 0, y: 0 });
  const mapContainerRef = useRef<View>(null);
  const animatedPan = useRef(new Animated.ValueXY({ x: 0, y: 0 })).current;
  const lastDistanceRef = useRef<number | null>(null); // For pinch zoom
  const scaleAnimation = useRef(new Animated.Value(1)).current; // For smoother zoom animation
  const lastZoomUpdateTime = useRef<number>(0); // To throttle zoom updates

  // Optimized UI state updater
  const updateUiState = useCallback((updates: Partial<typeof uiState>) => {
    setUiState(prev => ({ ...prev, ...updates }));
  }, []);

  // Debounced save to undo stack for better performance
  const saveToUndoStack = useCallback(
    debounce(() => {
      setUndoStack(prev => [...prev.slice(-19), { nodes: [...nodes], connections: [...connections] }]); // Keep max 20 items
      setRedoStack([]);
    }, 300),
    [nodes, connections]
  );

  // Optimized undo action
  const handleUndo = useCallback(() => {
    if (undoStack.length > 0) {
      const lastState = undoStack[undoStack.length - 1];
      setRedoStack(prev => [...prev, { nodes: [...nodes], connections: [...connections] }]);
      setNodes(lastState.nodes);
      setConnections(lastState.connections);
      setUndoStack(prev => prev.slice(0, -1));
    }
  }, [undoStack, nodes, connections]);

  // Optimized redo action
  const handleRedo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[redoStack.length - 1];
      setUndoStack(prev => [...prev, { nodes: [...nodes], connections: [...connections] }]);
      setNodes(nextState.nodes);
      setConnections(nextState.connections);
      setRedoStack(prev => prev.slice(0, -1));
    }
  }, [redoStack, nodes, connections]);

  // Throttled pan update for better performance
  const throttledPanUpdate = useCallback(
    throttle((panValue: { x: number; y: number }) => {
      animatedPan.setValue(panValue);
    }, 16), // ~60fps
    []
  );

  // Throttled zoom update for better performance
  const throttledZoomUpdate = useCallback(
    throttle((newZoom: number) => {
      setZoomLevel(newZoom);
      Animated.spring(scaleAnimation, {
        toValue: newZoom,
        friction: 10,
        tension: 40,
        useNativeDriver: true
      }).start();
    }, 16), // ~60fps
    []
  );

  // Pan responder for mobile with performance optimizations
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: (_, gestureState) => {
        // Only respond to gestures that are clearly intentional
        return Math.abs(gestureState.dx) > 2 || Math.abs(gestureState.dy) > 2;
      },
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to gestures that are clearly intentional and not when adding nodes/connections
        return (Math.abs(gestureState.dx) > 2 || Math.abs(gestureState.dy) > 2) &&
               !uiState.isAddingNode && !uiState.isAddingConnection && !selectedNode && !uiState.nodePlacementMode;
      },
      onPanResponderGrant: () => {
        lastPanRef.current = { x: panRef.current.x, y: panRef.current.y };
        lastDistanceRef.current = null; // Reset pinch zoom reference
      },
      onPanResponderMove: (event, gestureState) => {
        // Check for pinch gesture (two touches)
        if (event.nativeEvent.touches && event.nativeEvent.touches.length === 2) {
          // Get the two touch points
          const touch1 = event.nativeEvent.touches[0];
          const touch2 = event.nativeEvent.touches[1];

          // Calculate the distance between the two touch points
          const dx = touch1.pageX - touch2.pageX;
          const dy = touch1.pageY - touch2.pageY;
          const distance = Math.sqrt(dx * dx + dy * dy);

          // If we have a previous distance, calculate the change for zoom
          if (lastDistanceRef.current !== null) {
            const change = distance - lastDistanceRef.current;
            const zoomChange = change * 0.005; // Reduced sensitivity for more precise control
            const newZoom = Math.max(0.5, Math.min(3, zoomLevel + zoomChange));

            // Use throttled zoom update for better performance
            throttledZoomUpdate(newZoom);
          }

          // Save current distance for next comparison
          lastDistanceRef.current = distance;
        } else if (uiState.isMovingNode && selectedNode) {
          // Move selected node with throttled updates
          const updatedNodes = nodes.map(node =>
            node.id === selectedNode.id
              ? {
                  ...node,
                  x: node.x + gestureState.dx / zoomLevel,
                  y: node.y + gestureState.dy / zoomLevel
                }
              : node
          );
          setNodes(updatedNodes);
        } else if (uiState.nodePlacementMode) {
          // Update temp node position for preview
          setTempNodePosition({
            x: tempNodePosition.x + gestureState.dx / zoomLevel,
            y: tempNodePosition.y + gestureState.dy / zoomLevel
          });
        } else if (uiState.isAddingConnection && connectionStart) {
          // Update connection preview end point
          const startNode = nodes.find(n => n.id === connectionStart.id);
          if (startNode) {
            setConnectionPreview({
              start: { x: startNode.x, y: startNode.y },
              end: {
                x: startNode.x + gestureState.dx / zoomLevel,
                y: startNode.y + gestureState.dy / zoomLevel
              }
            });
          }
        } else {
          // Pan the map (single touch) with throttled updates
          const newPan = {
            x: lastPanRef.current.x + gestureState.dx / zoomLevel,
            y: lastPanRef.current.y + gestureState.dy / zoomLevel,
          };
          panRef.current = newPan;
          throttledPanUpdate(newPan);
        }
      },
      onPanResponderRelease: () => {
        if (uiState.isMovingNode) {
          updateUiState({ isMovingNode: false });
          saveToUndoStack();
        }
        // Reset pinch zoom reference
        lastDistanceRef.current = null;
      },
    })
  ).current;

  // Handle zoom in
  const handleZoomIn = () => {
    const newZoom = Math.min(zoomLevel + 0.2, 3);
    setZoomLevel(newZoom);

    // Animate the scale change for smoother zoom
    Animated.spring(scaleAnimation, {
      toValue: newZoom,
      friction: 8,
      tension: 40,
      useNativeDriver: true
    }).start();
  };

  // Handle zoom out
  const handleZoomOut = () => {
    const newZoom = Math.max(zoomLevel - 0.2, 0.5);
    setZoomLevel(newZoom);

    // Animate the scale change for smoother zoom
    Animated.spring(scaleAnimation, {
      toValue: newZoom,
      friction: 8,
      tension: 40,
      useNativeDriver: true
    }).start();
  };

  // Reset zoom and pan
  const handleResetView = () => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
    panRef.current = { x: 0, y: 0 };
    animatedPan.setValue({ x: 0, y: 0 });

    // Animate the scale reset
    Animated.spring(scaleAnimation, {
      toValue: 1,
      friction: 8,
      tension: 40,
      useNativeDriver: true
    }).start();
  };

  // Memoized React Flow nodes conversion for better performance
  const reactFlowNodes = useMemo(() => {
    if (Platform.OS !== 'web' || !ReactFlow) return [];

    return nodes.map((node) => ({
      id: node.id,
      position: { x: node.x, y: node.y },
      data: {
        label: node.text,
        shape: node.shape || 'circle',
        style: node.style || 'filled',
      },
      style: {
        background: node.style === 'filled' ? node.color || colors.primary : colors.card,
        color: node.style === 'filled' ? colors.white : colors.text,
        border: `2px solid ${node.color || colors.primary}`,
        borderRadius: node.shape === 'circle' ? '50%' : node.shape === 'square' ? '4px' : '8px',
        padding: 10,
        minWidth: node.size === 'small' ? 100 : node.size === 'large' ? 200 : 150,
        minHeight: node.size === 'small' ? 40 : node.size === 'large' ? 80 : 60,
        fontSize: node.size === 'small' ? 12 : node.size === 'large' ? 16 : 14,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      },
    }));
  }, [nodes]);

  // Memoized React Flow edges conversion for better performance
  const reactFlowEdges = useMemo(() => {
    if (Platform.OS !== 'web' || !ReactFlow) return [];

    return connections.map((conn) => ({
      id: conn.id,
      source: conn.source,
      target: conn.target,
      markerEnd: {
        type: MarkerType.ArrowClosed,
        color: conn.color || colors.primary,
      },
      style: {
        stroke: conn.color || colors.primary,
        strokeWidth: conn.width || 2,
        strokeDasharray: conn.style === 'dashed' ? '5,5' : conn.style === 'dotted' ? '2,2' : undefined,
      },
      animated: conn.animated || false,
      type: conn.type || 'default',
    }));
  }, [connections]);

  const handleSave = () => {
    if (mindMap) {
      updateMindMap(mindMap.id, {
        title: mapTitle,
        description: mapDescription,
        nodes,
        connections,
        lastEdited: new Date().toISOString(),
      });
      updateUiState({ editMode: false, isEditingDetails: false });
      Alert.alert("Sucesso", "Mapa mental salvo com sucesso!");
    }
  };

  const handleAddNode = () => {
    if (newNodeText.trim()) {
      saveToUndoStack();

      // If in placement mode, use the temp position
      const nodeX = uiState.nodePlacementMode ? tempNodePosition.x : width / 2 - panRef.current.x;
      const nodeY = uiState.nodePlacementMode ? tempNodePosition.y : height / 2 - panRef.current.y;

      const newNode: MindMapNode = {
        id: `node_${Date.now()}`,
        text: newNodeText,
        x: nodeX,
        y: nodeY,
        color: selectedColor,
        shape: selectedShape,
        style: selectedStyle,
        size: nodeSize,
      };

      setNodes([...nodes, newNode]);
      setNewNodeText("");
      updateUiState({
        isAddingNode: false,
        nodePlacementMode: false,
        showNodePreview: false
      });

      // Show a success message
      Alert.alert("Nó adicionado", "O nó foi adicionado com sucesso ao mapa!");
    } else {
      Alert.alert("Erro", "Por favor, digite um texto para o nó.");
    }
  };

  const handleStartNodePlacement = () => {
    if (newNodeText.trim()) {
      updateUiState({ nodePlacementMode: true, showNodePreview: true });
      setTempNodePosition({
        x: width / 2 - panRef.current.x,
        y: height / 2 - panRef.current.y
      });

      // Show guidance
      Alert.alert(
        "Posicionar Nó",
        "Toque e arraste para posicionar o nó no mapa, depois toque em 'Adicionar' para confirmar.",
        [
          { text: "Entendi", style: "default" }
        ]
      );
    } else {
      Alert.alert("Erro", "Por favor, digite um texto para o nó.");
    }
  };

  const handleNodePress = (node: MindMapNode) => {
    if (uiState.editMode) {
      if (uiState.isAddingConnection) {
        if (connectionStart) {
          // Finish adding connection
          if (connectionStart.id !== node.id) {
            saveToUndoStack();

            const newConnection: MindMapConnection = {
              id: `conn_${Date.now()}`,
              source: connectionStart.id,
              target: node.id,
              color: selectedConnectionColor,
              width: selectedConnectionWidth,
              style: selectedConnectionStyle,
              type: connectionType,
            };

            setConnections([...connections, newConnection]);

            // Show success message
            Alert.alert("Conexão criada", "A conexão entre os nós foi criada com sucesso!");
          } else {
            Alert.alert("Erro", "Não é possível conectar um nó a ele mesmo.");
          }
          setConnectionStart(null);
          updateUiState({ isAddingConnection: false });
          setConnectionPreview(null);
        } else {
          // Start adding connection
          setConnectionStart(node);

          // Show guidance
          Alert.alert(
            "Conectar Nós",
            "Agora selecione o nó de destino para criar a conexão.",
            [
              { text: "Entendi", style: "default" }
            ]
          );
        }
      } else if (uiState.nodePlacementMode) {
        // Cancel node placement if a node is clicked
        updateUiState({ nodePlacementMode: false, showNodePreview: false });
      } else {
        setSelectedNode(node);
        setSelectedColor(node.color || colors.primary);
        setSelectedShape(node.shape || 'circle');
        setSelectedStyle(node.style || 'filled');
        setNodeSize(node.size || 'medium');
      }
    }
  };

  // Mobile canvas handlers
  const handleMobileNodeMove = useCallback((nodeId: string, deltaX: number, deltaY: number) => {
    if (uiState.editMode) {
      saveToUndoStack();
      setNodes(prevNodes =>
        prevNodes.map(node =>
          node.id === nodeId
            ? { ...node, x: node.x + deltaX, y: node.y + deltaY }
            : node
        )
      );
    }
  }, [uiState.editMode, saveToUndoStack]);

  const handleMobileCanvasPress = useCallback((x: number, y: number) => {
    if (uiState.editMode && uiState.nodePlacementMode && newNodeText.trim()) {
      // Add node at tapped position
      saveToUndoStack();

      const newNode: MindMapNode = {
        id: `node_${Date.now()}`,
        text: newNodeText,
        x,
        y,
        color: selectedColor,
        shape: selectedShape,
        style: selectedStyle,
        size: nodeSize,
      };

      setNodes([...nodes, newNode]);
      setNewNodeText("");
      updateUiState({
        isAddingNode: false,
        nodePlacementMode: false,
        showNodePreview: false
      });

      Alert.alert("Nó adicionado", "O nó foi adicionado com sucesso ao mapa!");
    }
  }, [uiState.editMode, uiState.nodePlacementMode, newNodeText, selectedColor, selectedShape, selectedStyle, nodeSize, nodes, saveToUndoStack]);

  const handleUpdateNode = (text: string) => {
    if (selectedNode) {
      saveToUndoStack();

      const updatedNodes = nodes.map((node) =>
        node.id === selectedNode.id ? { ...node, text } : node
      );
      setNodes(updatedNodes);
    }
  };

  const handleUpdateNodeStyle = () => {
    if (selectedNode) {
      saveToUndoStack();

      const updatedNodes = nodes.map((node) =>
        node.id === selectedNode.id
          ? {
              ...node,
              color: selectedColor,
              shape: selectedShape,
              style: selectedStyle,
              size: nodeSize,
            }
          : node
      );
      setNodes(updatedNodes);
      updateUiState({ showNodeStyler: false });

      // Show success message
      Alert.alert("Estilo atualizado", "O estilo do nó foi atualizado com sucesso!");
    }
  };

  const handleDeleteNode = () => {
    if (selectedNode) {
      saveToUndoStack();

      // Remove node
      const filteredNodes = nodes.filter((node) => node.id !== selectedNode.id);

      // Remove connections involving this node
      const filteredConnections = connections.filter(
        (conn) => conn.source !== selectedNode.id && conn.target !== selectedNode.id
      );

      setNodes(filteredNodes);
      setConnections(filteredConnections);
      setSelectedNode(null);

      // Show success message
      Alert.alert("Nó excluído", "O nó e suas conexões foram excluídos com sucesso!");
    }
  };

  const handleDeleteConnection = (connectionId: string) => {
    saveToUndoStack();

    const filteredConnections = connections.filter(
      (conn) => conn.id !== connectionId
    );
    setConnections(filteredConnections);
    setSelectedConnectionId(null);

    // Show success message
    Alert.alert("Conexão excluída", "A conexão foi excluída com sucesso!");
  };

  const handleConnectionPress = (connectionId: string) => {
    if (uiState.editMode) {
      const connection = connections.find(conn => conn.id === connectionId);
      if (connection) {
        setSelectedConnectionId(connectionId);
        setSelectedConnectionColor(connection.color || colors.primary);
        setSelectedConnectionWidth(connection.width || 2);
        setSelectedConnectionStyle(connection.style || 'solid');
        updateUiState({ showConnectionStyler: true });
      }
    }
  };

  const handleUpdateConnectionStyle = () => {
    if (selectedConnectionId) {
      saveToUndoStack();

      const updatedConnections = connections.map((conn) =>
        conn.id === selectedConnectionId
          ? {
              ...conn,
              color: selectedConnectionColor,
              width: selectedConnectionWidth,
              style: selectedConnectionStyle,
            }
          : conn
      );
      setConnections(updatedConnections);
      updateUiState({ showConnectionStyler: false });
      setSelectedConnectionId(null);

      // Show success message
      Alert.alert("Estilo atualizado", "O estilo da conexão foi atualizado com sucesso!");
    }
  };

  const handleDuplicateNode = () => {
    if (selectedNode) {
      saveToUndoStack();

      const newNode: MindMapNode = {
        ...selectedNode,
        id: `node_${Date.now()}`,
        text: `${selectedNode.text} (cópia)`,
        x: selectedNode.x + 50,
        y: selectedNode.y + 50,
      };

      setNodes([...nodes, newNode]);
      setSelectedNode(newNode);

      // Show success message
      Alert.alert("Nó duplicado", "O nó foi duplicado com sucesso!");
    }
  };

  const handleCancelNodeCreation = () => {
    updateUiState({
      isAddingNode: false,
      nodePlacementMode: false,
      showNodePreview: false
    });
    setNewNodeText("");
  };

  const handleCancelConnectionCreation = () => {
    updateUiState({ isAddingConnection: false });
    setConnectionStart(null);
    setConnectionPreview(null);
  };

  // Optimized React Flow handlers with performance improvements
  const onConnect = useCallback((params: any) => {
    try {
      if (Platform.OS === 'web' && isReactFlowSupported && ReactFlow) {
        // Use InteractionManager for better performance
        InteractionManager.runAfterInteractions(() => {
          saveToUndoStack();

          const newConnection: MindMapConnection = {
            id: `conn_${Date.now()}`,
            source: params.source,
            target: params.target,
            color: selectedConnectionColor,
            width: selectedConnectionWidth,
            style: selectedConnectionStyle,
            type: connectionType,
          };

          setConnections((prevConnections) => [...prevConnections, newConnection]);
        });
      }
    } catch (error) {
      console.error('Error in onConnect:', error);
      Alert.alert('Erro', 'Falha ao conectar os nós. Tente novamente.');
    }
  }, [selectedConnectionColor, selectedConnectionWidth, selectedConnectionStyle, connectionType, saveToUndoStack]);

  const onNodesChange = useCallback(
    throttle((changes: any) => {
      try {
        if (Platform.OS === 'web' && isReactFlowSupported && ReactFlow) {
          const positionChanges = changes.filter((c: any) => c.type === 'position' && c.position && c.id);
          const removeChanges = changes.filter((c: any) => c.type === 'remove' && c.id);

          if (positionChanges.length > 0) {
            setNodes((prevNodes) =>
              prevNodes.map((node) => {
                const change = positionChanges.find((c: any) => c.id === node.id);
                return change
                  ? { ...node, x: change.position.x, y: change.position.y }
                  : node;
              })
            );
          }

          if (removeChanges.length > 0) {
            saveToUndoStack();
            setNodes((prevNodes) =>
              prevNodes.filter((node) => !removeChanges.some((c: any) => c.id === node.id))
            );
          }
        }
      } catch (error) {
        console.error('Error in onNodesChange:', error);
      }
    }, 16), // ~60fps
    [saveToUndoStack]
  );

  const onEdgesChange = useCallback((changes: any) => {
    try {
      if (Platform.OS === 'web' && isReactFlowSupported && ReactFlow) {
        const removeChanges = changes.filter((c: any) => c.type === 'remove' && c.id);

        if (removeChanges.length > 0) {
          saveToUndoStack();
          setConnections((prevConnections) =>
            prevConnections.filter((conn) => !removeChanges.some((c: any) => c.id === conn.id))
          );
        }
      }
    } catch (error) {
      console.error('Error in onEdgesChange:', error);
    }
  }, [saveToUndoStack]);

  const onDragOver = useCallback((event: any) => {
    try {
      if (Platform.OS === 'web' && event.preventDefault) {
        event.preventDefault();
        if (event.dataTransfer) {
          event.dataTransfer.dropEffect = 'move';
        }
      }
    } catch (error) {
      console.error('Error in onDragOver:', error);
    }
  }, []);

  const onDrop = useCallback((event: any) => {
    try {
      if (Platform.OS === 'web' && event.preventDefault) {
        event.preventDefault();

        if (reactFlowInstance && reactFlowInstance.screenToFlowPosition) {
          saveToUndoStack();

          const position = reactFlowInstance.screenToFlowPosition({
            x: event.clientX,
            y: event.clientY,
          });

          const newNode: MindMapNode = {
            id: `node_${Date.now()}`,
            text: newNodeText || 'Novo Nó',
            x: position.x,
            y: position.y,
            color: selectedColor,
            shape: selectedShape,
            style: selectedStyle,
            size: nodeSize,
          };

          setNodes((prevNodes) => [...prevNodes, newNode]);
          setNewNodeText("");
        }
      }
    } catch (error) {
      console.error('Error in onDrop:', error);
      Alert.alert('Erro', 'Falha ao adicionar nó. Tente novamente.');
    }
  }, [reactFlowInstance, newNodeText, selectedColor, selectedShape, selectedStyle, nodeSize, saveToUndoStack]);

  // Show tutorial on first edit
  useEffect(() => {
    if (uiState.editMode && nodes.length === 0) {
      updateUiState({ showTutorial: true });
    }
  }, [uiState.editMode, nodes.length]);

  // Sync scale animation with zoom level on initial load
  useEffect(() => {
    scaleAnimation.setValue(zoomLevel);
  }, []);

  // Sync scale animation with zoom level when it changes directly
  useEffect(() => {
    // Only update when the difference is significant to avoid unnecessary animations
    if (Math.abs(scaleAnimation._value - zoomLevel) > 0.01) {
      scaleAnimation.setValue(zoomLevel);
    }
  }, [zoomLevel]);

  if (!mindMap) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Mapa não encontrado" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Mapa mental não encontrado</Text>
          <Button
            title="Voltar para mapas"
            onPress={() => router.push("/mind-maps")}
            variant="primary"
            size="large"
          />
        </View>
      </SafeAreaView>
    );
  }

  // Render React Flow for web or mobile canvas for native
  if (Platform.OS === 'web' && isReactFlowSupported && typeof ReactFlow === 'function') {
    try {
      return (
        <SafeAreaView style={styles.container}>
          <Stack.Screen
            options={{
              title: mindMap?.title || 'Mapa Mental',
              headerRight: () => (
                <View style={styles.headerButtons}>
                  {uiState.editMode ? (
                    <Pressable
                      style={styles.editButton}
                      onPress={handleSave}
                    >
                      <Save size={24} color={colors.success} />
                    </Pressable>
                  ) : (
                    <Pressable
                      style={styles.editButton}
                      onPress={() => updateUiState({ editMode: true })}
                    >
                      <Edit2 size={24} color={colors.primary} />
                    </Pressable>
                  )}
                </View>
              ),
            }}
          />

        {uiState.isEditingDetails && (
          <GlassCard style={styles.editDetailsContainer}>
            <Text style={styles.editDetailsTitle}>Detalhes do Mapa</Text>
            <TextInput
              style={styles.editDetailsInput}
              placeholder="Título do mapa"
              value={mapTitle}
              onChangeText={setMapTitle}
            />
            <TextInput
              style={[styles.editDetailsInput, styles.editDetailsTextarea]}
              placeholder="Descrição (opcional)"
              value={mapDescription}
              onChangeText={setMapDescription}
              multiline
            />
            <View style={styles.editDetailsButtons}>
              <Button
                title="Cancelar"
                onPress={() => {
                  setMapTitle(mindMap.title);
                  setMapDescription(mindMap.description || "");
                  updateUiState({ isEditingDetails: false });
                }}
                variant="outline"
                size="medium"
              />
              <Button
                title="Salvar"
                onPress={() => {
                  updateUiState({ isEditingDetails: false });
                }}
                variant="primary"
                size="medium"
              />
            </View>
          </GlassCard>
        )}

        <View style={styles.reactFlowContainer} ref={reactFlowWrapper}>
          {typeof ReactFlow === 'function' && (
            <ReactFlow
              nodes={reactFlowNodes}
              edges={reactFlowEdges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              fitView
              attributionPosition="bottom-left"
              defaultEdgeOptions={{
                animated: false, // Disable animations for better performance
                style: { stroke: colors.primary, strokeWidth: 2 },
              }}
              nodesDraggable={uiState.editMode}
              nodesConnectable={uiState.editMode}
              elementsSelectable={uiState.editMode}
              edgesFocusable={uiState.editMode}
              // Performance optimizations
              nodeOrigin={[0.5, 0.5]}
              maxZoom={3}
              minZoom={0.5}
              defaultViewport={{ x: 0, y: 0, zoom: 1 }}
              // Disable expensive features for better performance
              deleteKeyCode={null}
              multiSelectionKeyCode={null}
              panOnDrag={true}
              panOnScroll={false}
              zoomOnScroll={true}
              zoomOnPinch={true}
              zoomOnDoubleClick={false}
          >
            {mapSettings.gridEnabled && typeof Background === 'function' &&
              <Background variant="dots" gap={12} size={1} />
            }
            {mapSettings.showMinimap && typeof MiniMap === 'function' && (
              <MiniMap
                nodeColor={(node: any) => {
                  const nodeData = nodes.find(n => n.id === node.id);
                  return nodeData?.color || colors.primary;
                }}
                nodeStrokeWidth={3}
                nodeBorderRadius={2}
              />
            )}
            {typeof Controls === 'function' && <Controls />}

            {uiState.editMode && typeof Panel === 'function' && (
              <Panel position="top-right">
                <View style={styles.webEditToolbar}>
                  <Button
                    title="Editar Detalhes"
                    onPress={() => updateUiState({ isEditingDetails: true })}
                    variant="outline"
                    size="medium"
                    icon={Edit2}
                  />
                  <Button
                    title="Adicionar Nó"
                    onPress={() => updateUiState({ isAddingNode: true })}
                    variant={uiState.isAddingNode ? "primary" : "outline"}
                    size="medium"
                    icon={Plus}
                  />
                  <Button
                    title="Configurações"
                    onPress={() => updateUiState({ showSettings: true })}
                    variant="outline"
                    size="medium"
                    icon={Settings}
                  />
                  <Button
                    title="Salvar Mapa"
                    onPress={handleSave}
                    variant="success"
                    size="medium"
                    icon={Save}
                  />
                </View>
              </Panel>
            )}

            {uiState.editMode && typeof Panel === 'function' && (
              <Panel position="bottom-left">
                <View style={styles.webEditToolbar}>
                  <Button
                    title="Desfazer"
                    onPress={handleUndo}
                    variant="outline"
                    size="small"
                    icon={Undo}
                    disabled={undoStack.length === 0}
                  />
                  <Button
                    title="Refazer"
                    onPress={handleRedo}
                    variant="outline"
                    size="small"
                    icon={Redo}
                    disabled={redoStack.length === 0}
                  />
                </View>
              </Panel>
            )}
          </ReactFlow>
          )}

          {uiState.isAddingNode && (
            <GlassCard style={styles.webAddNodeContainer}>
              <Text style={styles.addNodeTitle}>Adicionar Novo Nó</Text>
              <TextInput
                style={styles.addNodeInput}
                placeholder="Digite o texto do nó"
                value={newNodeText}
                onChangeText={setNewNodeText}
                autoFocus
              />

              <View style={styles.nodeStylerSection}>
                <Text style={styles.nodeStylerLabel}>Cor:</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorPicker}>
                  {nodeColors.map((color) => (
                    <Pressable
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        selectedColor === color && styles.selectedColorOption,
                      ]}
                      onPress={() => setSelectedColor(color)}
                    />
                  ))}
                </ScrollView>
              </View>

              <View style={styles.nodeStylerSection}>
                <Text style={styles.nodeStylerLabel}>Forma:</Text>
                <View style={styles.shapePicker}>
                  {nodeShapes.map((shape) => (
                    <Pressable
                      key={shape}
                      style={[
                        styles.shapeOption,
                        selectedShape === shape && styles.selectedShapeOption,
                      ]}
                      onPress={() => setSelectedShape(shape)}
                    >
                      <NodeShapeComponent
                        shape={shape}
                        size={24}
                        color={selectedShape === shape ? colors.primary : colors.textLight}
                      />
                    </Pressable>
                  ))}
                </View>
              </View>

              <View style={styles.nodeStylerSection}>
                <Text style={styles.nodeStylerLabel}>Estilo:</Text>
                <View style={styles.stylePicker}>
                  <Pressable
                    style={[
                      styles.styleOption,
                      selectedStyle === 'filled' && styles.selectedStyleOption,
                    ]}
                    onPress={() => setSelectedStyle('filled')}
                  >
                    <Text style={styles.styleOptionText}>Preenchido</Text>
                  </Pressable>
                  <Pressable
                    style={[
                      styles.styleOption,
                      selectedStyle === 'outline' && styles.selectedStyleOption,
                    ]}
                    onPress={() => setSelectedStyle('outline')}
                  >
                    <Text style={styles.styleOptionText}>Contorno</Text>
                  </Pressable>
                  <Pressable
                    style={[
                      styles.styleOption,
                      selectedStyle === 'minimal' && styles.selectedStyleOption,
                    ]}
                    onPress={() => setSelectedStyle('minimal')}
                  >
                    <Text style={styles.styleOptionText}>Mínimo</Text>
                  </Pressable>
                </View>
              </View>

              <View style={styles.nodeStylerSection}>
                <Text style={styles.nodeStylerLabel}>Tamanho:</Text>
                <View style={styles.sizePicker}>
                  <Pressable
                    style={[
                      styles.sizeOption,
                      nodeSize === 'small' && styles.selectedSizeOption,
                    ]}
                    onPress={() => setNodeSize('small')}
                  >
                    <Text style={styles.sizeOptionText}>P</Text>
                  </Pressable>
                  <Pressable
                    style={[
                      styles.sizeOption,
                      nodeSize === 'medium' && styles.selectedSizeOption,
                    ]}
                    onPress={() => setNodeSize('medium')}
                  >
                    <Text style={styles.sizeOptionText}>M</Text>
                  </Pressable>
                  <Pressable
                    style={[
                      styles.sizeOption,
                      nodeSize === 'large' && styles.selectedSizeOption,
                    ]}
                    onPress={() => setNodeSize('large')}
                  >
                    <Text style={styles.sizeOptionText}>G</Text>
                  </Pressable>
                </View>
              </View>

              <Text style={styles.addNodeHint}>
                Após adicionar, você pode arrastar o nó para posicioná-lo
              </Text>
              <View style={styles.addNodeButtons}>
                <Button
                  title="Cancelar"
                  onPress={() => {
                    updateUiState({ isAddingNode: false });
                    setNewNodeText("");
                  }}
                  variant="outline"
                  size="medium"
                />
                <Button
                  title="Adicionar"
                  onPress={handleAddNode}
                  variant="primary"
                  size="medium"
                />
              </View>
            </GlassCard>
          )}

          {uiState.showSettings && (
            <GlassCard style={styles.settingsContainer}>
              <View style={styles.settingsHeader}>
                <Text style={styles.settingsTitle}>Configurações do Mapa</Text>
                <Pressable onPress={() => updateUiState({ showSettings: false })} style={styles.closeButton}>
                  <X size={24} color={colors.text} />
                </Pressable>
              </View>

              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Mostrar grade</Text>
                <Pressable
                  style={[
                    styles.toggleButton,
                    mapSettings.gridEnabled && styles.toggleButtonActive,
                  ]}
                  onPress={() => setMapSettings({...mapSettings, gridEnabled: !mapSettings.gridEnabled})}
                >
                  <View style={[
                    styles.toggleIndicator,
                    mapSettings.gridEnabled && styles.toggleIndicatorActive,
                  ]} />
                </Pressable>
              </View>

              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Alinhar à grade</Text>
                <Pressable
                  style={[
                    styles.toggleButton,
                    mapSettings.snapToGrid && styles.toggleButtonActive,
                  ]}
                  onPress={() => setMapSettings({...mapSettings, snapToGrid: !mapSettings.snapToGrid})}
                >
                  <View style={[
                    styles.toggleIndicator,
                    mapSettings.snapToGrid && styles.toggleIndicatorActive,
                  ]} />
                </Pressable>
              </View>

              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Layout automático</Text>
                <Pressable
                  style={[
                    styles.toggleButton,
                    mapSettings.autoLayout && styles.toggleButtonActive,
                  ]}
                  onPress={() => setMapSettings({...mapSettings, autoLayout: !mapSettings.autoLayout})}
                >
                  <View style={[
                    styles.toggleIndicator,
                    mapSettings.autoLayout && styles.toggleIndicatorActive,
                  ]} />
                </Pressable>
              </View>

              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Mostrar minimapa</Text>
                <Pressable
                  style={[
                    styles.toggleButton,
                    mapSettings.showMinimap && styles.toggleButtonActive,
                  ]}
                  onPress={() => setMapSettings({...mapSettings, showMinimap: !mapSettings.showMinimap})}
                >
                  <View style={[
                    styles.toggleIndicator,
                    mapSettings.showMinimap && styles.toggleIndicatorActive,
                  ]} />
                </Pressable>
              </View>

              <View style={styles.settingsButtons}>
                <Button
                  title="Fechar"
                  onPress={() => updateUiState({ showSettings: false })}
                  variant="primary"
                  size="medium"
                />
              </View>
            </GlassCard>
          )}
        </View>
      </SafeAreaView>
    );
    } catch (error) {
      console.error('Error rendering React Flow:', error);
      // Fallback to mobile implementation if React Flow fails
      console.log('Falling back to mobile implementation due to React Flow error');
    }
  }

  // Fallback for web when React Flow is not supported or fails
  if (Platform.OS === 'web' && !isReactFlowSupported) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: mindMap?.title || 'Mapa Mental',
            headerRight: () => (
              <View style={styles.headerButtons}>
                <Pressable
                  style={styles.editButton}
                  onPress={() => router.push("/mind-maps")}
                >
                  <X size={24} color={colors.text} />
                </Pressable>
              </View>
            ),
          }}
        />

        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={colors.error} />
          <Text style={styles.errorTitle}>React Flow não suportado</Text>
          <Text style={styles.errorMessage}>
            Esta funcionalidade requer React Flow, que não está disponível neste ambiente.
            Por favor, use um dispositivo móvel ou verifique se todas as dependências estão instaladas.
          </Text>
          <Button
            title="Voltar para mapas"
            onPress={() => router.push("/mind-maps")}
            variant="primary"
            size="large"
          />
        </View>
      </SafeAreaView>
    );
  }

  // Mobile implementation
  return (
    <RouteGuard resourceId={id as string} tableName="mind_maps">
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: mindMap.title,
            headerRight: () => (
              <View style={styles.headerButtons}>
                {uiState.editMode ? (
                  <Pressable
                    style={styles.editButton}
                    onPress={handleSave}
                  >
                    <Save size={24} color={colors.success} />
                  </Pressable>
                ) : (
                  <Pressable
                    style={styles.editButton}
                    onPress={() => updateUiState({ editMode: true })}
                  >
                    <Edit2 size={24} color={colors.primary} />
                  </Pressable>
                )}
              </View>
            ),
          }}
        />

      <View style={styles.mapContainer}>
        {uiState.isEditingDetails && (
          <GlassCard style={styles.editDetailsContainer}>
            <Text style={styles.editDetailsTitle}>Detalhes do Mapa</Text>
            <TextInput
              style={styles.editDetailsInput}
              placeholder="Título do mapa"
              value={mapTitle}
              onChangeText={setMapTitle}
            />
            <TextInput
              style={[styles.editDetailsInput, styles.editDetailsTextarea]}
              placeholder="Descrição (opcional)"
              value={mapDescription}
              onChangeText={setMapDescription}
              multiline
            />
            <View style={styles.editDetailsButtons}>
              <Button
                title="Cancelar"
                onPress={() => {
                  setMapTitle(mindMap.title);
                  setMapDescription(mindMap.description || "");
                  updateUiState({ isEditingDetails: false });
                }}
                variant="outline"
                size="medium"
              />
              <Button
                title="Salvar"
                onPress={() => {
                  updateUiState({ isEditingDetails: false });
                }}
                variant="primary"
                size="medium"
              />
            </View>
          </GlassCard>
        )}

        <View style={styles.canvasContainer}>
          <MobileMindMapCanvas
            nodes={nodes}
            connections={connections}
            editMode={uiState.editMode}
            onNodePress={handleNodePress}
            onNodeMove={handleMobileNodeMove}
            onCanvasPress={handleMobileCanvasPress}
            selectedNodeId={selectedNode?.id}
            connectionPreview={connectionPreview}
          />
        </View>

        {/* Zoom controls */}
        {uiState.editMode && (
          <View style={styles.zoomControls}>
            <Pressable style={styles.zoomButton} onPress={handleZoomIn}>
              <ZoomIn size={20} color={colors.text} />
            </Pressable>
            <Pressable style={styles.zoomButton} onPress={handleZoomOut}>
              <ZoomOut size={20} color={colors.text} />
            </Pressable>
            <Pressable style={styles.zoomButton} onPress={handleResetView}>
              <RotateCcw size={20} color={colors.text} />
            </Pressable>
          </View>
        )}

        {/* Edit toolbar */}
        {uiState.editMode && (
          <View style={styles.editToolbar}>
            <View style={styles.toolbarButton}>
              <Button
                title="Detalhes"
                onPress={() => {
                  updateUiState({
                    isEditingDetails: true,
                    isAddingNode: false,
                    isAddingConnection: false,
                    showNodeStyler: false,
                    showConnectionStyler: false,
                    nodePlacementMode: false,
                    showNodePreview: false,
                  });
                  setConnectionStart(null);
                }}
                variant={uiState.isEditingDetails ? "primary" : "outline"}
                size="small"
                icon={Edit2}
                style={{ flex: 1 }}
              />
            </View>
            <View style={styles.toolbarButton}>
              <Button
                title="Nó"
                onPress={() => {
                  updateUiState({
                    isAddingNode: true,
                    isAddingConnection: false,
                    isEditingDetails: false,
                    showNodeStyler: false,
                    showConnectionStyler: false,
                    nodePlacementMode: false,
                    showNodePreview: false,
                  });
                  setConnectionStart(null);
                }}
                variant={uiState.isAddingNode ? "primary" : "outline"}
                size="small"
                icon={Plus}
                style={{ flex: 1 }}
              />
            </View>
            <View style={styles.toolbarButton}>
              <Button
                title="Conexão"
                onPress={() => {
                  updateUiState({
                    isAddingConnection: true,
                    isAddingNode: false,
                    isEditingDetails: false,
                    showNodeStyler: false,
                    showConnectionStyler: false,
                    nodePlacementMode: false,
                    showNodePreview: false,
                  });
                  setSelectedNode(null);

                  // Show guidance
                  Alert.alert(
                    "Criar Conexão",
                    "Selecione o nó de origem para iniciar a conexão.",
                    [
                      { text: "Entendi", style: "default" }
                    ]
                  );
                }}
                variant={uiState.isAddingConnection ? "primary" : "outline"}
                size="small"
                icon={GitBranch}
                style={{ flex: 1 }}
              />
            </View>

          </View>
        )}

        {/* Add node form */}
        {uiState.isAddingNode && (
          <GlassCard style={styles.addNodeContainer}>
            <View style={styles.addNodeHeader}>
              <Text style={styles.addNodeTitle}>Adicionar Novo Nó</Text>
              <Pressable
                style={styles.closeButton}
                onPress={handleCancelNodeCreation}
              >
                <X size={20} color={colors.text} />
              </Pressable>
            </View>

            <TextInput
              style={styles.addNodeInput}
              placeholder="Digite o texto do nó"
              value={newNodeText}
              onChangeText={setNewNodeText}
              autoFocus
            />

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Cor:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorPicker}>
                {nodeColors.map((color) => (
                  <Pressable
                    key={color}
                    style={[
                      styles.colorOption,
                      { backgroundColor: color },
                      selectedColor === color && styles.selectedColorOption,
                    ]}
                    onPress={() => setSelectedColor(color)}
                  />
                ))}
              </ScrollView>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Forma:</Text>
              <View style={styles.shapePicker}>
                {nodeShapes.map((shape) => (
                  <Pressable
                    key={shape}
                    style={[
                      styles.shapeOption,
                      selectedShape === shape && styles.selectedShapeOption,
                    ]}
                    onPress={() => setSelectedShape(shape)}
                  >
                    <NodeShapeComponent
                      shape={shape}
                      size={24}
                      color={selectedShape === shape ? colors.primary : colors.textLight}
                    />
                  </Pressable>
                ))}
              </View>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Estilo:</Text>
              <View style={styles.stylePicker}>
                <Pressable
                  style={[
                    styles.styleOption,
                    selectedStyle === 'filled' && styles.selectedStyleOption,
                  ]}
                  onPress={() => setSelectedStyle('filled')}
                >
                  <Text style={styles.styleOptionText}>Preenchido</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.styleOption,
                    selectedStyle === 'outline' && styles.selectedStyleOption,
                  ]}
                  onPress={() => setSelectedStyle('outline')}
                >
                  <Text style={styles.styleOptionText}>Contorno</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.styleOption,
                    selectedStyle === 'minimal' && styles.selectedStyleOption,
                  ]}
                  onPress={() => setSelectedStyle('minimal')}
                >
                  <Text style={styles.styleOptionText}>Mínimo</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Tamanho:</Text>
              <View style={styles.sizePicker}>
                <Pressable
                  style={[
                    styles.sizeOption,
                    nodeSize === 'small' && styles.selectedSizeOption,
                  ]}
                  onPress={() => setNodeSize('small')}
                >
                  <Text style={styles.sizeOptionText}>P</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.sizeOption,
                    nodeSize === 'medium' && styles.selectedSizeOption,
                  ]}
                  onPress={() => setNodeSize('medium')}
                >
                  <Text style={styles.sizeOptionText}>M</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.sizeOption,
                    nodeSize === 'large' && styles.selectedSizeOption,
                  ]}
                  onPress={() => setNodeSize('large')}
                >
                  <Text style={styles.sizeOptionText}>G</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.addNodeButtons}>
              <Button
                title="Posicionar"
                onPress={handleStartNodePlacement}
                variant="outline"
                size="medium"
                icon={Move}
              />
              <Button
                title="Adicionar"
                onPress={handleAddNode}
                variant="primary"
                size="medium"
                icon={Plus}
              />
            </View>
          </GlassCard>
        )}

        {/* Node styler */}
        {uiState.showNodeStyler && selectedNode && (
          <GlassCard style={styles.nodeStylerContainer}>
            <View style={styles.nodeStylerHeader}>
              <Text style={styles.nodeStylerTitle}>Estilo do Nó</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => updateUiState({ showNodeStyler: false })}
              >
                <X size={20} color={colors.text} />
              </Pressable>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Texto:</Text>
              <TextInput
                style={styles.nodeStylerInput}
                value={selectedNode.text}
                onChangeText={handleUpdateNode}
              />
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Cor:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorPicker}>
                {nodeColors.map((color) => (
                  <Pressable
                    key={color}
                    style={[
                      styles.colorOption,
                      { backgroundColor: color },
                      selectedColor === color && styles.selectedColorOption,
                    ]}
                    onPress={() => setSelectedColor(color)}
                  />
                ))}
              </ScrollView>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Forma:</Text>
              <View style={styles.shapePicker}>
                {nodeShapes.map((shape) => (
                  <Pressable
                    key={shape}
                    style={[
                      styles.shapeOption,
                      selectedShape === shape && styles.selectedShapeOption,
                    ]}
                    onPress={() => setSelectedShape(shape)}
                  >
                    <NodeShapeComponent
                      shape={shape}
                      size={24}
                      color={selectedShape === shape ? colors.primary : colors.textLight}
                    />
                  </Pressable>
                ))}
              </View>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Estilo:</Text>
              <View style={styles.stylePicker}>
                <Pressable
                  style={[
                    styles.styleOption,
                    selectedStyle === 'filled' && styles.selectedStyleOption,
                  ]}
                  onPress={() => setSelectedStyle('filled')}
                >
                  <Text style={styles.styleOptionText}>Preenchido</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.styleOption,
                    selectedStyle === 'outline' && styles.selectedStyleOption,
                  ]}
                  onPress={() => setSelectedStyle('outline')}
                >
                  <Text style={styles.styleOptionText}>Contorno</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.styleOption,
                    selectedStyle === 'minimal' && styles.selectedStyleOption,
                  ]}
                  onPress={() => setSelectedStyle('minimal')}
                >
                  <Text style={styles.styleOptionText}>Mínimo</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.nodeStylerSection}>
              <Text style={styles.nodeStylerLabel}>Tamanho:</Text>
              <View style={styles.sizePicker}>
                <Pressable
                  style={[
                    styles.sizeOption,
                    nodeSize === 'small' && styles.selectedSizeOption,
                  ]}
                  onPress={() => setNodeSize('small')}
                >
                  <Text style={styles.sizeOptionText}>P</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.sizeOption,
                    nodeSize === 'medium' && styles.selectedSizeOption,
                  ]}
                  onPress={() => setNodeSize('medium')}
                >
                  <Text style={styles.sizeOptionText}>M</Text>
                </Pressable>
                <Pressable
                  style={[
                    styles.sizeOption,
                    nodeSize === 'large' && styles.selectedSizeOption,
                  ]}
                  onPress={() => setNodeSize('large')}
                >
                  <Text style={styles.sizeOptionText}>G</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.nodeStylerButtons}>
              <Button
                title="Cancelar"
                onPress={() => updateUiState({ showNodeStyler: false })}
                variant="outline"
                size="medium"
              />
              <Button
                title="Aplicar"
                onPress={handleUpdateNodeStyle}
                variant="primary"
                size="medium"
              />
            </View>
          </GlassCard>
        )}

        {/* Connection styler */}
        {uiState.showConnectionStyler && selectedConnectionId && (
          <GlassCard style={styles.connectionStylerContainer}>
            <View style={styles.connectionStylerHeader}>
              <Text style={styles.connectionStylerTitle}>Estilo da Conexão</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => {
                  updateUiState({ showConnectionStyler: false });
                  setSelectedConnectionId(null);
                }}
              >
                <X size={20} color={colors.text} />
              </Pressable>
            </View>

            <View style={styles.connectionStylerSection}>
              <Text style={styles.connectionStylerLabel}>Cor:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorPicker}>
                {nodeColors.map((color) => (
                  <Pressable
                    key={color}
                    style={[
                      styles.colorOption,
                      { backgroundColor: color },
                      selectedConnectionColor === color && styles.selectedColorOption,
                    ]}
                    onPress={() => setSelectedConnectionColor(color)}
                  />
                ))}
              </ScrollView>
            </View>

            <View style={styles.connectionStylerSection}>
              <Text style={styles.connectionStylerLabel}>Espessura:</Text>
              <View style={styles.widthPicker}>
                <Pressable
                  style={[
                    styles.widthOption,
                    selectedConnectionWidth === 1 && styles.selectedWidthOption,
                  ]}
                  onPress={() => setSelectedConnectionWidth(1)}
                >
                  <View style={[styles.widthLine, { height: 1 }]} />
                </Pressable>
                <Pressable
                  style={[
                    styles.widthOption,
                    selectedConnectionWidth === 2 && styles.selectedWidthOption,
                  ]}
                  onPress={() => setSelectedConnectionWidth(2)}
                >
                  <View style={[styles.widthLine, { height: 2 }]} />
                </Pressable>
                <Pressable
                  style={[
                    styles.widthOption,
                    selectedConnectionWidth === 3 && styles.selectedWidthOption,
                  ]}
                  onPress={() => setSelectedConnectionWidth(3)}
                >
                  <View style={[styles.widthLine, { height: 3 }]} />
                </Pressable>
                <Pressable
                  style={[
                    styles.widthOption,
                    selectedConnectionWidth === 4 && styles.selectedWidthOption,
                  ]}
                  onPress={() => setSelectedConnectionWidth(4)}
                >
                  <View style={[styles.widthLine, { height: 4 }]} />
                </Pressable>
              </View>
            </View>

            <View style={styles.connectionStylerSection}>
              <Text style={styles.connectionStylerLabel}>Estilo:</Text>
              <View style={styles.lineStylePicker}>
                <Pressable
                  style={[
                    styles.lineStyleOption,
                    selectedConnectionStyle === 'solid' && styles.selectedLineStyleOption,
                  ]}
                  onPress={() => setSelectedConnectionStyle('solid')}
                >
                  <View style={styles.solidLine} />
                </Pressable>
                <Pressable
                  style={[
                    styles.lineStyleOption,
                    selectedConnectionStyle === 'dashed' && styles.selectedLineStyleOption,
                  ]}
                  onPress={() => setSelectedConnectionStyle('dashed')}
                >
                  <View style={styles.dashedLine} />
                </Pressable>
                <Pressable
                  style={[
                    styles.lineStyleOption,
                    selectedConnectionStyle === 'dotted' && styles.selectedLineStyleOption,
                  ]}
                  onPress={() => setSelectedConnectionStyle('dotted')}
                >
                  <View style={styles.dottedLine} />
                </Pressable>
              </View>
            </View>

            <View style={styles.connectionStylerButtons}>
              <Button
                title="Excluir"
                onPress={() => {
                  handleDeleteConnection(selectedConnectionId);
                  updateUiState({ showConnectionStyler: false });
                }}
                variant="error"
                size="medium"
                icon={Trash2}
              />
              <Button
                title="Aplicar"
                onPress={handleUpdateConnectionStyle}
                variant="primary"
                size="medium"
              />
            </View>
          </GlassCard>
        )}

        {/* Connection help */}
        {uiState.isAddingConnection && (
          <GlassCard style={styles.connectionHelpContainer}>
            <View style={styles.connectionHelpContent}>
              <BrainCircuit size={24} color={colors.primary} />
              <Text style={styles.connectionHelpText}>
                {connectionStart
                  ? "Agora selecione o nó de destino"
                  : "Selecione o nó de origem"}
              </Text>
            </View>
            <Button
              title="Cancelar"
              onPress={handleCancelConnectionCreation}
              variant="outline"
              size="medium"
            />
          </GlassCard>
        )}

        {/* Edit node */}
        {selectedNode && !uiState.showNodeStyler && !uiState.isMovingNode && (
          <GlassCard style={styles.editNodeContainer}>
            <View style={styles.editNodeHeader}>
              <Text style={styles.editNodeTitle}>Editar Nó</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => setSelectedNode(null)}
              >
                <X size={20} color={colors.text} />
              </Pressable>
            </View>

            <TextInput
              style={styles.editNodeInput}
              placeholder="Editar texto do nó"
              value={selectedNode.text}
              onChangeText={handleUpdateNode}
              autoFocus
            />
            <View style={styles.editNodeButtons}>
              <Button
                title="Estilo"
                onPress={() => updateUiState({ showNodeStyler: true })}
                variant="outline"
                size="medium"
                icon={Palette}
              />
              <Button
                title="Duplicar"
                onPress={handleDuplicateNode}
                variant="outline"
                size="medium"
                icon={Copy}
              />
              <Button
                title="Excluir"
                onPress={handleDeleteNode}
                variant="error"
                size="medium"
                icon={Trash2}
              />
            </View>
          </GlassCard>
        )}

        {/* Tutorial overlay */}
        {uiState.showTutorial && (
          <View style={styles.tutorialOverlay}>
            <GlassCard style={styles.tutorialCard}>
              <View style={styles.tutorialHeader}>
                <Text style={styles.tutorialTitle}>Como Criar um Mapa Mental</Text>
                <Pressable
                  style={styles.closeButton}
                  onPress={() => updateUiState({ showTutorial: false })}
                >
                  <X size={20} color={colors.text} />
                </Pressable>
              </View>

              <ScrollView style={styles.tutorialContent}>
                <View style={styles.tutorialStep}>
                  <View style={styles.tutorialStepNumber}>
                    <Text style={styles.tutorialStepNumberText}>1</Text>
                  </View>
                  <View style={styles.tutorialStepContent}>
                    <Text style={styles.tutorialStepTitle}>Adicionar Nós</Text>
                    <Text style={styles.tutorialStepText}>
                      Toque no botão "Nó" para adicionar um novo nó ao mapa. Digite o texto e escolha o estilo.
                    </Text>
                  </View>
                </View>

                <View style={styles.tutorialStep}>
                  <View style={styles.tutorialStepNumber}>
                    <Text style={styles.tutorialStepNumberText}>2</Text>
                  </View>
                  <View style={styles.tutorialStepContent}>
                    <Text style={styles.tutorialStepTitle}>Posicionar Nós</Text>
                    <Text style={styles.tutorialStepText}>
                      Você pode posicionar o nó manualmente ou usar o botão "Posicionar" para arrastar e soltar.
                    </Text>
                  </View>
                </View>

                <View style={styles.tutorialStep}>
                  <View style={styles.tutorialStepNumber}>
                    <Text style={styles.tutorialStepNumberText}>3</Text>
                  </View>
                  <View style={styles.tutorialStepContent}>
                    <Text style={styles.tutorialStepTitle}>Conectar Nós</Text>
                    <Text style={styles.tutorialStepText}>
                      Toque no botão "Conexão", selecione o nó de origem e depois o nó de destino para criar uma conexão.
                    </Text>
                  </View>
                </View>

                <View style={styles.tutorialStep}>
                  <View style={styles.tutorialStepNumber}>
                    <Text style={styles.tutorialStepNumberText}>4</Text>
                  </View>
                  <View style={styles.tutorialStepContent}>
                    <Text style={styles.tutorialStepTitle}>Editar Elementos</Text>
                    <Text style={styles.tutorialStepText}>
                      Toque em qualquer nó ou conexão para editar suas propriedades, como cor, forma e estilo.
                    </Text>
                  </View>
                </View>

                <View style={styles.tutorialStep}>
                  <View style={styles.tutorialStepNumber}>
                    <Text style={styles.tutorialStepNumberText}>5</Text>
                  </View>
                  <View style={styles.tutorialStepContent}>
                    <Text style={styles.tutorialStepTitle}>Navegar no Mapa</Text>
                    <Text style={styles.tutorialStepText}>
                      Arraste para mover o mapa. Use os botões de zoom ou o gesto de pinça com dois dedos para ampliar ou reduzir a visualização.
                    </Text>
                  </View>
                </View>
              </ScrollView>

              <View style={styles.tutorialButtons}>
                <Button
                  title="Não mostrar novamente"
                  onPress={() => updateUiState({ showTutorial: false })}
                  variant="outline"
                  size="medium"
                />
                <Button
                  title="Entendi"
                  onPress={() => updateUiState({ showTutorial: false })}
                  variant="primary"
                  size="medium"
                />
              </View>
            </GlassCard>
          </View>
        )}

        {/* Node placement instructions */}
        {uiState.nodePlacementMode && (
          <GlassCard style={styles.placementInstructionsContainer}>
            <View style={styles.placementInstructionsContent}>
              <Move size={24} color={colors.primary} />
              <Text style={styles.placementInstructionsText}>
                Arraste para posicionar o nó e toque em "Adicionar" para confirmar
              </Text>
            </View>
            <View style={styles.placementInstructionsButtons}>
              <Button
                title="Cancelar"
                onPress={() => {
                  updateUiState({
                    nodePlacementMode: false,
                    showNodePreview: false
                  });
                }}
                variant="outline"
                size="medium"
              />
              <Button
                title="Adicionar"
                onPress={handleAddNode}
                variant="primary"
                size="medium"
              />
            </View>
          </GlassCard>
        )}
      </View>
    </SafeAreaView>
    </RouteGuard>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  mapContainer: {
    flex: 1,
    position: "relative",
  },
  canvasContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  mobileMapView: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  treeViewContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
    position: "relative",
  },
  reactFlowContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: colors.backgroundLight,
  },
  headerButtons: {
    flexDirection: "row",
  },
  editButton: {
    padding: 8,
  },
  nodeContainer: {
    position: "absolute",
    minWidth: 100,
    minHeight: 50,
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    zIndex: 10,
  },
  triangleShape: {
    position: 'absolute',
    width: 0,
    height: 0,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderStyle: 'solid',
  },
  hexagonShape: {
    position: 'absolute',
    borderRadius: 0,
  },
  selectedNode: {
    borderWidth: 2,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
    elevation: 5,
  },
  connectionStartNode: {
    borderWidth: 3,
    shadowColor: colors.secondary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
    elevation: 5,
  },
  nodeText: {
    fontSize: 14,
    textAlign: "center",
    color: colors.text,
  },
  nodeActions: {
    position: "absolute",
    top: -15,
    right: -15,
    flexDirection: "row",
    gap: 4,
  },
  nodeActionButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.card,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  connectionContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    zIndex: 5,
  },
  connectionLine: {
    height: 2,
    backgroundColor: colors.primary,
    position: "absolute",
    transformOrigin: "left center",
  },
  arrowHead: {
    position: "absolute",
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderBottomWidth: 12,
    borderStyle: "solid",
    backgroundColor: "transparent",
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: colors.primary,
    transformOrigin: "center bottom",
  },
  connectionDeleteButton: {
    position: "absolute",
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.error,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 20,
  },
  editToolbar: {
    position: "absolute",
    bottom: 30,
    left: 16,
    right: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: colors.card,
    borderRadius: 20,
    padding: 8,
    paddingHorizontal: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 100,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  webEditToolbar: {
    flexDirection: "row",
    gap: 8,
    padding: 12,
    backgroundColor: colors.card,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  toolbarButton: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  zoomControls: {
    position: "absolute",
    top: 16,
    right: 16,
    flexDirection: "column",
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 8,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    zIndex: 100,
  },
  zoomButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  addNodeContainer: {
    position: "absolute",
    bottom: 90,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 100,
  },
  addNodeHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  addNodeTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  webAddNodeContainer: {
    position: "absolute",
    top: 80,
    right: 16,
    width: 300,
    padding: 16,
    zIndex: 1000,
    backgroundColor: colors.card,
  },
  addNodeInput: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
    fontSize: 16,
  },
  addNodeHint: {
    fontSize: 12,
    color: colors.textLight,
    marginBottom: 12,
    fontStyle: "italic",
  },
  addNodeButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 8,
    marginTop: 8,
  },
  nodeStylerContainer: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 100,
  },
  nodeStylerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  nodeStylerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
  },
  nodeStylerSection: {
    marginBottom: 16,
  },
  nodeStylerLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  nodeStylerInput: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
    fontSize: 16,
  },
  colorPicker: {
    flexDirection: "row",
    marginBottom: 8,
  },
  colorOption: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: colors.text,
    transform: [{ scale: 1.1 }],
  },
  shapePicker: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  shapeOption: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedShapeOption: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}10`,
  },
  stylePicker: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  styleOption: {
    flex: 1,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
  },
  selectedStyleOption: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}10`,
  },
  styleOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  sizePicker: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  sizeOption: {
    flex: 1,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
  },
  selectedSizeOption: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}10`,
  },
  sizeOptionText: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
  },
  nodeStylerButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 8,
    marginTop: 8,
  },
  connectionStylerContainer: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 100,
  },
  connectionStylerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  connectionStylerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  connectionStylerSection: {
    marginBottom: 16,
  },
  connectionStylerLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  widthPicker: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  widthOption: {
    width: 60,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
  },
  selectedWidthOption: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}10`,
  },
  widthLine: {
    width: 30,
    backgroundColor: colors.text,
  },
  lineStylePicker: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  lineStyleOption: {
    flex: 1,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
  },
  selectedLineStyleOption: {
    borderColor: colors.primary,
    backgroundColor: `${colors.primary}10`,
  },
  solidLine: {
    width: 30,
    height: 2,
    backgroundColor: colors.text,
  },
  dashedLine: {
    width: 30,
    height: 2,
    backgroundColor: colors.text,
    borderStyle: "dashed",
    borderWidth: 1,
    borderColor: colors.text,
  },
  dottedLine: {
    width: 30,
    height: 2,
    backgroundColor: colors.text,
    borderStyle: "dotted",
    borderWidth: 1,
    borderColor: colors.text,
  },
  connectionStylerButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  editNodeContainer: {
    position: "absolute",
    bottom: 90,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 100,
  },
  editNodeHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  editNodeTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  editNodeInput: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
    fontSize: 16,
  },
  editNodeButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 8,
  },
  connectionHelpContainer: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    padding: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    zIndex: 100,
  },
  connectionHelpContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 8,
  },
  connectionHelpText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  editDetailsContainer: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 1000,
  },
  editDetailsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  editDetailsInput: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
    fontSize: 16,
  },
  editDetailsTextarea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  editDetailsButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 8,
    marginTop: 8,
  },
  settingsContainer: {
    position: "absolute",
    top: 80,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 1000,
  },
  settingsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  settingsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingLabel: {
    fontSize: 16,
    color: colors.text,
  },
  toggleButton: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.backgroundDark,
    padding: 2,
  },
  toggleButtonActive: {
    backgroundColor: colors.primary,
  },
  toggleIndicator: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: colors.white,
  },
  toggleIndicatorActive: {
    transform: [{ translateX: 20 }],
  },
  settingsButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 16,
  },
  tutorialOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  tutorialCard: {
    width: "90%",
    maxHeight: "80%",
    padding: 20,
    borderRadius: 20,
  },
  tutorialHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  tutorialTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
  },
  tutorialContent: {
    maxHeight: 400,
  },
  tutorialStep: {
    flexDirection: "row",
    marginBottom: 16,
  },
  tutorialStepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  tutorialStepNumberText: {
    color: colors.white,
    fontWeight: "700",
    fontSize: 16,
  },
  tutorialStepContent: {
    flex: 1,
  },
  tutorialStepTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  tutorialStepText: {
    fontSize: 14,
    color: colors.textLight,
    lineHeight: 20,
  },
  tutorialButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  placementInstructionsContainer: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    padding: 16,
    zIndex: 100,
  },
  placementInstructionsContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    gap: 12,
  },
  placementInstructionsText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  placementInstructionsButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  errorMessage: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
});

// Export the memoized component
export default MindMapScreen;