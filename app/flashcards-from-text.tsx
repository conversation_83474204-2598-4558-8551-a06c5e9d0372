import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TextInput,
  Pressable,
  ActivityIndicator,
  Alert,
  Keyboard,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";
import { Button } from "@/components/Button";
import { useStudyStore } from "@/store/studyStore";
import { useUserStore } from "@/store/userStore";
import { generateChatCompletion, hasApiKey } from "@/services/openai";
import { APIKeyModal } from "@/components/APIKeyModal";
import {
  Sparkles,
  BookOpen,
  Check,
  X,
  Edit,
  Plus,
  Trash,
} from "lucide-react-native";

export default function FlashcardsFromTextScreen() {
  const params = useLocalSearchParams<{ text: string }>();
  const router = useRouter();
  const { addFlashcardSet, addFlashcard } = useStudyStore();
  const { addXP } = useUserStore();

  const [text, setText] = useState(params.text || "");
  const [setTitle, setSetTitle] = useState("");
  const [subject, setSubject] = useState("");
  const [flashcards, setFlashcards] = useState<Array<{ question: string; answer: string }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editQuestion, setEditQuestion] = useState("");
  const [editAnswer, setEditAnswer] = useState("");

  useEffect(() => {
    if (params.text) {
      setText(params.text);
    }
  }, [params.text]);

  const handleGenerate = async () => {
    if (!text.trim()) {
      Alert.alert("Erro", "Por favor, insira um texto para gerar flashcards.");
      return;
    }

    const hasKey = await hasApiKey();
    if (!hasKey) {
      setShowApiKeyModal(true);
      return;
    }

    setIsLoading(true);
    try {
      const prompt = `Analise o seguinte texto e crie 5 flashcards de estudo com perguntas e respostas.
      As perguntas devem ser claras e específicas, e as respostas devem ser concisas mas informativas.
      Formate a resposta como um array JSON de objetos com as propriedades "question" e "answer".

      Texto: "${text.substring(0, 3000)}"`;

      const messages = [
        {
          role: "system",
          content:
            "Você é um assistente educacional especializado em criar flashcards de alta qualidade para estudo. Suas perguntas são claras e específicas, e suas respostas são concisas mas informativas.",
        },
        { role: "user", content: prompt },
      ];

      const response = await generateChatCompletion(messages);

      // Extract JSON from response
      const jsonMatch = response?.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const generatedFlashcards = JSON.parse(jsonMatch[0]);
        setFlashcards(generatedFlashcards);

        // Generate a title if none exists
        if (!setTitle) {
          const titlePrompt = `Baseado no texto fornecido, sugira um título curto e descritivo para um conjunto de flashcards de estudo. Responda apenas com o título, sem explicações adicionais.

          Texto: "${text.substring(0, 500)}"`;

          const titleResponse = await generateChatCompletion([
            { role: "system", content: "Você gera títulos concisos e descritivos." },
            { role: "user", content: titlePrompt }
          ]);

          setSetTitle(titleResponse || "Novo Conjunto de Flashcards");
        }
      } else {
        throw new Error("Não foi possível extrair flashcards da resposta");
      }
    } catch (error) {
      console.error("Error generating flashcards:", error);
      Alert.alert(
        "Erro",
        "Ocorreu um erro ao gerar os flashcards. Por favor, verifique sua conexão e tente novamente."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = () => {
    if (flashcards.length === 0) {
      Alert.alert("Erro", "Gere pelo menos um flashcard antes de salvar.");
      return;
    }

    if (!setTitle.trim()) {
      Alert.alert("Erro", "Por favor, insira um título para o conjunto de flashcards.");
      return;
    }

    // Dismiss keyboard before proceeding
    Keyboard.dismiss();

    // Create a new flashcard set
    const newSetId = `set_${Date.now()}`;
    const newSet = {
      id: newSetId,
      title: setTitle,
      description: `Flashcards gerados a partir de texto sobre ${subject || "diversos temas"}`,
      subject: subject || "Geral",
      totalCards: flashcards.length,
      mastered: 0,
      lastStudied: null,
      color: colors.primary,
    };

    // Add the set
    addFlashcardSet(newSet);

    // Add each flashcard
    flashcards.forEach((card, index) => {
      const flashcard = {
        id: `${newSetId}_card_${index}`,
        question: card.question,
        answer: card.answer,
        difficulty: "medium" as const,
        lastReviewed: null,
        nextReview: null,
        aiGenerated: true,
      };

      addFlashcard(flashcard);
    });

    // Add XP for creating flashcards
    addXP(20);

    // Navigate to the new set
    Alert.alert(
      "Sucesso",
      "Conjunto de flashcards criado com sucesso!",
      [{ text: "OK", onPress: () => router.push(`/flashcards/${newSetId}`) }]
    );
  };

  const handleAddFlashcard = () => {
    setFlashcards([...flashcards, { question: "", answer: "" }]);
    setEditingIndex(flashcards.length);
    setEditQuestion("");
    setEditAnswer("");
  };

  const handleEditFlashcard = (index: number) => {
    setEditingIndex(index);
    setEditQuestion(flashcards[index].question);
    setEditAnswer(flashcards[index].answer);
  };

  const handleSaveEdit = () => {
    if (editingIndex === null) return;

    if (!editQuestion.trim() || !editAnswer.trim()) {
      Alert.alert("Erro", "Pergunta e resposta não podem estar vazias.");
      return;
    }

    // Dismiss keyboard before proceeding
    Keyboard.dismiss();

    const updatedFlashcards = [...flashcards];
    updatedFlashcards[editingIndex] = {
      question: editQuestion,
      answer: editAnswer,
    };

    setFlashcards(updatedFlashcards);
    setEditingIndex(null);
  };

  const handleCancelEdit = () => {
    Keyboard.dismiss();
    setEditingIndex(null);
  };

  const handleDeleteFlashcard = (index: number) => {
    const updatedFlashcards = [...flashcards];
    updatedFlashcards.splice(index, 1);
    setFlashcards(updatedFlashcards);
  };

  const handleApiKeySuccess = () => {
    setShowApiKeyModal(false);
    handleGenerate();
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Stack.Screen
          options={{
            title: "Flashcards a partir de Texto",
            headerShown: true,
          }}
        />
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{flex: 1}}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
        <View style={styles.headerSection}>
          <GlassCard style={styles.headerCard} gradient>
            <View style={styles.headerIconContainer}>
              <BookOpen size={24} color={colors.primary} />
            </View>
            <Text style={styles.headerTitle}>Gerar Flashcards a partir de Texto</Text>
            <Text style={styles.headerText}>
              Transforme textos em flashcards de estudo automaticamente com a ajuda da IA.
            </Text>
          </GlassCard>
        </View>

        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>Texto</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Cole ou digite o texto aqui..."
            value={text}
            onChangeText={setText}
            multiline
            numberOfLines={8}
          />

          <Button
            title={isLoading ? "Gerando..." : "Gerar Flashcards"}
            onPress={handleGenerate}
            variant="primary"
            disabled={isLoading || !text.trim()}
            icon={isLoading ? undefined : Sparkles}
            style={styles.generateButton}
          />
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Gerando flashcards...</Text>
          </View>
        ) : flashcards.length > 0 ? (
          <View style={styles.resultSection}>
            <Text style={styles.sectionTitle}>Flashcards Gerados</Text>

            <View style={styles.setInfoContainer}>
              <Text style={styles.inputLabel}>Título do Conjunto</Text>
              <TextInput
                style={styles.input}
                placeholder="Digite um título para o conjunto..."
                value={setTitle}
                onChangeText={setSetTitle}
              />

              <Text style={styles.inputLabel}>Matéria/Assunto (opcional)</Text>
              <TextInput
                style={styles.input}
                placeholder="Ex: Biologia, História, Matemática..."
                value={subject}
                onChangeText={setSubject}
              />
            </View>

            {flashcards.map((card, index) => (
              <GlassCard key={index} style={styles.flashcardCard}>
                {editingIndex === index ? (
                  <View style={styles.editContainer}>
                    <Text style={styles.editLabel}>Pergunta:</Text>
                    <TextInput
                      style={styles.editInput}
                      value={editQuestion}
                      onChangeText={setEditQuestion}
                      multiline
                      autoFocus
                    />

                    <Text style={styles.editLabel}>Resposta:</Text>
                    <TextInput
                      style={styles.editInput}
                      value={editAnswer}
                      onChangeText={setEditAnswer}
                      multiline
                    />

                    <View style={styles.editActions}>
                      <Button
                        title="Cancelar"
                        onPress={handleCancelEdit}
                        variant="outline"
                        size="small"
                        icon={X}
                        style={styles.editButton}
                      />
                      <Button
                        title="Salvar"
                        onPress={handleSaveEdit}
                        variant="primary"
                        size="small"
                        icon={Check}
                        style={styles.editButton}
                      />
                    </View>
                  </View>
                ) : (
                  <>
                    <View style={styles.flashcardHeader}>
                      <Text style={styles.flashcardNumber}>Flashcard {index + 1}</Text>
                      <View style={styles.flashcardActions}>
                        <Pressable
                          style={styles.actionButton}
                          onPress={() => handleEditFlashcard(index)}
                        >
                          <Edit size={16} color={colors.text} />
                        </Pressable>
                        <Pressable
                          style={styles.actionButton}
                          onPress={() => handleDeleteFlashcard(index)}
                        >
                          <Trash size={16} color={colors.danger} />
                        </Pressable>
                      </View>
                    </View>

                    <View style={styles.flashcardContent}>
                      <View style={styles.questionContainer}>
                        <Text style={styles.questionLabel}>Pergunta:</Text>
                        <Text style={styles.questionText}>{card.question}</Text>
                      </View>

                      <View style={styles.answerContainer}>
                        <Text style={styles.answerLabel}>Resposta:</Text>
                        <Text style={styles.answerText}>{card.answer}</Text>
                      </View>
                    </View>
                  </>
                )}
              </GlassCard>
            ))}

            <View style={styles.actionButtons}>
              <Button
                title="Adicionar Flashcard"
                onPress={handleAddFlashcard}
                variant="outline"
                icon={Plus}
                style={styles.addButton}
              />
              <Button
                title="Salvar Conjunto"
                onPress={handleSave}
                variant="primary"
                icon={Check}
                style={styles.saveButton}
              />
            </View>
          </View>
        ) : null}
          </ScrollView>
        </KeyboardAvoidingView>

        <APIKeyModal
          visible={showApiKeyModal}
          onClose={() => setShowApiKeyModal(false)}
          onSuccess={handleApiKeySuccess}
        />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  headerSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerCard: {
    padding: 20,
  },
  headerIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}20`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
  },
  headerText: {
    fontSize: 16,
    color: colors.textLight,
    lineHeight: 22,
  },
  inputSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.text,
    minHeight: 150,
    textAlignVertical: "top",
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  generateButton: {
    marginTop: 8,
  },
  loadingContainer: {
    padding: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
  },
  resultSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  setInfoContainer: {
    marginBottom: 16,
  },
  flashcardCard: {
    padding: 16,
    marginBottom: 16,
  },
  flashcardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  flashcardNumber: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.primary,
  },
  flashcardActions: {
    flexDirection: "row",
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  flashcardContent: {
    gap: 12,
  },
  questionContainer: {
    marginBottom: 12,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textLight,
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 22,
    fontWeight: "500",
  },
  answerContainer: {
    backgroundColor: `${colors.primary}15`,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.primary,
    marginBottom: 4,
  },
  answerText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 22,
    fontWeight: "500",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  addButton: {
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
  },
  editContainer: {
    padding: 8,
  },
  editLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 4,
  },
  editInput: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    minHeight: 60,
    textAlignVertical: "top",
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  editActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 8,
  },
  editButton: {
    minWidth: 100,
    marginLeft: 8,
  },
});
