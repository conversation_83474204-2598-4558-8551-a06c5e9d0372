import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Pressable,
  Alert,
  TextInput,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { useL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { useSeparatorStore } from "@/store/separatorStore";
import { useMindMapStore } from "@/store/mindMapStore";
import { useNoteStore } from "@/store/noteStore";
import { useQuizStore } from "@/store/quizStore";
import { ProgressBar } from "@/components/ProgressBar";
import { ActivityCard } from "@/components/ActivityCard";
import { FlashcardSetCard } from "@/components/FlashcardSetCard";
import { MindMapCard } from "@/components/MindMapCard";
import { NoteCard } from "@/components/NoteCard";
import { QuizCard } from "@/components/QuizCard";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
// Importando o componente RouteGuard
import RouteGuard from '@/components/RouteGuard';
import * as Icons from "lucide-react-native";
import { Activity, FlashcardSet, MindMap, Note, Subject } from "@/types";
import { LucideIcon, Edit, Trash2, MoreVertical } from "lucide-react-native";

export default function SubjectScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { subjects, activities, flashcardSets, updateSubject, removeSubject } = useStudyStore();
  const { separators } = useSeparatorStore();
  const { mindMaps } = useMindMapStore();
  const { notes } = useNoteStore();
  const { quizzes, fetchQuizzes } = useQuizStore();

  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [editedSubject, setEditedSubject] = useState<Partial<Subject>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Carregar quizzes quando o componente for montado
  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchQuizzes();
      } catch (error) {
        console.error('Erro ao carregar quizzes:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [fetchQuizzes]);

  const subject = subjects.find((s) => s.id === id);
  const subjectActivities = activities.filter((a) => a.subject === subject?.title);
  const subjectFlashcardSets = flashcardSets.filter((s) => s.subject === subject?.title);
  const subjectMindMaps = mindMaps.filter((m) => m.subject === subject?.title);
  const subjectNotes = notes.filter((n) => n.subject === subject?.title);
  const subjectQuizzes = quizzes.filter((q) => q.subject === subject?.title);

  // Get the icon component from the subject's icon name
  const IconComponent = subject?.icon ?
    (Icons[subject.icon as keyof typeof Icons] as LucideIcon) :
    Icons.Book;

  const handleActivityPress = (activity: Activity) => {
    if (activity.type === "flashcards") {
      router.push(`/flashcards/${activity.id}`);
    } else if (activity.type === "quiz") {
      router.push(`/quiz/${activity.id}`);
    } else if (activity.type === "chat") {
      router.push("/chat");
    } else if (activity.type === "mindmap") {
      router.push(`/mind-map/${activity.id}`);
    }
  };

  const handleFlashcardSetPress = (set: FlashcardSet) => {
    router.push(`/flashcards/${set.id}`);
  };

  const handleMindMapPress = (mindMap: MindMap) => {
    router.push(`/mind-map/${mindMap.id}`);
  };

  const handleNotePress = (note: Note) => {
    router.push(`/notes/${note.id}`);
  };

  const handleEditSubject = () => {
    if (!subject) return;

    setEditedSubject({
      title: subject.title,
      description: subject.description,
      icon: subject.icon,
      color: subject.color,
      separator_id: subject.separator_id,
    });
    setIsMenuVisible(false);
    setIsEditModalVisible(true);
  };

  const handleSaveSubject = async () => {
    if (!subject || !editedSubject.title) return;

    try {
      await updateSubject(subject.id, editedSubject);
      setIsEditModalVisible(false);
      Alert.alert("Sucesso", "Matéria atualizada com sucesso!");
    } catch (error) {
      Alert.alert("Erro", "Não foi possível atualizar a matéria.");
    }
  };

  const handleDeleteSubject = () => {
    if (!subject) return;

    Alert.alert(
      "Excluir Matéria",
      `Tem certeza que deseja excluir a matéria ${subject.title}? Esta ação não pode ser desfeita.`,
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            try {
              await removeSubject(subject.id);
              router.replace("/");
            } catch (error) {
              Alert.alert("Erro", "Não foi possível excluir a matéria.");
            }
          }
        },
      ]
    );
    setIsMenuVisible(false);
  };

  const handleCreateContent = (type: string) => {
    switch (type) {
      case "flashcards":
        // In a real app, this would navigate to a creation screen
        alert("Esta funcionalidade (flashcards) estará disponível em breve!");
        break;
      case "quiz":
        // Create a new quiz and navigate to it
        router.push("/quiz/create");
        break;
      case "notes":
        // Create a new note and navigate to it
        const newNote: Note = {
          id: `note_${Date.now()}`,
          title: `Nova Anotação - ${subject?.title}`,
          content: "",
          subject: subject?.title || "Geral",
          tags: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        useNoteStore.getState().addNote(newNote);
        router.push(`/notes/${newNote.id}`);
        break;
      case "mindmap":
        // Create a new mind map and navigate to it
        const newMindMap: MindMap = {
          id: `mindmap_${Date.now()}`,
          title: `Novo Mapa Mental - ${subject?.title}`,
          subject: subject?.title || "Geral",
          description: "",
          lastEdited: new Date().toISOString(),
          nodes: [
            {
              id: "node_1",
              text: subject?.title || "Novo Tema",
              x: 400,
              y: 100,
              color: "#333333",
              shape: "rectangle",
              style: "filled",
              size: "large",
            }
          ],
          connections: [],
          type: "concept",
          color: subject?.color || colors.primary,
        };

        // Adicionar o mapa mental e navegar para ele
        useMindMapStore.getState().addMindMap(newMindMap)
          .then(addedMap => {
            if (addedMap) {
              router.push(`/mind-map/${addedMap.id}`);
            } else {
              router.push(`/mind-map/${newMindMap.id}`);
            }
          })
          .catch(error => {
            console.error("Erro ao adicionar mapa mental:", error);
            router.push(`/mind-map/${newMindMap.id}`);
          });
        break;
      default:
        alert(`Esta funcionalidade (${type}) estará disponível em breve!`);
    }
  };

  if (!subject) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Matéria não encontrada" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Matéria não encontrada</Text>
          <Button
            title="Voltar para o início"
            onPress={() => router.push("/")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <RouteGuard resourceId={id} tableName="subjects">
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: subject.title,
            headerRight: () => (
              <View style={styles.headerRightContainer}>
                <Pressable
                  style={styles.menuButton}
                  onPress={() => setIsMenuVisible(!isMenuVisible)}
                >
                  <MoreVertical size={24} color={colors.text} />
                </Pressable>
                {isMenuVisible && (
                <View style={styles.menuContainer}>
                  <Pressable
                    style={styles.menuItem}
                    onPress={handleEditSubject}
                  >
                    <Edit size={20} color={colors.primary} />
                    <Text style={styles.menuItemText}>Editar</Text>
                  </Pressable>
                  <Pressable
                    style={[styles.menuItem, styles.menuItemDanger]}
                    onPress={handleDeleteSubject}
                  >
                    <Trash2 size={20} color={colors.error} />
                    <Text style={[styles.menuItemText, styles.menuItemTextDanger]}>Excluir</Text>
                  </Pressable>
                </View>
              )}
            </View>
          )
        }}
      />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      {/* Modal de edição */}
      <Modal
        visible={isEditModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsEditModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <GlassCard style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Editar Matéria</Text>

            <Text style={styles.inputLabel}>Título</Text>
            <TextInput
              style={styles.input}
              value={editedSubject.title}
              onChangeText={(text) => setEditedSubject({...editedSubject, title: text})}
              placeholder="Título da matéria"
            />

            <Text style={styles.inputLabel}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={editedSubject.description}
              onChangeText={(text) => setEditedSubject({...editedSubject, description: text})}
              placeholder="Descrição da matéria"
              multiline
            />

            {separators.length > 0 && (
              <>
                <Text style={styles.inputLabel}>Separador (opcional)</Text>
                <View style={styles.separatorContainer}>
                  <TouchableOpacity
                    style={[
                      styles.separatorOption,
                      editedSubject.separator_id === undefined && styles.selectedSeparatorOption,
                    ]}
                    onPress={() => setEditedSubject({...editedSubject, separator_id: undefined})}
                  >
                    <Text style={[
                      styles.separatorText,
                      editedSubject.separator_id === undefined && styles.selectedSeparatorText,
                    ]}>Nenhum</Text>
                  </TouchableOpacity>

                  {separators.map((separator) => (
                    <TouchableOpacity
                      key={separator.id}
                      style={[
                        styles.separatorOption,
                        { borderLeftColor: separator.color },
                        editedSubject.separator_id === separator.id && styles.selectedSeparatorOption,
                      ]}
                      onPress={() => setEditedSubject({...editedSubject, separator_id: separator.id})}
                    >
                      <Text style={[
                        styles.separatorText,
                        editedSubject.separator_id === separator.id && styles.selectedSeparatorText,
                      ]}>{separator.title}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            )}

            <View style={styles.modalButtons}>
              <Button
                title="Cancelar"
                onPress={() => setIsEditModalVisible(false)}
                variant="outline"
                size="medium"
                style={styles.modalButton}
              />
              <Button
                title="Salvar"
                onPress={handleSaveSubject}
                variant="primary"
                size="medium"
                style={styles.modalButton}
              />
            </View>
          </GlassCard>
        </View>
      </Modal>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando dados...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
        <View style={styles.headerSection}>
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: `${subject.color}20` },
            ]}
          >
            {React.createElement(IconComponent, { size: 32, color: subject.color })}
          </View>
          <Text style={styles.title}>{subject.title}</Text>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              Progresso: {subject.progress}%
            </Text>
            <ProgressBar
              progress={subject.progress}
              color={subject.color}
              height={8}
            />
          </View>
        </View>

        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Criar Conteúdo</Text>
          <View style={styles.actionsGrid}>
            <Pressable
              style={styles.actionCard}
              onPress={() => handleCreateContent("flashcards")}
            >
              <View
                style={[
                  styles.actionIconContainer,
                  { backgroundColor: `${colors.primary}20` },
                ]}
              >
                <Icons.BookOpen size={24} color={colors.primary} />
              </View>
              <Text style={styles.actionText}>Flashcards</Text>
            </Pressable>
            <Pressable
              style={styles.actionCard}
              onPress={() => handleCreateContent("quiz")}
            >
              <View
                style={[
                  styles.actionIconContainer,
                  { backgroundColor: `${colors.secondary}20` },
                ]}
              >
                <Icons.FileText size={24} color={colors.secondary} />
              </View>
              <Text style={styles.actionText}>Quiz</Text>
            </Pressable>
            <Pressable
              style={styles.actionCard}
              onPress={() => handleCreateContent("notes")}
            >
              <View
                style={[
                  styles.actionIconContainer,
                  { backgroundColor: `${colors.success}20` },
                ]}
              >
                <Icons.FileEdit size={24} color={colors.success} />
              </View>
              <Text style={styles.actionText}>Anotações</Text>
            </Pressable>
            <Pressable
              style={styles.actionCard}
              onPress={() => handleCreateContent("mindmap")}
            >
              <View
                style={[
                  styles.actionIconContainer,
                  { backgroundColor: `${colors.accent1}20` },
                ]}
              >
                <Icons.Network size={24} color={colors.accent1} />
              </View>
              <Text style={styles.actionText}>Mapa Mental</Text>
            </Pressable>
          </View>
        </View>

        {!isLoading && subjectFlashcardSets && subjectFlashcardSets.length > 0 && (
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Flashcards</Text>
            {subjectFlashcardSets.map((set) => (
              <FlashcardSetCard
                key={set.id}
                set={set}
                onPress={() => handleFlashcardSetPress(set)}
              />
            ))}
          </View>
        )}

        {!isLoading && subjectQuizzes && subjectQuizzes.length > 0 && (
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Quizzes</Text>
            {subjectQuizzes.map((quiz) => (
              <QuizCard
                key={quiz.id}
                quiz={quiz}
                onPress={() => router.push(`/quiz/${quiz.id}`)}
              />
            ))}
          </View>
        )}

        {!isLoading && subjectMindMaps && subjectMindMaps.length > 0 && (
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Mapas Mentais</Text>
            {subjectMindMaps.map((mindMap) => (
              <MindMapCard
                key={mindMap.id}
                mindMap={mindMap}
                onPress={() => handleMindMapPress(mindMap)}
              />
            ))}
          </View>
        )}

        {!isLoading && subjectNotes && subjectNotes.length > 0 && (
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Anotações</Text>
            {subjectNotes.map((note) => (
              <NoteCard
                key={note.id}
                note={note}
                onPress={() => handleNotePress(note)}
              />
            ))}
          </View>
        )}

        {!isLoading && subjectActivities && subjectActivities.length > 0 && (
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Atividades Recentes</Text>
            {subjectActivities.map((activity) => (
              <ActivityCard
                key={activity.id}
                activity={activity}
                onPress={() => handleActivityPress(activity)}
              />
            ))}
          </View>
        )}

        <View style={styles.aiSection}>
          <Text style={styles.sectionTitle}>Assistente de IA</Text>
          <GlassCard style={styles.aiCard} gradient>
            <View style={styles.aiCardContent}>
              <View style={styles.aiIconContainer}>
                <Icons.MessageSquare size={24} color={colors.primary} />
              </View>
              <View style={styles.aiTextContainer}>
                <Text style={styles.aiTitle}>Tire suas dúvidas</Text>
                <Text style={styles.aiDescription}>
                  Converse com a LIA sobre {subject.title} e esclareça qualquer dúvida.
                </Text>
              </View>
            </View>
            <Pressable
              style={styles.aiButton}
              onPress={() => router.push("/chat")}
            >
              <Icons.ArrowRight size={20} color={colors.primary} />
            </Pressable>
          </GlassCard>
        </View>
      </ScrollView>
      )}
    </SafeAreaView>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  separatorContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  separatorOption: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.border,
  },
  selectedSeparatorOption: {
    backgroundColor: `${colors.primary}15`,
    borderLeftColor: colors.primary,
  },
  separatorText: {
    fontSize: 16,
    color: colors.text,
  },
  selectedSeparatorText: {
    fontWeight: "600",
    color: colors.primary,
  },
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerRightContainer: {
    position: "relative",
    marginRight: 8,
  },
  menuButton: {
    padding: 8,
  },
  menuContainer: {
    position: "absolute",
    top: 40,
    right: 0,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1000,
    width: 150,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
  },
  menuItemDanger: {
    marginTop: 4,
  },
  menuItemText: {
    fontSize: 16,
    marginLeft: 8,
    color: colors.text,
  },
  menuItemTextDanger: {
    color: colors.error,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  modalContainer: {
    width: "100%",
    padding: 24,
    borderRadius: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
    textAlign: "center",
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  headerSection: {
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  progressContainer: {
    width: "100%",
  },
  progressText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
  actionsSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  actionCard: {
    width: "48%",
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
  },
  contentSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  aiSection: {
    paddingHorizontal: 16,
  },
  aiCard: {
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  aiCardContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  aiIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  aiTextContainer: {
    flex: 1,
  },
  aiTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  aiDescription: {
    fontSize: 14,
    color: colors.textLight,
  },
  aiButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 16,
  },
});