import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  ScrollView,
  Animated,
  PanResponder,
  Pressable,
  Platform,
  Alert,
  Image,
} from "react-native";
import { useLocalSearch<PERSON><PERSON><PERSON>, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { useUserStore } from "@/store/userStore";
import { Button } from "@/components/Button";
import { ArrowLeft, ArrowRight, Check, X, RotateCw, BookOpen, MessageSquare, Sparkles, Plus, Play, Image as ImageIcon } from "lucide-react-native";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";
import { FlashcardConversationModal } from "@/components/FlashcardConversationModal";
import { AIGenerationModal } from "@/components/AIGenerationModal";
import { FlashcardForm } from "@/components/FlashcardForm";
import { FlashcardStats } from "@/components/FlashcardStats";
import { generateFlashcards } from "@/services/openai";

const { width, height } = Dimensions.get("window");
const SWIPE_THRESHOLD = 120;

import RouteGuard from '@/components/RouteGuard';

export default function FlashcardScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { flashcardSets, flashcards, updateFlashcard, addFlashcard } = useStudyStore();
  const { addXP } = useUserStore();

  const [currentSet, setCurrentSet] = useState(
    flashcardSets.find((set) => set.id === id)
  );
  const [currentFlashcards, setCurrentFlashcards] = useState(
    flashcards.filter((card) => card.id.startsWith(id))
  );
  const [currentIndex, setCurrentIndex] = useState(0);
  const [completed, setCompleted] = useState(false);
  const [stats, setStats] = useState({
    easy: 0,
    medium: 0,
    hard: 0,
    total: 0,
  });

  // Modal states
  const [showConversationModal, setShowConversationModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [showFlashcardForm, setShowFlashcardForm] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Animation values
  const [flipped, setFlipped] = useState(false);
  const flipAnimation = useRef(new Animated.Value(0)).current;
  const swipeAnimation = useRef(new Animated.Value(0)).current;
  const nextCardAnimation = useRef(new Animated.Value(width)).current;

  useEffect(() => {
    // Verificar se o conjunto existe
    if (!currentSet) {
      console.error('Conjunto de flashcards não encontrado:', id);
      return;
    }

    // Verificar se o ID é um UUID válido
    const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    if (!isValidUUID) {
      console.error('ID do conjunto inválido:', id);
      Alert.alert('Erro', 'ID do conjunto inválido. Por favor, selecione um conjunto válido.');
      return;
    }

    // Buscar flashcards para este conjunto do Supabase
    const loadFlashcards = async () => {
      try {
        console.log('Carregando flashcards para o conjunto:', id);
        const loadedFlashcards = await useStudyStore.getState().fetchFlashcards(id);
        console.log(`Carregados ${loadedFlashcards.length} flashcards`);

        if (loadedFlashcards && loadedFlashcards.length > 0) {
          setCurrentFlashcards(loadedFlashcards);
        } else {
          console.log('Nenhum flashcard encontrado para este conjunto');
        }
      } catch (error) {
        console.error('Erro ao carregar flashcards:', error);
        Alert.alert('Erro', 'Não foi possível carregar os flashcards. Por favor, tente novamente.');
      }
    };

    loadFlashcards();
  }, [id, currentSet]);

  // Create pan responder for swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        if (!flipped) {
          swipeAnimation.setValue(gestureState.dx);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (flipped) return;

        if (gestureState.dx > SWIPE_THRESHOLD) {
          // Swipe right (easy)
          handleSwipe("easy");
        } else if (gestureState.dx < -SWIPE_THRESHOLD) {
          // Swipe left (hard)
          handleSwipe("hard");
        } else {
          // Return to center
          Animated.spring(swipeAnimation, {
            toValue: 0,
            friction: 5,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const handleFlip = () => {
    if (!flipped) {
      // First hide the front card at 90 degrees, then show the back card
      Animated.sequence([
        Animated.timing(flipAnimation, {
          toValue: 0.5,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(flipAnimation, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        })
      ]).start(() => setFlipped(true));
    } else {
      // First hide the back card at 90 degrees, then show the front card
      Animated.sequence([
        Animated.timing(flipAnimation, {
          toValue: 0.5,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(flipAnimation, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        })
      ]).start(() => setFlipped(false));
    }
  };

  const handleSwipe = (difficultyLevel: "easy" | "medium" | "hard") => {
    // Update flashcard difficulty
    if (currentFlashcards.length > 0) {
      // Convert string difficulty to number
      let difficultyValue = 0;
      if (difficultyLevel === "easy") difficultyValue = 1;
      else if (difficultyLevel === "medium") difficultyValue = 2;
      else if (difficultyLevel === "hard") difficultyValue = 3;

      try {
        updateFlashcard(currentFlashcards[currentIndex].id, { difficulty: difficultyValue });
      } catch (error) {
        console.error('Erro ao atualizar dificuldade do flashcard:', error);
      }
    }

    // Update stats
    setStats((prev) => ({
      ...prev,
      [difficultyLevel]: prev[difficultyLevel] + 1,
      total: prev.total + 1,
    }));

    // Add XP based on difficulty
    if (difficultyLevel === "easy") {
      addXP(5);
    } else if (difficultyLevel === "medium") {
      addXP(10);
    } else if (difficultyLevel === "hard") {
      addXP(15);
    }

    // Animate swipe out
    const direction = difficultyLevel === "easy" ? 1 : -1;
    Animated.timing(swipeAnimation, {
      toValue: direction * width,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      if (currentIndex < currentFlashcards.length - 1) {
        // Prepare next card animation
        nextCardAnimation.setValue(width * direction * -1);
        setCurrentIndex(currentIndex + 1);
        setFlipped(false);
        flipAnimation.setValue(0);
        swipeAnimation.setValue(0);

        // Animate next card in
        Animated.spring(nextCardAnimation, {
          toValue: 0,
          friction: 8,
          useNativeDriver: true,
        }).start();
      } else {
        setCompleted(true);
      }
    });
  };

  const handleRestart = () => {
    setCurrentIndex(0);
    setCompleted(false);
    setStats({
      easy: 0,
      medium: 0,
      hard: 0,
      total: 0,
    });
    setFlipped(false);
    flipAnimation.setValue(0);
    swipeAnimation.setValue(0);
  };

  // Função para calcular a pontuação média
  const calculateAverageScore = () => {
    if (currentFlashcards.length === 0) return 0;

    // Calcular com base na dificuldade dos flashcards
    // Dificuldade 1 (fácil) = 100%, 2 (médio) = 66%, 3 (difícil) = 33%, 0 (não estudado) = 0%
    const totalScore = currentFlashcards.reduce((score, card) => {
      switch (card.difficulty) {
        case 1: return score + 100;
        case 2: return score + 66;
        case 3: return score + 33;
        default: return score;
      }
    }, 0);

    return Math.round(totalScore / currentFlashcards.length);
  };

  // Função para calcular dias consecutivos de estudo
  const calculateStreakDays = () => {
    // Simulação de dias consecutivos - em uma implementação real, isso seria calculado
    // com base em um histórico de estudo armazenado no banco de dados
    return Math.min(7, Math.floor(Math.random() * 10)); // Valor aleatório entre 0 e 7 para demonstração
  };

  const handleStartStudy = (smartMode: boolean = false) => {
    if (currentFlashcards.length === 0) {
      Alert.alert("Sem flashcards", "Este conjunto não possui flashcards para estudar.");
      return;
    }

    // Determinar o modo de estudo e o número de flashcards
    const studyMode = smartMode ? "inteligente" : "completa";
    const filteredCount = smartMode ?
      currentFlashcards.filter(card => {
        // Lógica simplificada para estimar quantos flashcards serão mostrados no modo inteligente
        if (card.reviewCount === 0) return true; // Nunca revisados
        if (card.difficulty === 3) return true; // Difíceis
        if (card.nextReview) {
          const nextReviewDate = new Date(card.nextReview);
          const today = new Date();
          const diffDays = Math.floor((nextReviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          return diffDays <= 2; // Próximos da data de revisão
        }
        return false;
      }).length :
      currentFlashcards.length;

    Alert.alert(
      `Iniciar Revisão ${smartMode ? 'Inteligente' : 'Completa'}`,
      `Você está prestes a iniciar a revisão ${studyMode} ${smartMode ? `de aproximadamente ${filteredCount}` : `de todos os ${currentFlashcards.length}`} flashcards. Deseja continuar?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Iniciar",
          onPress: () => router.push({
            pathname: `/flashcards/${id}/study-enhanced`,
            params: { smartMode: smartMode ? 'true' : 'false' }
          })
        }
      ]
    );
  };

  const handleAddFlashcard = async (question: string, answer: string, subjectId?: string, imageUri?: string) => {
    try {
      setIsSaving(true);

      // Verificar se o conjunto existe
      if (!currentSet) {
        throw new Error("Conjunto de flashcards não encontrado");
      }

      // Verificar se o ID é um UUID válido
      const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
      if (!isValidUUID) {
        throw new Error("ID do conjunto inválido");
      }

      // Criar um novo flashcard
      const newFlashcard = {
        setId: id,
        front: question,
        back: answer,
        difficulty: 0, // Garantir que seja um número
        nextReview: null,
        reviewCount: 0,
        imageUrl: imageUri || null,
        subject_id: subjectId // Adicionar o ID da matéria selecionada
      };

      console.log("Adicionando flashcard:", JSON.stringify(newFlashcard, null, 2));

      // Adicionar o flashcard ao banco de dados
      const savedFlashcard = await addFlashcard(newFlashcard);

      if (!savedFlashcard) {
        throw new Error("Falha ao adicionar flashcard");
      }

      console.log("Flashcard salvo com sucesso:", JSON.stringify(savedFlashcard, null, 2));

      // Fechar o modal
      setShowFlashcardForm(false);

      // Mostrar mensagem de sucesso
      Alert.alert(
        "Sucesso",
        "Flashcard adicionado com sucesso!",
        [
          {
            text: "OK",
            onPress: () => {
              // Se a sessão de estudo estiver concluída, reiniciá-la
              if (completed) {
                handleRestart();
              }
            }
          }
        ]
      );

      // Atualizar a lista de flashcards
      const updatedFlashcards = await useStudyStore.getState().fetchFlashcards(id);
      setCurrentFlashcards(updatedFlashcards);

    } catch (error) {
      console.error("Erro ao adicionar flashcard:", error);
      Alert.alert(
        "Erro",
        `Ocorreu um erro ao adicionar o flashcard: ${error instanceof Error ? error.message : 'Erro desconhecido'}. Por favor, tente novamente.`
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleGenerateFlashcards = async (topic: string) => {
    try {
      setIsGenerating(true);

      // Verificar se o conjunto existe
      if (!currentSet) {
        throw new Error("Conjunto de flashcards não encontrado");
      }

      // Verificar se o ID é um UUID válido
      const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
      if (!isValidUUID) {
        throw new Error("ID do conjunto inválido");
      }

      // Gerar flashcards usando OpenAI
      const generatedFlashcards = await generateFlashcards(topic, 5);

      if (!generatedFlashcards || generatedFlashcards.length === 0) {
        Alert.alert("Erro", "Não foi possível gerar flashcards. Por favor, tente novamente.");
        return;
      }

      // Adicionar cada flashcard ao conjunto atual
      const savedFlashcards = [];
      for (const card of generatedFlashcards) {
        try {
          const flashcard = {
            setId: id,
            front: card.question || card.front,
            back: card.answer || card.back,
            difficulty: 0, // Garantir que seja um número
            nextReview: null,
            reviewCount: 0,
            imageUrl: null
          };

          console.log("Adicionando flashcard gerado:", JSON.stringify(flashcard, null, 2));

          const savedFlashcard = await addFlashcard(flashcard);
          if (savedFlashcard) {
            savedFlashcards.push(savedFlashcard);
            console.log("Flashcard gerado salvo com sucesso:", JSON.stringify(savedFlashcard, null, 2));
          }
        } catch (cardError) {
          console.error("Erro ao adicionar flashcard individual:", cardError);
          // Continuar com o próximo card mesmo se um falhar
        }
      }

      // Atualizar a lista de flashcards
      const updatedFlashcards = await useStudyStore.getState().fetchFlashcards(id);
      setCurrentFlashcards(updatedFlashcards);

      // Fechar modal
      setShowAIModal(false);

      // Mostrar mensagem de sucesso
      Alert.alert(
        "Sucesso",
        `${savedFlashcards.length} novos flashcards foram adicionados ao conjunto.`,
        [
          {
            text: "OK",
            onPress: () => {
              // Se a sessão de estudo estiver concluída, reiniciá-la
              if (completed) {
                handleRestart();
              }
            }
          }
        ]
      );

    } catch (error) {
      console.error("Erro ao gerar flashcards:", error);
      Alert.alert(
        "Erro",
        `Ocorreu um erro ao gerar os flashcards: ${error instanceof Error ? error.message : 'Erro desconhecido'}. Por favor, verifique sua conexão e tente novamente.`
      );
    } finally {
      setIsGenerating(false);
    }
  };

  // Interpolate rotation and opacity based on flip animation
  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ["0deg", "90deg", "180deg"],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ["180deg", "270deg", "360deg"],
  });

  // Use a sharp cutoff for opacity to avoid seeing both cards at once
  const frontOpacity = flipAnimation.interpolate({
    inputRange: [0, 0.49, 0.51, 1],
    outputRange: [1, 1, 0, 0],
  });

  const backOpacity = flipAnimation.interpolate({
    inputRange: [0, 0.49, 0.51, 1],
    outputRange: [0, 0, 1, 1],
  });

  // Scale down slightly during flip for better effect
  const scaleInterpolate = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 0.95, 1],
  });

  // Interpolate opacity based on swipe animation
  const rightOpacity = swipeAnimation.interpolate({
    inputRange: [-width / 2, 0, width / 2],
    outputRange: [0, 0, 1],
    extrapolate: "clamp",
  });

  const leftOpacity = swipeAnimation.interpolate({
    inputRange: [-width / 2, 0, width / 2],
    outputRange: [1, 0, 0],
    extrapolate: "clamp",
  });

  if (!currentSet) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Flashcards não encontrados" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Conjunto de flashcards não encontrado</Text>
          <Button
            title="Voltar para flashcards"
            onPress={() => router.push("/flashcards")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <RouteGuard resourceId={id as string} tableName="flashcard_sets">
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: currentSet?.title || "Flashcards",
            headerBackTitle: "Voltar",
            headerRight: () => (
              <View style={styles.headerButtons}>
                {!completed && (
                  <Pressable
                    style={styles.headerButton}
                    onPress={() => setShowFlashcardForm(true)}
                  >
                    <Plus size={24} color={colors.primary} />
                  </Pressable>
                )}
                {!completed && currentFlashcards.length > 0 && (
                  <Pressable
                    style={styles.headerButton}
                    onPress={() => setShowConversationModal(true)}
                  >
                    <MessageSquare size={24} color={colors.primary} />
                  </Pressable>
                )}
                <Pressable
                  style={styles.headerButton}
                  onPress={() => setShowAIModal(true)}
                >
                  <Sparkles size={24} color={colors.primary} />
                </Pressable>
              </View>
            ),
          }}
        />

      {/* Estatísticas do conjunto */}
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
      >
        <View style={styles.contentContainer}>
          {/* Cabeçalho do conjunto */}
          <View style={styles.setHeaderContainer}>
            <View style={styles.setInfoContainer}>
              <Text style={styles.setTitle}>{currentSet?.title}</Text>
              <Text style={styles.setDescription}>{currentSet?.description}</Text>
              <View style={styles.setMetaContainer}>
                <Text style={styles.setMetaText}>
                  {currentFlashcards.length} flashcards
                </Text>
                {currentSet?.lastStudied && (
                  <Text style={styles.setMetaText}>
                    Último estudo: {new Date(currentSet.lastStudied).toLocaleDateString()}
                  </Text>
                )}
              </View>
            </View>
          </View>

          {/* Estatísticas */}
          <FlashcardStats
            flashcards={currentFlashcards}
            lastReviewed={currentSet?.lastStudied || null}
          />

          {/* Botões de ação */}
          <View style={styles.actionButtonsContainer}>
            <Button
              title="Adicionar Flashcard"
              onPress={() => setShowFlashcardForm(true)}
              variant="secondary"
              size="medium"
              icon={Plus}
              style={styles.actionButton}
            />
            <Button
              title="Gerar com IA"
              onPress={() => setShowAIModal(true)}
              variant="outline"
              size="medium"
              icon={Sparkles}
              style={styles.actionButton}
            />
          </View>

          {!completed ? (
            <>
              {/* Seletor de modo de estudo */}
              {currentFlashcards.length > 0 && (
                <View style={styles.studyModeContainer}>
                  <Text style={styles.studySectionTitle}>Iniciar Estudo</Text>

                  <View style={styles.studyModeOptions}>
                    <Pressable
                      style={[styles.studyModeOption, styles.studyModeOptionComplete]}
                      onPress={() => handleStartStudy(false)}
                    >
                      <View style={styles.studyModeIconContainer}>
                        <BookOpen size={24} color={colors.white} />
                      </View>
                      <Text style={styles.studyModeTitle}>Revisão Completa</Text>
                      <Text style={styles.studyModeDescription}>
                        Revise todos os {currentFlashcards.length} flashcards deste conjunto
                      </Text>
                    </Pressable>

                    <Pressable
                      style={[styles.studyModeOption, styles.studyModeOptionSmart]}
                      onPress={() => handleStartStudy(true)}
                    >
                      <View style={styles.studyModeIconContainer}>
                        <Brain size={24} color={colors.white} />
                      </View>
                      <Text style={styles.studyModeTitle}>Revisão Inteligente</Text>
                      <Text style={styles.studyModeDescription}>
                        Foque nos flashcards que precisam de mais atenção
                      </Text>
                    </Pressable>
                  </View>
                </View>
              )}

              {/* Lista de flashcards */}
              {currentFlashcards.length > 0 && (
                <View style={styles.flashcardsListContainer}>
                  <Text style={styles.flashcardsSectionTitle}>Flashcards ({currentFlashcards.length})</Text>

                  {currentFlashcards.map((card, index) => (
                    <View key={card.id} style={styles.flashcardItemContainer}>
                      <View style={styles.flashcardItemHeader}>
                        <Text style={styles.flashcardItemNumber}>#{index + 1}</Text>
                        <View style={[styles.difficultyIndicator, {
                          backgroundColor:
                            card.difficulty === 1 ? colors.success :
                            card.difficulty === 2 ? colors.secondary :
                            card.difficulty === 3 ? colors.error :
                            colors.backgroundDark
                        }]} />
                      </View>
                      <View style={styles.flashcardItemContent}>
                        <Text style={styles.flashcardItemFront} numberOfLines={2}>{card.front}</Text>
                        <Text style={styles.flashcardItemBack} numberOfLines={1}>{card.back}</Text>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </>
          ) : (
            <View style={styles.completedContainer}>
              <Text style={styles.completedTitle}>Revisão Concluída!</Text>
              <Text style={styles.completedSubtitle}>
                Você revisou {stats.total} cartões
              </Text>

              <View style={styles.statsContainer}>
                <GlassCard style={styles.statCard} gradient>
                  <Text style={[styles.statValue, { color: colors.success }]}>
                    {stats.easy}
                  </Text>
                  <Text style={styles.statLabel}>Fácil</Text>
                </GlassCard>
                <GlassCard style={styles.statCard} gradient>
                  <Text style={[styles.statValue, { color: colors.secondary }]}>
                    {stats.medium || 0}
                  </Text>
                  <Text style={styles.statLabel}>Médio</Text>
                </GlassCard>
                <GlassCard style={styles.statCard} gradient>
                  <Text style={[styles.statValue, { color: colors.error }]}>
                    {stats.hard}
                  </Text>
                  <Text style={styles.statLabel}>Difícil</Text>
                </GlassCard>
              </View>

              <GlassCard style={styles.xpCard} gradient>
                <Text style={styles.xpText}>
                  +{(stats.easy || 0) * 5 + (stats.medium || 0) * 10 + (stats.hard || 0) * 15} XP ganhos!
                </Text>
              </GlassCard>

              <View style={styles.buttonsContainer}>
                <Button
                  title="Recomeçar"
                  onPress={handleRestart}
                  variant="primary"
                  size="large"
                  fullWidth
                />
                <Button
                  title="Voltar para Flashcards"
                  onPress={() => router.push("/flashcards")}
                  variant="outline"
                  size="large"
                  fullWidth
                />
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Conversation Modal */}
      {currentFlashcards.length > 0 && (
        <FlashcardConversationModal
          visible={showConversationModal}
          onClose={() => setShowConversationModal(false)}
          flashcard={currentFlashcards[currentIndex]}
        />
      )}

      {/* AI Generation Modal */}
      <AIGenerationModal
        visible={showAIModal}
        onClose={() => setShowAIModal(false)}
        title="Gerar Flashcards com IA"
        placeholder="Digite um tópico ou assunto para gerar flashcards..."
        onGenerate={handleGenerateFlashcards}
        loading={isGenerating}
        type="flashcards"
      />

      {/* Flashcard Form Modal */}
      <FlashcardForm
        visible={showFlashcardForm}
        onClose={() => setShowFlashcardForm(false)}
        onSave={handleAddFlashcard}
        loading={isSaving}
        setId={id}
      />
    </SafeAreaView>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 120, // Aumentado para garantir que o conteúdo não fique embaixo do menu
  },
  setHeaderContainer: {
    marginBottom: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  setInfoContainer: {
    width: '100%',
  },
  setTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 8,
  },
  setDescription: {
    fontSize: 16,
    color: colors.textLight,
    marginBottom: 12,
  },
  setMetaContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  setMetaText: {
    fontSize: 14,
    color: colors.textLight,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  studyModeContainer: {
    marginBottom: 24,
  },
  studySectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  studyModeOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  studyModeOption: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  studyModeOptionComplete: {
    backgroundColor: colors.primary,
  },
  studyModeOptionSmart: {
    backgroundColor: colors.secondary,
  },
  studyModeIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  studyModeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 8,
  },
  studyModeDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  flashcardsListContainer: {
    marginBottom: 24,
  },
  flashcardsSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  flashcardItemContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  flashcardItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  flashcardItemNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  difficultyIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  flashcardItemContent: {
    gap: 8,
  },
  flashcardItemFront: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  flashcardItemBack: {
    fontSize: 14,
    color: colors.textLight,
    fontStyle: 'italic',
  },
  contentContainer: {
    flex: 1,
  },
  studyButton: {
    marginBottom: 16,
  },
  flashcardInfoContainer: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    alignItems: 'center',
  },
  flashcardInfoText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  flashcardInfoSubtext: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  flashcardContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 80 : 64, // Increased bottom padding to accommodate the higher tab bar
  },
  progressContainer: {
    width: "100%",
    marginBottom: 16,
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textLight,
    marginBottom: 8,
    textAlign: "center",
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.backgroundDark,
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  cardContainer: {
    width: width - 40,
    height: height * 0.5,
    maxHeight: 450,
    alignItems: "center",
    justifyContent: "center",
    perspective: 1500, // Increased perspective for better 3D effect
    marginVertical: 10,
  },
  // Adding a wrapper to stabilize the animation
  cardWrapper: {
    width: "100%",
    height: "100%",
    position: "relative",
  },
  card: {
    width: "100%",
    height: "100%",
    position: "absolute",
    backfaceVisibility: "hidden",
    // Ensuring the card doesn't shift during animation
    left: 0,
    top: 0,
    borderRadius: 20,
    overflow: "hidden",
  },
  cardBack: {
    transform: [{ rotateY: "180deg" }],
  },
  cardInner: {
    width: "100%",
    height: "100%",
    padding: 24,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  cardContent: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  cardIcon: {
    marginBottom: 16,
    opacity: 0.8,
  },
  questionText: {
    fontSize: 26,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
    marginVertical: 20,
  },
  answerText: {
    fontSize: 22,
    color: colors.text,
    textAlign: "center",
    lineHeight: 32,
    marginVertical: 20,
  },
  tapHint: {
    position: "absolute",
    bottom: 16,
    alignItems: "center",
  },
  tapHintText: {
    fontSize: 14,
    color: colors.textLight,
  },
  cardScrollView: {
    flex: 1,
    width: '100%',
    maxHeight: 300,
  },
  cardScrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  resultIndicator: {
    position: "absolute",
    top: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  rightIndicator: {
    right: 20,
    backgroundColor: colors.success,
  },
  leftIndicator: {
    left: 20,
    backgroundColor: colors.error,
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginTop: 24,
    marginBottom: 16,
  },
  swipeHintContainer: {
    flex: 1,
  },
  swipeHint: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16, // Increased from 12 to 16 for better touch targets
    gap: 8,
  },
  swipeHintText: {
    fontSize: 16,
    fontWeight: "600",
  },
  flipButtonContainer: {
    marginHorizontal: 16,
  },
  flipButton: {
    width: 70, // Increased from 64 to 70 for better touch targets
    height: 70, // Increased from 64 to 70 for better touch targets
    borderRadius: 35, // Adjusted to match new size
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  difficultyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginTop: 16,
    gap: 8,
  },
  difficultyButton: {
    flex: 1,
    height: 70, // Increased from 60 to 70 for better touch targets
  },
  completedContainer: {
    flex: 1,
  },
  completedContent: {
    padding: 24,
    alignItems: "center",
    paddingBottom: 100, // Added extra padding at the bottom to account for the tab bar
  },
  completedTitle: {
    fontSize: 28,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    marginTop: 24,
  },
  completedSubtitle: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 32,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  statValue: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 16,
    color: colors.textLight,
  },
  xpCard: {
    width: "100%",
    padding: 20,
    alignItems: "center",
    marginBottom: 40,
  },
  xpText: {
    fontSize: 22,
    fontWeight: "600",
    color: colors.primary,
  },
  buttonsContainer: {
    width: "100%",
    gap: 16,
  },
  cardImageContainer: {
    width: "100%",
    height: 150,
    marginBottom: 16,
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: colors.backgroundLight,
  },
  cardImage: {
    width: "100%",
    height: "100%",
  },
  headerButtons: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
});