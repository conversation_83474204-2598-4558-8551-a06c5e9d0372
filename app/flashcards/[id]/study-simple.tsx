import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  ScrollView,
  Pressable,
  Platform,
  Alert,
  Image,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { useUserStore } from "@/store/userStore";
import { Button } from "@/components/Button";
import { ArrowLeft, ArrowRight, RotateCw, BookOpen } from "lucide-react-native";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";

const { width, height } = Dimensions.get("window");

export default function StudyFlashcardsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { flashcardSets, flashcards, updateFlashcard, fetchFlashcards } = useStudyStore();
  const { addXP } = useUserStore();

  // Estado para controlar o carregamento
  const [loading, setLoading] = useState(true);
  
  // Estado para armazenar os dados
  const [currentSet, setCurrentSet] = useState(null);
  const [currentFlashcards, setCurrentFlashcards] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [completed, setCompleted] = useState(false);
  const [flipped, setFlipped] = useState(false);
  const [stats, setStats] = useState({
    easy: 0,
    medium: 0,
    hard: 0,
    total: 0,
  });

  // Carregar dados quando o componente for montado
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Verificar se o ID é um UUID válido
        const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
        if (!isValidUUID) {
          console.error('ID do conjunto inválido:', id);
          Alert.alert('Erro', 'ID do conjunto inválido. Por favor, selecione um conjunto válido.');
          router.back();
          return;
        }
        
        // Buscar o conjunto atual
        const set = flashcardSets.find(s => s.id === id);
        if (!set) {
          console.error('Conjunto não encontrado:', id);
          Alert.alert('Erro', 'Conjunto não encontrado. Por favor, selecione outro conjunto.');
          router.back();
          return;
        }
        
        setCurrentSet(set);
        
        // Buscar flashcards
        const loadedFlashcards = await fetchFlashcards(id);
        console.log(`Carregados ${loadedFlashcards.length} flashcards`);
        
        if (loadedFlashcards && loadedFlashcards.length > 0) {
          setCurrentFlashcards(loadedFlashcards);
        } else {
          Alert.alert(
            'Sem flashcards', 
            'Este conjunto não possui flashcards. Adicione alguns antes de iniciar o estudo.',
            [{ text: 'OK', onPress: () => router.back() }]
          );
        }
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao carregar os dados. Por favor, tente novamente.');
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [id]);

  // Função para virar o cartão
  const handleFlip = () => {
    setFlipped(!flipped);
  };

  // Função para lidar com a classificação de dificuldade
  const handleDifficulty = (difficultyLevel: "easy" | "medium" | "hard") => {
    if (currentFlashcards.length === 0) return;
    
    // Update flashcard difficulty
    try {
      // Convert string difficulty to number
      let difficultyValue = 0;
      if (difficultyLevel === "easy") difficultyValue = 1;
      else if (difficultyLevel === "medium") difficultyValue = 2;
      else if (difficultyLevel === "hard") difficultyValue = 3;

      updateFlashcard(currentFlashcards[currentIndex].id, { difficulty: difficultyValue });
    } catch (error) {
      console.error('Erro ao atualizar dificuldade do flashcard:', error);
    }

    // Update stats
    setStats((prev) => ({
      ...prev,
      [difficultyLevel]: prev[difficultyLevel] + 1,
      total: prev.total + 1,
    }));

    // Add XP based on difficulty
    if (difficultyLevel === "easy") {
      addXP(5);
    } else if (difficultyLevel === "medium") {
      addXP(10);
    } else if (difficultyLevel === "hard") {
      addXP(15);
    }

    // Move to next card or complete
    if (currentIndex < currentFlashcards.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setFlipped(false);
    } else {
      setCompleted(true);
    }
  };

  const handleRestart = () => {
    setCurrentIndex(0);
    setCompleted(false);
    setFlipped(false);
    setStats({
      easy: 0,
      medium: 0,
      hard: 0,
      total: 0,
    });
  };

  // Tela de carregamento
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: "Carregando...",
            headerBackTitle: "Voltar",
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando flashcards...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Tela de erro se não houver conjunto
  if (!currentSet) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Flashcards não encontrados" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Conjunto de flashcards não encontrado</Text>
          <Button
            title="Voltar para flashcards"
            onPress={() => router.push("/flashcards")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  // Verificar se há flashcards para mostrar
  if (currentFlashcards.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: currentSet?.title || "Estudo de Flashcards" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Este conjunto não possui flashcards</Text>
          <Button
            title="Voltar para flashcards"
            onPress={() => router.push("/flashcards")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: currentSet?.title || "Estudo de Flashcards",
          headerBackTitle: "Voltar",
        }}
      />

      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      {!completed ? (
        <View style={styles.flashcardContainer}>
          {/* Barra de progresso */}
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              {currentIndex + 1} de {currentFlashcards.length}
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${
                      ((currentIndex + 1) / currentFlashcards.length) * 100
                    }%`,
                  },
                ]}
              />
            </View>
          </View>

          {/* Cartão */}
          <GlassCard style={styles.card} gradient>
            <Pressable
              style={styles.cardContent}
              onPress={handleFlip}
            >
              {!flipped ? (
                // Frente do cartão
                <>
                  <BookOpen size={28} color={colors.primary} style={styles.cardIcon} />
                  <ScrollView
                    style={styles.cardScrollView}
                    contentContainerStyle={styles.cardScrollContent}
                    showsVerticalScrollIndicator={false}
                  >
                    {currentFlashcards[currentIndex].imageUrl && (
                      <View style={styles.cardImageContainer}>
                        <Image
                          source={{ uri: currentFlashcards[currentIndex].imageUrl }}
                          style={styles.cardImage}
                          resizeMode="contain"
                        />
                      </View>
                    )}
                    <Text style={styles.questionText}>
                      {currentFlashcards[currentIndex].front}
                    </Text>
                  </ScrollView>
                </>
              ) : (
                // Verso do cartão
                <ScrollView
                  style={styles.cardScrollView}
                  contentContainerStyle={styles.cardScrollContent}
                  showsVerticalScrollIndicator={false}
                >
                  <Text style={styles.answerText}>
                    {currentFlashcards[currentIndex].back}
                  </Text>
                </ScrollView>
              )}
              <View style={styles.tapHint}>
                <Text style={styles.tapHintText}>
                  {flipped ? "Toque para ver a pergunta" : "Toque para ver a resposta"}
                </Text>
              </View>
            </Pressable>
          </GlassCard>

          {/* Botão de virar */}
          <Pressable
            style={styles.flipButtonContainer}
            onPress={handleFlip}
          >
            <LinearGradient
              colors={colors.primaryGradient}
              style={styles.flipButton}
            >
              <RotateCw size={28} color="#fff" />
            </LinearGradient>
          </Pressable>

          {/* Botões de dificuldade */}
          <View style={styles.difficultyButtons}>
            <Button
              title="Difícil"
              onPress={() => handleDifficulty("hard")}
              variant="error"
              size="large"
              style={styles.difficultyButton}
              leftIcon={ArrowLeft}
            />
            <Button
              title="Médio"
              onPress={() => handleDifficulty("medium")}
              variant="secondary"
              size="large"
              style={styles.difficultyButton}
            />
            <Button
              title="Fácil"
              onPress={() => handleDifficulty("easy")}
              variant="success"
              size="large"
              style={styles.difficultyButton}
              rightIcon={ArrowRight}
            />
          </View>
        </View>
      ) : (
        <ScrollView
          style={styles.completedContainer}
          contentContainerStyle={styles.completedContent}
        >
          <Text style={styles.completedTitle}>Revisão Concluída!</Text>
          <Text style={styles.completedSubtitle}>
            Você revisou {stats.total} cartões
          </Text>

          <View style={styles.statsContainer}>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.success }]}>
                {stats.easy}
              </Text>
              <Text style={styles.statLabel}>Fácil</Text>
            </GlassCard>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.secondary }]}>
                {stats.medium || 0}
              </Text>
              <Text style={styles.statLabel}>Médio</Text>
            </GlassCard>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.error }]}>
                {stats.hard}
              </Text>
              <Text style={styles.statLabel}>Difícil</Text>
            </GlassCard>
          </View>

          <GlassCard style={styles.xpCard} gradient>
            <Text style={styles.xpText}>
              +{(stats.easy || 0) * 5 + (stats.medium || 0) * 10 + (stats.hard || 0) * 15} XP ganhos!
            </Text>
          </GlassCard>

          <View style={styles.buttonsContainer}>
            <Button
              title="Recomeçar"
              onPress={handleRestart}
              variant="primary"
              size="large"
              fullWidth
            />
            <Button
              title="Voltar para Flashcards"
              onPress={() => router.push("/flashcards")}
              variant="outline"
              size="large"
              fullWidth
            />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 16,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  flashcardContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 80 : 64,
  },
  progressContainer: {
    width: "100%",
    marginBottom: 16,
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textLight,
    marginBottom: 8,
    textAlign: "center",
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.backgroundDark,
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  card: {
    width: width - 40,
    height: height * 0.5,
    maxHeight: 450,
    padding: 24,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    marginVertical: 10,
  },
  cardContent: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  cardIcon: {
    marginBottom: 16,
    opacity: 0.8,
  },
  cardImageContainer: {
    width: "100%",
    height: 150,
    marginBottom: 16,
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: colors.backgroundLight,
  },
  cardImage: {
    width: "100%",
    height: "100%",
  },
  questionText: {
    fontSize: 26,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
    marginVertical: 20,
  },
  answerText: {
    fontSize: 22,
    color: colors.text,
    textAlign: "center",
    lineHeight: 32,
    marginVertical: 20,
  },
  tapHint: {
    position: "absolute",
    bottom: 16,
    alignItems: "center",
  },
  tapHintText: {
    fontSize: 14,
    color: colors.textLight,
  },
  cardScrollView: {
    flex: 1,
    width: '100%',
    maxHeight: 300,
  },
  cardScrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  flipButtonContainer: {
    marginVertical: 16,
  },
  flipButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  difficultyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginTop: 16,
    gap: 8,
  },
  difficultyButton: {
    flex: 1,
  },
  completedContainer: {
    flex: 1,
  },
  completedContent: {
    padding: 24,
    alignItems: "center",
    paddingBottom: 100,
  },
  completedTitle: {
    fontSize: 28,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    marginTop: 24,
  },
  completedSubtitle: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 32,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  statValue: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 16,
    color: colors.textLight,
  },
  xpCard: {
    width: "100%",
    padding: 20,
    alignItems: "center",
    marginBottom: 40,
  },
  xpText: {
    fontSize: 22,
    fontWeight: "600",
    color: colors.primary,
  },
  buttonsContainer: {
    width: "100%",
    gap: 16,
  },
});
