import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  ScrollView,
  Animated,
  PanResponder,
  Pressable,
  Platform,
  Alert,
  Image,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { useUserStore } from "@/store/userStore";
import { Button } from "@/components/Button";
import { ArrowLeft, ArrowRight, Check, X, RotateCw, BookOpen } from "lucide-react-native";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";

const { width, height } = Dimensions.get("window");
const SWIPE_THRESHOLD = 120;

export default function StudyFlashcardsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { flashcardSets, flashcards, updateFlashcard } = useStudyStore();
  const { addXP } = useUserStore();

  const [currentSet, setCurrentSet] = useState(
    flashcardSets.find((set) => set.id === id)
  );
  const [currentFlashcards, setCurrentFlashcards] = useState(
    flashcards.filter((card) => card.setId === id)
  );
  const [currentIndex, setCurrentIndex] = useState(0);
  const [completed, setCompleted] = useState(false);
  const [stats, setStats] = useState({
    easy: 0,
    medium: 0,
    hard: 0,
    total: 0,
  });

  // Animation values
  const [flipped, setFlipped] = useState(false);
  const flipAnimation = useRef(new Animated.Value(0)).current;
  const swipeAnimation = useRef(new Animated.Value(0)).current;
  const nextCardAnimation = useRef(new Animated.Value(width)).current;

  useEffect(() => {
    // Verificar se o conjunto existe
    if (!currentSet) {
      console.error('Conjunto de flashcards não encontrado:', id);
      return;
    }

    // Verificar se o ID é um UUID válido
    const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    if (!isValidUUID) {
      console.error('ID do conjunto inválido:', id);
      Alert.alert('Erro', 'ID do conjunto inválido. Por favor, selecione um conjunto válido.');
      return;
    }

    // Buscar flashcards para este conjunto do Supabase
    const loadFlashcards = async () => {
      try {
        console.log('Carregando flashcards para o conjunto:', id);
        const loadedFlashcards = await useStudyStore.getState().fetchFlashcards(id);
        console.log(`Carregados ${loadedFlashcards.length} flashcards`);

        if (loadedFlashcards && loadedFlashcards.length > 0) {
          setCurrentFlashcards(loadedFlashcards);
        } else {
          console.log('Nenhum flashcard encontrado para este conjunto');
        }
      } catch (error) {
        console.error('Erro ao carregar flashcards:', error);
        Alert.alert('Erro', 'Não foi possível carregar os flashcards. Por favor, tente novamente.');
      }
    };

    loadFlashcards();
  }, [id, currentSet]);

  // Create pan responder for swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        if (!flipped) {
          swipeAnimation.setValue(gestureState.dx);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (flipped) return;

        if (gestureState.dx > SWIPE_THRESHOLD) {
          // Swipe right (easy)
          handleSwipe("easy");
        } else if (gestureState.dx < -SWIPE_THRESHOLD) {
          // Swipe left (hard)
          handleSwipe("hard");
        } else {
          // Return to center
          Animated.spring(swipeAnimation, {
            toValue: 0,
            friction: 5,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const handleFlip = () => {
    if (!flipped) {
      // First hide the front card at 90 degrees, then show the back card
      Animated.sequence([
        Animated.timing(flipAnimation, {
          toValue: 0.5,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(flipAnimation, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        })
      ]).start(() => setFlipped(true));
    } else {
      // First hide the back card at 90 degrees, then show the front card
      Animated.sequence([
        Animated.timing(flipAnimation, {
          toValue: 0.5,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(flipAnimation, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        })
      ]).start(() => setFlipped(false));
    }
  };

  const handleSwipe = (difficultyLevel: "easy" | "medium" | "hard") => {
    // Update flashcard difficulty
    if (currentFlashcards.length > 0) {
      // Convert string difficulty to number
      let difficultyValue = 0;
      if (difficultyLevel === "easy") difficultyValue = 1;
      else if (difficultyLevel === "medium") difficultyValue = 2;
      else if (difficultyLevel === "hard") difficultyValue = 3;

      try {
        updateFlashcard(currentFlashcards[currentIndex].id, { difficulty: difficultyValue });
      } catch (error) {
        console.error('Erro ao atualizar dificuldade do flashcard:', error);
      }
    }

    // Update stats
    setStats((prev) => ({
      ...prev,
      [difficultyLevel]: prev[difficultyLevel] + 1,
      total: prev.total + 1,
    }));

    // Add XP based on difficulty
    if (difficultyLevel === "easy") {
      addXP(5);
    } else if (difficultyLevel === "medium") {
      addXP(10);
    } else if (difficultyLevel === "hard") {
      addXP(15);
    }

    // Animate swipe out
    const direction = difficultyLevel === "easy" ? 1 : -1;
    Animated.timing(swipeAnimation, {
      toValue: direction * width,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      if (currentIndex < currentFlashcards.length - 1) {
        // Prepare next card animation
        nextCardAnimation.setValue(width * direction * -1);
        setCurrentIndex(currentIndex + 1);
        setFlipped(false);
        flipAnimation.setValue(0);
        swipeAnimation.setValue(0);

        // Animate next card in
        Animated.spring(nextCardAnimation, {
          toValue: 0,
          friction: 8,
          useNativeDriver: true,
        }).start();
      } else {
        setCompleted(true);
      }
    });
  };

  const handleRestart = () => {
    setCurrentIndex(0);
    setCompleted(false);
    setStats({
      easy: 0,
      medium: 0,
      hard: 0,
      total: 0,
    });
    setFlipped(false);
    flipAnimation.setValue(0);
    swipeAnimation.setValue(0);
  };

  // Interpolate rotation and opacity based on flip animation
  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ["0deg", "90deg", "180deg"],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ["180deg", "270deg", "360deg"],
  });

  // Use a sharp cutoff for opacity to avoid seeing both cards at once
  const frontOpacity = flipAnimation.interpolate({
    inputRange: [0, 0.49, 0.51, 1],
    outputRange: [1, 1, 0, 0],
  });

  const backOpacity = flipAnimation.interpolate({
    inputRange: [0, 0.49, 0.51, 1],
    outputRange: [0, 0, 1, 1],
  });

  // Scale down slightly during flip for better effect
  const scaleInterpolate = flipAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 0.95, 1],
  });

  // Interpolate opacity based on swipe animation
  const rightOpacity = swipeAnimation.interpolate({
    inputRange: [-width / 2, 0, width / 2],
    outputRange: [0, 0, 1],
    extrapolate: "clamp",
  });

  const leftOpacity = swipeAnimation.interpolate({
    inputRange: [-width / 2, 0, width / 2],
    outputRange: [1, 0, 0],
    extrapolate: "clamp",
  });

  if (!currentSet) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Flashcards não encontrados" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Conjunto de flashcards não encontrado</Text>
          <Button
            title="Voltar para flashcards"
            onPress={() => router.push("/flashcards")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: currentSet?.title || "Estudo de Flashcards",
          headerBackTitle: "Voltar",
        }}
      />

      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      {!completed ? (
        <View style={styles.flashcardContainer}>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              {currentIndex + 1} de {currentFlashcards.length}
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${
                      ((currentIndex + 1) / currentFlashcards.length) * 100
                    }%`,
                  },
                ]}
              />
            </View>
          </View>

          {currentFlashcards.length > 0 && (
            <View style={styles.cardContainer}>
              {/* Adding a wrapper to stabilize the animation */}
              <View style={styles.cardWrapper}>
                <Animated.View
                  style={[
                    styles.card,
                    {
                      transform: [
                        { translateX: swipeAnimation },
                        { rotateY: frontInterpolate },
                        { scale: scaleInterpolate },
                      ],
                      opacity: frontOpacity,
                      zIndex: 1,
                    },
                  ]}
                  {...panResponder.panHandlers}
                >
                  <GlassCard style={styles.cardInner} gradient>
                    <Pressable
                      style={styles.cardContent}
                      onPress={handleFlip}
                    >
                      <BookOpen size={28} color={colors.primary} style={styles.cardIcon} />
                      <ScrollView
                        style={styles.cardScrollView}
                        contentContainerStyle={styles.cardScrollContent}
                        showsVerticalScrollIndicator={false}
                      >
                        {currentFlashcards[currentIndex].imageUrl && (
                          <View style={styles.cardImageContainer}>
                            <Image
                              source={{ uri: currentFlashcards[currentIndex].imageUrl }}
                              style={styles.cardImage}
                              resizeMode="contain"
                            />
                          </View>
                        )}
                        <Text style={styles.questionText}>
                          {currentFlashcards[currentIndex].front}
                        </Text>
                      </ScrollView>
                      <View style={styles.tapHint}>
                        <Text style={styles.tapHintText}>Toque para virar</Text>
                      </View>
                    </Pressable>
                  </GlassCard>

                  <Animated.View
                    style={[
                      styles.resultIndicator,
                      styles.rightIndicator,
                      { opacity: rightOpacity },
                    ]}
                  >
                    <Check size={40} color="#fff" />
                  </Animated.View>

                  <Animated.View
                    style={[
                      styles.resultIndicator,
                      styles.leftIndicator,
                      { opacity: leftOpacity },
                    ]}
                  >
                    <X size={40} color="#fff" />
                  </Animated.View>
                </Animated.View>

                <Animated.View
                  style={[
                    styles.card,
                    styles.cardBack,
                    {
                      transform: [
                        { translateX: swipeAnimation },
                        { rotateY: backInterpolate },
                        { scale: scaleInterpolate },
                      ],
                      opacity: backOpacity,
                      zIndex: 1,
                    },
                  ]}
                >
                  <GlassCard style={styles.cardInner} gradient>
                    <Pressable
                      style={styles.cardContent}
                      onPress={handleFlip}
                    >
                      <ScrollView
                        style={styles.cardScrollView}
                        contentContainerStyle={styles.cardScrollContent}
                        showsVerticalScrollIndicator={false}
                      >
                        <Text style={styles.answerText}>
                          {currentFlashcards[currentIndex].back}
                        </Text>
                      </ScrollView>
                      <View style={styles.tapHint}>
                        <Text style={styles.tapHintText}>Toque para virar</Text>
                      </View>
                    </Pressable>
                  </GlassCard>
                </Animated.View>
              </View>
            </View>
          )}

          <View style={styles.actionsContainer}>
            <Pressable
              style={styles.swipeHintContainer}
              onPress={() => handleSwipe("hard")}
            >
              <GlassCard style={styles.swipeHint} gradient>
                <ArrowLeft size={24} color={colors.error} />
                <Text style={[styles.swipeHintText, { color: colors.error }]}>Difícil</Text>
              </GlassCard>
            </Pressable>

            <Pressable
              style={styles.flipButtonContainer}
              onPress={handleFlip}
            >
              <LinearGradient
                colors={colors.primaryGradient}
                style={styles.flipButton}
              >
                <RotateCw size={28} color="#fff" />
              </LinearGradient>
            </Pressable>

            <Pressable
              style={styles.swipeHintContainer}
              onPress={() => handleSwipe("easy")}
            >
              <GlassCard style={styles.swipeHint} gradient>
                <Text style={[styles.swipeHintText, { color: colors.success }]}>Fácil</Text>
                <ArrowRight size={24} color={colors.success} />
              </GlassCard>
            </Pressable>
          </View>

          {flipped && (
            <View style={styles.difficultyButtons}>
              <Button
                title="Difícil"
                onPress={() => handleSwipe("hard")}
                variant="error"
                size="large"
                style={styles.difficultyButton}
              />
              <Button
                title="Médio"
                onPress={() => handleSwipe("medium")}
                variant="secondary"
                size="large"
                style={styles.difficultyButton}
              />
              <Button
                title="Fácil"
                onPress={() => handleSwipe("easy")}
                variant="success"
                size="large"
                style={styles.difficultyButton}
              />
            </View>
          )}
        </View>
      ) : (
        <ScrollView
          style={styles.completedContainer}
          contentContainerStyle={styles.completedContent}
        >
          <Text style={styles.completedTitle}>Revisão Concluída!</Text>
          <Text style={styles.completedSubtitle}>
            Você revisou {stats.total} cartões
          </Text>

          <View style={styles.statsContainer}>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.success }]}>
                {stats.easy}
              </Text>
              <Text style={styles.statLabel}>Fácil</Text>
            </GlassCard>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.secondary }]}>
                {stats.medium || 0}
              </Text>
              <Text style={styles.statLabel}>Médio</Text>
            </GlassCard>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.error }]}>
                {stats.hard}
              </Text>
              <Text style={styles.statLabel}>Difícil</Text>
            </GlassCard>
          </View>

          <GlassCard style={styles.xpCard} gradient>
            <Text style={styles.xpText}>
              +{(stats.easy || 0) * 5 + (stats.medium || 0) * 10 + (stats.hard || 0) * 15} XP ganhos!
            </Text>
          </GlassCard>

          <View style={styles.buttonsContainer}>
            <Button
              title="Recomeçar"
              onPress={handleRestart}
              variant="primary"
              size="large"
              fullWidth
            />
            <Button
              title="Voltar para Flashcards"
              onPress={() => router.push(`/flashcards/${id}`)}
              variant="outline"
              size="large"
              fullWidth
            />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  flashcardContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 80 : 64,
  },
  progressContainer: {
    width: "100%",
    marginBottom: 16,
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textLight,
    marginBottom: 8,
    textAlign: "center",
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.backgroundDark,
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  cardContainer: {
    width: width - 40,
    height: height * 0.5,
    maxHeight: 450,
    alignItems: "center",
    justifyContent: "center",
    perspective: 1500,
    marginVertical: 10,
  },
  cardWrapper: {
    width: "100%",
    height: "100%",
    position: "relative",
  },
  card: {
    width: "100%",
    height: "100%",
    position: "absolute",
    backfaceVisibility: "hidden",
    left: 0,
    top: 0,
    borderRadius: 20,
    overflow: "hidden",
  },
  cardBack: {
    transform: [{ rotateY: "180deg" }],
  },
  cardInner: {
    width: "100%",
    height: "100%",
    padding: 24,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  cardContent: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  cardIcon: {
    marginBottom: 16,
    opacity: 0.8,
  },
  questionText: {
    fontSize: 26,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
    marginVertical: 20,
  },
  answerText: {
    fontSize: 22,
    color: colors.text,
    textAlign: "center",
    lineHeight: 32,
    marginVertical: 20,
  },
  tapHint: {
    position: "absolute",
    bottom: 16,
    alignItems: "center",
  },
  tapHintText: {
    fontSize: 14,
    color: colors.textLight,
  },
  cardScrollView: {
    flex: 1,
    width: '100%',
    maxHeight: 300,
  },
  cardScrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  resultIndicator: {
    position: "absolute",
    top: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  rightIndicator: {
    right: 20,
    backgroundColor: colors.success,
  },
  leftIndicator: {
    left: 20,
    backgroundColor: colors.error,
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginTop: 24,
    marginBottom: 16,
  },
  swipeHintContainer: {
    flex: 1,
  },
  swipeHint: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    gap: 8,
  },
  swipeHintText: {
    fontSize: 16,
    fontWeight: "600",
  },
  flipButtonContainer: {
    marginHorizontal: 16,
  },
  flipButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  difficultyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginTop: 16,
    gap: 8,
  },
  difficultyButton: {
    flex: 1,
    height: 70,
  },
  completedContainer: {
    flex: 1,
  },
  completedContent: {
    padding: 24,
    alignItems: "center",
    paddingBottom: 100,
  },
  completedTitle: {
    fontSize: 28,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    marginTop: 24,
  },
  completedSubtitle: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 32,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  statValue: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 16,
    color: colors.textLight,
  },
  xpCard: {
    width: "100%",
    padding: 20,
    alignItems: "center",
    marginBottom: 40,
  },
  xpText: {
    fontSize: 22,
    fontWeight: "600",
    color: colors.primary,
  },
  buttonsContainer: {
    width: "100%",
    gap: 16,
  },
  cardImageContainer: {
    width: "100%",
    height: 150,
    marginBottom: 16,
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: colors.backgroundLight,
  },
  cardImage: {
    width: "100%",
    height: "100%",
  },
});
