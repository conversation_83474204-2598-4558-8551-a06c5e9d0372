import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  ScrollView,
  Platform,
  Alert,
  ActivityIndicator,
  Pressable,
  Switch,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { useUserStore } from "@/store/userStore";
import { Button } from "@/components/Button";
import { ArrowLeft, ArrowRight, RotateCw, BookOpen, Brain, Zap, Filter, Sparkles } from "lucide-react-native";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";
import { FlipCard } from "@/components/FlipCard";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  Easing,
  interpolate,
  Extrapolate
} from "react-native-reanimated";

const { width, height } = Dimensions.get("window");

export default function EnhancedStudyScreen() {
  const { id, smartMode } = useLocalSearchParams<{ id: string, smartMode?: string }>();
  const router = useRouter();
  const { flashcardSets, flashcards, updateFlashcard, fetchFlashcards } = useStudyStore();
  const { addXP } = useUserStore();

  // Estado para controlar o carregamento
  const [loading, setLoading] = useState(true);

  // Estado para armazenar os dados
  const [currentSet, setCurrentSet] = useState(null);
  const [currentFlashcards, setCurrentFlashcards] = useState([]);
  const [filteredFlashcards, setFilteredFlashcards] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [completed, setCompleted] = useState(false);
  const [flipped, setFlipped] = useState(false);
  const [isSmartReview, setIsSmartReview] = useState(smartMode === 'true');
  const [stats, setStats] = useState({
    easy: 0,
    medium: 0,
    hard: 0,
    total: 0,
  });

  // Valores de animação
  const cardScale = useSharedValue(1);
  const cardOpacity = useSharedValue(1);
  const buttonsOpacity = useSharedValue(0);
  const progressValue = useSharedValue(0);

  // Carregar dados quando o componente for montado
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Verificar se o ID é um UUID válido
        const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
        if (!isValidUUID) {
          console.error('ID do conjunto inválido:', id);
          Alert.alert('Erro', 'ID do conjunto inválido. Por favor, selecione um conjunto válido.');
          router.back();
          return;
        }

        // Buscar o conjunto atual
        const set = flashcardSets.find(s => s.id === id);
        if (!set) {
          console.error('Conjunto não encontrado:', id);
          Alert.alert('Erro', 'Conjunto não encontrado. Por favor, selecione outro conjunto.');
          router.back();
          return;
        }

        setCurrentSet(set);

        // Buscar flashcards
        const loadedFlashcards = await fetchFlashcards(id);
        console.log(`Carregados ${loadedFlashcards.length} flashcards`);

        if (loadedFlashcards && loadedFlashcards.length > 0) {
          setCurrentFlashcards(loadedFlashcards);

          // Aplicar filtro inteligente se necessário
          const filtered = isSmartReview ? filterFlashcardsForSmartReview(loadedFlashcards) : loadedFlashcards;
          setFilteredFlashcards(filtered);

          // Atualizar a barra de progresso
          progressValue.value = withTiming(1 / filtered.length, {
            duration: 500,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          });
        } else {
          Alert.alert(
            'Sem flashcards',
            'Este conjunto não possui flashcards. Adicione alguns antes de iniciar o estudo.',
            [{ text: 'OK', onPress: () => router.back() }]
          );
        }
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao carregar os dados. Por favor, tente novamente.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id]);

  // Função para virar o cartão
  const handleFlip = () => {
    setFlipped(!flipped);

    // Animar a aparição dos botões de dificuldade quando o cartão é virado
    if (!flipped) {
      buttonsOpacity.value = withTiming(1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      });
    } else {
      buttonsOpacity.value = withTiming(0, {
        duration: 200,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      });
    }
  };

  // Função para filtrar flashcards para revisão inteligente
  const filterFlashcardsForSmartReview = (flashcards) => {
    if (!flashcards || flashcards.length === 0) return [];

    // Filtrar flashcards que precisam de revisão:
    // 1. Cartões difíceis (dificuldade = 3)
    // 2. Cartões que estão próximos ou passaram da data de revisão
    // 3. Cartões que nunca foram revisados (reviewCount = 0)
    const today = new Date();

    return flashcards.filter(card => {
      // Nunca revisados
      if (card.reviewCount === 0) return true;

      // Cartões difíceis
      if (card.difficulty === 3) return true;

      // Cartões com data de revisão próxima ou passada
      if (card.nextReview) {
        const nextReviewDate = new Date(card.nextReview);
        const diffDays = Math.floor((nextReviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        return diffDays <= 2; // Incluir cartões que devem ser revisados nos próximos 2 dias
      }

      return false;
    });
  };

  // Função para alternar entre revisão completa e inteligente
  const toggleReviewMode = () => {
    const newMode = !isSmartReview;
    setIsSmartReview(newMode);

    // Aplicar filtro se necessário
    const filtered = newMode ? filterFlashcardsForSmartReview(currentFlashcards) : currentFlashcards;
    setFilteredFlashcards(filtered);

    // Resetar o índice e a barra de progresso
    setCurrentIndex(0);
    setFlipped(false);

    // Atualizar a barra de progresso
    progressValue.value = withTiming(filtered.length > 0 ? 1 / filtered.length : 0, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });

    // Resetar animações
    cardScale.value = 1;
    cardOpacity.value = 1;
    buttonsOpacity.value = 0;
  };

  // Função para lidar com a classificação de dificuldade
  const handleDifficulty = (difficultyLevel: "easy" | "medium" | "hard") => {
    const cards = isSmartReview ? filteredFlashcards : currentFlashcards;
    if (cards.length === 0) return;

    // Animar a saída do cartão
    cardScale.value = withTiming(0.8, { duration: 150 });
    cardOpacity.value = withTiming(0, { duration: 300 });

    // Update flashcard difficulty
    try {
      // Convert string difficulty to number
      let difficultyValue = 0;
      if (difficultyLevel === "easy") difficultyValue = 1;
      else if (difficultyLevel === "medium") difficultyValue = 2;
      else if (difficultyLevel === "hard") difficultyValue = 3;

      // Obter o E-Factor atual (ou usar o valor padrão)
      const currentCard = cards[currentIndex];
      const currentEFactor = currentCard.eFactor || 2.5;

      // Atualizar o flashcard com a nova dificuldade, E-Factor e data da última revisão
      updateFlashcard(cards[currentIndex].id, {
        difficulty: difficultyValue,
        eFactor: currentEFactor,
        lastReviewed: new Date().toISOString()
      });

      // Atualizar também a data do último estudo do conjunto
      if (currentSet) {
        useStudyStore.getState().updateFlashcardSet(currentSet.id, {
          lastStudied: new Date().toISOString()
        });
      }

      // Adicionar mensagem de feedback
      let message = "";
      if (difficultyLevel === "easy") {
        message = "Ótimo! Este cartão será revisado em um intervalo maior.";
      } else if (difficultyLevel === "medium") {
        message = "Bom trabalho! Este cartão será revisado em um intervalo médio.";
      } else {
        message = "Sem problemas! Este cartão será revisado em breve para reforçar o aprendizado.";
      }

      console.log(message);
    } catch (error) {
      console.error('Erro ao atualizar dificuldade do flashcard:', error);
    }

    // Update stats
    setStats((prev) => ({
      ...prev,
      [difficultyLevel]: prev[difficultyLevel] + 1,
      total: prev.total + 1,
    }));

    // Add XP based on difficulty
    if (difficultyLevel === "easy") {
      addXP(5);
    } else if (difficultyLevel === "medium") {
      addXP(10);
    } else if (difficultyLevel === "hard") {
      addXP(15);
    }

    // Esconder os botões de dificuldade
    buttonsOpacity.value = withTiming(0, { duration: 200 });

    // Aguardar a animação terminar antes de mudar para o próximo cartão
    setTimeout(() => {
      const cards = isSmartReview ? filteredFlashcards : currentFlashcards;
      if (currentIndex < cards.length - 1) {
        // Move to next card
        setCurrentIndex(currentIndex + 1);
        setFlipped(false);

        // Atualizar a barra de progresso
        progressValue.value = withTiming((currentIndex + 2) / cards.length, {
          duration: 500,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        });

        // Animar a entrada do novo cartão
        cardScale.value = withSpring(1, {
          damping: 15,
          stiffness: 150,
        });
        cardOpacity.value = withTiming(1, { duration: 300 });
      } else {
        setCompleted(true);
      }
    }, 300);
  };

  const handleRestart = () => {
    setCurrentIndex(0);
    setCompleted(false);
    setFlipped(false);
    setStats({
      easy: 0,
      medium: 0,
      hard: 0,
      total: 0,
    });

    // Resetar animações
    cardScale.value = 1;
    cardOpacity.value = 1;
    buttonsOpacity.value = 0;

    // Resetar barra de progresso
    const cards = isSmartReview ? filteredFlashcards : currentFlashcards;
    progressValue.value = withTiming(1 / cards.length, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  };

  // Estilos animados
  const cardAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: cardScale.value }],
      opacity: cardOpacity.value,
    };
  });

  const buttonsAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: buttonsOpacity.value,
      transform: [
        {
          translateY: interpolate(
            buttonsOpacity.value,
            [0, 1],
            [20, 0],
            Extrapolate.CLAMP
          )
        }
      ],
    };
  });

  const progressBarAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: `${progressValue.value * 100}%`,
    };
  });

  // Tela de carregamento
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: "Carregando...",
            headerBackTitle: "Voltar",
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando flashcards...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Tela de erro se não houver conjunto
  if (!currentSet) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Flashcards não encontrados" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Conjunto de flashcards não encontrado</Text>
          <Button
            title="Voltar para flashcards"
            onPress={() => router.push("/flashcards")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  // Verificar se há flashcards para mostrar
  if (currentFlashcards.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: currentSet?.title || "Estudo de Flashcards" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Este conjunto não possui flashcards</Text>
          <Button
            title="Voltar para flashcards"
            onPress={() => router.push("/flashcards")}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: currentSet?.title || "Estudo de Flashcards",
          headerBackTitle: "Voltar",
        }}
      />

      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      {!completed ? (
        <View style={styles.flashcardContainer}>
          {/* Seletor de modo de revisão */}
          <View style={styles.reviewModeContainer}>
            <View style={styles.reviewModeSelector}>
              <View style={styles.reviewModeOption}>
                <Text style={[styles.reviewModeText, !isSmartReview && styles.reviewModeActive]}>Completa</Text>
              </View>
              <Switch
                value={isSmartReview}
                onValueChange={toggleReviewMode}
                trackColor={{ false: colors.backgroundDark, true: colors.primary }}
                thumbColor={colors.white}
                ios_backgroundColor={colors.backgroundDark}
              />
              <View style={styles.reviewModeOption}>
                <Text style={[styles.reviewModeText, isSmartReview && styles.reviewModeActive]}>Inteligente</Text>
                <Sparkles size={16} color={isSmartReview ? colors.primary : colors.textLight} style={{ marginLeft: 4 }} />
              </View>
            </View>
            <Text style={styles.reviewModeDescription}>
              {isSmartReview
                ? `Mostrando ${filteredFlashcards.length} cartões que precisam de revisão`
                : `Revisando todos os ${currentFlashcards.length} cartões`}
            </Text>
          </View>

          {/* Barra de progresso */}
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              {currentIndex + 1} de {isSmartReview ? filteredFlashcards.length : currentFlashcards.length}
            </Text>
            <View style={styles.progressBar}>
              <Animated.View
                style={[styles.progressFill, progressBarAnimatedStyle]}
              />
            </View>
          </View>

          {/* Cartão */}
          <Animated.View style={[styles.cardWrapper, cardAnimatedStyle]}>
            <FlipCard
              front={(isSmartReview ? filteredFlashcards : currentFlashcards)[currentIndex].front}
              back={(isSmartReview ? filteredFlashcards : currentFlashcards)[currentIndex].back}
              imageUrl={(isSmartReview ? filteredFlashcards : currentFlashcards)[currentIndex].imageUrl}
              onFlip={handleFlip}
            />
          </Animated.View>

          {/* Botão de virar */}
          <Pressable
            style={styles.flipButtonContainer}
            onPress={handleFlip}
          >
            <LinearGradient
              colors={colors.primaryGradient}
              style={styles.flipButton}
            >
              <RotateCw size={28} color="#fff" />
            </LinearGradient>
          </Pressable>

          {/* Botões de dificuldade - sempre visíveis */}
          <View style={styles.difficultyButtonsContainer}>
            <View style={styles.difficultyLabel}>
              <Brain size={18} color={colors.textLight} />
              <Text style={styles.difficultyLabelText}>
                {flipped ? "Como foi sua lembrança?" : "Classifique sem virar:"}
              </Text>
            </View>

            <View style={styles.difficultyButtons}>
              <Button
                title="Difícil"
                onPress={() => handleDifficulty("hard")}
                variant="error"
                size="large"
                style={styles.difficultyButton}
                leftIcon={ArrowLeft}
              />
              <Button
                title="Médio"
                onPress={() => handleDifficulty("medium")}
                variant="secondary"
                size="large"
                style={styles.difficultyButton}
              />
              <Button
                title="Fácil"
                onPress={() => handleDifficulty("easy")}
                variant="success"
                size="large"
                style={styles.difficultyButton}
                rightIcon={ArrowRight}
              />
            </View>

            <View style={styles.difficultyHint}>
              <Text style={styles.difficultyHintText}>
                {flipped
                  ? "Dica: Cartões difíceis aparecerão com mais frequência para revisão"
                  : "Você pode virar o cartão para ver a resposta antes de classificar"}
              </Text>
            </View>
          </View>
        </View>
      ) : (
        <ScrollView
          style={styles.completedContainer}
          contentContainerStyle={styles.completedContent}
        >
          <Text style={styles.completedTitle}>Revisão Concluída!</Text>
          <Text style={styles.completedSubtitle}>
            Você revisou {stats.total} cartões
          </Text>

          <View style={styles.statsContainer}>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.success }]}>
                {stats.easy}
              </Text>
              <Text style={styles.statLabel}>Fácil</Text>
              <Text style={styles.statHint}>Revisão em ~7 dias</Text>
            </GlassCard>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.secondary }]}>
                {stats.medium || 0}
              </Text>
              <Text style={styles.statLabel}>Médio</Text>
              <Text style={styles.statHint}>Revisão em ~3 dias</Text>
            </GlassCard>
            <GlassCard style={styles.statCard} gradient>
              <Text style={[styles.statValue, { color: colors.error }]}>
                {stats.hard}
              </Text>
              <Text style={styles.statLabel}>Difícil</Text>
              <Text style={styles.statHint}>Revisão em ~1 dia</Text>
            </GlassCard>
          </View>

          <GlassCard style={styles.xpCard} gradient>
            <View style={styles.xpHeader}>
              <Zap size={24} color={colors.primary} />
              <Text style={styles.xpTitle}>Experiência Ganha</Text>
            </View>
            <Text style={styles.xpText}>
              +{(stats.easy || 0) * 5 + (stats.medium || 0) * 10 + (stats.hard || 0) * 15} XP
            </Text>
            <Text style={styles.xpDescription}>
              Fácil: +5 XP | Médio: +10 XP | Difícil: +15 XP
            </Text>
          </GlassCard>

          <View style={styles.buttonsContainer}>
            <Button
              title="Recomeçar"
              onPress={handleRestart}
              variant="primary"
              size="large"
              fullWidth
            />
            <Button
              title="Voltar para Flashcards"
              onPress={() => router.push("/flashcards")}
              variant="outline"
              size="large"
              fullWidth
            />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 16,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  flashcardContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 80 : 64,
  },
  progressContainer: {
    width: "100%",
    marginBottom: 16,
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textLight,
    marginBottom: 8,
    textAlign: "center",
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.backgroundDark,
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  cardWrapper: {
    width: width - 40,
    height: 450,
    alignItems: "center",
    justifyContent: "center",
  },
  flipButtonContainer: {
    marginVertical: 16,
  },
  flipButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  difficultyButtonsContainer: {
    width: "100%",
    marginTop: 16,
    marginBottom: 16,
  },
  reviewModeContainer: {
    width: "100%",
    marginBottom: 16,
    alignItems: "center",
  },
  reviewModeSelector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
    backgroundColor: colors.backgroundDark,
    borderRadius: 20,
    padding: 4,
  },
  reviewModeOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
  },
  reviewModeText: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.textLight,
  },
  reviewModeActive: {
    color: colors.primary,
    fontWeight: "700",
  },
  reviewModeDescription: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  difficultyLabel: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  difficultyLabelText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textLight,
    marginLeft: 8,
  },
  difficultyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    gap: 8,
  },
  difficultyButton: {
    flex: 1,
  },
  difficultyHint: {
    marginTop: 12,
    alignItems: "center",
  },
  difficultyHintText: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  completedContainer: {
    flex: 1,
  },
  completedContent: {
    padding: 24,
    alignItems: "center",
    paddingBottom: 100,
  },
  completedTitle: {
    fontSize: 28,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
    marginTop: 24,
  },
  completedSubtitle: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 32,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  statValue: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  statHint: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  xpCard: {
    width: "100%",
    padding: 20,
    alignItems: "center",
    marginBottom: 40,
  },
  xpHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  xpTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginLeft: 8,
  },
  xpText: {
    fontSize: 32,
    fontWeight: "700",
    color: colors.primary,
    marginBottom: 8,
  },
  xpDescription: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: "center",
  },
  buttonsContainer: {
    width: "100%",
    gap: 16,
  },
});
