import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Alert,
  ScrollView,
  FlatList,
  Pressable,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator
} from "react-native";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { colors } from "@/constants/colors";
import { useStudyStore } from "@/store/studyStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import { SubjectSelector } from "@/components/SubjectSelector";
import { Edit, Trash, Plus, ArrowLeft, Check, BookOpen } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { RouteGuard } from '@/components/RouteGuard';

interface FlashcardItem {
  id: string;
  front: string;
  back: string;
}

export default function EditFlashcardSetScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const {
    subjects,
    flashcardSets,
    flashcards,
    fetchSubjects,
    fetchFlashcardSets,
    fetchFlashcards,
    addFlashcard,
    updateFlashcard,
    updateFlashcardSet
  } = useStudyStore();

  // Estado de carregamento
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Conjunto de flashcards
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | undefined>();

  // Flashcards
  const [currentFlashcards, setCurrentFlashcards] = useState<FlashcardItem[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState("");
  const [currentAnswer, setCurrentAnswer] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showFlashcardForm, setShowFlashcardForm] = useState(false);

  // Etapas do formulário
  const [currentStep, setCurrentStep] = useState(1); // 1: Informações do conjunto, 2: Editar flashcards

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Carregar matérias
        await fetchSubjects();

        // Carregar conjuntos de flashcards se ainda não estiverem carregados
        if (flashcardSets.length === 0) {
          await fetchFlashcardSets();
        }

        // Carregar flashcards do conjunto
        const cards = await fetchFlashcards(id as string);

        // Encontrar o conjunto atual
        const currentSet = flashcardSets.find(set => set.id === id);

        if (currentSet) {
          setTitle(currentSet.title);
          setDescription(currentSet.description || "");
          setSelectedSubject(currentSet.subject || "");

          // Encontrar o ID da matéria selecionada
          const subject = subjects.find(s => s.title === currentSet.subject);
          setSelectedSubjectId(subject?.id);

          // Formatar flashcards para o estado local
          const formattedCards = cards.map(card => ({
            id: card.id,
            front: card.front,
            back: card.back
          }));

          setCurrentFlashcards(formattedCards);
        } else {
          Alert.alert("Erro", "Conjunto de flashcards não encontrado.");
          router.back();
        }
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        Alert.alert("Erro", "Não foi possível carregar os dados do conjunto de flashcards.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [id]);

  const handleAddFlashcard = async () => {
    if (!currentQuestion.trim()) {
      Alert.alert("Erro", "Por favor, insira uma pergunta para o flashcard.");
      return;
    }

    if (!currentAnswer.trim()) {
      Alert.alert("Erro", "Por favor, insira uma resposta para o flashcard.");
      return;
    }

    try {
      if (editingIndex !== null) {
        // Editar flashcard existente
        const flashcard = currentFlashcards[editingIndex];

        await updateFlashcard(flashcard.id, {
          front: currentQuestion,
          back: currentAnswer
        });

        // Atualizar estado local
        const updatedFlashcards = [...currentFlashcards];
        updatedFlashcards[editingIndex] = {
          ...flashcard,
          front: currentQuestion,
          back: currentAnswer
        };

        setCurrentFlashcards(updatedFlashcards);
        setEditingIndex(null);
      } else {
        // Adicionar novo flashcard
        const newFlashcard = {
          setId: id as string,
          front: currentQuestion,
          back: currentAnswer,
          difficulty: 0,
          nextReview: null,
          reviewCount: 0,
          imageUrl: null,
          subject_id: selectedSubjectId
        };

        const savedFlashcard = await addFlashcard(newFlashcard);

        // Adicionar ao estado local
        setCurrentFlashcards([...currentFlashcards, {
          id: savedFlashcard.id,
          front: savedFlashcard.front,
          back: savedFlashcard.back
        }]);
      }

      // Limpar formulário
      setCurrentQuestion("");
      setCurrentAnswer("");
      setShowFlashcardForm(false);
    } catch (error) {
      console.error("Erro ao salvar flashcard:", error);
      Alert.alert("Erro", "Não foi possível salvar o flashcard. Por favor, tente novamente.");
    }
  };

  const handleEditFlashcard = (index: number) => {
    const flashcard = currentFlashcards[index];
    setCurrentQuestion(flashcard.front);
    setCurrentAnswer(flashcard.back);
    setEditingIndex(index);
    setShowFlashcardForm(true);
  };

  const handleDeleteFlashcard = (index: number) => {
    Alert.alert(
      "Confirmar exclusão",
      "Tem certeza que deseja excluir este flashcard?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Excluir",
          onPress: async () => {
            try {
              const flashcard = currentFlashcards[index];

              // Excluir flashcard no Supabase
              await updateFlashcard(flashcard.id, {
                deleted: true
              });

              // Atualizar estado local
              const updatedFlashcards = [...currentFlashcards];
              updatedFlashcards.splice(index, 1);
              setCurrentFlashcards(updatedFlashcards);
            } catch (error) {
              console.error("Erro ao excluir flashcard:", error);
              Alert.alert("Erro", "Não foi possível excluir o flashcard. Por favor, tente novamente.");
            }
          },
          style: "destructive"
        }
      ]
    );
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      if (!title.trim()) {
        Alert.alert("Erro", "Por favor, insira um título para o conjunto.");
        return;
      }
      setCurrentStep(2);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSaveSet = async () => {
    if (!title.trim()) {
      Alert.alert("Erro", "Por favor, insira um título para o conjunto.");
      return;
    }

    try {
      setIsSaving(true);

      // Atualizar o conjunto de flashcards
      await updateFlashcardSet(id as string, {
        title,
        description,
        subject: selectedSubject
      });

      Alert.alert(
        "Sucesso",
        "Conjunto de flashcards atualizado com sucesso.",
        [
          {
            text: "OK",
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error("Erro ao atualizar conjunto de flashcards:", error);
      Alert.alert("Erro", "Não foi possível atualizar o conjunto de flashcards. Por favor, tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };

  const renderFlashcardItem = ({ item, index }: { item: FlashcardItem, index: number }) => (
    <GlassCard style={styles.flashcardItem}>
      <View style={styles.flashcardContent}>
        <View style={styles.flashcardHeader}>
          <Text style={styles.flashcardNumber}>Cartão {index + 1}</Text>
          <View style={styles.flashcardActions}>
            <Pressable
              style={styles.actionButton}
              onPress={() => handleEditFlashcard(index)}
            >
              <Edit size={18} color={colors.primary} />
            </Pressable>
            <Pressable
              style={styles.actionButton}
              onPress={() => handleDeleteFlashcard(index)}
            >
              <Trash size={18} color={colors.error} />
            </Pressable>
          </View>
        </View>
        <View style={styles.flashcardBody}>
          <View style={styles.flashcardSection}>
            <Text style={styles.flashcardLabel}>Pergunta:</Text>
            <Text style={styles.flashcardText}>{item.front}</Text>
          </View>
          <View style={styles.flashcardSection}>
            <Text style={styles.flashcardLabel}>Resposta:</Text>
            <Text style={styles.flashcardText}>{item.back}</Text>
          </View>
        </View>
      </View>
    </GlassCard>
  );

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      <View style={[styles.stepDot, currentStep >= 1 && styles.activeStepDot]} />
      <View style={styles.stepLine} />
      <View style={[styles.stepDot, currentStep >= 2 && styles.activeStepDot]} />
    </View>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Informações do Conjunto</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Título</Text>
              <TextInput
                style={styles.input}
                placeholder="Título do conjunto"
                placeholderTextColor={colors.textMedium}
                value={title}
                onChangeText={setTitle}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Matéria</Text>
              <SubjectSelector
                value={selectedSubject}
                onChange={(value) => {
                  setSelectedSubject(value);
                  // Encontrar o ID da matéria selecionada
                  const subject = subjects.find(s => s.title === value);
                  setSelectedSubjectId(subject?.id);
                }}
                placeholder="Selecione uma matéria"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Descrição do conjunto (opcional)"
                placeholderTextColor={colors.textMedium}
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Editar Flashcards</Text>

            {showFlashcardForm ? (
              <View style={styles.flashcardForm}>
                <Text style={styles.formTitle}>
                  {editingIndex !== null ? "Editar Flashcard" : "Novo Flashcard"}
                </Text>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Pergunta</Text>
                  <TextInput
                    style={[styles.input, styles.textArea]}
                    placeholder="Digite a pergunta do flashcard"
                    placeholderTextColor={colors.textMedium}
                    value={currentQuestion}
                    onChangeText={setCurrentQuestion}
                    multiline
                    numberOfLines={3}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Resposta</Text>
                  <TextInput
                    style={[styles.input, styles.textArea]}
                    placeholder="Digite a resposta do flashcard"
                    placeholderTextColor={colors.textMedium}
                    value={currentAnswer}
                    onChangeText={setCurrentAnswer}
                    multiline
                    numberOfLines={5}
                  />
                </View>

                <View style={styles.formButtons}>
                  <Button
                    title="Cancelar"
                    onPress={() => {
                      setCurrentQuestion("");
                      setCurrentAnswer("");
                      setEditingIndex(null);
                      setShowFlashcardForm(false);
                    }}
                    variant="secondary"
                    style={styles.formButton}
                  />
                  <Button
                    title={editingIndex !== null ? "Atualizar" : "Adicionar"}
                    onPress={handleAddFlashcard}
                    variant="primary"
                    style={styles.formButton}
                  />
                </View>
              </View>
            ) : (
              <>
                {currentFlashcards.length > 0 ? (
                  <View style={styles.flashcardList}>
                    {currentFlashcards.map((item, index) => (
                      <View key={item.id}>
                        {renderFlashcardItem({item, index})}
                      </View>
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <BookOpen size={48} color={colors.textLight} style={styles.emptyIcon} />
                    <Text style={styles.emptyText}>
                      Você ainda não adicionou nenhum flashcard.
                    </Text>
                  </View>
                )}

                <Button
                  title="Adicionar Flashcard"
                  onPress={() => {
                    setCurrentQuestion("");
                    setCurrentAnswer("");
                    setEditingIndex(null);
                    setShowFlashcardForm(true);
                  }}
                  variant="primary"
                  icon={Plus}
                  style={styles.addButton}
                />
              </>
            )}
          </View>
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando conjunto de flashcards...</Text>
      </View>
    );
  }

  return (
    <RouteGuard resourceId={id as string} tableName="flashcard_sets">
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <Stack.Screen
          options={{
            title: "Editar Conjunto de Flashcards",
            headerBackTitle: "Voltar",
          }}
        />

      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      <View style={styles.content}>
        {renderStepIndicator()}

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {renderStepContent()}
        </ScrollView>

        <View style={styles.footer}>
          {currentStep > 1 && (
            <Button
              title="Voltar"
              onPress={handlePreviousStep}
              variant="secondary"
              style={styles.footerButton}
              icon={ArrowLeft}
            />
          )}

          {currentStep < 2 ? (
            <Button
              title="Próximo"
              onPress={handleNextStep}
              variant="primary"
              style={styles.footerButton}
              iconPosition="right"
            />
          ) : (
            <Button
              title={isSaving ? "Salvando..." : "Salvar Alterações"}
              onPress={handleSaveSet}
              variant="primary"
              style={styles.footerButton}
              icon={Check}
              disabled={isSaving}
            />
          )}
        </View>
      </View>
    </KeyboardAvoidingView>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textMedium,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  stepIndicator: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  stepDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.border,
  },
  activeStepDot: {
    backgroundColor: colors.primary,
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: colors.border,
    marginHorizontal: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  stepContent: {
    paddingBottom: 16,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  flashcardList: {
    maxHeight: 400,
  },
  flashcardListContent: {
    paddingBottom: 16,
  },
  flashcardItem: {
    marginBottom: 12,
    padding: 12,
  },
  flashcardContent: {
    width: "100%",
  },
  flashcardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  flashcardNumber: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
  },
  flashcardActions: {
    flexDirection: "row",
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  flashcardBody: {
    paddingVertical: 4,
  },
  flashcardSection: {
    marginBottom: 8,
  },
  flashcardLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textMedium,
    marginBottom: 4,
  },
  flashcardText: {
    fontSize: 16,
    color: colors.text,
  },
  emptyContainer: {
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    marginBottom: 16,
  },
  emptyIcon: {
    marginBottom: 16,
    opacity: 0.5,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: "center",
  },
  addButton: {
    marginTop: 8,
  },
  flashcardForm: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
    textAlign: "center",
  },
  formButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  formButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});
