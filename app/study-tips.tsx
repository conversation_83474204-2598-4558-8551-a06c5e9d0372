import React from "react";
import { View, Text, StyleSheet, ScrollView, SafeAreaView } from "react-native";
import { Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { GlassCard } from "@/components/GlassCard";
import { LinearGradient } from "expo-linear-gradient";
import { Button } from "@/components/Button";
import { Clock, Brain, BookOpen, Calendar, Coffee, Moon, Sun, Lightbulb } from "lucide-react-native";

export default function StudyTipsScreen() {
  const router = useRouter();

  const studyTips = [
    {
      title: "Técnica Pomodoro",
      description: "Estude por 25 minutos e descanse por 5 minutos. Após 4 ciclos, faça uma pausa mais longa de 15-30 minutos.",
      icon: Clock,
      color: colors.primary,
    },
    {
      title: "Revisão Espaçada",
      description: "Revise o conteúdo em intervalos crescentes: 1 dia, 3 dias, 1 semana, 2 semanas, 1 mês.",
      icon: Calendar,
      color: colors.secondary,
    },
    {
      title: "Aprendizado Ativo",
      description: "Explique o conteúdo em voz alta, faça resumos e crie mapas mentais para fixar melhor o conhecimento.",
      icon: Brain,
      color: colors.accent1,
    },
    {
      title: "Ambiente Adequado",
      description: "Estude em um local silencioso, bem iluminado e livre de distrações. Desligue notificações do celular.",
      icon: Lightbulb,
      color: colors.accent2,
    },
    {
      title: "Sono de Qualidade",
      description: "Durma pelo menos 7-8 horas por noite. O sono é essencial para a consolidação da memória.",
      icon: Moon,
      color: colors.accent3,
    },
    {
      title: "Pausas Estratégicas",
      description: "Faça pausas curtas durante o estudo para manter a concentração e evitar a fadiga mental.",
      icon: Coffee,
      color: colors.success,
    },
    {
      title: "Estudo pela Manhã",
      description: "Aproveite as primeiras horas do dia quando seu cérebro está mais alerta para estudar conteúdos complexos.",
      icon: Sun,
      color: colors.secondary,
    },
    {
      title: "Leitura Ativa",
      description: "Faça anotações, sublinhe pontos importantes e formule perguntas enquanto lê para melhorar a compreensão.",
      icon: BookOpen,
      color: colors.primary,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: "Dicas de Estudo" }} />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <GlassCard style={styles.headerCard} gradient>
          <View style={styles.headerContent}>
            <Lightbulb size={40} color={colors.primary} />
            <Text style={styles.headerTitle}>Dicas para Estudar Melhor</Text>
            <Text style={styles.headerDescription}>
              Conheça técnicas e estratégias para otimizar seu tempo de estudo e melhorar sua aprendizagem.
            </Text>
          </View>
        </GlassCard>

        <View style={styles.tipsContainer}>
          {studyTips.map((tip, index) => (
            <GlassCard key={index} style={styles.tipCard}>
              <View style={styles.tipHeader}>
                <View
                  style={[styles.tipIconContainer, { backgroundColor: tip.color + "20" }]}
                >
                  <tip.icon size={24} color={tip.color} />
                </View>
                <Text style={styles.tipTitle}>{tip.title}</Text>
              </View>
              <Text style={styles.tipDescription}>{tip.description}</Text>
            </GlassCard>
          ))}
        </View>

        <Button
          title="Voltar para o Início"
          onPress={() => router.push("/")}
          variant="primary"
          size="large"
          style={styles.button}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    marginBottom: 24,
    padding: 20,
  },
  headerContent: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  headerDescription: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: "center",
    lineHeight: 22,
  },
  tipsContainer: {
    marginBottom: 24,
  },
  tipCard: {
    marginBottom: 16,
    padding: 16,
  },
  tipHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  tipIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  tipDescription: {
    fontSize: 14,
    color: colors.textLight,
    lineHeight: 20,
  },
  button: {
    marginTop: 8,
  },
});
