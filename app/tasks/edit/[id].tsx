import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  ScrollView,
  Alert,
  Switch,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator
} from 'react-native';
import { colors } from '@/constants/colors';
import { TodoItem } from '@/types';
import { Calendar, Link } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useCalendarStore } from '@/store/calendarStore';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { GlassCard } from '@/components/GlassCard';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';

export default function EditTaskScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { todos, events, fetchTodos, fetchEvents, updateTodo } = useCalendarStore();
  
  const [loading, setLoading] = useState(true);
  const [originalTask, setOriginalTask] = useState<TodoItem | null>(null);
  const [linkedEvent, setLinkedEvent] = useState<string | null>(null);
  
  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState(new Date());
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [hasDeadline, setHasDeadline] = useState(true);
  const [linkedToEvent, setLinkedToEvent] = useState(false);
  const [completed, setCompleted] = useState(false);
  
  // Date picker visibility
  const [showDatePicker, setShowDatePicker] = useState(false);
  
  // Form validation
  const [titleError, setTitleError] = useState(false);
  
  // Priority options
  const priorityOptions = [
    { value: 'low', label: 'Baixa', color: '#10B981' },
    { value: 'medium', label: 'Média', color: '#F59E0B' },
    { value: 'high', label: 'Alta', color: '#EF4444' }
  ];
  
  // Load task data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchTodos();
      await fetchEvents();
      setLoading(false);
    };
    
    loadData();
  }, []);
  
  // Find task and initialize form
  useEffect(() => {
    if (todos.length > 0 && id) {
      const foundTask = todos.find(t => t.id === id);
      if (foundTask) {
        setOriginalTask(foundTask);
        
        // Initialize form with task data
        setTitle(foundTask.title);
        setDescription(foundTask.description || '');
        setCompleted(foundTask.completed || false);
        setPriority(foundTask.priority || 'medium');
        
        if (foundTask.dueDate) {
          setDueDate(parseISO(foundTask.dueDate));
          setHasDeadline(true);
        } else {
          setHasDeadline(false);
        }
        
        if (foundTask.event_id) {
          setLinkedToEvent(true);
          setLinkedEvent(foundTask.event_id);
          
          // Find linked event to display its title
          const event = events.find(e => e.id === foundTask.event_id);
          if (event) {
            setLinkedEvent(event.id);
          }
        } else {
          setLinkedToEvent(false);
        }
      } else {
        Alert.alert('Erro', 'Tarefa não encontrada');
        router.back();
      }
    }
  }, [todos, events, id]);
  
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };
  
  // Validate form before saving
  const validateForm = () => {
    let isValid = true;
    
    // Title validation
    if (!title.trim()) {
      setTitleError(true);
      isValid = false;
    } else {
      setTitleError(false);
    }
    
    return isValid;
  };
  
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Erro', 'Por favor, corrija os erros no formulário antes de salvar.');
      return;
    }
    
    if (!originalTask) {
      Alert.alert('Erro', 'Tarefa não encontrada');
      return;
    }
    
    const updatedTask: Partial<TodoItem> = {
      title,
      description,
      priority,
      completed,
      dueDate: hasDeadline ? dueDate.toISOString() : undefined,
      event_id: linkedToEvent ? linkedEvent : undefined
    };
    
    try {
      await updateTodo(originalTask.id, updatedTask);
      Alert.alert('Sucesso', 'Tarefa atualizada com sucesso!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Erro ao atualizar tarefa:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao atualizar a tarefa. Tente novamente.');
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="dark" />
        <Stack.Screen options={{ title: "Editar Tarefa" }} />
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <Stack.Screen options={{ title: "Editar Tarefa" }} />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      
      <ScrollView style={styles.scrollView}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Título <Text style={styles.requiredMark}>*</Text></Text>
            <TextInput
              style={[styles.input, titleError && styles.inputError]}
              value={title}
              onChangeText={(text) => {
                setTitle(text);
                if (text.trim()) setTitleError(false);
              }}
              placeholder="Título da tarefa"
              placeholderTextColor={colors.textLight}
            />
            {titleError && (
              <Text style={styles.errorText}>O título é obrigatório</Text>
            )}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descrição da tarefa"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
          
          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Prazo</Text>
              <Switch
                value={hasDeadline}
                onValueChange={setHasDeadline}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={hasDeadline ? colors.primary : colors.white}
              />
            </View>
          </View>
          
          {linkedEvent && (
            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Vincular ao evento</Text>
                <Switch
                  value={linkedToEvent}
                  onValueChange={setLinkedToEvent}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={linkedToEvent ? colors.primary : colors.white}
                />
              </View>
              {linkedToEvent && linkedEvent && (
                <View style={styles.linkedEventContainer}>
                  <Link size={16} color={colors.primary} />
                  <Text style={styles.linkedEventText}>
                    {events.find(e => e.id === linkedEvent)?.title || 'Evento vinculado'}
                  </Text>
                </View>
              )}
            </View>
          )}
          
          {hasDeadline && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de prazo</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(dueDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showDatePicker && (
                <DateTimePicker
                  value={dueDate}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                />
              )}
            </View>
          )}
          
          <View style={styles.formGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Concluída</Text>
              <Switch
                value={completed}
                onValueChange={setCompleted}
                trackColor={{ false: colors.border, true: `${colors.success}80` }}
                thumbColor={completed ? colors.success : colors.white}
              />
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Prioridade</Text>
            <View style={styles.priorityContainer}>
              {priorityOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.priorityButton,
                    priority === option.value && styles.priorityButtonActive,
                    priority === option.value && { borderColor: option.color }
                  ]}
                  onPress={() => setPriority(option.value as any)}
                >
                  <View style={[styles.priorityDot, { backgroundColor: option.color }]} />
                  <Text
                    style={[
                      styles.priorityText,
                      priority === option.value && styles.priorityTextActive,
                      priority === option.value && { color: option.color }
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.buttonContainer}>
            <Pressable
              style={[styles.button, styles.cancelButton]}
              onPress={() => router.back()}
            >
              <Text style={styles.buttonText}>Cancelar</Text>
            </Pressable>
            <Pressable
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
            >
              <Text style={[styles.buttonText, styles.saveButtonText]}>Salvar</Text>
            </Pressable>
          </View>
        </GlassCard>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  requiredMark: {
    color: colors.error,
  },
  input: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  inputError: {
    borderColor: colors.error,
    borderWidth: 1.5,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
  textArea: {
    minHeight: 100,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  priorityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 8,
    flex: 1,
    marginHorizontal: 4,
    backgroundColor: colors.background,
  },
  priorityButtonActive: {
    backgroundColor: `${colors.backgroundLight}80`,
    borderWidth: 1.5,
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
  },
  priorityTextActive: {
    fontWeight: '700',
  },
  linkedEventContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}10`,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  linkedEventText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  saveButtonText: {
    color: colors.white,
  },
});
