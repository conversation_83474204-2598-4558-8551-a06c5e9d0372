import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  ScrollView,
  Alert,
  ActivityIndicator,
  SafeAreaView
} from 'react-native';
import { colors } from '@/constants/colors';
import { TodoItem, CalendarEvent } from '@/types';
import { Calendar, Clock, BookOpen, Edit, Trash2, CheckCircle, Link } from 'lucide-react-native';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useCalendarStore } from '@/store/calendarStore';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { GlassCard } from '@/components/GlassCard';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Button } from '@/components/Button';

export default function TaskDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { todos, events, fetchTodos, fetchEvents, deleteTodo, toggleTodoCompleted } = useCalendarStore();
  
  const [task, setTask] = useState<TodoItem | null>(null);
  const [linkedEvent, setLinkedEvent] = useState<CalendarEvent | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Fetch task details
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchTodos();
      await fetchEvents();
      setLoading(false);
    };
    
    loadData();
  }, []);
  
  // Find task and linked event
  useEffect(() => {
    if (todos.length > 0 && id) {
      const foundTask = todos.find(t => t.id === id);
      if (foundTask) {
        setTask(foundTask);
        
        // Find linked event if exists
        if (foundTask.event_id) {
          const event = events.find(e => e.id === foundTask.event_id);
          if (event) {
            setLinkedEvent(event);
          }
        }
      } else {
        Alert.alert('Erro', 'Tarefa não encontrada');
        router.back();
      }
    }
  }, [todos, events, id]);
  
  const handleEditTask = () => {
    if (task) {
      router.push(`/tasks/edit/${task.id}`);
    }
  };
  
  const handleDeleteTask = () => {
    if (task) {
      Alert.alert(
        'Excluir Tarefa',
        'Tem certeza que deseja excluir esta tarefa? Esta ação não pode ser desfeita.',
        [
          {
            text: 'Cancelar',
            style: 'cancel'
          },
          {
            text: 'Excluir',
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteTodo(task.id);
                Alert.alert('Sucesso', 'Tarefa excluída com sucesso');
                router.back();
              } catch (error) {
                console.error('Erro ao excluir tarefa:', error);
                Alert.alert('Erro', 'Ocorreu um erro ao excluir a tarefa');
              }
            }
          }
        ]
      );
    }
  };
  
  const handleToggleCompleted = () => {
    if (task) {
      toggleTodoCompleted(task.id);
      // Atualizar o estado local
      setTask({
        ...task,
        completed: !task.completed
      });
    }
  };
  
  const handleViewEvent = () => {
    if (linkedEvent) {
      router.push(`/events/${linkedEvent.id}`);
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="dark" />
        <Stack.Screen options={{ title: "Detalhes da Tarefa" }} />
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }
  
  if (!task) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="dark" />
        <Stack.Screen options={{ title: "Detalhes da Tarefa" }} />
        <Text style={styles.errorText}>Tarefa não encontrada</Text>
      </SafeAreaView>
    );
  }
  
  // Verificar se a tarefa está atrasada
  const isOverdue = task.dueDate && !task.completed && new Date(task.dueDate) < new Date();
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <Stack.Screen 
        options={{ 
          title: "Detalhes da Tarefa",
          headerRight: () => (
            <View style={styles.headerButtons}>
              <Pressable 
                style={styles.headerButton} 
                onPress={handleEditTask}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Edit size={24} color={colors.primary} />
              </Pressable>
              <Pressable 
                style={styles.headerButton} 
                onPress={handleDeleteTask}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Trash2 size={24} color={colors.error} />
              </Pressable>
            </View>
          )
        }} 
      />
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      
      <ScrollView style={styles.scrollView}>
        <GlassCard style={[
          styles.taskCard, 
          { borderLeftColor: getPriorityColor(task.priority) }
        ]}>
          <View style={styles.taskHeader}>
            <View style={styles.titleContainer}>
              <Text style={[
                styles.taskTitle,
                task.completed && styles.completedTaskTitle,
                isOverdue && !task.completed && styles.overdueTaskTitle
              ]}>
                {task.title}
              </Text>
              <View style={styles.priorityContainer}>
                <View style={[
                  styles.priorityDot,
                  { backgroundColor: getPriorityColor(task.priority) }
                ]} />
                <Text style={styles.priorityText}>
                  {getPriorityLabel(task.priority)}
                </Text>
              </View>
            </View>
            <Pressable
              style={styles.completedButton}
              onPress={handleToggleCompleted}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              {task.completed ? (
                <CheckCircle size={28} color={colors.success} />
              ) : (
                <CheckCircle size={28} color={isOverdue ? colors.error : colors.border} />
              )}
            </Pressable>
          </View>
          
          {task.description ? (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Descrição</Text>
              <Text style={[
                styles.description,
                task.completed && styles.completedText
              ]}>
                {task.description}
              </Text>
            </View>
          ) : null}
          
          {task.dueDate && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Data de Prazo</Text>
              <View style={styles.dateTimeItem}>
                <Calendar size={20} color={isOverdue && !task.completed ? colors.error : colors.primary} />
                <Text style={[
                  styles.dateTimeText,
                  isOverdue && !task.completed && styles.overdueText,
                  task.completed && styles.completedText
                ]}>
                  {format(parseISO(task.dueDate), 'dd/MM/yyyy', { locale: ptBR })}
                  {isOverdue && !task.completed && ' (Atrasada)'}
                </Text>
              </View>
            </View>
          )}
          
          {task.subject && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Matéria</Text>
              <View style={styles.dateTimeItem}>
                <BookOpen size={20} color={colors.primary} />
                <Text style={[
                  styles.dateTimeText,
                  task.completed && styles.completedText
                ]}>
                  {task.subject}
                </Text>
              </View>
            </View>
          )}
          
          {linkedEvent && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Evento Vinculado</Text>
              <Pressable 
                style={styles.linkedEventContainer}
                onPress={handleViewEvent}
              >
                <Link size={20} color={colors.primary} />
                <View style={styles.linkedEventContent}>
                  <Text style={styles.linkedEventTitle}>{linkedEvent.title}</Text>
                  <Text style={styles.linkedEventDate}>
                    {format(parseISO(linkedEvent.startDate), 'dd/MM/yyyy', { locale: ptBR })}
                  </Text>
                </View>
              </Pressable>
            </View>
          )}
          
          <View style={styles.statusSection}>
            <Text style={[
              styles.statusText,
              task.completed ? styles.completedStatusText : (isOverdue ? styles.overdueStatusText : styles.pendingStatusText)
            ]}>
              {task.completed 
                ? 'Concluída' 
                : (isOverdue 
                  ? 'Atrasada' 
                  : 'Pendente')}
            </Text>
          </View>
          
          <Button
            title={task.completed ? "Marcar como Pendente" : "Marcar como Concluída"}
            onPress={handleToggleCompleted}
            variant={task.completed ? "outline" : "primary"}
            style={styles.toggleButton}
          />
        </GlassCard>
      </ScrollView>
    </SafeAreaView>
  );
}

function getPriorityColor(priority: string) {
  switch (priority) {
    case 'high':
      return '#EF4444';
    case 'medium':
      return '#F59E0B';
    case 'low':
      return '#10B981';
    default:
      return '#8B5CF6';
  }
}

function getPriorityLabel(priority: string) {
  switch (priority) {
    case 'high':
      return 'Alta';
    case 'medium':
      return 'Média';
    case 'low':
      return 'Baixa';
    default:
      return 'Média';
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 16,
  },
  taskCard: {
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 5,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  completedTaskTitle: {
    textDecorationLine: 'line-through',
    color: colors.textLight,
  },
  overdueTaskTitle: {
    color: colors.error,
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 14,
    color: colors.textLight,
  },
  completedButton: {
    marginLeft: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  completedText: {
    color: colors.textLight,
    textDecorationLine: 'line-through',
  },
  overdueText: {
    color: colors.error,
  },
  dateTimeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  dateTimeText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  linkedEventContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}10`,
    padding: 12,
    borderRadius: 8,
  },
  linkedEventContent: {
    marginLeft: 8,
    flex: 1,
  },
  linkedEventTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
  },
  linkedEventDate: {
    fontSize: 14,
    color: colors.textLight,
  },
  statusSection: {
    marginBottom: 16,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  pendingStatusText: {
    backgroundColor: `${colors.primary}20`,
    color: colors.primary,
  },
  completedStatusText: {
    backgroundColor: `${colors.success}20`,
    color: colors.success,
  },
  overdueStatusText: {
    backgroundColor: `${colors.error}20`,
    color: colors.error,
  },
  toggleButton: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
  },
});
