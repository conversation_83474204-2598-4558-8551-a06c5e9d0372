import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Pressable,
  KeyboardAvoidingView,
  Platform,
  Modal,
  Alert,
} from "react-native";
import { useLocalSearchParams, Stack, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { useNoteStore } from "@/store/noteStore";
import { Button } from "@/components/Button";
import { GlassCard } from "@/components/GlassCard";
import { Edit2, Save, Tag, Clock, Book, Trash2, Plus, Image as ImageIcon, File } from "lucide-react-native";
import { formatDateTime } from "@/utils/dateUtils";
import { Note, NoteBlock, NoteBlockType, Attachment } from "@/types";
import { NoteBlock as NoteBlockComponent } from "@/components/NoteBlock";
import { BlockMenu } from "@/components/BlockMenu";
import { TableEditor } from "@/components/TableEditor";
import { KeyboardToolbar } from "@/components/KeyboardToolbar";
import { Keyboard } from "react-native";
import RouteGuard from '@/components/RouteGuard';

export default function NoteScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { notes, updateNote, deleteNote } = useNoteStore();

  const note = notes.find((n) => n.id === id);

  // Iniciar em modo de edição se for uma nota nova (sem conteúdo)
  const [isEditing, setIsEditing] = useState(note?.content === "");
  const [title, setTitle] = useState(note?.title || "");
  const [content, setContent] = useState(note?.content || "");
  const [newTag, setNewTag] = useState("");
  const [tags, setTags] = useState<string[]>(note?.tags || []);
  const [blocks, setBlocks] = useState<NoteBlock[]>(note?.blocks || []);
  const [attachments, setAttachments] = useState<Attachment[]>(note?.attachments || []);
  const [showBlockMenu, setShowBlockMenu] = useState(false);
  const [showTableEditor, setShowTableEditor] = useState(false);
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
  const [tableData, setTableData] = useState<string[][]>([["", ""], ["", ""]]);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Keyboard listeners
  useEffect(() => {
    const keyboardShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardShowListener.remove();
      keyboardHideListener.remove();
    };
  }, []);

  // Initialize blocks if they don't exist
  useEffect(() => {
    if (note && (!note.blocks || note.blocks.length === 0) && note.content) {
      // Convert legacy content to blocks
      const initialBlock: NoteBlock = {
        id: `block_${Date.now()}`,
        type: "text",
        content: note.content,
        position: 0,
      };
      setBlocks([initialBlock]);
    } else if (note && note.blocks) {
      setBlocks(note.blocks);
    }
  }, [note]);

  const handleSave = () => {
    if (note && title.trim()) {
      // Combine all text blocks into content for backward compatibility
      const combinedContent = blocks
        .filter(block => block.type === "text")
        .map(block => block.content)
        .join("\n\n");

      updateNote(note.id, {
        title: title.trim(),
        content: combinedContent,
        tags,
        blocks,
        attachments,
        updatedAt: new Date().toISOString(),
      });
      setIsEditing(false);
    }
  };

  const handleAddBlock = (type: NoteBlockType, afterBlockId?: string) => {
    // Encontrar o bloco atual com base na seleção do cursor
    const currentBlock = blocks.find(b => b.selection !== undefined);

    // Se temos um bloco com cursor ativo, usamos ele como referência
    const targetBlockId = afterBlockId || (currentBlock ? currentBlock.id : null);

    const newBlock: NoteBlock = {
      id: `block_${Date.now()}`,
      type,
      content: "",
      position: targetBlockId
        ? blocks.findIndex(b => b.id === targetBlockId) + 1
        : blocks.length,
    };

    let newBlocks: NoteBlock[];

    if (targetBlockId) {
      // Insert after the specified block
      const index = blocks.findIndex(b => b.id === targetBlockId);
      newBlocks = [
        ...blocks.slice(0, index + 1),
        newBlock,
        ...blocks.slice(index + 1),
      ];
      // Update positions
      newBlocks = newBlocks.map((block, idx) => ({
        ...block,
        position: idx,
      }));
    } else {
      // Add to the end
      newBlocks = [...blocks, newBlock];
    }

    setBlocks(newBlocks);

    if (type === "table") {
      setSelectedBlockId(newBlock.id);
      setShowTableEditor(true);
    }
  };

  const handleUpdateBlock = (updatedBlock: NoteBlock) => {
    const newBlocks = blocks.map(block =>
      block.id === updatedBlock.id ? updatedBlock : block
    );
    setBlocks(newBlocks);
  };

  const handleDeleteBlock = (blockId: string) => {
    const newBlocks = blocks.filter(block => block.id !== blockId);
    // Update positions
    const updatedBlocks = newBlocks.map((block, idx) => ({
      ...block,
      position: idx,
    }));
    setBlocks(updatedBlocks);
  };

  const handleMoveBlock = (blockId: string, direction: 'up' | 'down') => {
    const index = blocks.findIndex(block => block.id === blockId);
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === blocks.length - 1)
    ) {
      return;
    }

    const newBlocks = [...blocks];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    const temp = newBlocks[index];
    newBlocks[index] = newBlocks[targetIndex];
    newBlocks[targetIndex] = temp;

    // Update positions
    const updatedBlocks = newBlocks.map((block, idx) => ({
      ...block,
      position: idx,
    }));

    setBlocks(updatedBlocks);
  };

  const handleSaveTable = (data: string[][]) => {
    if (selectedBlockId) {
      const blockIndex = blocks.findIndex(block => block.id === selectedBlockId);
      if (blockIndex !== -1) {
        const updatedBlock = {
          ...blocks[blockIndex],
          content: JSON.stringify(data),
          metadata: { rows: data.length, cols: data[0].length },
        };
        handleUpdateBlock(updatedBlock);
      }
    }
    setShowTableEditor(false);
    setSelectedBlockId(null);
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleDelete = () => {
    if (note) {
      deleteNote(note.id);
      router.back();
    }
  };

  if (!note) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: "Nota não encontrada" }} />
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Nota não encontrada</Text>
          <Button
            title="Voltar para anotações"
            onPress={() => router.back()}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <RouteGuard resourceId={id} tableName="notes">
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: isEditing ? "Editar Nota" : note.title,
            headerRight: () => (
              <View style={styles.headerButtons}>
                {isEditing && (
                  <Pressable
                    style={[styles.editButton, styles.deleteHeaderButton]}
                    onPress={() => {
                      Alert.alert(
                        "Excluir Nota",
                        "Tem certeza que deseja excluir esta nota? Esta ação não pode ser desfeita.",
                        [
                          { text: "Cancelar", style: "cancel" },
                          { text: "Excluir", onPress: handleDelete, style: "destructive" }
                        ]
                      );
                    }}
                  >
                    <Trash2 size={24} color={colors.error} />
                  </Pressable>
                )}
                {!isEditing && (
                  <Pressable
                    style={[styles.editButton, styles.deleteHeaderButton]}
                    onPress={() => {
                      Alert.alert(
                        "Excluir Nota",
                        "Tem certeza que deseja excluir esta nota? Esta ação não pode ser desfeita.",
                        [
                          { text: "Cancelar", style: "cancel" },
                          { text: "Excluir", onPress: handleDelete, style: "destructive" }
                        ]
                      );
                    }}
                  >
                    <Trash2 size={24} color={colors.error} />
                  </Pressable>
                )}
                <Pressable
                  style={styles.editButton}
                  onPress={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? (
                    <Save size={24} color={colors.primary} />
                  ) : (
                    <Edit2 size={24} color={colors.text} />
                  )}
                </Pressable>
              </View>
            ),
          }}
        />

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 140 : 50} // Increased offset to account for our toolbar
        enabled={true} // Always enabled to ensure content is visible above the keyboard
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {isEditing ? (
            <View style={styles.editContainer}>
              <TextInput
                style={styles.titleInput}
                value={title}
                onChangeText={setTitle}
                placeholder="Título da nota"
                maxLength={100}
              />

              <View style={styles.tagInputContainer}>
                <TextInput
                  style={styles.tagInput}
                  value={newTag}
                  onChangeText={setNewTag}
                  placeholder="Adicionar tag"
                  maxLength={20}
                />
                <Button
                  title="Adicionar"
                  onPress={handleAddTag}
                  variant="primary"
                  size="small"
                />
              </View>

              <View style={styles.tagsContainer}>
                {tags.map((tag) => (
                  <Pressable
                    key={tag}
                    style={styles.tagChip}
                    onPress={() => handleRemoveTag(tag)}
                  >
                    <Text style={styles.tagText}>{tag}</Text>
                    <View style={styles.tagRemove}>
                      <Text style={styles.tagRemoveText}>×</Text>
                    </View>
                  </Pressable>
                ))}
              </View>

              <View style={styles.blocksContainer}>
                {blocks.map((block) => (
                  <NoteBlockComponent
                    key={block.id}
                    block={block}
                    isEditing={true}
                    onUpdate={handleUpdateBlock}
                    onDelete={handleDeleteBlock}
                    onAddBlockAfter={(blockId) => {
                      setSelectedBlockId(blockId);
                      setShowBlockMenu(true);
                    }}
                    onMoveBlock={handleMoveBlock}
                  />
                ))}
              </View>

              {/* Botões de edição removidos daqui para evitar duplicação */}
            </View>
          ) : (
            <View style={styles.viewContainer}>
              <GlassCard style={styles.metadataCard} gradient>
                <View style={styles.metadataItem}>
                  <Book size={16} color={colors.textLight} />
                  <Text style={styles.metadataText}>{note.subject}</Text>
                </View>
                <View style={styles.metadataItem}>
                  <Clock size={16} color={colors.textLight} />
                  <Text style={styles.metadataText}>
                    Atualizado: {formatDateTime(new Date(note.updatedAt))}
                  </Text>
                </View>
              </GlassCard>

              <View style={styles.tagsRow}>
                <Tag size={16} color={colors.textLight} />
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.tagsScrollContent}
                >
                  {note.tags.length > 0 ? (
                    note.tags.map((tag) => (
                      <View key={tag} style={styles.viewTagChip}>
                        <Text style={styles.viewTagText}>{tag}</Text>
                      </View>
                    ))
                  ) : (
                    <Text style={styles.noTagsText}>Sem tags</Text>
                  )}
                </ScrollView>
              </View>

              {blocks.length > 0 ? (
                <View style={styles.blocksContainer}>
                  {blocks.map((block) => (
                    <NoteBlockComponent
                      key={block.id}
                      block={block}
                      isEditing={false}
                      onUpdate={handleUpdateBlock}
                      onDelete={handleDeleteBlock}
                      onAddBlockAfter={() => {}}
                    />
                  ))}
                </View>
              ) : (
                <Text style={styles.contentText}>{note.content}</Text>
              )}
            </View>
          )}
        </ScrollView>

        {isEditing && (
          <View style={styles.saveButtonContainer}>
            <View style={styles.bottomButtons}>
              <Button
                title="Cancelar"
                onPress={() => {
                  setIsEditing(false);
                  setTitle(note.title);
                  setContent(note.content);
                  setTags(note.tags);
                }}
                variant="outline"
                size="medium"
                style={{ flex: 1, marginRight: 8 }}
              />
              <Button
                title="Salvar"
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={{ flex: 1 }}
              />
            </View>
          </View>
        )}

        {isEditing && (
          <KeyboardToolbar
            onSelectBlock={(type) => handleAddBlock(type)}
            onShowFullMenu={() => setShowBlockMenu(true)}
          />
        )}

        {/* Block Menu Modal */}
        <Modal
          visible={showBlockMenu}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowBlockMenu(false)}
        >
          <Pressable
            style={styles.modalOverlay}
            onPress={() => setShowBlockMenu(false)}
          >
            <View style={styles.modalContainer}>
              <Pressable onPress={(e) => e.stopPropagation()}>
                <BlockMenu
                  onSelectBlock={(type) => {
                    handleAddBlock(type, selectedBlockId || undefined);
                    setShowBlockMenu(false);
                    setSelectedBlockId(null);
                  }}
                  onClose={() => {
                    setShowBlockMenu(false);
                    setSelectedBlockId(null);
                  }}
                />
              </Pressable>
            </View>
          </Pressable>
        </Modal>

        {/* Table Editor Modal */}
        <Modal
          visible={showTableEditor}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowTableEditor(false)}
        >
          <Pressable
            style={styles.modalOverlay}
            onPress={() => setShowTableEditor(false)}
          >
            <View style={styles.modalContainer}>
              <Pressable onPress={(e) => e.stopPropagation()}>
                <TableEditor
                  initialData={tableData}
                  onSave={handleSaveTable}
                  onCancel={() => {
                    setShowTableEditor(false);
                    setSelectedBlockId(null);
                  }}
                />
              </Pressable>
            </View>
          </Pressable>
        </Modal>
      </KeyboardAvoidingView>
    </SafeAreaView>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.textLight,
    marginBottom: 16,
  },
  keyboardAvoidingView: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: Platform.OS === "ios" ? 140 : 120, // Extra padding for keyboard toolbar
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButton: {
    padding: 8,
  },
  deleteHeaderButton: {
    marginRight: 8,
  },
  editContainer: {
    flex: 1,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  tagInputContainer: {
    flexDirection: "row",
    marginBottom: 16,
  },
  tagInput: {
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 12,
    marginRight: 8,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  tagChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}15`,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
    color: colors.primary,
  },
  tagRemove: {
    marginLeft: 4,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  tagRemoveText: {
    fontSize: 12,
    color: "#fff",
    fontWeight: "bold",
  },
  contentInput: {
    flex: 1,
    minHeight: 300,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    lineHeight: 24,
    color: colors.text,
    marginBottom: 16,
  },
  blocksContainer: {
    flex: 1,
    marginBottom: 16,
  },
  // Removed add block button styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: "90%",
    maxWidth: 500,
  },
  editButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  deleteButton: {
    marginTop: 16,
  },
  saveButtonContainer: {
    position: "absolute",
    bottom: 16,
    left: 16,
    right: 16,
  },
  bottomButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewContainer: {
    flex: 1,
  },
  metadataCard: {
    padding: 12,
    marginBottom: 16,
  },
  metadataItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  metadataText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
  },
  tagsRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  tagsScrollContent: {
    paddingLeft: 8,
  },
  viewTagChip: {
    backgroundColor: `${colors.primary}15`,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  viewTagText: {
    fontSize: 14,
    color: colors.primary,
  },
  noTagsText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
  },
  contentText: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text,
  },
});