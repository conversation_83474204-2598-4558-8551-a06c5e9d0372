import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, TextInput, ActivityIndicator } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { ArrowLeft, UserPlus, Mail, Copy, Check, X } from "lucide-react-native";
import { supabase } from "@/lib/supabase";
import { useUserStore } from "@/store/userStore";
import { StudyGroup, StudyGroupInvite } from "@/types";

export default function InvitesScreen() {
  const { groupId } = useLocalSearchParams();
  const router = useRouter();
  const { user, supabaseUser } = useUserStore();
  
  const [studyGroup, setStudyGroup] = useState<StudyGroup | null>(null);
  const [invites, setInvites] = useState<StudyGroupInvite[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [email, setEmail] = useState("");
  const [userRole, setUserRole] = useState<'admin' | 'moderator' | 'member' | null>(null);

  useEffect(() => {
    if (groupId) {
      fetchGroupDetails();
      fetchInvites();
    }
  }, [groupId]);

  const fetchGroupDetails = async () => {
    try {
      setLoading(true);
      
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Fetch group details
      const { data: groupData, error: groupError } = await supabase
        .from('study_groups')
        .select('*')
        .eq('id', groupId)
        .single();

      if (groupError || !groupData) {
        console.error('Error fetching group:', groupError);
        Alert.alert('Erro', 'Não foi possível carregar os detalhes do grupo.');
        router.back();
        return;
      }

      // Format group data
      const formattedGroup: StudyGroup = {
        id: groupData.id,
        name: groupData.name,
        description: groupData.description || '',
        coverImage: groupData.cover_image || '',
        adminId: groupData.admin_id,
        isOpen: groupData.is_open || false,
        inviteCode: groupData.invite_code || '',
        createdAt: groupData.created_at || new Date().toISOString(),
        updatedAt: groupData.updated_at || new Date().toISOString(),
      };

      setStudyGroup(formattedGroup);

      // Check user role
      const { data: memberData, error: memberError } = await supabase
        .from('study_group_members')
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', authUser.id)
        .single();

      if (!memberError && memberData) {
        setUserRole(memberData.role as any);
      }
    } catch (error) {
      console.error('Error fetching group details:', error);
      Alert.alert('Erro', 'Não foi possível carregar os detalhes do grupo.');
    } finally {
      setLoading(false);
    }
  };

  const fetchInvites = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Fetch invites
      const { data: invitesData, error: invitesError } = await supabase
        .from('study_group_invites')
        .select('*, users!inviter_id(name)')
        .eq('group_id', groupId);

      if (invitesError) {
        console.error('Error fetching invites:', invitesError);
        return;
      }

      // Format invites data
      const formattedInvites: StudyGroupInvite[] = invitesData.map(invite => ({
        id: invite.id,
        groupId: invite.group_id,
        inviterId: invite.inviter_id,
        inviterName: invite.users?.name || 'Usuário',
        inviteeEmail: invite.invitee_email,
        status: invite.status as any,
        createdAt: invite.created_at || new Date().toISOString(),
        expiresAt: invite.expires_at || undefined,
      }));

      setInvites(formattedInvites);
    } catch (error) {
      console.error('Error fetching invites:', error);
    }
  };

  const handleSendInvite = async () => {
    if (!email.trim() || !email.includes('@')) {
      Alert.alert('Erro', 'Por favor, insira um email válido.');
      return;
    }

    try {
      setSending(true);
      
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Check if user is already a member
      const { data: existingMember, error: memberCheckError } = await supabase
        .from('study_group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', authUser.id);

      if (existingMember && existingMember.length > 0) {
        Alert.alert('Erro', 'Este usuário já é membro do grupo.');
        setSending(false);
        return;
      }

      // Check if invite already exists
      const { data: existingInvite, error: inviteCheckError } = await supabase
        .from('study_group_invites')
        .select('id')
        .eq('group_id', groupId)
        .eq('invitee_email', email.trim())
        .eq('status', 'pending');

      if (existingInvite && existingInvite.length > 0) {
        Alert.alert('Erro', 'Já existe um convite pendente para este email.');
        setSending(false);
        return;
      }

      // Create expiration date (30 days from now)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30);

      // Create invite
      const { data: inviteData, error: inviteError } = await supabase
        .from('study_group_invites')
        .insert([
          {
            group_id: groupId,
            inviter_id: authUser.id,
            invitee_email: email.trim(),
            status: 'pending',
            expires_at: expiresAt.toISOString(),
          },
        ])
        .select()
        .single();

      if (inviteError) {
        console.error('Error creating invite:', inviteError);
        Alert.alert('Erro', 'Não foi possível enviar o convite. Tente novamente.');
        return;
      }

      Alert.alert('Sucesso', 'Convite enviado com sucesso!');
      setEmail("");
      fetchInvites();
    } catch (error) {
      console.error('Error sending invite:', error);
      Alert.alert('Erro', 'Não foi possível enviar o convite. Tente novamente.');
    } finally {
      setSending(false);
    }
  };

  const handleCancelInvite = async (inviteId: string) => {
    try {
      const { error } = await supabase
        .from('study_group_invites')
        .update({ status: 'cancelled' })
        .eq('id', inviteId);

      if (error) {
        console.error('Error cancelling invite:', error);
        Alert.alert('Erro', 'Não foi possível cancelar o convite.');
        return;
      }

      fetchInvites();
    } catch (error) {
      console.error('Error cancelling invite:', error);
      Alert.alert('Erro', 'Não foi possível cancelar o convite.');
    }
  };

  const handleCopyInviteCode = () => {
    if (!studyGroup?.inviteCode) return;
    
    // In a real app, you would use Clipboard.setString(studyGroup.inviteCode)
    Alert.alert('Copiado', 'Código de convite copiado para a área de transferência.');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando...</Text>
      </View>
    );
  }

  if (!studyGroup) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Grupo não encontrado.</Text>
        <Button
          title="Voltar"
          onPress={() => router.back()}
          variant="primary"
          size="medium"
        />
      </View>
    );
  }

  // Only admins and moderators can manage invites
  if (userRole !== 'admin' && userRole !== 'moderator') {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Você não tem permissão para gerenciar convites.</Text>
        <Button
          title="Voltar"
          onPress={() => router.back()}
          variant="primary"
          size="medium"
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Convites"
        leftComponent={
          <Pressable
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </Pressable>
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.groupInfo}>
          <Text style={styles.groupLabel}>Grupo:</Text>
          <Text style={styles.groupName}>{studyGroup.name}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Código de Convite</Text>
          <View style={styles.inviteCodeContainer}>
            <Text style={styles.inviteCode}>{studyGroup.inviteCode}</Text>
            <Pressable
              style={styles.copyButton}
              onPress={handleCopyInviteCode}
            >
              <Copy size={20} color={colors.primary} />
            </Pressable>
          </View>
          <Text style={styles.helperText}>
            Compartilhe este código para que outras pessoas possam entrar no grupo.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Convidar por Email</Text>
          <View style={styles.inputContainer}>
            <Mail size={20} color={colors.textMedium} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              value={email}
              onChangeText={setEmail}
              placeholder="Digite o email do convidado"
              placeholderTextColor={colors.textLight}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          <Button
            title="Enviar Convite"
            onPress={handleSendInvite}
            variant="primary"
            size="medium"
            icon={UserPlus}
            loading={sending}
            disabled={sending || !email.trim() || !email.includes('@')}
            style={styles.sendButton}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Convites Pendentes</Text>
          
          {invites.filter(invite => invite.status === 'pending').length > 0 ? (
            invites
              .filter(invite => invite.status === 'pending')
              .map(invite => (
                <View key={invite.id} style={styles.inviteCard}>
                  <View style={styles.inviteInfo}>
                    <Text style={styles.inviteEmail}>{invite.inviteeEmail}</Text>
                    <Text style={styles.inviteDate}>
                      Enviado em {new Date(invite.createdAt).toLocaleDateString()}
                    </Text>
                  </View>
                  <Pressable
                    style={styles.cancelButton}
                    onPress={() => handleCancelInvite(invite.id)}
                  >
                    <X size={20} color={colors.danger} />
                  </Pressable>
                </View>
              ))
          ) : (
            <Text style={styles.emptyText}>Nenhum convite pendente.</Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textMedium,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 20,
    textAlign: "center",
  },
  groupInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 16,
  },
  groupLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textMedium,
    marginRight: 8,
  },
  groupName: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 16,
  },
  inviteCodeContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inviteCode: {
    flex: 1,
    fontSize: 18,
    fontWeight: "bold",
    color: colors.primary,
  },
  copyButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}10`,
    alignItems: "center",
    justifyContent: "center",
  },
  helperText: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textDark,
  },
  sendButton: {
    alignSelf: "flex-start",
  },
  inviteCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inviteInfo: {
    flex: 1,
  },
  inviteEmail: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textDark,
    marginBottom: 4,
  },
  inviteDate: {
    fontSize: 14,
    color: colors.textMedium,
  },
  cancelButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.danger}10`,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: "center",
    paddingVertical: 20,
  },
});
