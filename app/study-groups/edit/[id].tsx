import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { colors } from "@/constants/colors";
import { Button } from "@/components/Button";
import RouteGuard from '@/components/RouteGuard';

export default function EditStudyGroupScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();

  return (
    <RouteGuard groupId={id as string} requireAdmin={true}>
      <View style={styles.container}>
        <Stack.Screen options={{ title: "Editar Grupo de Estudo" }} />
        <View style={styles.content}>
          <Text style={styles.title}>Funcionalidade em Desenvolvimento</Text>
          <Text style={styles.message}>
            A edição de grupos de estudo estará disponível em breve.
          </Text>
          <Button
            title="Voltar"
            onPress={() => router.back()}
            variant="primary"
            size="large"
            style={styles.button}
          />
        </View>
      </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: "bold",
    color: colors.text,
    marginBottom: 12,
    textAlign: "center",
  },
  message: {
    fontSize: 16,
    color: colors.textLight,
    marginBottom: 24,
    textAlign: "center",
  },
  button: {
    marginTop: 20,
    width: "80%",
  },
});
