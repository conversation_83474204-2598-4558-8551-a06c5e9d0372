import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, Switch, ActivityIndicator } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { ArrowLeft, Settings, Users, Shield, UserPlus, Trash2, LogOut, Smartphone } from "lucide-react-native";
import { supabase } from "@/lib/supabase";
import { useStudyGroupStore } from "@/store/studyGroupStore";
import { StudyGroup, StudyGroupMember, StudyGroupSettings } from "@/types";
import { AppBlockingSettings } from "@/components/AppBlockingSettings";
import { RouteGuard } from '@/components/RouteGuard';

export default function GroupSettingsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();

  const {
    fetchStudyGroup,
    fetchGroupMembers,
    fetchGroupSettings,
    updateGroupSettings,
    removeMember,
    updateMemberRole,
    transferAdmin,
    deleteStudyGroup
  } = useStudyGroupStore();

  const [studyGroup, setStudyGroup] = useState<StudyGroup | null>(null);
  const [members, setMembers] = useState<StudyGroupMember[]>([]);
  const [settings, setSettings] = useState<StudyGroupSettings | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Configurações
  const [allowMemberContent, setAllowMemberContent] = useState(false);
  const [allowMemberInvites, setAllowMemberInvites] = useState(false);
  const [requireAdminApproval, setRequireAdminApproval] = useState(true);
  const [enableAppBlocking, setEnableAppBlocking] = useState(false);

  useEffect(() => {
    if (id) {
      loadGroupData();
    }
  }, [id]);

  const loadGroupData = async () => {
    try {
      setLoading(true);

      // Carregar dados do grupo
      const group = await fetchStudyGroup(id as string);
      setStudyGroup(group);

      // Carregar membros
      const groupMembers = await fetchGroupMembers(id as string);
      setMembers(groupMembers);

      // Verificar papel do usuário atual
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (authUser) {
        const currentMember = groupMembers.find(member => member.userId === authUser.id);
        if (currentMember) {
          setUserRole(currentMember.role);
        }
      }

      // Carregar configurações
      const groupSettings = await fetchGroupSettings(id as string);
      setSettings(groupSettings);

      // Atualizar estados de configuração
      setAllowMemberContent(groupSettings.allowMemberContent);
      setAllowMemberInvites(groupSettings.allowMemberInvites);
      setRequireAdminApproval(groupSettings.requireAdminApproval);
      setEnableAppBlocking(groupSettings.enableAppBlocking || false);

    } catch (error) {
      console.error('Error loading group data:', error);
      Alert.alert('Erro', 'Não foi possível carregar os dados do grupo.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!settings || !id) return;

    try {
      setSaving(true);

      const updatedSettings = await updateGroupSettings(id as string, {
        allowMemberContent,
        allowMemberInvites,
        requireAdminApproval,
        enableAppBlocking
      });

      setSettings(updatedSettings);
      Alert.alert('Sucesso', 'Configurações atualizadas com sucesso!');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Erro', 'Não foi possível salvar as configurações.');
    } finally {
      setSaving(false);
    }
  };

  const handlePromoteMember = async (member: StudyGroupMember) => {
    if (member.role === 'admin') return;

    try {
      await updateMemberRole(member.id, 'admin');
      Alert.alert('Sucesso', `${member.userName} agora é administrador.`);
      loadGroupData(); // Recarregar dados
    } catch (error) {
      console.error('Error promoting member:', error);
      Alert.alert('Erro', 'Não foi possível promover o membro.');
    }
  };

  const handleDemoteMember = async (member: StudyGroupMember) => {
    if (member.role !== 'admin') return;

    try {
      await updateMemberRole(member.id, 'member');
      Alert.alert('Sucesso', `${member.userName} agora é membro comum.`);
      loadGroupData(); // Recarregar dados
    } catch (error) {
      console.error('Error demoting member:', error);
      Alert.alert('Erro', 'Não foi possível rebaixar o administrador.');
    }
  };

  const handleRemoveMember = async (member: StudyGroupMember) => {
    Alert.alert(
      'Remover Membro',
      `Tem certeza que deseja remover ${member.userName} do grupo?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Remover',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeMember(member.id, id as string);
              Alert.alert('Sucesso', 'Membro removido com sucesso.');
              loadGroupData(); // Recarregar dados
            } catch (error) {
              console.error('Error removing member:', error);
              Alert.alert('Erro', 'Não foi possível remover o membro.');
            }
          }
        }
      ]
    );
  };

  const handleTransferAdmin = async (member: StudyGroupMember) => {
    if (member.role === 'admin') return;

    Alert.alert(
      'Transferir Administração',
      `Tem certeza que deseja transferir a administração para ${member.userName}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Transferir',
          onPress: async () => {
            try {
              await transferAdmin(id as string, member.userId);
              Alert.alert('Sucesso', 'Administração transferida com sucesso.');
              loadGroupData(); // Recarregar dados
            } catch (error) {
              console.error('Error transferring admin:', error);
              Alert.alert('Erro', 'Não foi possível transferir a administração.');
            }
          }
        }
      ]
    );
  };

  const handleDeleteGroup = () => {
    Alert.alert(
      'Excluir Grupo',
      'Tem certeza que deseja excluir este grupo? Esta ação não pode ser desfeita.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteStudyGroup(id as string);
              Alert.alert('Sucesso', 'Grupo excluído com sucesso.');
              router.replace('/study-groups');
            } catch (error) {
              console.error('Error deleting group:', error);
              Alert.alert('Erro', 'Não foi possível excluir o grupo.');
            }
          }
        }
      ]
    );
  };

  // Verificar se o usuário é administrador
  if (userRole !== 'admin' && !loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Header
          title="Configurações"
          leftComponent={
            <Pressable
              style={styles.headerButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.primary} />
            </Pressable>
          }
        />
        <View style={styles.errorContainer}>
          <Shield size={48} color={colors.danger} />
          <Text style={styles.errorTitle}>Acesso Negado</Text>
          <Text style={styles.errorText}>
            Apenas administradores podem acessar as configurações do grupo.
          </Text>
          <Button
            title="Voltar"
            onPress={() => router.back()}
            variant="primary"
            size="medium"
          />
        </View>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Header
          title="Configurações"
          leftComponent={
            <Pressable
              style={styles.headerButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.primary} />
            </Pressable>
          }
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando configurações...</Text>
        </View>
      </View>
    );
  }

  return (
    <RouteGuard groupId={id as string} requireAdmin={true}>
      <View style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Header
          title="Configurações do Grupo"
          leftComponent={
            <Pressable
              style={styles.headerButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.primary} />
            </Pressable>
          }
        />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Seção de Configurações Gerais */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Settings size={20} color={colors.primary} />
            <Text style={styles.sectionTitle}>Configurações Gerais</Text>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Permitir que membros adicionem conteúdo</Text>
              <Text style={styles.settingDescription}>
                Se desativado, apenas administradores podem adicionar materiais ao grupo
              </Text>
            </View>
            <Switch
              value={allowMemberContent}
              onValueChange={setAllowMemberContent}
              trackColor={{ false: colors.textLight, true: `${colors.primary}80` }}
              thumbColor={allowMemberContent ? colors.primary : colors.white}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Permitir que membros convidem outras pessoas</Text>
              <Text style={styles.settingDescription}>
                Se desativado, apenas administradores podem convidar novos membros
              </Text>
            </View>
            <Switch
              value={allowMemberInvites}
              onValueChange={setAllowMemberInvites}
              trackColor={{ false: colors.textLight, true: `${colors.primary}80` }}
              thumbColor={allowMemberInvites ? colors.primary : colors.white}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Exigir aprovação de administrador</Text>
              <Text style={styles.settingDescription}>
                Se ativado, novos conteúdos precisam ser aprovados por um administrador
              </Text>
            </View>
            <Switch
              value={requireAdminApproval}
              onValueChange={setRequireAdminApproval}
              trackColor={{ false: colors.textLight, true: `${colors.primary}80` }}
              thumbColor={requireAdminApproval ? colors.primary : colors.white}
            />
          </View>

          <Button
            title="Salvar Configurações"
            onPress={handleSaveSettings}
            variant="primary"
            size="medium"
            loading={saving}
            style={styles.saveButton}
          />
        </View>

        {/* Seção de Bloqueio de Aplicativos */}
        {settings && (
          <AppBlockingSettings
            groupId={id as string}
            settings={settings}
            onSettingsUpdated={setSettings}
          />
        )}

        {/* Seção de Gerenciamento de Membros */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Users size={20} color={colors.primary} />
            <Text style={styles.sectionTitle}>Gerenciamento de Membros</Text>
          </View>

          {members.map(member => (
            <View key={member.id} style={styles.memberItem}>
              <View style={styles.memberInfo}>
                <View style={styles.memberAvatar}>
                  {member.userAvatar ? (
                    <Text style={styles.memberInitial}>{member.userName?.charAt(0)}</Text>
                  ) : (
                    <Text style={styles.memberInitial}>{member.userName?.charAt(0)}</Text>
                  )}
                </View>
                <View style={styles.memberDetails}>
                  <Text style={styles.memberName}>{member.userName}</Text>
                  <View style={styles.memberRoleContainer}>
                    <Text style={[
                      styles.memberRole,
                      member.role === 'admin' && styles.adminRole
                    ]}>
                      {member.role === 'admin' ? 'Administrador' :
                       member.role === 'moderator' ? 'Moderador' : 'Membro'}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.memberActions}>
                {member.role !== 'admin' && (
                  <Pressable
                    style={styles.memberActionButton}
                    onPress={() => handlePromoteMember(member)}
                  >
                    <Shield size={18} color={colors.primary} />
                  </Pressable>
                )}

                {member.role === 'admin' && (
                  <Pressable
                    style={styles.memberActionButton}
                    onPress={() => handleDemoteMember(member)}
                  >
                    <Shield size={18} color={colors.warning} />
                  </Pressable>
                )}

                {member.role !== 'admin' && (
                  <Pressable
                    style={styles.memberActionButton}
                    onPress={() => handleTransferAdmin(member)}
                  >
                    <UserPlus size={18} color={colors.success} />
                  </Pressable>
                )}

                <Pressable
                  style={styles.memberActionButton}
                  onPress={() => handleRemoveMember(member)}
                >
                  <Trash2 size={18} color={colors.danger} />
                </Pressable>
              </View>
            </View>
          ))}
        </View>

        {/* Seção de Ações Perigosas */}
        <View style={styles.dangerSection}>
          <Text style={styles.dangerTitle}>Ações Perigosas</Text>

          <Button
            title="Excluir Grupo"
            onPress={handleDeleteGroup}
            variant="danger"
            size="medium"
            icon={Trash2}
            style={styles.dangerButton}
          />
        </View>
      </ScrollView>
    </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: "100%",
  },
  headerButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  section: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    marginLeft: 8,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textDark,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: colors.textMedium,
  },
  saveButton: {
    marginTop: 16,
  },
  memberItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  memberInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  memberInitial: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.white,
  },
  memberDetails: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textDark,
    marginBottom: 2,
  },
  memberRoleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  memberRole: {
    fontSize: 14,
    color: colors.textMedium,
  },
  adminRole: {
    color: colors.primary,
    fontWeight: "600",
  },
  memberActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  memberActionButton: {
    padding: 8,
    marginLeft: 4,
  },
  dangerSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: colors.danger,
  },
  dangerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.danger,
    marginBottom: 16,
  },
  dangerButton: {
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.textDark,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: "center",
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    fontSize: 16,
    color: colors.textMedium,
    marginTop: 16,
  },
});
