import React, { useEffect } from "react";
import { View, Text, StyleSheet, Pressable, Alert, ActivityIndicator, ScrollView } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import { ArrowLeft, Timer, Users, Smartphone, Lock } from "lucide-react-native";
import { useUserStore } from "@/store/userStore";
import { useStudyGroupStore } from "@/store/studyGroupStore";
import { StudyGroupTimer } from "@/components/StudyGroupTimer";
import { StudyGroupSettings } from "@/types";

export default function StudyGroupTimerScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useUserStore();
  const { currentGroup, fetchStudyGroup, fetchGroupSettings, loading } = useStudyGroupStore();
  const [groupSettings, setGroupSettings] = React.useState<StudyGroupSettings | null>(null);

  useEffect(() => {
    if (id) {
      fetchStudyGroup(id as string);
      loadGroupSettings();
    }
  }, [id]);

  const loadGroupSettings = async () => {
    try {
      const settings = await fetchGroupSettings(id as string);
      setGroupSettings(settings);
    } catch (error) {
      console.error('Error loading group settings:', error);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Header
          title="Timer de Estudo"
          leftIcon={<ArrowLeft size={24} color={colors.textDark} />}
          onLeftPress={handleGoBack}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando...</Text>
        </View>
      </View>
    );
  }

  if (!currentGroup) {
    return (
      <View style={styles.container}>
        <Header
          title="Timer de Estudo"
          leftIcon={<ArrowLeft size={24} color={colors.textDark} />}
          onLeftPress={handleGoBack}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Grupo não encontrado</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Timer de Estudo"
        leftIcon={<ArrowLeft size={24} color={colors.textDark} />}
        onLeftPress={handleGoBack}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <GlassCard style={styles.groupCard} gradient>
          <View style={styles.groupInfo}>
            <View style={styles.groupIconContainer}>
              <Users size={24} color="#fff" />
            </View>
            <Text style={styles.groupName}>{currentGroup.name}</Text>
          </View>
        </GlassCard>

        <Text style={styles.sectionTitle}>Timer Pomodoro</Text>
        <Text style={styles.sectionDescription}>
          Use o timer Pomodoro para estudar com mais eficiência. Alterne entre períodos de foco e pausas.
        </Text>

        <StudyGroupTimer groupId={id as string} />

        {groupSettings?.enableAppBlocking && (
          <View style={styles.appBlockingInfoContainer}>
            <View style={styles.appBlockingHeader}>
              <Smartphone size={20} color={colors.primary} />
              <Text style={styles.appBlockingTitle}>Bloqueio de Aplicativos</Text>
            </View>
            <Text style={styles.appBlockingDescription}>
              Este grupo tem o bloqueio de aplicativos ativado. Durante as sessões de estudo,
              os aplicativos configurados pelo administrador serão bloqueados para ajudar você a manter o foco.
            </Text>
            <View style={styles.appBlockingStatusContainer}>
              <Lock size={16} color={colors.primary} />
              <Text style={styles.appBlockingStatus}>
                {groupSettings.blockedApps.length} aplicativos configurados para bloqueio
              </Text>
            </View>
          </View>
        )}

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Como funciona o método Pomodoro?</Text>
          <Text style={styles.infoText}>
            1. Defina uma tarefa para trabalhar{"\n"}
            2. Configure o timer para 25 minutos{"\n"}
            3. Trabalhe na tarefa até o timer tocar{"\n"}
            4. Faça uma pausa curta (5 minutos){"\n"}
            5. A cada 4 pomodoros, faça uma pausa longa (15 minutos){"\n"}
            6. Repita o processo
          </Text>
          <Text style={styles.infoText}>
            Seu tempo de estudo será registrado automaticamente no grupo quando você finalizar a sessão.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textMedium,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 16,
    color: colors.danger,
  },
  groupCard: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 20,
  },
  groupInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  groupIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}80`,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  groupName: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.white,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 16,
  },
  infoContainer: {
    backgroundColor: colors.lightGray,
    borderRadius: 16,
    padding: 16,
    marginTop: 20,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: colors.textMedium,
    lineHeight: 20,
    marginBottom: 8,
  },
  appBlockingInfoContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
  },
  appBlockingHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  appBlockingTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginLeft: 8,
  },
  appBlockingDescription: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 12,
    lineHeight: 20,
  },
  appBlockingStatusContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}10`,
    padding: 12,
    borderRadius: 8,
  },
  appBlockingStatus: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
    marginLeft: 8,
  },
});
