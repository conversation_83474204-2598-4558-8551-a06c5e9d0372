import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, TextInput, ActivityIndicator } from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { ArrowLeft, Users, Image as ImageIcon } from "lucide-react-native";
import { useUserStore } from "@/store/userStore";
import { useStudyGroupStore } from "@/store/studyGroupStore";
import { generateRandomString } from "@/utils/stringUtils";

export default function CreateStudyGroupScreen() {
  const router = useRouter();
  const { user } = useUserStore();
  const { createStudyGroup, loading } = useStudyGroupStore();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isOpen, setIsOpen] = useState(true);

  const handleCreateGroup = async () => {
    if (!name.trim()) {
      Alert.alert("Erro", "O nome do grupo é obrigatório.");
      return;
    }

    try {
      // Generate a random invite code
      const inviteCode = generateRandomString(8);

      // Create the group using the store
      const newGroup = await createStudyGroup({
        name: name.trim(),
        description: description.trim(),
        isOpen: isOpen,
        inviteCode: inviteCode,
      });

      Alert.alert(
        "Sucesso",
        "Grupo criado com sucesso!",
        [
          {
            text: "OK",
            onPress: () => router.replace(`/study-groups/${newGroup.id}`),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error creating group:', error);
      Alert.alert("Erro", error.message || "Não foi possível criar o grupo. Tente novamente.");
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Criar Grupo de Estudo"
        leftComponent={
          <Pressable
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </Pressable>
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Informações do Grupo</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Nome do Grupo*</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Digite o nome do grupo"
              placeholderTextColor={colors.textLight}
              maxLength={50}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descreva o propósito do grupo, matérias de estudo, etc."
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={4}
              maxLength={200}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Privacidade</Text>
            <View style={styles.optionsContainer}>
              <Pressable
                style={[
                  styles.optionButton,
                  isOpen && styles.optionButtonActive,
                ]}
                onPress={() => setIsOpen(true)}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    isOpen && styles.optionButtonTextActive,
                  ]}
                >
                  Aberto (qualquer pessoa pode entrar com o código)
                </Text>
              </Pressable>
              <Pressable
                style={[
                  styles.optionButton,
                  !isOpen && styles.optionButtonActive,
                ]}
                onPress={() => setIsOpen(false)}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    !isOpen && styles.optionButtonTextActive,
                  ]}
                >
                  Fechado (apenas por convite)
                </Text>
              </Pressable>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Imagem de Capa (opcional)</Text>
            <Pressable style={styles.imageUploadButton}>
              <ImageIcon size={24} color={colors.textMedium} />
              <Text style={styles.imageUploadText}>Adicionar imagem</Text>
            </Pressable>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Criar Grupo"
            onPress={handleCreateGroup}
            variant="primary"
            size="large"
            loading={loading}
            disabled={loading || !name.trim()}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textDark,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textDark,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  optionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: "hidden",
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  optionButtonActive: {
    backgroundColor: `${colors.primary}10`,
  },
  optionButtonText: {
    fontSize: 16,
    color: colors.textDark,
  },
  optionButtonTextActive: {
    color: colors.primary,
    fontWeight: "500",
  },
  imageUploadButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: "dashed",
  },
  imageUploadText: {
    fontSize: 16,
    color: colors.textMedium,
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 20,
    marginBottom: 40,
  },
});
