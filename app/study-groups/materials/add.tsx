import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, TextInput, ActivityIndicator } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { ArrowLeft, FileText, BookOpen, Link, Upload, File } from "lucide-react-native";
import { supabase } from "@/lib/supabase";
import { useUserStore } from "@/store/userStore";
import { StudyGroup } from "@/types";

type MaterialType = "note" | "flashcard" | "document" | "link";

export default function AddMaterialScreen() {
  const { groupId } = useLocalSearchParams();
  const router = useRouter();
  const { user, supabaseUser } = useUserStore();
  
  const [studyGroup, setStudyGroup] = useState<StudyGroup | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [materialType, setMaterialType] = useState<MaterialType>("note");
  const [content, setContent] = useState("");
  const [linkUrl, setLinkUrl] = useState("");

  useEffect(() => {
    if (groupId) {
      fetchGroupDetails();
    }
  }, [groupId]);

  const fetchGroupDetails = async () => {
    try {
      setLoading(true);
      
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Fetch group details
      const { data: groupData, error: groupError } = await supabase
        .from('study_groups')
        .select('*')
        .eq('id', groupId)
        .single();

      if (groupError || !groupData) {
        console.error('Error fetching group:', groupError);
        Alert.alert('Erro', 'Não foi possível carregar os detalhes do grupo.');
        router.back();
        return;
      }

      // Format group data
      const formattedGroup: StudyGroup = {
        id: groupData.id,
        name: groupData.name,
        description: groupData.description || '',
        coverImage: groupData.cover_image || '',
        adminId: groupData.admin_id,
        isOpen: groupData.is_open || false,
        inviteCode: groupData.invite_code || '',
        createdAt: groupData.created_at || new Date().toISOString(),
        updatedAt: groupData.updated_at || new Date().toISOString(),
      };

      setStudyGroup(formattedGroup);
    } catch (error) {
      console.error('Error fetching group details:', error);
      Alert.alert('Erro', 'Não foi possível carregar os detalhes do grupo.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveMaterial = async () => {
    if (!title.trim()) {
      Alert.alert("Erro", "O título do material é obrigatório.");
      return;
    }

    if (materialType === "link" && !linkUrl.trim()) {
      Alert.alert("Erro", "O URL do link é obrigatório.");
      return;
    }

    if (materialType === "note" && !content.trim()) {
      Alert.alert("Erro", "O conteúdo da anotação é obrigatório.");
      return;
    }

    try {
      setSaving(true);
      
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) {
        Alert.alert("Erro", "Você precisa estar logado para adicionar materiais.");
        return;
      }

      // Prepare material data
      const materialData: any = {
        group_id: groupId,
        user_id: authUser.id,
        title: title.trim(),
        description: description.trim(),
        type: materialType,
      };

      // Add type-specific data
      if (materialType === "note") {
        materialData.content = { text: content.trim() };
      } else if (materialType === "link") {
        materialData.content = { url: linkUrl.trim() };
      }

      // Save to Supabase
      const { data, error } = await supabase
        .from('study_group_materials')
        .insert([materialData])
        .select()
        .single();

      if (error) {
        console.error('Error adding material:', error);
        Alert.alert("Erro", "Não foi possível adicionar o material. Tente novamente.");
        return;
      }

      Alert.alert(
        "Sucesso",
        "Material adicionado com sucesso!",
        [
          {
            text: "OK",
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error('Error adding material:', error);
      Alert.alert("Erro", "Não foi possível adicionar o material. Tente novamente.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#F9FAFB", "#F3F4F6"]}
        style={styles.backgroundGradient}
      />
      <Header
        title="Adicionar Material"
        leftComponent={
          <Pressable
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </Pressable>
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.groupInfo}>
          <Text style={styles.groupLabel}>Grupo:</Text>
          <Text style={styles.groupName}>{studyGroup?.name}</Text>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Informações do Material</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Título*</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Digite o título do material"
              placeholderTextColor={colors.textLight}
              maxLength={100}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Descrição</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descreva brevemente este material"
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={2}
              maxLength={200}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Material</Text>
            <View style={styles.typeContainer}>
              <Pressable
                style={[
                  styles.typeButton,
                  materialType === "note" && styles.typeButtonActive,
                ]}
                onPress={() => setMaterialType("note")}
              >
                <FileText size={24} color={materialType === "note" ? colors.primary : colors.textMedium} />
                <Text
                  style={[
                    styles.typeButtonText,
                    materialType === "note" && styles.typeButtonTextActive,
                  ]}
                >
                  Anotação
                </Text>
              </Pressable>
              
              <Pressable
                style={[
                  styles.typeButton,
                  materialType === "flashcard" && styles.typeButtonActive,
                ]}
                onPress={() => {
                  Alert.alert("Em breve", "Esta funcionalidade estará disponível em breve.");
                  // setMaterialType("flashcard");
                }}
              >
                <BookOpen size={24} color={materialType === "flashcard" ? colors.primary : colors.textMedium} />
                <Text
                  style={[
                    styles.typeButtonText,
                    materialType === "flashcard" && styles.typeButtonTextActive,
                  ]}
                >
                  Flashcards
                </Text>
              </Pressable>
              
              <Pressable
                style={[
                  styles.typeButton,
                  materialType === "document" && styles.typeButtonActive,
                ]}
                onPress={() => {
                  Alert.alert("Em breve", "Esta funcionalidade estará disponível em breve.");
                  // setMaterialType("document");
                }}
              >
                <File size={24} color={materialType === "document" ? colors.primary : colors.textMedium} />
                <Text
                  style={[
                    styles.typeButtonText,
                    materialType === "document" && styles.typeButtonTextActive,
                  ]}
                >
                  Documento
                </Text>
              </Pressable>
              
              <Pressable
                style={[
                  styles.typeButton,
                  materialType === "link" && styles.typeButtonActive,
                ]}
                onPress={() => setMaterialType("link")}
              >
                <Link size={24} color={materialType === "link" ? colors.primary : colors.textMedium} />
                <Text
                  style={[
                    styles.typeButtonText,
                    materialType === "link" && styles.typeButtonTextActive,
                  ]}
                >
                  Link
                </Text>
              </Pressable>
            </View>
          </View>

          {materialType === "note" && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Conteúdo*</Text>
              <TextInput
                style={[styles.input, styles.contentArea]}
                value={content}
                onChangeText={setContent}
                placeholder="Digite o conteúdo da sua anotação aqui..."
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={10}
              />
            </View>
          )}

          {materialType === "link" && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>URL*</Text>
              <TextInput
                style={styles.input}
                value={linkUrl}
                onChangeText={setLinkUrl}
                placeholder="https://exemplo.com"
                placeholderTextColor={colors.textLight}
                autoCapitalize="none"
                keyboardType="url"
              />
            </View>
          )}

          {materialType === "document" && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Arquivo</Text>
              <Pressable style={styles.uploadButton}>
                <Upload size={24} color={colors.textMedium} />
                <Text style={styles.uploadText}>Selecionar arquivo</Text>
              </Pressable>
              <Text style={styles.helperText}>Formatos suportados: PDF, DOCX, PPTX (máx. 10MB)</Text>
            </View>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Adicionar Material"
            onPress={handleSaveMaterial}
            variant="primary"
            size="large"
            loading={saving}
            disabled={saving || !title.trim()}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textMedium,
  },
  groupInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 16,
  },
  groupLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textMedium,
    marginRight: 8,
  },
  groupName: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textDark,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textDark,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 60,
    textAlignVertical: "top",
  },
  contentArea: {
    minHeight: 200,
    textAlignVertical: "top",
  },
  typeContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  typeButton: {
    width: "48%",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 12,
  },
  typeButtonActive: {
    backgroundColor: `${colors.primary}10`,
    borderColor: colors.primary,
  },
  typeButtonText: {
    fontSize: 14,
    color: colors.textDark,
    marginTop: 8,
  },
  typeButtonTextActive: {
    color: colors.primary,
    fontWeight: "500",
  },
  uploadButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: "dashed",
  },
  uploadText: {
    fontSize: 16,
    color: colors.textMedium,
    marginLeft: 8,
  },
  helperText: {
    fontSize: 12,
    color: colors.textLight,
    marginTop: 8,
  },
  buttonContainer: {
    marginTop: 20,
    marginBottom: 40,
  },
});
