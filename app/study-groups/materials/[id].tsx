import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, ActivityIndicator, Linking } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { ArrowLeft, FileText, BookOpen, Link, Download, ExternalLink, Trash2, Edit } from "lucide-react-native";
import { supabase } from "@/lib/supabase";
import { useUserStore } from "@/store/userStore";
import { useStudyGroupStore } from "@/store/studyGroupStore";
import { StudyGroupMaterial } from "@/types";
import RouteGuard from '@/components/RouteGuard';

export default function MaterialDetailScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user, supabaseUser } = useUserStore();
  const { deleteMaterial: deleteGroupMaterial } = useStudyGroupStore();

  const [material, setMaterial] = useState<StudyGroupMaterial | null>(null);
  const [loading, setLoading] = useState(true);
  const [isOwner, setIsOwner] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (id) {
      fetchMaterialDetails();
    }
  }, [id]);

  const fetchMaterialDetails = async () => {
    try {
      setLoading(true);

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      // Fetch material details
      const { data: materialData, error: materialError } = await supabase
        .from('study_group_materials')
        .select('*, users(name)')
        .eq('id', id)
        .single();

      if (materialError || !materialData) {
        console.error('Error fetching material:', materialError);
        Alert.alert('Erro', 'Não foi possível carregar os detalhes do material.');
        router.back();
        return;
      }

      // Format material data
      const formattedMaterial: StudyGroupMaterial = {
        id: materialData.id,
        groupId: materialData.group_id,
        userId: materialData.user_id,
        userName: materialData.users?.name || 'Usuário',
        title: materialData.title,
        description: materialData.description || '',
        type: materialData.type,
        content: materialData.content,
        fileUrl: materialData.file_url || '',
        createdAt: materialData.created_at || new Date().toISOString(),
        updatedAt: materialData.updated_at || new Date().toISOString(),
      };

      setMaterial(formattedMaterial);
      setIsOwner(materialData.user_id === authUser.id);

      // Verificar se o usuário é administrador do grupo
      const { data: memberData, error: memberError } = await supabase
        .from('study_group_members')
        .select('role')
        .eq('group_id', materialData.group_id)
        .eq('user_id', authUser.id)
        .single();

      if (!memberError && memberData) {
        setIsAdmin(memberData.role === 'admin');
      }
    } catch (error) {
      console.error('Error fetching material details:', error);
      Alert.alert('Erro', 'Não foi possível carregar os detalhes do material.');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenLink = async () => {
    if (!material || material.type !== 'link' || !material.content?.url) return;

    try {
      const url = material.content.url;
      const canOpen = await Linking.canOpenURL(url);

      if (canOpen) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Erro', 'Não foi possível abrir este link.');
      }
    } catch (error) {
      console.error('Error opening link:', error);
      Alert.alert('Erro', 'Não foi possível abrir este link.');
    }
  };

  const handleDeleteMaterial = () => {
    Alert.alert(
      'Excluir Material',
      'Tem certeza que deseja excluir este material? Esta ação não pode ser desfeita.',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: deleteMaterial,
        },
      ]
    );
  };

  const deleteMaterial = async () => {
    try {
      if (!material) return;

      // Usar a função do store para excluir o material
      await deleteGroupMaterial(material.id, material.groupId);

      Alert.alert('Sucesso', 'Material excluído com sucesso.');
      router.back();
    } catch (error) {
      console.error('Error deleting material:', error);
      Alert.alert('Erro', 'Não foi possível excluir o material.');
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando material...</Text>
      </View>
    );
  }

  if (!material) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Material não encontrado.</Text>
        <Button
          title="Voltar"
          onPress={() => router.back()}
          variant="primary"
          size="medium"
        />
      </View>
    );
  }

  return (
    <RouteGuard resourceId={id as string} tableName="study_group_materials">
      <View style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Header
          title={material.title}
          leftComponent={
            <Pressable
              style={styles.headerButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.primary} />
            </Pressable>
          }
          rightComponent={
          (isOwner || isAdmin) && (
            <Pressable
              style={styles.headerButton}
              onPress={handleDeleteMaterial}
            >
              <Trash2 size={24} color={colors.danger} />
            </Pressable>
          )
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.materialHeader}>
          <View style={styles.materialIconContainer}>
            {material.type === 'note' && <FileText size={24} color={colors.white} />}
            {material.type === 'flashcard' && <BookOpen size={24} color={colors.white} />}
            {material.type === 'document' && <FileText size={24} color={colors.white} />}
            {material.type === 'link' && <Link size={24} color={colors.white} />}
          </View>
          <View style={styles.materialInfo}>
            <Text style={styles.materialTitle}>{material.title}</Text>
            <Text style={styles.materialMeta}>
              Compartilhado por {material.userName} • {new Date(material.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </View>

        {material.description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>{material.description}</Text>
          </View>
        )}

        {material.type === 'note' && material.content?.text && (
          <View style={styles.contentContainer}>
            <Text style={styles.contentText}>{material.content.text}</Text>
          </View>
        )}

        {material.type === 'link' && material.content?.url && (
          <View style={styles.linkContainer}>
            <Text style={styles.linkText}>{material.content.url}</Text>
            <Button
              title="Abrir Link"
              onPress={handleOpenLink}
              variant="primary"
              size="medium"
              icon={ExternalLink}
              style={styles.linkButton}
            />
          </View>
        )}

        {material.type === 'document' && material.fileUrl && (
          <View style={styles.documentContainer}>
            <Text style={styles.documentText}>Documento disponível para download</Text>
            <Button
              title="Baixar Documento"
              onPress={() => Alert.alert('Em breve', 'Esta funcionalidade estará disponível em breve.')}
              variant="primary"
              size="medium"
              icon={Download}
              style={styles.documentButton}
            />
          </View>
        )}

        {(isOwner || isAdmin) && (
          <View style={styles.actionContainer}>
            {isOwner && (
              <Button
                title="Editar Material"
                onPress={() => router.push(`/study-groups/materials/edit/${material.id}`)}
                variant="secondary"
                size="medium"
                icon={Edit}
                style={styles.actionButton}
              />
            )}
            <Button
              title="Excluir Material"
              onPress={handleDeleteMaterial}
              variant="danger"
              size="medium"
              icon={Trash2}
              style={styles.actionButton}
            />
          </View>
        )}
      </ScrollView>
    </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textMedium,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 20,
  },
  materialHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 20,
  },
  materialIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  materialInfo: {
    flex: 1,
  },
  materialTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 4,
  },
  materialMeta: {
    fontSize: 14,
    color: colors.textMedium,
  },
  descriptionContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  descriptionText: {
    fontSize: 16,
    color: colors.textDark,
    lineHeight: 24,
  },
  contentContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  contentText: {
    fontSize: 16,
    color: colors.textDark,
    lineHeight: 24,
  },
  linkContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  linkText: {
    fontSize: 16,
    color: colors.primary,
    marginBottom: 16,
  },
  linkButton: {
    alignSelf: "center",
  },
  documentContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  documentText: {
    fontSize: 16,
    color: colors.textDark,
    marginBottom: 16,
  },
  documentButton: {
    alignSelf: "center",
  },
  actionContainer: {
    marginTop: 20,
    marginBottom: 40,
  },
  actionButton: {
    marginBottom: 12,
  },
});
