import React, { useState, useEffect, useCallback } from "react";
import { View, Text, StyleSheet, ScrollView, Pressable, Alert, ActivityIndicator, Image, RefreshControl, Share } from "react-native";
import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { Header } from "@/components/Header";
import { Button } from "@/components/Button";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "@/components/GlassCard";
import * as Clipboard from 'expo-clipboard';
import RouteGuard from '@/components/RouteGuard';
import {
  Users,
  UserPlus,
  Clock,
  BookOpen,
  Trophy,
  Settings,
  Share2,
  Plus,
  Timer,
  FileText,
  ChevronRight,
  ArrowLeft,
  Copy,
  RefreshCw,
  Lock,
  Unlock,
  Calendar,
  Check,
  Network,
  BrainCircuit,
  HelpCircle,
  Medal,
  Award,
  Star,
  Smartphone,
  XCircle,
  MessageSquare,
  Edit,
  Trash2,
  MoreVertical,
  Filter,
  Key,
  CalendarDays,
  CalendarRange,
  Calendar<PERSON>lock,
  AlertCircle
} from "lucide-react-native";
import { formatDistanceToNow, format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { StudyGroupMemberCard } from "@/components/StudyGroupMemberCard";
import { StudyGroupMaterialCard } from "@/components/StudyGroupMaterialCard";
import { MaterialContextMenu } from "@/components/MaterialContextMenu";
import { StudyGroupTimer } from "@/components/StudyGroupTimer";
import { useUserStore } from "@/store/userStore";
import { useStudyGroupStore } from "@/store/studyGroupStore";
import { StudyGroup, StudyGroupMember, StudyGroupMaterial, StudyGroupSettings } from "@/types";

export default function StudyGroupDetailScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useUserStore();
  const {
    currentGroup: studyGroup,
    members,
    materials,
    loading,
    error,
    fetchStudyGroup,
    fetchGroupMembers,
    fetchGroupMaterials,
    fetchGroupSettings,
    leaveStudyGroup,
    deleteMaterial
  } = useStudyGroupStore();

  const [userRole, setUserRole] = useState<'admin' | 'moderator' | 'member' | null>(null);
  const [activeTab, setActiveTab] = useState<'members' | 'ranking' | 'resources'>('members');
  const [rankingPeriod, setRankingPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const [refreshing, setRefreshing] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);
  const [selectedResourceType, setSelectedResourceType] = useState<'flashcards' | 'mindmaps' | 'notes' | 'quiz'>('flashcards');
  const [userXP, setUserXP] = useState(0);
  const [showTimer, setShowTimer] = useState(false);
  const [groupSettings, setGroupSettings] = useState<StudyGroupSettings | null>(null);
  const [localError, setError] = useState<string | null>(null);
  const [userActivities, setUserActivities] = useState({
    flashcardsAnswered: 0,
    quizzesCompleted: 0,
    resourcesCreated: 0,
    commentsPosted: 0
  });

  // Estados para os formulários de materiais
  const [showCreateNoteForm, setShowCreateNoteForm] = useState(false);
  const [showCreateFlashcardForm, setShowCreateFlashcardForm] = useState(false);
  const [showCreateMindMapForm, setShowCreateMindMapForm] = useState(false);

  useEffect(() => {
    if (id) {
      fetchGroupDetails();
    }
  }, [id]);

  const onRefresh = useCallback(async () => {
    if (!id) return;

    setRefreshing(true);
    try {
      await fetchGroupDetails();
    } catch (error) {
      console.error('Error refreshing group details:', error);
    } finally {
      setRefreshing(false);
    }
  }, [id, fetchGroupDetails]);

  const fetchGroupDetails = async () => {
    try {
      setError(null);

      // Fetch group details with timeout
      const groupPromise = fetchStudyGroup(id as string);
      const timeoutPromise = new Promise<null>((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 10000)
      );

      const group = await Promise.race([groupPromise, timeoutPromise]);

      if (!group) {
        const storeError = error; // Get error from store
        setError(storeError || 'Grupo não encontrado ou você não tem permissão para acessá-lo.');
        return;
      }

      // Fetch members with error handling
      try {
        const groupMembers = await fetchGroupMembers(id as string);

        // Set user role
        const currentUserMember = groupMembers.find(member => member.userId === user.id);
        if (currentUserMember) {
          setUserRole(currentUserMember.role);
        } else {
          // If user is not found in members but group exists, they might be admin
          if (group.adminId === user.id) {
            setUserRole('admin');
          } else {
            setError('Você não é membro deste grupo.');
            return;
          }
        }
      } catch (memberError) {
        console.error('Error fetching members:', memberError);
        // Continue without members data - set as admin if they own the group
        if (group.adminId === user.id) {
          setUserRole('admin');
        } else {
          setError('Erro ao carregar membros do grupo.');
          return;
        }
      }

      // Fetch materials with error handling
      try {
        await fetchGroupMaterials(id as string);
      } catch (materialError) {
        console.error('Error fetching materials:', materialError);
        // Continue without materials - not critical
      }

      // Fetch group settings with error handling
      try {
        const settings = await fetchGroupSettings(id as string);
        setGroupSettings(settings);
      } catch (settingsError) {
        console.error('Error fetching settings:', settingsError);
        // Continue without settings - not critical
      }

      // Simulate user stats (replace with actual data fetching)
      setUserActivities({
        studyTimeMinutes: Math.floor(Math.random() * 500) + 100,
        flashcardsAnswered: Math.floor(Math.random() * 200) + 50,
        quizzesCompleted: Math.floor(Math.random() * 50) + 10,
        resourcesCreated: Math.floor(Math.random() * 20) + 5,
        commentsPosted: Math.floor(Math.random() * 100) + 20
      });
    } catch (error: any) {
      console.error('Error fetching group details:', error);

      if (error.message === 'Timeout') {
        setError('Tempo limite excedido. Verifique sua conexão e tente novamente.');
      } else {
        setError('Ocorreu um erro ao carregar os detalhes do grupo. Tente novamente.');
      }
    }
  };

  const handleShareInvite = async () => {
    if (!studyGroup) return;

    try {
      await Share.share({
        message: `Junte-se ao meu grupo de estudo "${studyGroup.name}" no Lia App! Use o código de convite: ${studyGroup.inviteCode}`,
      });
    } catch (error) {
      console.error('Error sharing invite:', error);
    }
  };

  const handleCopyInviteCode = async () => {
    if (!studyGroup?.inviteCode) return;

    try {
      await Clipboard.setStringAsync(studyGroup.inviteCode);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    } catch (error) {
      console.error('Error copying invite code:', error);
      Alert.alert('Erro', 'Não foi possível copiar o código de convite.');
    }
  };

  const handleAddMaterial = () => {
    if (!studyGroup) return;
    router.push(`/study-groups/materials/add?groupId=${studyGroup.id}`);
  };

  const handleDeleteMaterial = async (material: StudyGroupMaterial) => {
    Alert.alert(
      'Excluir Material',
      `Tem certeza que deseja excluir "${material.title}"? Esta ação não pode ser desfeita.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteMaterial(material.id, material.groupId);
              Alert.alert('Sucesso', 'Material excluído com sucesso.');
              // Recarregar materiais
              if (studyGroup) {
                await fetchGroupMaterials(studyGroup.id);
              }
            } catch (error) {
              console.error('Error deleting material:', error);
              Alert.alert('Erro', 'Não foi possível excluir o material.');
            }
          }
        }
      ]
    );
  };

  const handleShareMaterial = (material: StudyGroupMaterial) => {
    if (!studyGroup) return;

    try {
      Share.share({
        message: `Confira este material "${material.title}" no grupo de estudos "${studyGroup.name}" no Lia App!`,
      });
    } catch (error) {
      console.error('Error sharing material:', error);
    }
  };

  const handleStartTimer = () => {
    if (!studyGroup) return;
    setShowTimer(true);
  };

  const handleLeaveGroup = () => {
    Alert.alert(
      'Sair do Grupo',
      'Tem certeza que deseja sair deste grupo de estudo?',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Sair',
          style: 'destructive',
          onPress: leaveGroup,
        },
      ]
    );
  };

  const leaveGroup = async () => {
    try {
      if (!studyGroup) return;

      // Check if user is the admin
      if (studyGroup.adminId === user.id) {
        Alert.alert(
          'Erro',
          'Você é o administrador deste grupo. Para sair, você precisa transferir a administração para outro membro ou excluir o grupo.'
        );
        return;
      }

      await leaveStudyGroup(studyGroup.id);
      Alert.alert('Sucesso', 'Você saiu do grupo com sucesso.');
      router.replace('/study-groups');
    } catch (error) {
      console.error('Error leaving group:', error);
      Alert.alert('Erro', 'Não foi possível sair do grupo.');
    }
  };

  const renderMembersTab = () => {
    // Ordenar membros por XP (usando flashcardsCreated como substituto temporário para XP)
    const sortedMembers = [...members].sort((a, b) => {
      // Primeiro por rank (se disponível)
      if (a.rank && b.rank) {
        return a.rank - b.rank;
      }
      // Depois por flashcards criados (como substituto para XP)
      return (b.flashcardsCreated || 0) - (a.flashcardsCreated || 0);
    });

    // Destacar o usuário atual
    const currentUserMember = sortedMembers.find(member => member.userId === user.id);
    const userXP = currentUserMember ? currentUserMember.flashcardsCreated * 10 : 0;

    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Membros</Text>
          <View style={styles.tabActions}>
            {(userRole === 'admin' || userRole === 'moderator') && (
              <Button
                title="Convidar"
                onPress={handleShareInvite}
                variant="secondary"
                size="small"
                icon={UserPlus}
                style={styles.tabActionButton}
              />
            )}
          </View>
        </View>

        {/* Ranking do usuário atual */}
        {currentUserMember && (
          <View style={styles.currentUserRankCard}>
            <View style={styles.rankCardHeader}>
              <Trophy size={20} color={colors.primary} />
              <Text style={styles.rankCardTitle}>Seu Ranking</Text>
            </View>
            <View style={styles.rankCardContent}>
              <View style={styles.rankCardStat}>
                <Text style={styles.rankCardStatValue}>{currentUserMember.rank || '-'}</Text>
                <Text style={styles.rankCardStatLabel}>Posição</Text>
              </View>
              <View style={styles.rankCardStat}>
                <Text style={styles.rankCardStatValue}>{currentUserMember.level || 1}</Text>
                <Text style={styles.rankCardStatLabel}>Nível</Text>
              </View>
              <View style={styles.rankCardStat}>
                <Text style={styles.rankCardStatValue}>{userXP}</Text>
                <Text style={styles.rankCardStatLabel}>XP</Text>
              </View>
            </View>
          </View>
        )}

        {/* Lista de membros */}
        <View style={styles.membersListHeader}>
          <Text style={styles.membersListTitle}>Classificação</Text>
          <Text style={styles.membersListSubtitle}>{members.length} membros</Text>
        </View>

        {sortedMembers.map((member) => (
          <View key={member.id} style={styles.memberCard}>
            <View style={styles.memberInfo}>
              <View style={styles.memberRank}>
                <Text style={styles.memberRankText}>{member.rank || '-'}</Text>
              </View>
              <Text style={styles.memberName}>{member.userName}</Text>
            </View>
            <View style={styles.memberStats}>
              <View style={styles.memberStat}>
                <Text style={styles.memberStatValue}>{member.level || 1}</Text>
                <Text style={styles.memberStatLabel}>Nível</Text>
              </View>
              <View style={styles.memberStat}>
                <Text style={styles.memberStatValue}>{member.flashcardsCreated * 10}</Text>
                <Text style={styles.memberStatLabel}>XP</Text>
              </View>
              {member.userId === user.id && (
                <View style={styles.currentUserBadge}>
                  <Text style={styles.currentUserBadgeText}>Você</Text>
                </View>
              )}
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderMaterialsTab = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Materiais</Text>
          <Button
            title="Adicionar"
            onPress={handleAddMaterial}
            variant="secondary"
            size="small"
            icon={Plus}
          />
        </View>

        {materials.length > 0 ? (
          materials.map((material) => (
            <View key={material.id} style={styles.materialCardContainer}>
              <StudyGroupMaterialCard
                material={material}
                onPress={(material) => router.push(`/study-groups/materials/${material.id}`)}
              />
              <MaterialContextMenu
                material={material}
                isOwner={material.userId === user.id}
                isAdmin={userRole === 'admin'}
                onEdit={() => router.push(`/study-groups/materials/edit/${material.id}`)}
                onDelete={() => handleDeleteMaterial(material)}
                onShare={() => handleShareMaterial(material)}
              />
            </View>
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <FileText size={40} color={`${colors.primary}50`} />
            <Text style={styles.emptyText}>
              Nenhum material compartilhado ainda. Adicione o primeiro material para o grupo!
            </Text>
            <Button
              title="Adicionar Material"
              onPress={handleAddMaterial}
              variant="primary"
              size="medium"
              icon={Plus}
            />
          </View>
        )}
      </View>
    );
  };

  const renderRankingTab = () => {
    // Encontrar o membro atual
    const currentMember = members.find(m => m.userId === user.id);

    // Ordenar membros por XP
    const sortedMembers = [...members].sort((a, b) => {
      return (b.xpPoints || 0) - (a.xpPoints || 0);
    });

    // Função para filtrar membros com base no período selecionado
    // Simulação - em uma implementação real, isso seria baseado em dados reais de atividade
    const getFilteredMembers = () => {
      // Em uma implementação real, você filtraria com base em timestamps reais
      // Por enquanto, vamos apenas simular diferentes conjuntos de dados
      return sortedMembers;
    };

    const filteredMembers = getFilteredMembers();
    const topMembers = filteredMembers.slice(0, 3);

    return (
      <View style={styles.tabContent}>
        {showTimer ? (
          <View style={styles.timerContainer}>
            <View style={styles.timerHeader}>
              <Text style={styles.timerTitle}>Sessão de Estudo</Text>
              <Pressable
                style={styles.closeTimerButton}
                onPress={() => setShowTimer(false)}
              >
                <XCircle size={24} color={colors.textMedium} />
              </Pressable>
            </View>
            <StudyGroupTimer groupId={id as string} />
          </View>
        ) : (
          <>
            <View style={styles.tabHeader}>
              <View style={styles.tabTitleContainer}>
                <Trophy size={20} color={colors.primary} />
                <Text style={styles.tabTitle}>Ranking de XP</Text>
              </View>
              <Button
                title="Iniciar Estudo"
                onPress={handleStartTimer}
                variant="primary"
                size="small"
                icon={Clock}
              />
            </View>

            {/* Card do usuário atual */}
            {currentMember && (
              <GlassCard style={styles.userRankCard}>
                <View style={styles.userRankHeader}>
                  <View style={styles.userRankPosition}>
                    <Text style={styles.userRankPositionNumber}>
                      {sortedMembers.findIndex(m => m.userId === user.id) + 1}
                    </Text>
                  </View>
                  <View style={styles.userRankInfo}>
                    <Text style={styles.userRankName}>{currentMember.userName}</Text>
                    <View style={styles.userRankRoleBadge}>
                      <Text style={styles.userRankRoleText}>
                        {currentMember.role === 'admin' ? 'Administrador' :
                         currentMember.role === 'moderator' ? 'Moderador' : 'Membro'}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.userRankStatsContainer}>
                  <View style={styles.userRankStat}>
                    <View style={styles.userRankStatIconContainer}>
                      <Trophy size={18} color={colors.primary} />
                    </View>
                    <Text style={styles.userRankStatValue}>{currentMember.xpPoints}</Text>
                    <Text style={styles.userRankStatLabel}>Pontos XP</Text>
                  </View>

                  <View style={styles.userRankStat}>
                    <View style={styles.userRankStatIconContainer}>
                      <Star size={18} color={colors.primary} />
                    </View>
                    <Text style={styles.userRankStatValue}>{currentMember.level || 1}</Text>
                    <Text style={styles.userRankStatLabel}>Nível</Text>
                  </View>
                </View>
              </GlassCard>
            )}

            {/* Filtros de período */}
            <View style={styles.periodFilterContainer}>
              <View style={styles.periodFilterHeader}>
                <Filter size={18} color={colors.primary} />
                <Text style={styles.periodFilterTitle}>Filtrar por período</Text>
              </View>
              <View style={styles.periodFilterButtons}>
                <Pressable
                  style={[styles.periodFilterButton, rankingPeriod === 'daily' && styles.periodFilterButtonActive]}
                  onPress={() => setRankingPeriod('daily')}
                >
                  <Calendar size={16} color={rankingPeriod === 'daily' ? colors.white : colors.primary} />
                  <Text style={[styles.periodFilterText, rankingPeriod === 'daily' && styles.periodFilterTextActive]}>Diário</Text>
                </Pressable>
                <Pressable
                  style={[styles.periodFilterButton, rankingPeriod === 'weekly' && styles.periodFilterButtonActive]}
                  onPress={() => setRankingPeriod('weekly')}
                >
                  <CalendarDays size={16} color={rankingPeriod === 'weekly' ? colors.white : colors.primary} />
                  <Text style={[styles.periodFilterText, rankingPeriod === 'weekly' && styles.periodFilterTextActive]}>Semanal</Text>
                </Pressable>
                <Pressable
                  style={[styles.periodFilterButton, rankingPeriod === 'monthly' && styles.periodFilterButtonActive]}
                  onPress={() => setRankingPeriod('monthly')}
                >
                  <CalendarRange size={16} color={rankingPeriod === 'monthly' ? colors.white : colors.primary} />
                  <Text style={[styles.periodFilterText, rankingPeriod === 'monthly' && styles.periodFilterTextActive]}>Mensal</Text>
                </Pressable>
                <Pressable
                  style={[styles.periodFilterButton, rankingPeriod === 'yearly' && styles.periodFilterButtonActive]}
                  onPress={() => setRankingPeriod('yearly')}
                >
                  <CalendarClock size={16} color={rankingPeriod === 'yearly' ? colors.white : colors.primary} />
                  <Text style={[styles.periodFilterText, rankingPeriod === 'yearly' && styles.periodFilterTextActive]}>Anual</Text>
                </Pressable>
              </View>
            </View>
          </>
        )}

        {/* Pódio dos top 3 */}
        <View style={styles.podiumContainer}>
          {topMembers.length > 1 && (
            <View style={styles.podiumSecond}>
              <View style={styles.podiumAvatar}>
                <View style={[styles.podiumAvatarInner, styles.silverAvatar]}>
                  {topMembers[1].userAvatar ? (
                    <Image source={{ uri: topMembers[1].userAvatar }} style={styles.podiumAvatarImage} />
                  ) : (
                    <Users size={24} color={colors.white} />
                  )}
                </View>
                <View style={[styles.medalBadge, styles.silverMedal]}>
                  <Medal size={16} color="#C0C0C0" />
                </View>
              </View>
              <Text style={styles.podiumName} numberOfLines={1}>{topMembers[1].userName}</Text>
              <Text style={styles.podiumXP}>{topMembers[1].xpPoints} XP</Text>
              <View style={[styles.podiumPillar, styles.podiumPillarSecond]}></View>
            </View>
          )}

          {topMembers.length > 0 && (
            <View style={styles.podiumFirst}>
              <View style={styles.crownContainer}>
                <Award size={24} color="#FFD700" />
              </View>
              <View style={styles.podiumAvatar}>
                <View style={[styles.podiumAvatarInner, styles.goldAvatar]}>
                  {topMembers[0].userAvatar ? (
                    <Image source={{ uri: topMembers[0].userAvatar }} style={styles.podiumAvatarImage} />
                  ) : (
                    <Users size={30} color={colors.white} />
                  )}
                </View>
                <View style={[styles.medalBadge, styles.goldMedal]}>
                  <Medal size={18} color="#FFD700" />
                </View>
              </View>
              <Text style={styles.podiumName} numberOfLines={1}>{topMembers[0].userName}</Text>
              <Text style={styles.podiumXP}>{topMembers[0].xpPoints} XP</Text>
              <View style={[styles.podiumPillar, styles.podiumPillarFirst]}></View>
            </View>
          )}

          {topMembers.length > 2 && (
            <View style={styles.podiumThird}>
              <View style={styles.podiumAvatar}>
                <View style={[styles.podiumAvatarInner, styles.bronzeAvatar]}>
                  {topMembers[2].userAvatar ? (
                    <Image source={{ uri: topMembers[2].userAvatar }} style={styles.podiumAvatarImage} />
                  ) : (
                    <Users size={20} color={colors.white} />
                  )}
                </View>
                <View style={[styles.medalBadge, styles.bronzeMedal]}>
                  <Medal size={14} color="#CD7F32" />
                </View>
              </View>
              <Text style={styles.podiumName} numberOfLines={1}>{topMembers[2].userName}</Text>
              <Text style={styles.podiumXP}>{topMembers[2].xpPoints} XP</Text>
              <View style={[styles.podiumPillar, styles.podiumPillarThird]}></View>
            </View>
          )}
        </View>

        {/* Lista completa de ranking */}
        <View style={styles.rankingListContainer}>
          <Text style={styles.rankingListTitle}>Classificação Completa</Text>

          {filteredMembers.map((member, index) => (
            <View key={member.id} style={styles.rankingListItem}>
              <View style={styles.rankingListPosition}>
                <Text style={styles.rankingListPositionText}>{index + 1}</Text>
              </View>
              <View style={styles.rankingListAvatar}>
                {member.userAvatar ? (
                  <Image source={{ uri: member.userAvatar }} style={styles.rankingListAvatarImage} />
                ) : (
                  <Users size={16} color={colors.white} />
                )}
              </View>
              <View style={styles.rankingListInfo}>
                <Text style={styles.rankingListName}>{member.userName}</Text>
                <View style={styles.rankingListLevelContainer}>
                  <Star size={12} color={colors.primary} />
                  <Text style={styles.rankingListLevel}>Nível {member.level || 1}</Text>
                </View>
              </View>
              <View style={styles.rankingListXP}>
                <Text style={styles.rankingListXPValue}>{member.xpPoints}</Text>
                <Text style={styles.rankingListXPLabel}>XP</Text>
              </View>
              {member.userId === user.id && (
                <View style={styles.rankingListCurrentUser}>
                  <View style={styles.rankingListCurrentUserIndicator}></View>
                </View>
              )}
            </View>
          ))}
        </View>

        {/* Atividades que geram XP */}
        <GlassCard style={styles.xpActivitiesCard} gradient gradientColors={[`${colors.secondary}40`, `${colors.secondary}20`]}>
          <View style={styles.xpActivitiesHeader}>
            <Star size={20} color={colors.secondary} />
            <Text style={styles.xpActivitiesTitle}>Como Ganhar XP</Text>
          </View>

          <View style={styles.xpActivitiesList}>
            <View style={styles.xpActivityItem}>
              <BookOpen size={16} color={colors.secondary} />
              <Text style={styles.xpActivityName}>Criar Flashcard</Text>
              <Text style={styles.xpActivityValue}>+15 XP</Text>
            </View>
            <View style={styles.xpActivityItem}>
              <HelpCircle size={16} color={colors.secondary} />
              <Text style={styles.xpActivityName}>Completar Quiz</Text>
              <Text style={styles.xpActivityValue}>+20 XP</Text>
            </View>
            <View style={styles.xpActivityItem}>
              <FileText size={16} color={colors.secondary} />
              <Text style={styles.xpActivityName}>Criar Material</Text>
              <Text style={styles.xpActivityValue}>+20 XP</Text>
            </View>
            <View style={styles.xpActivityItem}>
              <BrainCircuit size={16} color={colors.secondary} />
              <Text style={styles.xpActivityName}>Criar Mapa Mental</Text>
              <Text style={styles.xpActivityValue}>+25 XP</Text>
            </View>
            <View style={styles.xpActivityItem}>
              <MessageSquare size={16} color={colors.secondary} />
              <Text style={styles.xpActivityName}>Comentar</Text>
              <Text style={styles.xpActivityValue}>+5 XP</Text>
            </View>
            <View style={styles.xpActivityItem}>
              <Clock size={16} color={colors.secondary} />
              <Text style={styles.xpActivityName}>Estudar (por hora)</Text>
              <Text style={styles.xpActivityValue}>+10 XP</Text>
            </View>
          </View>
        </GlassCard>
      </View>
    );
  };

  const renderResourcesTab = () => {
    const handleAddMaterial = (type: string) => {
      switch (type) {
        case 'note':
          setShowCreateNoteForm(true);
          break;
        case 'flashcard':
          setShowCreateFlashcardForm(true);
          break;
        case 'mindmap':
          setShowCreateMindMapForm(true);
          break;
        case 'quiz':
          Alert.alert('Em breve', 'A criação de quizzes estará disponível em breve!');
          break;
        default:
          break;
      }
    };

    const handleMaterialSuccess = () => {
      setShowCreateNoteForm(false);
      setShowCreateFlashcardForm(false);
      setShowCreateMindMapForm(false);
      fetchGroupMaterials(id as string);
    };

    const handleMaterialPress = (material: StudyGroupMaterial) => {
      router.push(`/study-groups/materials/${material.id}`);
    };

    // Importar os componentes necessários
    const { MaterialsTabView } = require('@/components/MaterialsTabView');
    const { CreateNoteForm } = require('@/components/CreateNoteForm');
    const { CreateFlashcardForm } = require('@/components/CreateFlashcardForm');
    const { CreateMindMapForm } = require('@/components/CreateMindMapForm');

    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Recursos de Estudo</Text>
        </View>

        {showCreateNoteForm ? (
          <CreateNoteForm
            groupId={id as string}
            onCancel={() => setShowCreateNoteForm(false)}
            onSuccess={handleMaterialSuccess}
          />
        ) : showCreateFlashcardForm ? (
          <CreateFlashcardForm
            groupId={id as string}
            onCancel={() => setShowCreateFlashcardForm(false)}
            onSuccess={handleMaterialSuccess}
          />
        ) : showCreateMindMapForm ? (
          <CreateMindMapForm
            groupId={id as string}
            onCancel={() => setShowCreateMindMapForm(false)}
            onSuccess={handleMaterialSuccess}
          />
        ) : (
          <MaterialsTabView
            groupId={id as string}
            materials={materials}
            loading={loading}
            isAdmin={userRole === 'admin'}
            onAddMaterial={handleAddMaterial}
            onMaterialPress={handleMaterialPress}
          />
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <RouteGuard groupId={id as string}>
        <View style={styles.container}>
          <LinearGradient
            colors={["#F9FAFB", "#F3F4F6"]}
            style={styles.backgroundGradient}
          />
          <Header
            title="Carregando..."
            leftComponent={
              <Pressable
                style={styles.headerButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color={colors.primary} />
              </Pressable>
            }
          />
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Carregando detalhes do grupo...</Text>
          </View>
        </View>
      </RouteGuard>
    );
  }

  if (localError || error || (!loading && !studyGroup)) {
    return (
      <RouteGuard groupId={id as string}>
        <View style={styles.container}>
          <LinearGradient
            colors={["#F9FAFB", "#F3F4F6"]}
            style={styles.backgroundGradient}
          />
          <Header
            title="Erro"
            leftComponent={
              <Pressable
                style={styles.headerButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color={colors.primary} />
              </Pressable>
            }
          />
          <View style={styles.errorContainer}>
            <View style={styles.errorIconContainer}>
              <AlertCircle size={60} color={colors.error} />
            </View>
            <Text style={styles.errorTitle}>Ops! Algo deu errado</Text>
            <Text style={styles.errorText}>
              {localError || error || 'Grupo não encontrado ou você não tem permissão para acessá-lo.'}
            </Text>
            <View style={styles.errorButtonsContainer}>
              <Button
                title="Tentar Novamente"
                onPress={() => {
                  setError(null);
                  fetchGroupDetails();
                }}
                variant="primary"
                size="medium"
                icon={RefreshCw}
                style={styles.errorButton}
              />
              <Button
                title="Voltar"
                onPress={() => router.back()}
                variant="secondary"
                size="medium"
                icon={ArrowLeft}
                style={styles.errorButton}
              />
            </View>
          </View>
        </View>
      </RouteGuard>
    );
  }

  return (
    <RouteGuard groupId={id as string}>
      <View style={styles.container}>
        <LinearGradient
          colors={["#F9FAFB", "#F3F4F6"]}
          style={styles.backgroundGradient}
        />
        <Header
          title={studyGroup.name}
          leftComponent={
            <Pressable
              style={styles.headerButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.primary} />
            </Pressable>
          }
          rightComponent={
          <View style={styles.headerRightContainer}>
            <Pressable
              style={styles.headerButton}
              onPress={handleShareInvite}
            >
              <Share2 size={24} color={colors.primary} />
            </Pressable>
            {userRole === 'admin' && (
              <>
                <Pressable
                  style={[styles.headerButton, { marginLeft: 8 }]}
                  onPress={() => router.push(`/study-groups/edit/${studyGroup.id}`)}
                >
                  <Edit size={24} color={colors.primary} />
                </Pressable>
                <Pressable
                  style={[styles.headerButton, { marginLeft: 8 }]}
                  onPress={() => router.push(`/study-groups/settings/${studyGroup.id}`)}
                >
                  <Settings size={24} color={colors.primary} />
                </Pressable>
              </>
            )}
          </View>
        }
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <View style={styles.groupHeader}>
          <GlassCard style={styles.groupCard}>
            {/* Status Badge */}
            <View style={styles.statusBadgeContainer}>
              {studyGroup.isOpen ? (
                <View style={[styles.badge, styles.openBadge]}>
                  <Unlock size={14} color={colors.success} />
                  <Text style={[styles.badgeText, styles.openBadgeText]}>Aberto</Text>
                </View>
              ) : (
                <View style={[styles.badge, styles.closedBadge]}>
                  <Lock size={14} color={colors.warning} />
                  <Text style={[styles.badgeText, styles.closedBadgeText]}>Fechado</Text>
                </View>
              )}
            </View>

            {/* Group Icon and Name */}
            <View style={styles.groupTitleContainer}>
              <View style={styles.groupIconContainer}>
                <Users size={28} color={colors.white} />
              </View>
              <View style={styles.groupTitleContent}>
                <Text style={styles.groupName}>{studyGroup.name}</Text>
                <View style={styles.dateContainer}>
                  <Calendar size={14} color={colors.textMedium} />
                  <Text style={styles.dateText}>
                    Criado {formatDistanceToNow(new Date(studyGroup.createdAt), { addSuffix: true, locale: ptBR })}
                  </Text>
                </View>
              </View>
            </View>

            {/* Group Description */}
            <Text style={styles.groupDescription}>
              {studyGroup.description || 'Sem descrição'}
            </Text>

            {/* Group Stats Cards */}
            <View style={styles.statsCardsContainer}>
              <View style={styles.statCard}>
                <View style={styles.statIconContainer}>
                  <UserPlus size={18} color={colors.primary} />
                </View>
                <Text style={styles.statValue}>{members.length}</Text>
                <Text style={styles.statLabel}>Membros</Text>
              </View>

              <View style={styles.statCard}>
                <View style={styles.statIconContainer}>
                  <BookOpen size={18} color={colors.primary} />
                </View>
                <Text style={styles.statValue}>{materials.length}</Text>
                <Text style={styles.statLabel}>Recursos</Text>
              </View>

              <View style={styles.statCard}>
                <View style={styles.statIconContainer}>
                  <Trophy size={18} color={colors.primary} />
                </View>
                <Text style={styles.statValue}>XP</Text>
                <Text style={styles.statLabel}>Ranking</Text>
              </View>
            </View>

            {/* Invite Code */}
            {studyGroup.inviteCode && (
              <Pressable
                style={styles.inviteCodeContainer}
                onPress={handleCopyInviteCode}
              >
                <View style={styles.inviteCodeContent}>
                  <Key size={16} color={colors.primary} />
                  <View style={styles.inviteCodeTextContainer}>
                    <Text style={styles.inviteCodeLabel}>Código de Convite</Text>
                    <Text style={styles.inviteCode}>{studyGroup.inviteCode}</Text>
                  </View>
                </View>
                {copiedCode ? (
                  <View style={styles.copiedIcon}>
                    <Check size={16} color={colors.success} />
                  </View>
                ) : (
                  <View style={styles.copyIcon}>
                    <Copy size={16} color={colors.primary} />
                  </View>
                )}
              </Pressable>
            )}
          </GlassCard>
        </View>

        <View style={styles.tabsContainer}>
          <Pressable
            style={[styles.tabButton, activeTab === 'members' && styles.activeTabButton]}
            onPress={() => setActiveTab('members')}
          >
            <Users size={20} color={activeTab === 'members' ? colors.primary : colors.textMedium} />
            <Text style={[styles.tabButtonText, activeTab === 'members' && styles.activeTabButtonText]}>
              Membros
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tabButton, activeTab === 'resources' && styles.activeTabButton]}
            onPress={() => setActiveTab('resources')}
          >
            <BookOpen size={20} color={activeTab === 'resources' ? colors.primary : colors.textMedium} />
            <Text style={[styles.tabButtonText, activeTab === 'resources' && styles.activeTabButtonText]}>
              Recursos
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tabButton, activeTab === 'ranking' && styles.activeTabButton]}
            onPress={() => setActiveTab('ranking')}
          >
            <Trophy size={20} color={activeTab === 'ranking' ? colors.primary : colors.textMedium} />
            <Text style={[styles.tabButtonText, activeTab === 'ranking' && styles.activeTabButtonText]}>
              Ranking
            </Text>
          </Pressable>
        </View>

        {activeTab === 'members' && renderMembersTab()}
        {activeTab === 'resources' && renderResourcesTab()}
        {activeTab === 'ranking' && renderRankingTab()}

        {userRole === 'admin' ? (
          <View style={styles.adminActionsContainer}>
            <Button
              title="Configurações do Grupo"
              onPress={() => router.push(`/study-groups/settings/${studyGroup.id}`)}
              variant="primary"
              size="medium"
              icon={Settings}
              style={styles.adminActionButton}
            />
            <Button
              title="Sair do Grupo"
              onPress={handleLeaveGroup}
              variant="danger"
              size="medium"
              style={styles.adminActionButton}
            />
          </View>
        ) : (
          <Button
            title="Sair do Grupo"
            onPress={handleLeaveGroup}
            variant="danger"
            size="medium"
            style={styles.leaveButton}
          />
        )}
      </ScrollView>
    </View>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  // Novos estilos para badges, detalhes do grupo e ranking
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backgroundGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerRightContainer: {
    flexDirection: "row",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 0,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textMedium,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  errorIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: `${colors.error}10`,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 20,
    borderWidth: 2,
    borderColor: `${colors.error}20`,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.text,
    marginBottom: 12,
    textAlign: "center",
  },
  errorText: {
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 30,
    textAlign: "center",
    lineHeight: 24,
  },
  errorButtonsContainer: {
    flexDirection: "row",
    gap: 12,
    width: "100%",
    maxWidth: 300,
  },
  errorButton: {
    flex: 1,
  },
  groupHeader: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  groupCard: {
    padding: 20,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: `${colors.primary}15`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  statusBadgeContainer: {
    alignItems: 'flex-end',
    marginBottom: 10,
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
  },
  openBadge: {
    backgroundColor: `${colors.success}15`,
    borderWidth: 1,
    borderColor: `${colors.success}30`,
  },
  closedBadge: {
    backgroundColor: `${colors.warning}15`,
    borderWidth: 1,
    borderColor: `${colors.warning}30`,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: "bold",
    marginLeft: 6,
  },
  openBadgeText: {
    color: colors.success,
  },
  closedBadgeText: {
    color: colors.warning,
  },
  groupTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  groupTitleContent: {
    flex: 1,
  },
  groupIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  groupName: {
    fontSize: 22,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  dateText: {
    fontSize: 12,
    color: colors.textMedium,
    marginLeft: 6,
  },
  groupDescription: {
    fontSize: 15,
    color: colors.textLight,
    marginBottom: 20,
    lineHeight: 22,
  },
  statsCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    alignItems: "center",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: `${colors.primary}15`,
    backgroundColor: colors.white,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    minHeight: 90,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.primary,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.textLight,
    textAlign: "center",
  },
  inviteCodeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: `${colors.primary}10`,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: `${colors.primary}20`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  inviteCodeContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  inviteCodeTextContainer: {
    marginLeft: 12,
  },
  inviteCodeLabel: {
    fontSize: 12,
    color: colors.textMedium,
    marginBottom: 2,
  },
  inviteCode: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.primary,
  },
  copyIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${colors.primary}10`,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: `${colors.primary}20`,
  },
  copiedIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${colors.success}15`,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: `${colors.success}30`,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "center",
  },
  actionButton: {
    marginHorizontal: 6,
    flex: 1,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  groupStatText: {
    fontSize: 14,
    color: colors.white,
    marginLeft: 6,
    fontWeight: "600",
    textShadowColor: 'rgba(0, 0, 0, 0.15)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  tabsContainer: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    padding: 6,
    borderWidth: 1,
    borderColor: `${colors.primary}15`,
    marginHorizontal: 16,
  },
  tabButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  activeTabButton: {
    backgroundColor: `${colors.primary}15`,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  tabButtonText: {
    fontSize: 15,
    color: colors.textMedium,
    marginLeft: 8,
    fontWeight: "500",
  },
  activeTabButtonText: {
    color: colors.primary,
    fontWeight: "bold",
    letterSpacing: 0.2,
  },
  tabContent: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  tabHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: `${colors.primary}10`,
  },
  tabTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.textDark,
    letterSpacing: 0.2,
  },
  tabActions: {
    flexDirection: "row",
  },
  tabActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: `${colors.primary}25`,
  },
  currentUserRankCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  rankCardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  rankCardTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginLeft: 8,
  },
  rankCardContent: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  rankCardStat: {
    alignItems: "center",
  },
  rankCardStatValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.primary,
    marginBottom: 4,
  },
  rankCardStatLabel: {
    fontSize: 12,
    color: colors.textMedium,
  },
  membersListHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  membersListTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
  },
  membersListSubtitle: {
    fontSize: 14,
    color: colors.textMedium,
  },

  memberCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  memberInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  memberRank: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: `${colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  memberRankText: {
    fontSize: 14,
    fontWeight: "bold",
    color: colors.primary,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.textDark,
  },
  memberStats: {
    flexDirection: "row",
    alignItems: "center",
  },
  memberStat: {
    alignItems: "center",
    marginLeft: 16,
  },
  memberStatValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.primary,
  },
  memberStatLabel: {
    fontSize: 12,
    color: colors.textMedium,
  },
  currentUserBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 16,
  },
  currentUserBadgeText: {
    fontSize: 12,
    fontWeight: "bold",
    color: colors.white,
  },

  statText: {
    fontSize: 14,
    color: colors.textMedium,
    marginLeft: 6,
  },

  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    textAlign: "center",
    fontSize: 16,
    color: colors.textMedium,
    marginTop: 16,
    marginBottom: 24,
  },
  materialCardContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  timerCard: {
    padding: 20,
    borderRadius: 16,
    alignItems: "center",
    marginBottom: 20,
  },
  timerIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: `${colors.primary}80`,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  timerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.white,
    marginBottom: 8,
  },
  timerDescription: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    textAlign: "center",
    marginBottom: 20,
  },
  timerButton: {
    width: "100%",
  },
  statsCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statBlock: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textMedium,
  },
  leaveButton: {
    marginTop: 24,
    marginBottom: 40,
    marginHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  adminActionsContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 16,
    paddingHorizontal: 16,
  },
  adminActionButton: {
    width: '100%',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  // Resource tab styles
  resourceTypesContainer: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    padding: 6,
    borderWidth: 1,
    borderColor: `${colors.primary}15`,
  },
  resourceTypeButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 2,
  },
  resourceTypeButtonActive: {
    backgroundColor: `${colors.primary}15`,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  resourceTypeText: {
    fontSize: 13,
    color: colors.primary,
    marginLeft: 6,
    fontWeight: "500",
  },
  resourceTypeTextActive: {
    color: colors.primary,
    fontWeight: "bold",
    letterSpacing: 0.2,
  },
  resourceContent: {
    marginTop: 16,
    marginBottom: 20,
  },
  resourceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: `${colors.primary}10`,
  },
  resourceTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
    letterSpacing: 0.2,
  },
  emptyResourceContainer: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 40,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 1,
    borderColor: `${colors.primary}10`,
  },
  emptyResourceTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.textDark,
    marginTop: 20,
    marginBottom: 10,
    letterSpacing: 0.2,
  },
  emptyResourceText: {
    textAlign: "center",
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 30,
    lineHeight: 24,
    maxWidth: '90%',
  },

  // Estilos para o ranking
  rankingCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  rankingHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  rankingTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginLeft: 8,
  },
  rankingList: {
    marginBottom: 8,
  },
  rankingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  rankBadge: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  goldBadge: {
    backgroundColor: "#FFF7E0",
    borderWidth: 1,
    borderColor: "#FFD700",
  },
  silverBadge: {
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#C0C0C0",
  },
  bronzeBadge: {
    backgroundColor: "#FFF0E8",
    borderWidth: 1,
    borderColor: "#CD7F32",
  },
  rankingName: {
    flex: 1,
    fontSize: 14,
    fontWeight: "500",
    color: colors.textDark,
  },
  rankingTime: {
    fontSize: 14,
    fontWeight: "bold",
    color: colors.primary,
  },

  // Estilos para atividades de XP
  xpActivitiesCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },

  // Estilos melhorados para o ranking
  periodFilterContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 6,
    marginVertical: 16,
    justifyContent: 'space-between',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 1,
    borderColor: `${colors.primary}15`,
  },
  periodFilterButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
  },
  periodFilterButtonActive: {
    backgroundColor: `${colors.primary}15`,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  periodFilterText: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.primary,
  },
  periodFilterTextActive: {
    color: colors.primary,
    fontWeight: 'bold',
    letterSpacing: 0.2,
  },

  rankingListContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 1,
    borderColor: `${colors.primary}10`,
  },
  rankingListTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.textDark,
    marginBottom: 16,
    letterSpacing: 0.2,
  },
  rankingListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: `${colors.border}80`,
    marginBottom: 8,
  },
  rankingListPosition: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  rankingListPositionText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.primary,
  },
  rankingListAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${colors.primary}20`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  rankingListAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  rankingListInfo: {
    flex: 1,
  },
  rankingListName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textDark,
    marginBottom: 2,
  },
  rankingListLevelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rankingListLevel: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  rankingListXP: {
    alignItems: 'flex-end',
  },
  rankingListXPValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
  },
  rankingListXPLabel: {
    fontSize: 10,
    color: colors.textLight,
  },
  rankingListCurrentUser: {
    marginLeft: 8,
  },
  rankingListCurrentUserIndicator: {
    width: 4,
    height: 24,
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  xpActivitiesHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },

  // Estilos para o pódio
  podiumContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginVertical: 24,
    height: 180,
  },
  podiumFirst: {
    alignItems: 'center',
    zIndex: 3,
  },
  podiumSecond: {
    alignItems: 'center',
    marginRight: 10,
    zIndex: 2,
  },
  podiumThird: {
    alignItems: 'center',
    marginLeft: 10,
    zIndex: 1,
  },
  crownContainer: {
    position: 'absolute',
    top: -20,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 4,
  },
  podiumAvatar: {
    position: 'relative',
    marginBottom: 8,
  },
  podiumAvatarInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  goldAvatar: {
    borderColor: '#FFD700',
    backgroundColor: '#FFF7E0',
  },
  silverAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderColor: '#C0C0C0',
    backgroundColor: '#F5F5F5',
  },
  bronzeAvatar: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderColor: '#CD7F32',
    backgroundColor: '#FFF0E8',
  },
  podiumAvatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
  },
  medalBadge: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  goldMedal: {
    backgroundColor: '#FFF7E0',
    borderColor: '#FFD700',
  },
  silverMedal: {
    backgroundColor: '#F5F5F5',
    borderColor: '#C0C0C0',
  },
  bronzeMedal: {
    backgroundColor: '#FFF0E8',
    borderColor: '#CD7F32',
  },
  podiumName: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textDark,
    marginBottom: 4,
    textAlign: 'center',
    maxWidth: 80,
  },
  podiumXP: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 8,
  },
  podiumPillar: {
    width: 60,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  podiumPillarFirst: {
    height: 80,
    backgroundColor: '#FFD700',
  },
  podiumPillarSecond: {
    height: 60,
    width: 50,
    backgroundColor: '#C0C0C0',
  },
  podiumPillarThird: {
    height: 40,
    width: 45,
    backgroundColor: '#CD7F32',
  },
  xpActivitiesTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginLeft: 8,
  },

  // Estilos para o card do usuário atual
  currentUserRankContainer: {
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  currentUserRankGradient: {
    borderRadius: 16,
  },
  currentUserRankContent: {
    padding: 16,
  },
  currentUserRankHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  currentUserRankPosition: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  currentUserRankPositionNumber: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.white,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  currentUserRankInfo: {
    flex: 1,
  },
  currentUserRankName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 6,
  },
  currentUserRankRoleBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  currentUserRankRoleText: {
    fontSize: 12,
    color: colors.white,
    fontWeight: '600',
  },
  currentUserRankStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    padding: 12,
    marginTop: 8,
  },
  currentUserRankStat: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  currentUserRankStatTextContainer: {
    marginLeft: 10,
  },
  currentUserRankStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  currentUserRankStatLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },

  // Estilos para o card de usuário no ranking
  userRankCard: {
    marginBottom: 20,
    padding: 16,
  },
  userRankHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  userRankPosition: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: `${colors.primary}30`,
  },
  userRankPositionNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  userRankInfo: {
    flex: 1,
  },
  userRankName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  userRankRoleBadge: {
    backgroundColor: `${colors.primary}10`,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: `${colors.primary}20`,
  },
  userRankRoleText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
  },
  userRankStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  userRankStat: {
    flex: 1,
    padding: 12,
    alignItems: "center",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.white,
  },
  userRankStatIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  userRankStatValue: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.primary,
    marginBottom: 2,
  },
  userRankStatLabel: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.textLight,
    textAlign: "center",
  },
  xpActivitiesList: {
    marginBottom: 8,
  },
  xpActivityItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  xpActivityName: {
    flex: 1,
    fontSize: 14,
    color: colors.textDark,
    marginLeft: 8,
  },
  xpActivityValue: {
    fontSize: 14,
    fontWeight: "bold",
    color: colors.primary,
  },

  // Estilos para o timer
  timerContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  timerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
  },
  closeTimerButton: {
    padding: 4,
  },

  // Estilos para os filtros de período
  periodFilterContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.border,
  },
  periodFilterHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  periodFilterTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginLeft: 10,
  },
  periodFilterButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 8,
  },
  tabTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  periodFilterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.white,
    gap: 6,
  },
  periodFilterButtonActive: {
    backgroundColor: `${colors.primary}10`,
    borderColor: `${colors.primary}30`,
  },
  periodFilterText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textMedium,
  },
  periodFilterTextActive: {
    color: colors.primary,
    fontWeight: 'bold',
  },

  // Estilos para o MaterialsTabView
  materialTabsContainer: {
    marginBottom: 16,
  },
  materialTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: colors.cardLight,
  },
  activeMaterialTab: {
    backgroundColor: colors.card,
  },
  materialTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginLeft: 4,
  },
  activeMaterialTabText: {
    color: colors.text,
    fontWeight: '600',
  },
});
