import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, Pressable, Alert, ActivityIndicator, ScrollView } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { supabase } from '@/lib/supabase';
import { formatDate, formatDuration, formatDateISO } from '@/utils/dateUtils';
import { Trash2, ArrowLeft, Clock, Calendar, Target, BarChart, ChevronLeft, ChevronRight } from 'lucide-react-native';

interface StudySession {
  id: string;
  date: string;
  total_time: number;
  sessions_completed: number;
  created_at: string;
}

type ViewMode = 'daily' | 'weekly' | 'monthly' | 'all';

export default function StatisticsScreen() {
  const router = useRouter();
  const [sessions, setSessions] = useState<StudySession[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalStudyTime, setTotalStudyTime] = useState(0);
  const [totalSessions, setTotalSessions] = useState(0);
  const [viewMode, setViewMode] = useState<ViewMode>('daily');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [groupedData, setGroupedData] = useState<{[key: string]: StudySession[]}>({});

  useEffect(() => {
    fetchStudySessions();
  }, []);

  useEffect(() => {
    if (sessions.length > 0) {
      groupSessionsByViewMode();
    }
  }, [sessions, viewMode, currentDate]);

  const fetchStudySessions = async () => {
    try {
      setLoading(true);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Buscar dados dos últimos 3 meses
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      const startDate = formatDateISO(threeMonthsAgo);

      const { data, error } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', user.id)
        .gte('date', startDate)
        .order('date', { ascending: false });

      if (error) {
        console.error('Erro ao buscar sessões de estudo:', error);
        return;
      }

      if (data) {
        setSessions(data as StudySession[]);

        // Calcular totais
        const totalTime = data.reduce((sum, session) => sum + (session.total_time || 0), 0);
        const totalCompletedSessions = data.reduce((sum, session) => sum + (session.sessions_completed || 0), 0);

        setTotalStudyTime(totalTime);
        setTotalSessions(totalCompletedSessions);
      }
    } catch (error) {
      console.error('Erro ao buscar sessões de estudo:', error);
    } finally {
      setLoading(false);
    }
  };

  const groupSessionsByViewMode = () => {
    const grouped: {[key: string]: StudySession[]} = {};

    if (viewMode === 'daily') {
      // Agrupar por dia
      sessions.forEach(session => {
        grouped[session.date] = grouped[session.date] || [];
        grouped[session.date].push(session);
      });
    } else if (viewMode === 'weekly') {
      // Agrupar por semana
      sessions.forEach(session => {
        const date = new Date(session.date);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay()); // Domingo como início da semana
        const weekKey = formatDateISO(weekStart);

        grouped[weekKey] = grouped[weekKey] || [];
        grouped[weekKey].push(session);
      });
    } else if (viewMode === 'monthly') {
      // Agrupar por mês
      sessions.forEach(session => {
        const date = new Date(session.date);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        grouped[monthKey] = grouped[monthKey] || [];
        grouped[monthKey].push(session);
      });
    } else if (viewMode === 'all') {
      // Visualização geral - agrupar tudo em uma única entrada
      grouped['all'] = [...sessions];
    }

    setGroupedData(grouped);
  };

  const handleDeleteSession = async (id: string) => {
    Alert.alert(
      'Excluir registro',
      'Tem certeza que deseja excluir este registro de estudo?',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('study_sessions')
                .delete()
                .eq('id', id);

              if (error) {
                console.error('Erro ao excluir sessão de estudo:', error);
                Alert.alert('Erro', 'Não foi possível excluir o registro.');
                return;
              }

              // Atualizar a lista após excluir
              fetchStudySessions();
            } catch (error) {
              console.error('Erro ao excluir sessão de estudo:', error);
              Alert.alert('Erro', 'Não foi possível excluir o registro.');
            }
          },
        },
      ],
    );
  };

  const formatSessionDate = (dateStr: string) => {
    try {
      // Se a data estiver no formato ISO (YYYY-MM-DD)
      const parts = dateStr.split('-');
      if (parts.length === 3) {
        const date = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        return formatDate(date);
      }
      return dateStr;
    } catch (error) {
      return dateStr;
    }
  };

  const formatPeriodLabel = (key: string) => {
    if (viewMode === 'daily') {
      return formatSessionDate(key);
    } else if (viewMode === 'weekly') {
      const startDate = new Date(key);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);
      return `${formatDate(startDate)} - ${formatDate(endDate)}`;
    } else if (viewMode === 'monthly') {
      const [year, month] = key.split('-');
      const date = new Date(parseInt(year), parseInt(month) - 1, 1);
      return date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    } else if (viewMode === 'all' && key === 'all') {
      return 'Histórico Completo';
    }
    return key;
  };

  const calculatePeriodStats = (sessions: StudySession[]) => {
    const totalTime = sessions.reduce((sum, session) => sum + (session.total_time || 0), 0);
    const totalCompletedSessions = sessions.reduce((sum, session) => sum + (session.sessions_completed || 0), 0);
    return { totalTime, totalCompletedSessions };
  };

  const handlePrevPeriod = () => {
    const newDate = new Date(currentDate);
    if (viewMode === 'daily') {
      newDate.setDate(newDate.getDate() - 1);
    } else if (viewMode === 'weekly') {
      newDate.setDate(newDate.getDate() - 7);
    } else if (viewMode === 'monthly') {
      newDate.setMonth(newDate.getMonth() - 1);
    }
    setCurrentDate(newDate);
  };

  const handleNextPeriod = () => {
    const newDate = new Date(currentDate);
    if (viewMode === 'daily') {
      newDate.setDate(newDate.getDate() + 1);
    } else if (viewMode === 'weekly') {
      newDate.setDate(newDate.getDate() + 7);
    } else if (viewMode === 'monthly') {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const handleChangeViewMode = (mode: ViewMode) => {
    setViewMode(mode);
  };

  const renderPeriodItem = ({ item }: { item: [string, StudySession[]] }) => {
    const [key, periodSessions] = item;
    const { totalTime, totalCompletedSessions } = calculatePeriodStats(periodSessions);

    return (
      <View style={styles.sessionCard}>
        <View style={styles.sessionHeader}>
          <Text style={styles.sessionDate}>{formatPeriodLabel(key)}</Text>
          {viewMode === 'daily' && periodSessions.length === 1 && (
            <Pressable
              style={styles.deleteButton}
              onPress={() => handleDeleteSession(periodSessions[0].id)}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Trash2 size={16} color={colors.error} />
            </Pressable>
          )}
        </View>

        <View style={styles.sessionStats}>
          <View style={styles.statItem}>
            <Clock size={16} color={colors.primary} />
            <Text style={styles.statValue}>{formatDuration(totalTime)}</Text>
            <Text style={styles.statLabel}>Tempo de estudo</Text>
          </View>

          <View style={styles.statItem}>
            <Target size={16} color={colors.primary} />
            <Text style={styles.statValue}>{totalCompletedSessions}</Text>
            <Text style={styles.statLabel}>Sessões</Text>
          </View>

          <View style={styles.statItem}>
            <Calendar size={16} color={colors.primary} />
            <Text style={styles.statValue}>{periodSessions.length}</Text>
            <Text style={styles.statLabel}>Dias</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: 'Estatísticas de Estudo',
          headerLeft: () => (
            <Pressable
              onPress={() => router.back()}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <ArrowLeft size={24} color={colors.text} />
            </Pressable>
          ),
        }}
      />

      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Resumo Total</Text>
        <View style={styles.summaryStats}>
          <View style={styles.summaryStat}>
            <Clock size={20} color={colors.primary} />
            <Text style={styles.summaryValue}>{formatDuration(totalStudyTime)}</Text>
            <Text style={styles.summaryLabel}>Tempo total</Text>
          </View>

          <View style={styles.summaryStat}>
            <Target size={20} color={colors.primary} />
            <Text style={styles.summaryValue}>{totalSessions}</Text>
            <Text style={styles.summaryLabel}>Sessões totais</Text>
          </View>

          <View style={styles.summaryStat}>
            <Calendar size={20} color={colors.primary} />
            <Text style={styles.summaryValue}>{sessions.length}</Text>
            <Text style={styles.summaryLabel}>Dias de estudo</Text>
          </View>
        </View>
      </View>

      <View style={styles.viewModeContainer}>
        <Pressable
          style={[styles.viewModeButton, viewMode === 'daily' && styles.viewModeButtonActive]}
          onPress={() => handleChangeViewMode('daily')}
        >
          <Text style={[styles.viewModeText, viewMode === 'daily' && styles.viewModeTextActive]}>Diário</Text>
        </Pressable>

        <Pressable
          style={[styles.viewModeButton, viewMode === 'weekly' && styles.viewModeButtonActive]}
          onPress={() => handleChangeViewMode('weekly')}
        >
          <Text style={[styles.viewModeText, viewMode === 'weekly' && styles.viewModeTextActive]}>Semanal</Text>
        </Pressable>

        <Pressable
          style={[styles.viewModeButton, viewMode === 'monthly' && styles.viewModeButtonActive]}
          onPress={() => handleChangeViewMode('monthly')}
        >
          <Text style={[styles.viewModeText, viewMode === 'monthly' && styles.viewModeTextActive]}>Mensal</Text>
        </Pressable>

        <Pressable
          style={[styles.viewModeButton, viewMode === 'all' && styles.viewModeButtonActive]}
          onPress={() => handleChangeViewMode('all')}
        >
          <Text style={[styles.viewModeText, viewMode === 'all' && styles.viewModeTextActive]}>Geral</Text>
        </Pressable>
      </View>

      {viewMode !== 'all' && (
        <View style={styles.periodNavigator}>
          <Pressable
            style={styles.periodButton}
            onPress={handlePrevPeriod}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <ChevronLeft size={24} color={colors.primary} />
          </Pressable>

          <View style={styles.periodLabelContainer}>
            <BarChart size={16} color={colors.primary} />
            <Text style={styles.periodLabel}>
              {viewMode === 'daily' ? 'Dia' : viewMode === 'weekly' ? 'Semana' : 'Mês'}
            </Text>
          </View>

          <Pressable
            style={styles.periodButton}
            onPress={handleNextPeriod}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <ChevronRight size={24} color={colors.primary} />
          </Pressable>
        </View>
      )}

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : sessions.length > 0 ? (
        <FlatList
          data={Object.entries(groupedData)}
          renderItem={renderPeriodItem}
          keyExtractor={([key]) => key}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Nenhum registro de estudo encontrado.</Text>
          <Text style={styles.emptySubtext}>
            Use o temporizador Pomodoro na página inicial para registrar seu tempo de estudo.
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  viewModeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 4,
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  viewModeButtonActive: {
    backgroundColor: colors.primary,
  },
  viewModeText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
  },
  viewModeTextActive: {
    color: colors.white,
    fontWeight: '600',
  },
  periodNavigator: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  periodButton: {
    padding: 8,
  },
  periodLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  periodLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  summaryContainer: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryStat: {
    alignItems: 'center',
    flex: 1,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginTop: 8,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: colors.textLight,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginHorizontal: 16,
    marginBottom: 8,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  sessionCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sessionDate: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  deleteButton: {
    padding: 4,
  },
  sessionStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
  },
});
