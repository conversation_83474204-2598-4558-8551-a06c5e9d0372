import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Dimensions,
  Pressable,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Link, router } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { theme } from '@/constants/theme';
import { colors } from '@/constants/colors';
import { images } from '@/constants/images';
import { User, Mail, Lock, Eye, EyeOff, ArrowLeft, Apple, UserPlus, LogIn, Shield, CheckCircle } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import { AuthGuard } from '@/components/AuthGuard';
import { validators } from '@/utils/validation';
import SafeAreaWrapper from '@/components/SafeAreaWrapper';
import { LinearGradient } from 'expo-linear-gradient';
import SocialLoginButtons from '@/components/SocialLoginButtons';

const { width, height } = Dimensions.get('window');

export default function RegisterScreen() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [nameError, setNameError] = useState<string | null>(null);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [confirmPasswordError, setConfirmPasswordError] = useState<string | null>(null);
  const [termsError, setTermsError] = useState<string | null>(null);
  const { signUp, signInWithGoogle, signInWithApple, loading } = useAuthStore();

  // Validar nome
  const validateName = (value: string) => {
    if (!value.trim()) {
      setNameError('O nome é obrigatório');
      return false;
    }

    setNameError(null);
    return true;
  };

  // Validar email
  const validateEmail = (value: string) => {
    if (!value.trim()) {
      setEmailError('O email é obrigatório');
      return false;
    }

    if (!validators.isValidEmail(value)) {
      setEmailError('Digite um email válido');
      return false;
    }

    setEmailError(null);
    return true;
  };

  // Validar senha com critérios de segurança
  const validatePassword = (value: string) => {
    if (!value.trim()) {
      setPasswordError('A senha é obrigatória');
      return false;
    }

    if (value.length < 8) {
      setPasswordError('A senha deve ter pelo menos 8 caracteres');
      return false;
    }

    if (!/(?=.*[a-z])/.test(value)) {
      setPasswordError('A senha deve conter pelo menos uma letra minúscula');
      return false;
    }

    if (!/(?=.*[A-Z])/.test(value)) {
      setPasswordError('A senha deve conter pelo menos uma letra maiúscula');
      return false;
    }

    if (!/(?=.*\d)/.test(value)) {
      setPasswordError('A senha deve conter pelo menos um número');
      return false;
    }

    setPasswordError(null);
    return true;
  };

  // Validar confirmação de senha
  const validateConfirmPassword = (value: string) => {
    if (!value.trim()) {
      setConfirmPasswordError('Confirme sua senha');
      return false;
    }

    if (value !== password) {
      setConfirmPasswordError('As senhas não coincidem');
      return false;
    }

    setConfirmPasswordError(null);
    return true;
  };

  // Validar termos de uso
  const validateTerms = () => {
    if (!acceptTerms) {
      setTermsError('Você deve aceitar os termos de uso e política de privacidade');
      return false;
    }
    setTermsError(null);
    return true;
  };

  const handleRegister = async () => {
    // Validar todos os campos
    const isNameValid = validateName(name);
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);
    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);
    const isTermsValid = validateTerms();

    if (!isNameValid || !isEmailValid || !isPasswordValid || !isConfirmPasswordValid || !isTermsValid) {
      return;
    }

    // Sanitizar dados
    const sanitizedName = validators.sanitizeText(name.trim());
    const sanitizedEmail = validators.sanitizeText(email.trim().toLowerCase());

    // Tentar fazer cadastro
    try {
      await signUp(sanitizedEmail, password, sanitizedName);
      Alert.alert(
        'Cadastro realizado!',
        'Verifique seu e-mail para confirmar o cadastro antes de fazer login.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/login')
          }
        ]
      );
    } catch (error) {
      Alert.alert('Erro no cadastro', 'Não foi possível realizar o cadastro. Verifique os dados e tente novamente.');
    }
  };

  return (
    <AuthGuard requireAuth={false}>
      <SafeAreaWrapper>
        <StatusBar style="light" />
        <View style={styles.container}>
          <LinearGradient
            colors={['#F9FAFB', '#F3F4F6']}
            style={styles.backgroundGradient}
          />

          <KeyboardAvoidingView
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {/* Back Button */}
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <ArrowLeft size={theme.sizes.icon.md} color={colors.textDark} />
              </TouchableOpacity>

              {/* Hero Section */}
              <View style={styles.heroSection}>
                <LinearGradient
                  colors={['#3399FF', '#66B2FF']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.heroGradient}
                >
                  <View style={styles.heroContent}>
                    <View style={styles.logoContainer}>
                      <Image
                        source={images.logoLiaBranco}
                        style={styles.logo}
                        resizeMode="contain"
                      />
                      <Text style={styles.appName}>Lia</Text>
                      <Text style={styles.tagline}>Seu assistente de estudos inteligente</Text>
                    </View>
                    <View style={styles.heroActions}>
                      <View style={styles.heroIconContainer}>
                        <UserPlus size={theme.sizes.icon.lg} color="rgba(255, 255, 255, 0.9)" />
                      </View>
                    </View>
                  </View>
                </LinearGradient>
              </View>

              {/* Registration Form */}
              <GlassCard style={styles.formCard} gradient>
                <Text style={styles.welcomeText}>Crie sua conta</Text>
                <Text style={styles.subtitle}>Comece sua jornada de estudos</Text>

                {/* Campo de nome */}
                <Input
                  label="Nome completo"
                  placeholder="Digite seu nome completo"
                  value={name}
                  onChangeText={(text) => {
                    setName(text);
                    if (nameError) validateName(text);
                  }}
                  error={nameError}
                  autoCapitalize="words"
                  icon={<User size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validateName(name)}
                  required
                />

                {/* Campo de email */}
                <Input
                  label="E-mail"
                  placeholder="Digite seu e-mail"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) validateEmail(text);
                  }}
                  error={emailError}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  icon={<Mail size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validateEmail(email)}
                  required
                />

                {/* Campo de senha */}
                <Input
                  label="Senha"
                  placeholder="Digite sua senha"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (passwordError) validatePassword(text);
                    if (confirmPassword) validateConfirmPassword(confirmPassword);
                  }}
                  error={passwordError}
                  secureTextEntry={!showPassword}
                  icon={<Lock size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validatePassword(password)}
                  rightIcon={
                    <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                      {showPassword ?
                        <EyeOff size={theme.sizes.icon.sm} color={colors.textMedium} /> :
                        <Eye size={theme.sizes.icon.sm} color={colors.textMedium} />
                      }
                    </TouchableOpacity>
                  }
                  required
                />

                {/* Campo de confirmação de senha */}
                <Input
                  label="Confirmar senha"
                  placeholder="Confirme sua senha"
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text);
                    if (confirmPasswordError) validateConfirmPassword(text);
                  }}
                  error={confirmPasswordError}
                  secureTextEntry={!showConfirmPassword}
                  icon={<Lock size={theme.sizes.icon.sm} color={colors.textMedium} />}
                  onBlur={() => validateConfirmPassword(confirmPassword)}
                  rightIcon={
                    <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                      {showConfirmPassword ?
                        <EyeOff size={theme.sizes.icon.sm} color={colors.textMedium} /> :
                        <Eye size={theme.sizes.icon.sm} color={colors.textMedium} />
                      }
                    </TouchableOpacity>
                  }
                  required
                />

                {/* Terms and Conditions */}
                <View style={styles.termsContainer}>
                  <Pressable
                    style={styles.termsCheckboxContainer}
                    onPress={() => {
                      setAcceptTerms(!acceptTerms);
                      if (termsError) validateTerms();
                    }}
                  >
                    <View style={[styles.checkbox, acceptTerms && styles.checkboxChecked]}>
                      {acceptTerms && <CheckCircle size={16} color={colors.white} />}
                    </View>
                    <View style={styles.termsTextContainer}>
                      <Text style={styles.termsText}>
                        Eu aceito os{' '}
                        <Text style={styles.termsLink}>Termos de Uso</Text>
                        {' '}e a{' '}
                        <Text style={styles.termsLink}>Política de Privacidade</Text>
                      </Text>
                    </View>
                  </Pressable>
                  {termsError && (
                    <Text style={styles.termsError}>{termsError}</Text>
                  )}
                </View>

                {/* Botão de cadastro */}
                <Button
                  title="Cadastrar"
                  onPress={handleRegister}
                  variant="primary"
                  size="large"
                  loading={loading}
                  disabled={loading || !acceptTerms}
                  fullWidth
                  style={styles.registerButton}
                  icon={UserPlus}
                />

                {/* Botões de login social */}
                <SocialLoginButtons
                  onGooglePress={signInWithGoogle}
                  onApplePress={signInWithApple}
                  loading={loading}
                  disabled={loading}
                />
              </GlassCard>

              {/* Link para login */}
              <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Já tem uma conta?</Text>
                <Link href="/login" asChild>
                  <TouchableOpacity style={styles.loginButton}>
                    <LogIn size={theme.sizes.icon.sm} color={colors.primary} />
                    <Text style={styles.loginLink}>Entrar</Text>
                  </TouchableOpacity>
                </Link>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </View>
      </SafeAreaWrapper>
    </AuthGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  backgroundGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // Hero Section Styles
  heroSection: {
    marginHorizontal: 16,
    marginTop: 60,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  heroGradient: {
    borderRadius: 20,
  },
  heroContent: {
    padding: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 8,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
  },
  tagline: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.85)',
    fontWeight: '500',
  },
  heroActions: {
    alignItems: 'center',
  },
  heroIconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: theme.sizes.iconContainer.md / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  // Form Card Styles
  formCard: {
    marginHorizontal: 16,
    padding: 24,
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textDark,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textMedium,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  // Terms and Conditions Styles
  termsContainer: {
    marginBottom: 24,
  },
  termsCheckboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.border,
    marginRight: 12,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  termsTextContainer: {
    flex: 1,
  },
  termsText: {
    fontSize: 14,
    color: colors.textMedium,
    lineHeight: 20,
    fontWeight: '500',
  },
  termsLink: {
    color: colors.primary,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  termsError: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
    fontWeight: '500',
  },
  registerButton: {
    marginBottom: 24,
  },
  // Login Section Styles
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  loginText: {
    fontSize: 15,
    color: colors.textMedium,
    marginRight: 8,
    fontWeight: '500',
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  loginLink: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.primary,
  },
});
