{"timestamp": "2025-05-27T13:31:56.072Z", "summary": {"total": 8, "passed": 2, "critical": 6, "high": 0, "medium": 0, "low": 0}, "results": {"critical": [{"test": "RLS Status Check", "status": "FAIL", "details": "Erro ao verificar tabelas: relation \"public.information_schema.tables\" does not exist", "timestamp": "2025-05-27T13:31:55.693Z"}, {"test": "Data Connectivity - users", "status": "FAIL", "details": "Dados acessíveis sem autenticação", "timestamp": "2025-05-27T13:31:55.801Z"}, {"test": "Data Connectivity - flashcard_sets", "status": "FAIL", "details": "Dados acessíveis sem autenticação", "timestamp": "2025-05-27T13:31:55.878Z"}, {"test": "Data Connectivity - quizzes", "status": "FAIL", "details": "Dados acessíveis sem autenticação", "timestamp": "2025-05-27T13:31:55.951Z"}, {"test": "Data Connectivity - notes", "status": "FAIL", "details": "Dados acessíveis sem autenticação", "timestamp": "2025-05-27T13:31:56.023Z"}, {"test": "Data Connectivity - study_groups", "status": "FAIL", "details": "Dados acessíveis sem autenticação", "timestamp": "2025-05-27T13:31:56.072Z"}], "high": [], "medium": [], "low": [], "passed": [{"test": "Auth Session", "status": "PASS", "details": "Nenhuma sessão ativa (esperado)", "timestamp": "2025-05-27T13:31:56.072Z"}, {"test": "<PERSON>th Config", "status": "PASS", "details": "Cliente Supabase configurado", "timestamp": "2025-05-27T13:31:56.072Z"}]}}