import React, { useState } from "react";
import { View, Text, StyleSheet, Pressable, Alert, Modal } from "react-native";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { applyStandardSizes } from "@/utils/designSystem";
import { FileQuestion, Clock, Award, ChevronRight, MoreVertical, Edit, Trash2, X } from "lucide-react-native";
import { Quiz } from "@/types";
import { formatDateTime } from "@/utils/dateUtils";
import { useRouter } from "expo-router";
import { useQuizStore } from "@/store/quizStore";

interface QuizCardProps {
  quiz: Quiz;
  onPress: (quiz: Quiz) => void;
  onEdit?: (quiz: Quiz) => void;
  onDelete?: (quiz: Quiz) => void;
}

export const QuizCard: React.FC<QuizCardProps> = ({ quiz, onPress, onEdit, onDelete }) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const router = useRouter();
  const { deleteQuiz } = useQuizStore();

  const handleEdit = () => {
    setMenuVisible(false);
    if (onEdit) {
      onEdit(quiz);
    } else {
      router.push(`/quiz/edit/${quiz.id}`);
    }
  };

  const handleDelete = () => {
    setMenuVisible(false);
    Alert.alert(
      "Excluir Quiz",
      `Tem certeza que deseja excluir o quiz "${quiz.title}"? Esta ação não pode ser desfeita.`,
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteQuiz(quiz.id);
              if (onDelete) {
                onDelete(quiz);
              }
              Alert.alert("Sucesso", "Quiz excluído com sucesso!");
            } catch (error) {
              Alert.alert("Erro", "Não foi possível excluir o quiz. Tente novamente.");
            }
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Pressable
        style={styles.cardContent}
        onPress={() => onPress(quiz)}
      >
        <View style={styles.iconContainer}>
          <FileQuestion size={applyStandardSizes.cardIcon()} color={colors.primary} />
        </View>
        <View style={styles.content}>
          <Text style={styles.title}>{quiz.title}</Text>
          <Text style={styles.description} numberOfLines={2}>
            {quiz.description}
          </Text>
          <View style={styles.details}>
            <View style={styles.detailItem}>
              <FileQuestion size={applyStandardSizes.detailIcon()} color={colors.textLight} />
              <Text style={styles.detailText}>{quiz.questions.length} perguntas</Text>
            </View>
            {quiz.timeLimit && (
              <View style={styles.detailItem}>
                <Clock size={applyStandardSizes.detailIcon()} color={colors.textLight} />
                <Text style={styles.detailText}>
                  {Math.floor(quiz.timeLimit / 60)} min
                </Text>
              </View>
            )}
            {quiz.bestScore !== null && (
              <View style={styles.detailItem}>
                <Award size={applyStandardSizes.detailIcon()} color={colors.textLight} />
                <Text style={styles.detailText}>
                  {quiz.bestScore}%
                </Text>
              </View>
            )}
          </View>
          {quiz.lastAttempt && (
            <Text style={styles.lastAttempt}>
              Última tentativa: {formatDateTime(new Date(quiz.lastAttempt))}
            </Text>
          )}
        </View>
        <ChevronRight size={theme.sizes.icon.sm} color={colors.textLight} />
      </Pressable>

      <Pressable
        style={styles.menuButton}
        onPress={() => setMenuVisible(true)}
        hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
      >
        <MoreVertical size={theme.sizes.icon.sm} color={colors.textLight} />
      </Pressable>

      <Modal
        visible={menuVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setMenuVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setMenuVisible(false)}
        >
          <View style={styles.menuContainer}>
            <Pressable
              style={styles.closeButton}
              onPress={() => setMenuVisible(false)}
            >
              <X size={theme.sizes.icon.sm} color={colors.text} />
            </Pressable>

            <Pressable
              style={styles.menuItem}
              onPress={handleEdit}
            >
              <Edit size={theme.sizes.icon.sm} color={colors.primary} />
              <Text style={styles.menuItemText}>Editar Quiz</Text>
            </Pressable>

            <Pressable
              style={styles.menuItem}
              onPress={handleDelete}
            >
              <Trash2 size={theme.sizes.icon.sm} color={colors.error} />
              <Text style={[styles.menuItemText, { color: colors.error }]}>Excluir Quiz</Text>
            </Pressable>
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'relative',
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
  },
  iconContainer: {
    width: applyStandardSizes.cardIconContainer(),
    height: applyStandardSizes.cardIconContainer(),
    borderRadius: 12,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
  details: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  detailText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  lastAttempt: {
    fontSize: 12,
    color: colors.textLight,
    fontStyle: "italic",
  },
  menuButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    padding: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    width: '80%',
    maxWidth: 300,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  closeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    padding: 4,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 12,
  },
});
