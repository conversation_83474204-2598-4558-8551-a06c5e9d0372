import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet, Pressable, Animated, Dimensions } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import * as Icons from "lucide-react-native";

const { width } = Dimensions.get("window");

interface HeroSectionProps {
  title: string;
  subtitle?: string;
  description?: string;
  icon?: keyof typeof Icons;
  iconSize?: number;
  gradientColors?: string[];
  gradientDirection?: "horizontal" | "vertical" | "diagonal";
  onPress?: () => void;
  rightComponent?: React.ReactNode;
  leftComponent?: React.ReactNode;
  style?: any;
  animated?: boolean;
  variant?: "default" | "compact" | "large";
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  subtitle,
  description,
  icon,
  iconSize = theme.sizes.icon.lg,
  gradientColors = colors.primaryGradient,
  gradientDirection = "diagonal",
  onPress,
  rightComponent,
  leftComponent,
  style,
  animated = true,
  variant = "default",
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    if (animated) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: theme.animation.timing.slow,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: theme.animation.timing.slow,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          ...theme.animation.easing.spring,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(1);
      slideAnim.setValue(0);
      scaleAnim.setValue(1);
    }
  }, [animated]);

  const getGradientStart = () => {
    switch (gradientDirection) {
      case "horizontal": return { x: 0, y: 0.5 };
      case "vertical": return { x: 0.5, y: 0 };
      case "diagonal": return { x: 0, y: 0 };
      default: return { x: 0, y: 0 };
    }
  };

  const getGradientEnd = () => {
    switch (gradientDirection) {
      case "horizontal": return { x: 1, y: 0.5 };
      case "vertical": return { x: 0.5, y: 1 };
      case "diagonal": return { x: 1, y: 1 };
      default: return { x: 1, y: 1 };
    }
  };

  const getContainerStyle = () => {
    switch (variant) {
      case "compact":
        return [styles.container, styles.compactContainer];
      case "large":
        return [styles.container, styles.largeContainer];
      default:
        return styles.container;
    }
  };

  const IconComponent = icon ? Icons[icon] : null;

  const renderContent = () => (
    <View style={styles.content}>
      <View style={styles.leftContent}>
        {leftComponent && (
          <View style={styles.leftComponent}>
            {leftComponent}
          </View>
        )}
        
        <View style={styles.textContainer}>
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim },
              ],
            }}
          >
            <Text style={[styles.title, variant === "compact" && styles.compactTitle]}>
              {title}
            </Text>
            
            {subtitle && (
              <Text style={[styles.subtitle, variant === "compact" && styles.compactSubtitle]}>
                {subtitle}
              </Text>
            )}
            
            {description && variant !== "compact" && (
              <Text style={styles.description}>
                {description}
              </Text>
            )}
          </Animated.View>
        </View>
      </View>

      <View style={styles.rightContent}>
        {IconComponent && (
          <Animated.View
            style={[
              styles.iconContainer,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <View style={styles.iconBackground}>
              <IconComponent 
                size={iconSize} 
                color="rgba(255, 255, 255, 0.9)" 
              />
            </View>
          </Animated.View>
        )}
        
        {rightComponent && (
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            }}
          >
            {rightComponent}
          </Animated.View>
        )}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <Pressable
        style={({ pressed }) => [
          getContainerStyle(),
          style,
          pressed && { opacity: 0.95 },
        ]}
        onPress={onPress}
      >
        <LinearGradient
          colors={gradientColors}
          start={getGradientStart()}
          end={getGradientEnd()}
          style={styles.gradient}
        >
          {renderContent()}
        </LinearGradient>
      </Pressable>
    );
  }

  return (
    <View style={[getContainerStyle(), style]}>
      <LinearGradient
        colors={gradientColors}
        start={getGradientStart()}
        end={getGradientEnd()}
        style={styles.gradient}
      >
        {renderContent()}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    overflow: "hidden",
    marginHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.sm,
    maxHeight: 140, // Altura máxima compacta
    minHeight: 120, // Altura mínima consistente
    shadowColor: "#3399FF",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  compactContainer: {
    marginVertical: theme.spacing.xs,
    maxHeight: 100,
    minHeight: 80,
  },
  largeContainer: {
    marginVertical: theme.spacing.lg,
    maxHeight: 180,
    minHeight: 160,
  },
  gradient: {
    width: "100%",
    height: "100%",
    borderRadius: 20,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    minHeight: 120,
    maxHeight: 140,
  },
  leftContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  leftComponent: {
    marginRight: theme.spacing.md,
  },
  textContainer: {
    flex: 1,
  },
  rightContent: {
    alignItems: "center",
    justifyContent: "center",
    marginLeft: theme.spacing.md,
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.white,
    marginBottom: 4,
    lineHeight: 26,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  compactTitle: {
    fontSize: 18,
    lineHeight: 24,
  },
  subtitle: {
    fontSize: 13,
    fontWeight: "500",
    color: "rgba(255, 255, 255, 0.9)",
    marginBottom: 2,
    lineHeight: 18,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  compactSubtitle: {
    fontSize: 12,
    marginBottom: 0,
  },
  description: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.8)",
    lineHeight: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  iconContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  iconBackground: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.4)",
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default HeroSection;
