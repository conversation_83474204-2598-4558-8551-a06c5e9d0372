import React, { useRef, useEffect } from "react";
import { View, Text, StyleSheet, TextInput, Pressable, Animated } from "react-native";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { Search, X, Filter } from "lucide-react-native";

interface FilterOption {
  id: string;
  label: string;
  icon?: React.ReactNode;
  active?: boolean;
}

interface ModernSearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onClear?: () => void;
  filters?: FilterOption[];
  onFilterPress?: (filterId: string) => void;
  showFilters?: boolean;
  animated?: boolean;
  style?: any;
}

export const ModernSearchBar: React.FC<ModernSearchBarProps> = ({
  value,
  onChangeText,
  placeholder = "Buscar...",
  onClear,
  filters = [],
  onFilterPress,
  showFilters = false,
  animated = true,
  style,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-20)).current;

  useEffect(() => {
    if (animated) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: theme.animation.timing.normal,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: theme.animation.timing.normal,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(1);
      slideAnim.setValue(0);
    }
  }, [animated]);

  return (
    <Animated.View
      style={[
        styles.container,
        style,
        animated && {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <View style={styles.searchIconContainer}>
            <Search size={theme.sizes.icon.sm} color={colors.primary} />
          </View>
          
          <TextInput
            style={styles.searchInput}
            value={value}
            onChangeText={onChangeText}
            placeholder={placeholder}
            placeholderTextColor={colors.textMedium}
            returnKeyType="search"
          />
          
          {value.length > 0 && (
            <Pressable onPress={onClear} style={styles.clearButton}>
              <X size={theme.sizes.icon.xs} color={colors.textMedium} />
            </Pressable>
          )}
        </View>
      </View>

      {/* Filters */}
      {showFilters && filters.length > 0 && (
        <View style={styles.filtersContainer}>
          <View style={styles.filterHeader}>
            <View style={styles.filterIconContainer}>
              <Filter size={theme.sizes.icon.xs} color="#fff" />
            </View>
            <Text style={styles.filterLabel}>Filtros</Text>
          </View>
          
          <View style={styles.filterButtons}>
            {filters.map((filter) => (
              <Pressable
                key={filter.id}
                style={[
                  styles.filterButton,
                  filter.active && styles.filterButtonActive,
                ]}
                onPress={() => onFilterPress?.(filter.id)}
              >
                {filter.icon}
                <Text
                  style={[
                    styles.filterButtonText,
                    filter.active && styles.filterButtonTextActive,
                  ]}
                >
                  {filter.label}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  searchContainer: {
    marginBottom: theme.spacing.sm,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: theme.borderRadius.input,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    ...theme.shadows.sm,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  searchIconContainer: {
    marginRight: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    paddingVertical: 0,
  },
  clearButton: {
    padding: theme.spacing.xs,
    marginLeft: theme.spacing.xs,
  },
  filtersContainer: {
    backgroundColor: colors.white,
    borderRadius: theme.borderRadius.card,
    padding: theme.spacing.md,
    ...theme.shadows.sm,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  filterHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
  },
  filterIconContainer: {
    width: theme.sizes.iconContainer.xs,
    height: theme.sizes.iconContainer.xs,
    borderRadius: theme.sizes.iconContainer.xs / 2,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: theme.spacing.xs,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
  },
  filterButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: theme.spacing.xs,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.button,
    borderWidth: 1,
    borderColor: colors.border,
    gap: theme.spacing.xxs,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.textMedium,
  },
  filterButtonTextActive: {
    color: colors.white,
  },
});

export default ModernSearchBar;
