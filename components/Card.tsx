import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  ViewStyle,
  TextStyle,
  Animated,
  Platform,
} from "react-native";
import { theme } from "@/constants/theme";
import { colors } from "@/constants/colors";
import { LinearGradient } from "expo-linear-gradient";

interface CardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  elevation?: "none" | "low" | "medium" | "high";
  variant?: "default" | "outlined" | "glass" | "gradient";
  gradientColors?: string[];
  gradientStart?: { x: number; y: number };
  gradientEnd?: { x: number; y: number };
  borderRadius?: number;
  disabled?: boolean;
  animatePress?: boolean;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  testID?: string;
}

export const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  onPress,
  onLongPress,
  style,
  contentStyle,
  titleStyle,
  subtitleStyle,
  elevation = "medium",
  variant = "default",
  gradientColors,
  gradientStart = { x: 0, y: 0 },
  gradientEnd = { x: 1, y: 1 },
  borderRadius = theme.borderRadius.lg,
  disabled = false,
  animatePress = true,
  header,
  footer,
  testID,
}) => {
  // Animação para o efeito de pressionar
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  // Funções para animação de pressionar
  const handlePressIn = () => {
    if (animatePress && !disabled && (onPress || onLongPress)) {
      Animated.spring(scaleAnim, {
        toValue: 0.98,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animatePress && !disabled && (onPress || onLongPress)) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }).start();
    }
  };

  // Obter estilos de elevação (sombra)
  const getElevationStyle = () => {
    switch (elevation) {
      case "none":
        return theme.shadows.none;
      case "low":
        return theme.shadows.sm;
      case "high":
        return theme.shadows.xl;
      case "medium":
      default:
        return theme.shadows.md;
    }
  };

  // Renderizar o conteúdo do card
  const renderContent = () => (
    <>
      {header && <View style={styles.header}>{header}</View>}

      {(title || subtitle) && (
        <View style={styles.titleContainer}>
          {title && (
            <Text style={[styles.title, titleStyle]} numberOfLines={2}>
              {title}
            </Text>
          )}
          {subtitle && (
            <Text style={[styles.subtitle, subtitleStyle]} numberOfLines={3}>
              {subtitle}
            </Text>
          )}
        </View>
      )}

      <View style={[styles.content, contentStyle]}>{children}</View>

      {footer && <View style={styles.footer}>{footer}</View>}
    </>
  );

  // Aplicar estilos com base na variante
  const getCardStyle = () => {
    const baseStyle: ViewStyle = {
      borderRadius,
      ...getElevationStyle(),
    };

    switch (variant) {
      case "outlined":
        return {
          ...baseStyle,
          backgroundColor: colors.background,
          borderWidth: 1,
          borderColor: colors.border,
        };
      case "glass":
        return {
          ...baseStyle,
          backgroundColor: colors.glass,
          borderWidth: 1,
          borderColor: "rgba(255, 255, 255, 0.2)",
        };
      case "gradient":
        return {
          ...baseStyle,
          overflow: "hidden",
        };
      case "default":
      default:
        return {
          ...baseStyle,
          backgroundColor: colors.card,
        };
    }
  };

  // Renderizar o card com base na variante e se é pressionável
  const renderCard = () => {
    const cardStyle = [styles.container, getCardStyle(), style];

    if (variant === "gradient") {
      const gradientColorValues = gradientColors || colors.primaryGradient;

      return (
        <Animated.View
          style={[
            { transform: [{ scale: scaleAnim }] },
            disabled && styles.disabled,
          ]}
          testID={testID}
        >
          <Pressable
            style={cardStyle}
            onPress={onPress}
            onLongPress={onLongPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            disabled={disabled || (!onPress && !onLongPress)}
          >
            <LinearGradient
              colors={gradientColorValues}
              start={gradientStart}
              end={gradientEnd}
              style={styles.gradient}
            >
              {renderContent()}
            </LinearGradient>
          </Pressable>
        </Animated.View>
      );
    }

    return (
      <Animated.View
        style={[
          { transform: [{ scale: scaleAnim }] },
          disabled && styles.disabled,
        ]}
        testID={testID}
      >
        <Pressable
          style={cardStyle}
          onPress={onPress}
          onLongPress={onLongPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || (!onPress && !onLongPress)}
        >
          {renderContent()}
        </Pressable>
      </Animated.View>
    );
  };

  return renderCard();
};

const styles = StyleSheet.create({
  container: {
    borderRadius: theme.borderRadius.lg,
    overflow: "hidden",
    marginBottom: theme.spacing.md,
  },
  gradient: {
    width: "100%",
    height: "100%",
  },
  header: {
    width: "100%",
  },
  titleContainer: {
    padding: theme.spacing.md,
    paddingBottom: 0,
  },
  title: {
    ...theme.typography.heading3,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    ...theme.typography.subtitle1,
    marginBottom: theme.spacing.sm,
  },
  content: {
    padding: theme.spacing.md,
  },
  footer: {
    width: "100%",
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  disabled: {
    opacity: 0.6,
  },
});

export default Card;
