import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { colors } from "@/constants/colors";
import { StudyGroup } from "@/types";
import { Users, UserPlus, BookOpen, Lock, Unlock, Calendar, Trophy, RefreshCw, Activity, AlertCircle } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

interface StudyGroupCardProps {
  group: StudyGroup;
  onPress: (group: StudyGroup) => void;
  onLongPress?: (group: StudyGroup) => void;
}

export const StudyGroupCard: React.FC<StudyGroupCardProps> = ({
  group,
  onPress,
  onLongPress,
}) => {
  // Validate group data
  if (!group || !group.id) {
    return (
      <View style={[styles.container, styles.errorContainer]}>
        <View style={styles.errorContent}>
          <AlertCircle size={24} color={colors.error} />
          <Text style={styles.errorText}>Erro ao carregar grupo</Text>
        </View>
      </View>
    );
  }

  // Safe date formatting with error handling
  const formattedDate = (() => {
    try {
      if (!group.createdAt) return 'Data não disponível';
      return formatDistanceToNow(new Date(group.createdAt), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (error) {
      console.error('Error formatting creation date:', error);
      return 'Data inválida';
    }
  })();

  // Format the last update date with error handling
  const lastUpdated = (() => {
    try {
      if (!group.updatedAt || group.updatedAt === group.createdAt) return null;
      return formatDistanceToNow(new Date(group.updatedAt), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (error) {
      console.error('Error formatting update date:', error);
      return null;
    }
  })();

  // Determine if the group is active (updated in the last 7 days) with error handling
  const isActive = (() => {
    try {
      if (!group.updatedAt) return false;
      const diffInMs = new Date().getTime() - new Date(group.updatedAt).getTime();
      return diffInMs < 7 * 24 * 60 * 60 * 1000;
    } catch (error) {
      console.error('Error checking activity:', error);
      return false;
    }
  })();

  // Safe fallbacks for counts
  const memberCount = group.memberCount ?? 0;
  const materialsCount = group.materialsCount ?? 0;

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(group)}
      onLongPress={onLongPress ? () => onLongPress(group) : undefined}
    >
      <View style={styles.statusContainer}>
        {group.isOpen ? (
          <View style={[styles.badge, styles.openBadge]}>
            <Unlock size={12} color={colors.success} />
            <Text style={[styles.badgeText, styles.openBadgeText]}>Aberto</Text>
          </View>
        ) : (
          <View style={[styles.badge, styles.closedBadge]}>
            <Lock size={12} color={colors.warning} />
            <Text style={[styles.badgeText, styles.closedBadgeText]}>Fechado</Text>
          </View>
        )}

        {isActive && (
          <View style={[styles.badge, styles.activeBadge]}>
            <Activity size={12} color={colors.primary} />
            <Text style={[styles.badgeText, styles.activeBadgeText]}>Ativo</Text>
          </View>
        )}
      </View>

      <View style={styles.cardHeader}>
        <LinearGradient
          colors={[colors.primary, colors.primaryDark]}
          style={styles.groupIconContainer}
        >
          <Users size={24} color={colors.white} />
        </LinearGradient>
        <View style={styles.groupInfo}>
          <Text style={styles.groupName}>{group.name}</Text>
          <Text style={styles.groupDescription} numberOfLines={2}>
            {group.description || 'Sem descrição'}
          </Text>
        </View>
      </View>

      <View style={styles.groupStats}>
        <View style={styles.statItem}>
          <UserPlus size={16} color={colors.primary} />
          <Text style={styles.statText}>{memberCount} membros</Text>
        </View>
        <View style={styles.statItem}>
          <BookOpen size={16} color={colors.primary} />
          <Text style={styles.statText}>{materialsCount} recursos</Text>
        </View>
        <View style={styles.statBadge}>
          <Trophy size={14} color={colors.secondary} />
          <Text style={styles.statBadgeText}>Ranking XP</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.datesContainer}>
          <View style={styles.dateContainer}>
            <Calendar size={14} color={colors.textLight} />
            <Text style={styles.dateText}>Criado {formattedDate}</Text>
          </View>

          {lastUpdated && (
            <View style={styles.dateContainer}>
              <RefreshCw size={14} color={colors.textLight} />
              <Text style={styles.dateText}>Atualizado {lastUpdated}</Text>
            </View>
          )}
        </View>

        {group.inviteCode && (
          <View style={styles.inviteCodeContainer}>
            <Text style={styles.inviteCodeLabel}>Código:</Text>
            <Text style={styles.inviteCode}>{group.inviteCode}</Text>
          </View>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    position: "relative",
    overflow: "hidden",
    borderWidth: 1,
    borderColor: `${colors.primary}10`,
    minHeight: 180,
    maxHeight: 300,
  },
  statusContainer: {
    position: "absolute",
    top: 12,
    right: 12,
    zIndex: 1,
    flexDirection: "row",
    gap: 8,
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
  },
  openBadge: {
    backgroundColor: `${colors.success}20`,
    borderColor: `${colors.success}30`,
  },
  closedBadge: {
    backgroundColor: `${colors.warning}20`,
    borderColor: `${colors.warning}30`,
  },
  activeBadge: {
    backgroundColor: `${colors.primary}20`,
    borderColor: `${colors.primary}30`,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: "bold",
    marginLeft: 4,
  },
  openBadgeText: {
    color: colors.success,
  },
  closedBadgeText: {
    color: colors.warning,
  },
  activeBadgeText: {
    color: colors.primary,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  groupIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 20,
    fontWeight: "800",
    color: colors.text,
    marginBottom: 6,
    letterSpacing: 0.3,
  },
  groupDescription: {
    fontSize: 14,
    color: colors.textMedium,
    lineHeight: 20,
    opacity: 0.9,
    letterSpacing: 0.1,
  },
  groupStats: {
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: `${colors.border}80`,
    paddingTop: 14,
    marginTop: 4,
    flexWrap: "wrap",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
    marginBottom: 8,
    backgroundColor: `${colors.primary}08`,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statText: {
    fontSize: 14,
    color: colors.textMedium,
    marginLeft: 6,
    fontWeight: "500",
  },
  statBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.secondary}15`,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: `${colors.secondary}25`,
    marginLeft: 'auto',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  statBadgeText: {
    fontSize: 12,
    fontWeight: "bold",
    color: colors.secondary,
    marginLeft: 5,
    letterSpacing: 0.2,
  },
  footer: {
    marginTop: 14,
    paddingTop: 14,
    borderTopWidth: 1,
    borderTopColor: `${colors.border}80`,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    flexWrap: "wrap",
  },
  datesContainer: {
    flexDirection: "column",
    justifyContent: "flex-start",
    gap: 6,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  dateText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  inviteCodeContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}10`,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: `${colors.primary}20`,
    marginTop: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  inviteCodeLabel: {
    fontSize: 11,
    color: colors.textMedium,
    marginRight: 4,
  },
  inviteCode: {
    fontSize: 11,
    fontWeight: "bold",
    color: colors.primary,
    letterSpacing: 0.5,
  },
  errorContainer: {
    backgroundColor: `${colors.error}10`,
    borderColor: `${colors.error}30`,
    borderWidth: 1,
  },
  errorContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginLeft: 8,
    fontWeight: "500",
  },
});
