import React, { useState, useEffect, useRef } from "react";
import { View, Text, StyleSheet, Pressable, Alert, Switch } from "react-native";
import { colors } from "@/constants/colors";
import { Play, Pause, RotateCcw, Check, Smartphone, Lock } from "lucide-react-native";
import { useStudyGroupStore } from "@/store/studyGroupStore";

interface StudyGroupTimerProps {
  groupId: string;
}

type TimerMode = "focus" | "shortBreak" | "longBreak";

export const StudyGroupTimer: React.FC<StudyGroupTimerProps> = ({ groupId }) => {
  const { updateMemberStudyTime, fetchGroupSettings } = useStudyGroupStore();

  // Timer settings
  const [focusTime, setFocusTime] = useState(25 * 60); // 25 minutes in seconds
  const [shortBreakTime, setShortBreakTime] = useState(5 * 60); // 5 minutes in seconds
  const [longBreakTime, setLongBreakTime] = useState(15 * 60); // 15 minutes in seconds
  const [longBreakInterval, setLongBreakInterval] = useState(4); // After 4 focus sessions

  // Timer state
  const [timerMode, setTimerMode] = useState<TimerMode>("focus");
  const [timeRemaining, setTimeRemaining] = useState(focusTime);
  const [isActive, setIsActive] = useState(false);
  const [sessionsCompleted, setSessionsCompleted] = useState(0);
  const [totalFocusTime, setTotalFocusTime] = useState(0);

  // App blocking state
  const [appBlockingEnabled, setAppBlockingEnabled] = useState(false);
  const [isAppBlockingActive, setIsAppBlockingActive] = useState(false);

  // Refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);

  // Initialize timer and load settings
  useEffect(() => {
    resetTimer();
    loadGroupSettings();
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Load group settings to check if app blocking is enabled
  const loadGroupSettings = async () => {
    try {
      const settings = await fetchGroupSettings(groupId);
      setAppBlockingEnabled(settings.enableAppBlocking || false);
    } catch (error) {
      console.error('Error loading group settings:', error);
    }
  };

  // Update time remaining when timer mode changes
  useEffect(() => {
    switch (timerMode) {
      case "focus":
        setTimeRemaining(focusTime);
        break;
      case "shortBreak":
        setTimeRemaining(shortBreakTime);
        break;
      case "longBreak":
        setTimeRemaining(longBreakTime);
        break;
    }
  }, [timerMode, focusTime, shortBreakTime, longBreakTime]);

  // Timer logic
  useEffect(() => {
    if (isActive) {
      startTimeRef.current = Date.now();
      timerRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleTimerComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isActive]);

  const startTimer = () => {
    setIsActive(true);

    // Activate app blocking if enabled
    if (appBlockingEnabled) {
      setIsAppBlockingActive(true);
      Alert.alert(
        "Bloqueio de Aplicativos Ativado",
        "Os aplicativos configurados foram bloqueados durante esta sessão de estudo."
      );
    }
  };

  const pauseTimer = () => {
    setIsActive(false);

    // If we were in focus mode, add the elapsed time to total focus time
    if (timerMode === "focus" && startTimeRef.current) {
      const elapsedSeconds = Math.floor((Date.now() - startTimeRef.current) / 1000);
      setTotalFocusTime(prev => prev + elapsedSeconds);
    }
  };

  const resetTimer = () => {
    setIsActive(false);
    switch (timerMode) {
      case "focus":
        setTimeRemaining(focusTime);
        break;
      case "shortBreak":
        setTimeRemaining(shortBreakTime);
        break;
      case "longBreak":
        setTimeRemaining(longBreakTime);
        break;
    }
  };

  const handleTimerComplete = () => {
    setIsActive(false);

    // Play sound or vibration here

    if (timerMode === "focus") {
      // Increment sessions completed
      const newSessionsCompleted = sessionsCompleted + 1;
      setSessionsCompleted(newSessionsCompleted);

      // Add focus time to total
      setTotalFocusTime(prev => prev + focusTime);

      // Determine next break type
      if (newSessionsCompleted % longBreakInterval === 0) {
        setTimerMode("longBreak");
        Alert.alert("Tempo de foco concluído!", "Hora de fazer uma pausa longa.");
      } else {
        setTimerMode("shortBreak");
        Alert.alert("Tempo de foco concluído!", "Hora de fazer uma pausa curta.");
      }
    } else {
      // Break is over, back to focus
      setTimerMode("focus");
      Alert.alert("Pausa concluída!", "Hora de voltar ao foco.");
    }
  };

  const handleFinishSession = async () => {
    try {
      // Convert seconds to minutes
      const totalMinutes = Math.floor(totalFocusTime / 60);

      // Deactivate app blocking
      if (isAppBlockingActive) {
        setIsAppBlockingActive(false);
      }

      if (totalMinutes > 0) {
        // Update study time in the database
        await updateMemberStudyTime(groupId, totalMinutes);

        Alert.alert(
          "Sessão finalizada!",
          `Você estudou por ${totalMinutes} minutos. Seu tempo foi registrado.`,
          [
            {
              text: "OK",
              onPress: () => {
                // Reset timer and stats
                setTimerMode("focus");
                setTimeRemaining(focusTime);
                setIsActive(false);
                setSessionsCompleted(0);
                setTotalFocusTime(0);
              },
            },
          ]
        );
      } else {
        Alert.alert("Nenhum tempo registrado", "Você precisa completar pelo menos uma sessão de foco para registrar o tempo.");
      }
    } catch (error) {
      console.error("Error saving study time:", error);
      Alert.alert("Erro", "Não foi possível salvar seu tempo de estudo.");
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Get color based on timer mode
  const getTimerColor = () => {
    switch (timerMode) {
      case "focus":
        return colors.primary;
      case "shortBreak":
        return colors.success;
      case "longBreak":
        return colors.info;
    }
  };

  // Get mode label
  const getModeLabel = () => {
    switch (timerMode) {
      case "focus":
        return "Tempo de Foco";
      case "shortBreak":
        return "Pausa Curta";
      case "longBreak":
        return "Pausa Longa";
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.timerModeSelector}>
        <Pressable
          style={[
            styles.modeButton,
            timerMode === "focus" && styles.activeModeButton,
          ]}
          onPress={() => {
            if (!isActive) setTimerMode("focus");
          }}
          disabled={isActive}
        >
          <Text
            style={[
              styles.modeButtonText,
              timerMode === "focus" && styles.activeModeButtonText,
            ]}
          >
            Foco
          </Text>
        </Pressable>
        <Pressable
          style={[
            styles.modeButton,
            timerMode === "shortBreak" && styles.activeModeButton,
          ]}
          onPress={() => {
            if (!isActive) setTimerMode("shortBreak");
          }}
          disabled={isActive}
        >
          <Text
            style={[
              styles.modeButtonText,
              timerMode === "shortBreak" && styles.activeModeButtonText,
            ]}
          >
            Pausa Curta
          </Text>
        </Pressable>
        <Pressable
          style={[
            styles.modeButton,
            timerMode === "longBreak" && styles.activeModeButton,
          ]}
          onPress={() => {
            if (!isActive) setTimerMode("longBreak");
          }}
          disabled={isActive}
        >
          <Text
            style={[
              styles.modeButtonText,
              timerMode === "longBreak" && styles.activeModeButtonText,
            ]}
          >
            Pausa Longa
          </Text>
        </Pressable>
      </View>

      <View style={styles.timerContainer}>
        <Text style={[styles.timerLabel, { color: getTimerColor() }]}>
          {getModeLabel()}
        </Text>
        <Text style={[styles.timerText, { color: getTimerColor() }]}>
          {formatTime(timeRemaining)}
        </Text>

        <View style={styles.controlsContainer}>
          {!isActive ? (
            <Pressable
              style={[styles.controlButton, { backgroundColor: colors.success }]}
              onPress={startTimer}
            >
              <Play size={24} color={colors.white} />
            </Pressable>
          ) : (
            <Pressable
              style={[styles.controlButton, { backgroundColor: colors.warning }]}
              onPress={pauseTimer}
            >
              <Pause size={24} color={colors.white} />
            </Pressable>
          )}

          <Pressable
            style={[styles.controlButton, { backgroundColor: colors.danger }]}
            onPress={resetTimer}
          >
            <RotateCcw size={24} color={colors.white} />
          </Pressable>

          <Pressable
            style={[styles.controlButton, { backgroundColor: colors.primary }]}
            onPress={handleFinishSession}
          >
            <Check size={24} color={colors.white} />
          </Pressable>
        </View>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Sessões Completadas</Text>
          <Text style={styles.statValue}>{sessionsCompleted}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Tempo Total de Foco</Text>
          <Text style={styles.statValue}>{formatTime(totalFocusTime)}</Text>
        </View>
      </View>

      {appBlockingEnabled && (
        <View style={styles.appBlockingContainer}>
          <View style={styles.appBlockingHeader}>
            <View style={styles.appBlockingTitleContainer}>
              <Smartphone size={20} color={isAppBlockingActive ? colors.success : colors.textMedium} />
              <Text style={styles.appBlockingTitle}>Bloqueio de Aplicativos</Text>
            </View>
            <View style={styles.appBlockingStatusContainer}>
              {isAppBlockingActive ? (
                <View style={styles.activeBlockingBadge}>
                  <Lock size={12} color={colors.white} />
                  <Text style={styles.activeBlockingText}>Ativo</Text>
                </View>
              ) : (
                <Text style={styles.inactiveBlockingText}>Inativo</Text>
              )}
            </View>
          </View>
          <Text style={styles.appBlockingDescription}>
            {isAppBlockingActive
              ? "Os aplicativos configurados estão bloqueados durante esta sessão de estudo."
              : "O bloqueio de aplicativos será ativado quando você iniciar o timer."}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 20,
  },
  timerModeSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  modeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    alignItems: "center",
    marginHorizontal: 4,
    backgroundColor: colors.lightGray,
  },
  activeModeButton: {
    backgroundColor: colors.primary,
  },
  modeButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textMedium,
  },
  activeModeButtonText: {
    color: colors.white,
  },
  timerContainer: {
    alignItems: "center",
    marginBottom: 20,
  },
  timerLabel: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  timerText: {
    fontSize: 64,
    fontWeight: "bold",
    marginBottom: 20,
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "center",
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 20,
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 5,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
  },
  appBlockingContainer: {
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 20,
  },
  appBlockingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  appBlockingTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  appBlockingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textDark,
  },
  appBlockingStatusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  activeBlockingBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  activeBlockingText: {
    fontSize: 12,
    fontWeight: "600",
    color: colors.white,
  },
  inactiveBlockingText: {
    fontSize: 12,
    color: colors.textMedium,
  },
  appBlockingDescription: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 8,
  },
});
