import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableWithoutFeedback,
  Dimensions,
  ScrollView,
  Image,
  Platform,
  Pressable,
} from 'react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { BookOpen, ArrowLeft, ArrowRight } from 'lucide-react-native';
import { Button } from './Button';

const { width } = Dimensions.get('window');

interface FlipCardProps {
  front: string;
  back: string;
  imageUrl?: string | null;
  onFlip?: (isFlipped: boolean) => void;
  onDifficulty?: (level: "easy" | "medium" | "hard") => void;
  showFrontButtons?: boolean;
}

export const FlipCard: React.FC<FlipCardProps> = ({
  front,
  back,
  imageUrl,
  onFlip,
  onDifficulty,
  showFrontButtons = false,
}) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const flipAnimation = useRef(new Animated.Value(0)).current;

  // Interpolate flip animation
  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: ['0deg', '180deg'],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: ['180deg', '360deg'],
  });

  // Interpolate scale animation for 3D effect
  const frontScale = flipAnimation.interpolate({
    inputRange: [0, 90, 180],
    outputRange: [1, 0.9, 0.8],
  });

  const backScale = flipAnimation.interpolate({
    inputRange: [0, 90, 180],
    outputRange: [0.8, 0.9, 1],
  });

  // Interpolate opacity for smoother transition
  const frontOpacity = flipAnimation.interpolate({
    inputRange: [0, 90, 180],
    outputRange: [1, 0.5, 0],
  });

  const backOpacity = flipAnimation.interpolate({
    inputRange: [0, 90, 180],
    outputRange: [0, 0.5, 1],
  });

  // Shadow effect that changes during flip
  const frontShadowOpacity = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: [0.3, 0],
  });

  const backShadowOpacity = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: [0, 0.3],
  });

  const frontAnimatedStyle = {
    transform: [
      { perspective: 1000 },
      { rotateY: frontInterpolate },
      { scale: frontScale },
    ],
    opacity: frontOpacity,
    zIndex: isFlipped ? 0 : 1,
  };

  const backAnimatedStyle = {
    transform: [
      { perspective: 1000 },
      { rotateY: backInterpolate },
      { scale: backScale },
    ],
    opacity: backOpacity,
    zIndex: isFlipped ? 1 : 0,
  };

  const frontShadowStyle = {
    shadowOpacity: frontShadowOpacity,
  };

  const backShadowStyle = {
    shadowOpacity: backShadowOpacity,
  };

  const flipCard = () => {
    const toValue = isFlipped ? 0 : 180;

    // Add a spring animation for more natural feel
    Animated.spring(flipAnimation, {
      toValue,
      friction: 8,
      tension: 10,
      useNativeDriver: true,
    }).start(() => {
      setIsFlipped(!isFlipped);
      if (onFlip) {
        onFlip(!isFlipped);
      }
    });
  };

  return (
    <View style={styles.container}>
      {/* Front Card */}
      <Animated.View
        style={[styles.cardContainer, frontAnimatedStyle, frontShadowStyle]}
      >
        <GlassCard style={styles.card} gradient>
          <TouchableWithoutFeedback onPress={flipCard}>
            <View style={styles.cardContent}>
              <BookOpen size={28} color={colors.primary} style={styles.cardIcon} />
              <ScrollView
                style={styles.cardScrollView}
                contentContainerStyle={styles.cardScrollContent}
                showsVerticalScrollIndicator={false}
              >
                {imageUrl && (
                  <View style={styles.cardImageContainer}>
                    <Image
                      source={{ uri: imageUrl }}
                      style={styles.cardImage}
                      resizeMode="contain"
                    />
                  </View>
                )}
                <Text style={styles.questionText}>{front}</Text>
              </ScrollView>

              <View style={styles.tapHint}>
                <Text style={styles.tapHintText}>Toque para virar</Text>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </GlassCard>
      </Animated.View>

      {/* Back Card */}
      <Animated.View
        style={[styles.cardContainer, styles.cardBack, backAnimatedStyle, backShadowStyle]}
      >
        <GlassCard style={styles.card} gradient>
          <TouchableWithoutFeedback onPress={flipCard}>
            <View style={styles.cardContent}>
              <ScrollView
                style={styles.cardScrollView}
                contentContainerStyle={styles.cardScrollContent}
                showsVerticalScrollIndicator={false}
              >
                <Text style={styles.answerText}>{back}</Text>
              </ScrollView>
              <View style={styles.tapHint}>
                <Text style={styles.tapHintText}>Toque para virar</Text>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </GlassCard>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width - 40,
    height: 450,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowRadius: 10,
    elevation: Platform.OS === 'android' ? 10 : 0,
  },
  cardBack: {
    transform: [{ rotateY: '180deg' }],
  },
  card: {
    width: '100%',
    height: '100%',
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
  },
  cardContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  cardIcon: {
    marginBottom: 16,
    opacity: 0.8,
  },
  cardImageContainer: {
    width: '100%',
    height: 150,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: colors.backgroundLight,
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  questionText: {
    fontSize: 26,
    fontWeight: '700',
    color: colors.text,
    textAlign: 'center',
    marginVertical: 20,
  },
  answerText: {
    fontSize: 22,
    color: colors.text,
    textAlign: 'center',
    lineHeight: 32,
    marginVertical: 20,
  },
  tapHint: {
    position: 'absolute',
    bottom: 16,
    alignItems: 'center',
  },
  tapHintText: {
    fontSize: 14,
    color: colors.textLight,
  },
  frontButtonsContainer: {
    width: '100%',
    position: 'absolute',
    bottom: 16,
    paddingHorizontal: 16,
  },
  difficultyLabel: {
    alignItems: 'center',
    marginBottom: 8,
  },
  difficultyLabelText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
  difficultyButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 4,
  },
  difficultyButton: {
    flex: 1,
    minHeight: 36,
  },
  cardScrollView: {
    flex: 1,
    width: '100%',
    maxHeight: 300,
  },
  cardScrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
});
