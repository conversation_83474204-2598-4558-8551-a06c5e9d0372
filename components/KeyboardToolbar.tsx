import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, Pressable, ScrollView, Keyboard, Platform, Dimensions, KeyboardEvent, LayoutAnimation } from "react-native";
import { colors } from "@/constants/colors";
import {
  Type,
  Heading,
  Image as ImageIcon,
  Table,
  List,
  Code,
  Quote,
  Minus,
  File,
  X,
  ChevronDown
} from "lucide-react-native";
import { NoteBlockType } from "@/types";

interface KeyboardToolbarProps {
  onSelectBlock: (type: NoteBlockType) => void;
  onShowFullMenu: () => void;
}

export const KeyboardToolbar: React.FC<KeyboardToolbarProps> = ({
  onSelectBlock,
  onShowFullMenu
}) => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  // Listen for keyboard events to get the keyboard height
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event: KeyboardEvent) => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setKeyboardHeight(event.endCoordinates.height);
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  const blockTypes = [
    { type: "text" as NoteBlockType, icon: Type, label: "Texto" },
    { type: "heading" as NoteBlockType, icon: Heading, label: "Título" },
    { type: "image" as NoteBlockType, icon: ImageIcon, label: "Imagem" },
    { type: "table" as NoteBlockType, icon: Table, label: "Tabela" },
    { type: "list" as NoteBlockType, icon: List, label: "Lista" },
    { type: "code" as NoteBlockType, icon: Code, label: "Código" },
    { type: "quote" as NoteBlockType, icon: Quote, label: "Citação" },
    { type: "divider" as NoteBlockType, icon: Minus, label: "Divisor" },
    { type: "file" as NoteBlockType, icon: File, label: "Arquivo" },
  ];

  // Calculate the position based on keyboard height
  const toolbarStyle = {
    ...styles.container,
    bottom: keyboardHeight > 0 ? keyboardHeight : 0, // Position right above the keyboard
    display: keyboardHeight > 0 ? 'flex' : 'none', // Only show when keyboard is visible
  };

  return (
    <View style={toolbarStyle}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        scrollEnabled={true}
        directionalLockEnabled={true} // Impede rolagem vertical
        showsVerticalScrollIndicator={false}
      >
        {blockTypes.map((block) => (
          <Pressable
            key={block.type}
            style={styles.blockButton}
            onPress={() => onSelectBlock(block.type)}
          >
            <block.icon size={20} color={colors.text} />
            <Text style={styles.blockButtonLabel}>{block.label}</Text>
          </Pressable>
        ))}

        <Pressable
          style={styles.blockButton}
          onPress={onShowFullMenu}
        >
          <ChevronDown size={20} color={colors.text} />
          <Text style={styles.blockButtonLabel}>Mais</Text>
        </Pressable>
      </ScrollView>

      <Pressable
        style={styles.keyboardDismissButton}
        onPress={() => Keyboard.dismiss()}
      >
        <X size={20} color={colors.text} />
        <Text style={styles.blockButtonLabel}>Fechar</Text>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF", // Solid white background for visibility
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: "center",
    justifyContent: "space-between",
    // Positioned at the keyboard's top
    position: 'absolute',
    bottom: 0, // This will be adjusted by the Keyboard API
    left: 0,
    right: 0,
    zIndex: 1000,
    height: 76, // Fixed height for better visibility
    elevation: 8, // Android elevation
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  scrollContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 8,
  },
  blockButton: {
    width: 70,
    height: 60,
    borderRadius: 8,
    backgroundColor: "#F5F5F5", // Lighter background for better contrast
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#E8E8E8",
    paddingVertical: 8,
  },
  blockButtonLabel: {
    fontSize: 12,
    color: colors.text,
    marginTop: 4,
    textAlign: "center",
  },
  keyboardDismissButton: {
    width: 70,
    height: 60,
    borderRadius: 8,
    backgroundColor: "#F5F5F5",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#E8E8E8",
    paddingVertical: 8,
  },
});
