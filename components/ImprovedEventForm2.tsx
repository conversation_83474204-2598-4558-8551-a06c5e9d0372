import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Modal,
  Pressable,
  ScrollView,
  Alert,
  Platform,
  Switch,
  TouchableOpacity
} from 'react-native';
import { colors } from '@/constants/colors';
import { CalendarEvent } from '@/types';
import { X, Calendar, Clock, Palette, Bell, BookOpen, FileText, GraduationCap, Users, Tag } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, addHours, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { GlassCard } from './GlassCard';
import { useStudyStore } from '@/store/studyStore';

interface ImprovedEventFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialEvent?: Partial<CalendarEvent>;
  selectedDate?: string;
}

export const ImprovedEventForm2: React.FC<ImprovedEventFormProps> = ({
  visible,
  onClose,
  onSave,
  initialEvent,
  selectedDate
}) => {
  const { subjects } = useStudyStore();
  
  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(new Date().setHours(new Date().getHours() + 1)));
  const [allDay, setAllDay] = useState(false);
  const [color, setColor] = useState(colors.primary);
  const [reminder, setReminder] = useState(false);
  const [reminderTime, setReminderTime] = useState(new Date());
  const [type, setType] = useState<'study' | 'exam' | 'assignment' | 'meeting' | 'other'>('study');
  const [subject, setSubject] = useState<string | null>(null);
  const [subjectId, setSubjectId] = useState<string | null>(null);
  
  // Date picker visibility
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);
  
  // Form validation
  const [titleError, setTitleError] = useState(false);
  const [dateError, setDateError] = useState(false);
  
  // Color options
  const colorOptions = [
    colors.primary, '#F97316', '#10B981', '#8B5CF6',
    '#EC4899', '#F59E0B', '#EF4444', '#3B82F6'
  ];
  
  // Event type options
  const eventTypes = [
    { value: 'study', label: 'Estudo', icon: BookOpen, color: '#4F46E5' },
    { value: 'exam', label: 'Prova', icon: FileText, color: '#EF4444' },
    { value: 'assignment', label: 'Tarefa', icon: GraduationCap, color: '#10B981' },
    { value: 'meeting', label: 'Reunião', icon: Users, color: '#F59E0B' },
    { value: 'other', label: 'Outro', icon: Tag, color: '#8B5CF6' }
  ];
  
  // Reset form when modal is opened
  useEffect(() => {
    if (visible) {
      resetForm();
    }
  }, [visible]);
  
  // Validate dates when they change
  useEffect(() => {
    if (endDate < startDate) {
      setDateError(true);
    } else {
      setDateError(false);
    }
  }, [startDate, endDate]);
  
  // Initialize form with initial values or defaults
  const resetForm = () => {
    try {
      if (initialEvent && Object.keys(initialEvent).length > 0) {
        console.log('ImprovedEventForm: Inicializando com evento existente:', initialEvent);
        setTitle(initialEvent.title || '');
        setDescription(initialEvent.description || '');
        
        // Handle dates
        if (initialEvent.startDate) {
          setStartDate(new Date(initialEvent.startDate));
        } else {
          const defaultDate = new Date();
          defaultDate.setHours(10, 0, 0, 0);
          setStartDate(defaultDate);
        }
        
        if (initialEvent.endDate) {
          setEndDate(new Date(initialEvent.endDate));
        } else {
          setEndDate(addHours(startDate, 1));
        }
        
        setAllDay(initialEvent.allDay || false);
        setColor(initialEvent.color || colors.primary);
        setReminder(initialEvent.reminder || false);
        setType(initialEvent.type || 'study');
        setSubject(initialEvent.subject || null);
        setSubjectId(initialEvent.subject_id || null);
        
        if (initialEvent.reminderTime) {
          setReminderTime(new Date(initialEvent.reminderTime));
        } else {
          // Default reminder time (30 minutes before start)
          const defaultReminderTime = new Date(startDate);
          defaultReminderTime.setMinutes(defaultReminderTime.getMinutes() - 30);
          setReminderTime(defaultReminderTime);
        }
      } else {
        console.log('ImprovedEventForm: Inicializando com valores padrão');
        
        // Default values for new event
        let defaultDate = new Date();
        
        if (selectedDate) {
          try {
            const parsedDate = new Date(selectedDate);
            if (!isNaN(parsedDate.getTime())) {
              defaultDate = parsedDate;
            }
          } catch (e) {
            console.error('Erro ao parsear selectedDate:', e);
          }
        }
        
        defaultDate.setHours(10, 0, 0, 0); // 10:00 AM
        
        setTitle('');
        setDescription('');
        setStartDate(defaultDate);
        setEndDate(addHours(defaultDate, 1)); // 1 hora depois
        setAllDay(false);
        setColor(colors.primary);
        setReminder(false);
        setType('study');
        setSubject(null);
        setSubjectId(null);
        
        // Default reminder time (30 minutes before start)
        const defaultReminderTime = new Date(defaultDate);
        defaultReminderTime.setMinutes(defaultReminderTime.getMinutes() - 30);
        setReminderTime(defaultReminderTime);
      }
      
      // Reset validation errors
      setTitleError(false);
      setDateError(false);
    } catch (error) {
      console.error('Erro ao inicializar formulário:', error);
      
      // Fallback para valores padrão em caso de erro
      const defaultDate = new Date();
      defaultDate.setHours(10, 0, 0, 0);
      
      setTitle('');
      setDescription('');
      setStartDate(defaultDate);
      setEndDate(addHours(defaultDate, 1));
      setAllDay(false);
      setColor(colors.primary);
      setReminder(false);
      setType('study');
      setSubject(null);
      setSubjectId(null);
      
      // Default reminder time (30 minutes before start)
      const defaultReminderTime = new Date(defaultDate);
      defaultReminderTime.setMinutes(defaultReminderTime.getMinutes() - 30);
      setReminderTime(defaultReminderTime);
    }
  };
  
  // Validate form before saving
  const validateForm = () => {
    let isValid = true;
    
    // Title validation
    if (!title.trim()) {
      setTitleError(true);
      isValid = false;
    } else {
      setTitleError(false);
    }
    
    // Date validation
    if (endDate < startDate) {
      setDateError(true);
      isValid = false;
    } else {
      setDateError(false);
    }
    
    return isValid;
  };
  
  const handleSave = () => {
    if (!validateForm()) {
      Alert.alert('Erro', 'Por favor, corrija os erros no formulário antes de salvar.');
      return;
    }
    
    const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      allDay,
      color: color,
      type: type,
      completed: initialEvent?.completed || false,
      reminder: reminder,
      reminderTime: reminder ? reminderTime.toISOString() : undefined,
      subject: subject || undefined,
      subject_id: subjectId || undefined
    };
    
    console.log('ImprovedEventForm: Salvando evento:', event);
    onSave(event);
    onClose();
  };
  
  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      try {
        const newDate = new Date(selectedDate);
        
        // Validate the date
        if (isNaN(newDate.getTime())) {
          console.error('Invalid date selected');
          return;
        }
        
        newDate.setHours(
          startDate.getHours(),
          startDate.getMinutes(),
          startDate.getSeconds(),
          startDate.getMilliseconds()
        );
        setStartDate(newDate);
        
        // If end date is before new start date, update end date
        if (endDate < newDate) {
          setEndDate(addHours(newDate, 1));
        }
      } catch (error) {
        console.error('Error handling start date change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar a data. Por favor, tente novamente.');
      }
    }
  };
  
  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
    setShowStartTimePicker(false);
    if (selectedTime) {
      try {
        const newDate = new Date(startDate);
        
        // Validate the time
        if (isNaN(selectedTime.getTime())) {
          console.error('Invalid time selected');
          return;
        }
        
        newDate.setHours(
          selectedTime.getHours(),
          selectedTime.getMinutes(),
          selectedTime.getSeconds(),
          selectedTime.getMilliseconds()
        );
        setStartDate(newDate);
        
        // If end date is before new start date, update end date
        if (endDate < newDate) {
          setEndDate(addHours(newDate, 1));
        }
      } catch (error) {
        console.error('Error handling start time change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar o horário. Por favor, tente novamente.');
      }
    }
  };
  
  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      try {
        const newDate = new Date(selectedDate);
        
        // Validate the date
        if (isNaN(newDate.getTime())) {
          console.error('Invalid date selected');
          return;
        }
        
        newDate.setHours(
          endDate.getHours(),
          endDate.getMinutes(),
          endDate.getSeconds(),
          endDate.getMilliseconds()
        );
        setEndDate(newDate);
        
        // If end date is before start date, show error
        if (newDate < startDate) {
          setDateError(true);
        } else {
          setDateError(false);
        }
      } catch (error) {
        console.error('Error handling end date change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar a data. Por favor, tente novamente.');
      }
    }
  };
  
  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
    setShowEndTimePicker(false);
    if (selectedTime) {
      try {
        const newDate = new Date(endDate);
        
        // Validate the time
        if (isNaN(selectedTime.getTime())) {
          console.error('Invalid time selected');
          return;
        }
        
        newDate.setHours(
          selectedTime.getHours(),
          selectedTime.getMinutes(),
          selectedTime.getSeconds(),
          selectedTime.getMilliseconds()
        );
        setEndDate(newDate);
        
        // If end date is before start date, show error
        if (newDate < startDate) {
          setDateError(true);
        } else {
          setDateError(false);
        }
      } catch (error) {
        console.error('Error handling end time change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar o horário. Por favor, tente novamente.');
      }
    }
  };
  
  const handleReminderTimeChange = (event: any, selectedTime?: Date) => {
    setShowReminderTimePicker(false);
    if (selectedTime) {
      try {
        // Validate the time
        if (isNaN(selectedTime.getTime())) {
          console.error('Invalid reminder time selected');
          return;
        }
        
        setReminderTime(selectedTime);
      } catch (error) {
        console.error('Error handling reminder time change:', error);
        Alert.alert('Erro', 'Ocorreu um erro ao selecionar o horário do lembrete. Por favor, tente novamente.');
      }
    }
  };
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {initialEvent?.id ? 'Editar Evento' : 'Novo Evento'}
            </Text>
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>
          
          <ScrollView style={styles.scrollView}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Título <Text style={styles.requiredMark}>*</Text></Text>
              <TextInput
                style={[styles.input, titleError && styles.inputError]}
                value={title}
                onChangeText={(text) => {
                  setTitle(text);
                  if (text.trim()) setTitleError(false);
                }}
                placeholder="Título do evento"
                placeholderTextColor={colors.textLight}
              />
              {titleError && (
                <Text style={styles.errorText}>O título é obrigatório</Text>
              )}
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Descrição do evento"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Dia inteiro</Text>
              <Switch
                value={allDay}
                onValueChange={setAllDay}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={allDay ? colors.primary : colors.white}
              />
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de início <Text style={styles.requiredMark}>*</Text></Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showStartDatePicker && (
                <DateTimePicker
                  value={startDate}
                  mode="date"
                  display="default"
                  onChange={handleStartDateChange}
                />
              )}
            </View>
            
            {!allDay && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Hora de início</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowStartTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(startDate, 'HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showStartTimePicker && (
                  <DateTimePicker
                    value={startDate}
                    mode="time"
                    display="default"
                    onChange={handleStartTimeChange}
                  />
                )}
              </View>
            )}
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de término <Text style={styles.requiredMark}>*</Text></Text>
              <Pressable
                style={[styles.datePickerButton, dateError && styles.inputError]}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(endDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showEndDatePicker && (
                <DateTimePicker
                  value={endDate}
                  mode="date"
                  display="default"
                  onChange={handleEndDateChange}
                />
              )}
              {dateError && (
                <Text style={styles.errorText}>A data de término deve ser posterior à data de início</Text>
              )}
            </View>
            
            {!allDay && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Hora de término</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowEndTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(endDate, 'HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showEndTimePicker && (
                  <DateTimePicker
                    value={endDate}
                    mode="time"
                    display="default"
                    onChange={handleEndTimeChange}
                  />
                )}
              </View>
            )}
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Lembrete</Text>
              <Switch
                value={reminder}
                onValueChange={setReminder}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={reminder ? colors.primary : colors.white}
              />
            </View>
            
            {reminder && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Horário do lembrete</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowReminderTimePicker(true)}
                >
                  <Bell size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(reminderTime, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showReminderTimePicker && (
                  <DateTimePicker
                    value={reminderTime}
                    mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                    display="default"
                    onChange={handleReminderTimeChange}
                  />
                )}
              </View>
            )}
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Tipo de Evento</Text>
              <View style={styles.eventTypeContainer}>
                {eventTypes.map((eventType) => {
                  const Icon = eventType.icon;
                  return (
                    <TouchableOpacity
                      key={eventType.value}
                      style={[
                        styles.eventTypeButton,
                        type === eventType.value && styles.eventTypeButtonActive,
                        type === eventType.value && { borderColor: eventType.color }
                      ]}
                      onPress={() => setType(eventType.value as any)}
                    >
                      <Icon size={16} color={type === eventType.value ? eventType.color : colors.textLight} />
                      <Text
                        style={[
                          styles.eventTypeText,
                          type === eventType.value && styles.eventTypeTextActive,
                          type === eventType.value && { color: eventType.color }
                        ]}
                      >
                        {eventType.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>Cor</Text>
              <Pressable
                style={styles.colorButton}
                onPress={() => setShowColorPicker(true)}
              >
                <View style={[styles.colorPreview, { backgroundColor: color }]} />
                <Text style={styles.pickerButtonText}>Selecionar cor</Text>
              </Pressable>
              <Modal
                visible={showColorPicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowColorPicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Cor</Text>
                      <Pressable onPress={() => setShowColorPicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <View style={styles.colorGrid}>
                      {colorOptions.map((colorOption) => (
                        <Pressable
                          key={colorOption}
                          style={[styles.colorOption, { backgroundColor: colorOption }, color === colorOption && styles.colorOptionSelected]}
                          onPress={() => {
                            setColor(colorOption);
                            setShowColorPicker(false);
                          }}
                        />
                      ))}
                    </View>
                  </View>
                </View>
              </Modal>
            </View>
            
            <View style={styles.buttonContainer}>
              <Pressable
                style={[styles.button, styles.cancelButton]}
                onPress={onClose}
              >
                <Text style={styles.buttonText}>Cancelar</Text>
              </Pressable>
              <Pressable
                style={[styles.button, styles.saveButton]}
                onPress={handleSave}
              >
                <Text style={[styles.buttonText, styles.saveButtonText]}>Salvar</Text>
              </Pressable>
            </View>
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  formContainer: {
    width: '100%',
    maxHeight: '80%',
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 5,
  },
  scrollView: {
    flex: 1,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  requiredMark: {
    color: colors.error,
  },
  input: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  inputError: {
    borderColor: colors.error,
    borderWidth: 1.5,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
  textArea: {
    minHeight: 100,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  saveButtonText: {
    color: colors.white,
  },
  colorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
  },
  colorPreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  pickerButtonText: {
    fontSize: 16,
    color: colors.text,
  },
  pickerModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  pickerContent: {
    width: '100%',
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  colorOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    margin: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  colorOptionSelected: {
    borderWidth: 3,
    borderColor: colors.text,
  },
  eventTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  eventTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 8,
    minWidth: '48%',
    backgroundColor: colors.background,
  },
  eventTypeButtonActive: {
    backgroundColor: `${colors.backgroundLight}80`,
    borderWidth: 1.5,
  },
  eventTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginLeft: 6,
  },
  eventTypeTextActive: {
    fontWeight: '700',
  },
});
