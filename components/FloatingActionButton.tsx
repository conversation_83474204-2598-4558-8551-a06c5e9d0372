import React, { useState } from "react";
import { View, Text, StyleSheet, Pressable, Animated } from "react-native";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import {
  Plus,
  Image,
  Table,
  List,
  Code,
  File,
  X
} from "lucide-react-native";
import { NoteBlockType } from "@/types";

interface FloatingActionButtonProps {
  onAddBlock: (type: NoteBlockType) => void;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onAddBlock,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const animation = useState(new Animated.Value(0))[0];

  const toggleMenu = () => {
    const toValue = isOpen ? 0 : 1;

    Animated.spring(animation, {
      toValue,
      friction: 6,
      useNativeDriver: true,
    }).start();

    setIsOpen(!isOpen);
  };

  const actionItems = [
    { type: "text" as NoteBlockType, icon: Plus, label: "Texto" },
    { type: "image" as NoteBlockType, icon: Image, label: "Imagem" },
    { type: "table" as NoteBlockType, icon: Table, label: "Tabela" },
    { type: "list" as NoteBlockType, icon: List, label: "Lista" },
    { type: "code" as NoteBlockType, icon: Code, label: "Código" },
    { type: "file" as NoteBlockType, icon: File, label: "Arquivo" },
  ];

  const actionItemAnimations = actionItems.map((_, index) => {
    return {
      translateY: animation.interpolate({
        inputRange: [0, 1],
        outputRange: [0, -60 * (index + 1)],
      }),
      opacity: animation.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0, 0, 1],
      }),
    };
  });

  return (
    <View style={styles.container}>
      {actionItems.map((item, index) => (
        <Animated.View
          key={item.type}
          style={[
            styles.actionButton,
            {
              transform: [{ translateY: actionItemAnimations[index].translateY }],
              opacity: actionItemAnimations[index].opacity,
            },
          ]}
        >
          <Pressable
            style={styles.actionButtonInner}
            onPress={() => {
              onAddBlock(item.type);
              toggleMenu();
            }}
          >
            <item.icon size={theme.sizes.icon.sm} color="#fff" />
          </Pressable>
          <View style={styles.actionLabel}>
            <Text style={styles.actionLabelText}>{item.label}</Text>
          </View>
        </Animated.View>
      ))}

      <Pressable style={styles.mainButton} onPress={toggleMenu}>
        {isOpen ? (
          <X size={theme.sizes.icon.md} color="#fff" />
        ) : (
          <Plus size={theme.sizes.icon.md} color="#fff" />
        )}
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 20,
    right: 20,
    alignItems: "center",
  },
  mainButton: {
    width: theme.sizes.fab.main,
    height: theme.sizes.fab.main,
    borderRadius: theme.sizes.fab.main / 2,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 1,
  },
  actionButton: {
    position: "absolute",
    width: theme.sizes.fab.secondary,
    height: theme.sizes.fab.secondary,
    borderRadius: theme.sizes.fab.secondary / 2,
    justifyContent: "center",
    alignItems: "center",
  },
  actionButtonInner: {
    width: theme.sizes.fab.secondary,
    height: theme.sizes.fab.secondary,
    borderRadius: theme.sizes.fab.secondary / 2,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  actionLabel: {
    position: "absolute",
    right: 60,
    backgroundColor: colors.card,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  actionLabelText: {
    color: colors.text,
    fontSize: 14,
  },
});
