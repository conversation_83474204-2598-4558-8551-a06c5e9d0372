import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Pressable,
  Modal,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { X, Sparkles, AlertCircle } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { hasApiKey } from '@/services/openai';
import { APIKeyModal } from './APIKeyModal';

interface AIGenerationModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  placeholder?: string;
  onGenerate: (prompt: string) => Promise<void>;
  loading?: boolean;
  type?: 'flashcards' | 'mindmap' | 'conversation';
}

export const AIGenerationModal: React.FC<AIGenerationModalProps> = ({
  visible,
  onClose,
  title,
  placeholder = 'Digite um tópico ou assunto...',
  onGenerate,
  loading = false,
  type = 'flashcards',
}) => {
  const [prompt, setPrompt] = useState('');
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [isCheckingApiKey, setIsCheckingApiKey] = useState(false);

  const getTypeDescription = () => {
    switch (type) {
      case 'flashcards':
        return 'Gere flashcards automaticamente com base em um tópico ou assunto. A IA criará perguntas e respostas relevantes para ajudar nos seus estudos.';
      case 'mindmap':
        return 'Crie mapas mentais automaticamente com base em um tópico ou assunto. A IA organizará conceitos e suas relações de forma visual.';
      case 'conversation':
        return 'Inicie uma conversa de estudo com a IA sobre um tópico específico. Faça perguntas e receba explicações detalhadas.';
      default:
        return 'Gere conteúdo automaticamente com base em um tópico ou assunto.';
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      Alert.alert('Erro', 'Por favor, insira um tópico ou assunto.');
      return;
    }

    setIsCheckingApiKey(true);
    try {
      const apiKeyExists = await hasApiKey();
      if (!apiKeyExists) {
        setShowApiKeyModal(true);
        return;
      }

      await onGenerate(prompt);
    } catch (error) {
      console.error('Error in generation:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao gerar o conteúdo. Por favor, tente novamente.');
    } finally {
      setIsCheckingApiKey(false);
    }
  };

  const handleApiKeySuccess = async () => {
    // Try generation again after API key is set
    try {
      await onGenerate(prompt);
    } catch (error) {
      console.error('Error in generation after API key set:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao gerar o conteúdo. Por favor, tente novamente.');
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={onClose}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{title}</Text>
              <Pressable style={styles.closeButton} onPress={onClose}>
                <X size={24} color={colors.text} />
              </Pressable>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.descriptionContainer}>
                <Sparkles size={20} color={colors.primary} style={styles.descriptionIcon} />
                <Text style={styles.description}>{getTypeDescription()}</Text>
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder={placeholder}
                  placeholderTextColor={colors.textMedium}
                  value={prompt}
                  onChangeText={setPrompt}
                  multiline
                  numberOfLines={3}
                  maxLength={200}
                />
              </View>

              <View style={styles.tipContainer}>
                <AlertCircle size={16} color={colors.textLight} style={styles.tipIcon} />
                <Text style={styles.tipText}>
                  Dica: Seja específico para obter melhores resultados. Por exemplo, "Fotossíntese em plantas" em vez de apenas "Biologia".
                </Text>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Pressable
                style={[styles.button, styles.cancelButton]}
                onPress={onClose}
              >
                <Text style={styles.buttonText}>Cancelar</Text>
              </Pressable>
              <Pressable
                style={[
                  styles.button,
                  styles.generateButton,
                  (loading || isCheckingApiKey) && styles.buttonDisabled,
                ]}
                onPress={handleGenerate}
                disabled={loading || isCheckingApiKey}
              >
                {loading || isCheckingApiKey ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.generateButtonText}>Gerar</Text>
                )}
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>

      <APIKeyModal
        visible={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        onSuccess={handleApiKeySuccess}
      />
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 500,
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: '80%',
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
    maxHeight: 400,
  },
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    backgroundColor: 'rgba(79, 70, 229, 0.1)',
    padding: 12,
    borderRadius: 8,
  },
  descriptionIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  description: {
    flex: 1,
    fontSize: 15,
    color: colors.text,
    lineHeight: 22,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  input: {
    padding: 12,
    fontSize: 16,
    color: colors.text,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  tipIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: colors.textLight,
    fontStyle: 'italic',
    lineHeight: 20,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 12,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  generateButton: {
    backgroundColor: colors.primary,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  generateButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
});
