/**
 * Componente de dashboard para monitorar o desempenho das consultas ao banco de dados
 * 
 * Este componente exibe métricas de desempenho para consultas ao banco de dados
 * e fornece ferramentas para analisar e otimizar o desempenho.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  ActivityIndicator,
  FlatList,
  Alert,
  useWindowDimensions,
} from 'react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { queryPerformanceMonitor } from '@/services/queryPerformanceMonitor';
import { BarChart, LineChart } from 'react-native-chart-kit';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  Activity, 
  AlertTriangle, 
  BarChart2, 
  Clock, 
  Database, 
  Download, 
  Filter, 
  RefreshCw, 
  Search, 
  Trash2, 
  Upload, 
  X 
} from 'lucide-react-native';

// Tipos
interface QueryMetric {
  id: string;
  operation: string;
  tableName: string;
  timestamp: number;
  duration: number;
  success: boolean;
  error?: string;
  params?: Record<string, any>;
  fromCache?: boolean;
  userAgent?: string;
  platform?: string;
  appVersion?: string;
}

interface MetricSummary {
  tableName: string;
  totalQueries: number;
  averageDuration: number;
  slowQueries: number;
  errorCount: number;
  cacheHitRate: number;
}

// Componente principal
export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<QueryMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [summaries, setSummaries] = useState<MetricSummary[]>([]);
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [selectedOperation, setSelectedOperation] = useState<string | null>(null);
  const [showSlowOnly, setShowSlowOnly] = useState(false);
  const [showErrorsOnly, setShowErrorsOnly] = useState(false);
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('day');
  const { width } = useWindowDimensions();

  // Carregar métricas
  useEffect(() => {
    loadMetrics();
  }, []);

  // Calcular sumários quando as métricas mudarem
  useEffect(() => {
    calculateSummaries();
  }, [metrics]);

  // Carregar métricas do monitor
  const loadMetrics = async () => {
    setLoading(true);
    try {
      const allMetrics = queryPerformanceMonitor.getMetrics();
      setMetrics(allMetrics);
    } catch (error) {
      console.error('Erro ao carregar métricas:', error);
      Alert.alert('Erro', 'Não foi possível carregar as métricas de desempenho.');
    } finally {
      setLoading(false);
    }
  };

  // Calcular sumários para cada tabela
  const calculateSummaries = () => {
    if (metrics.length === 0) {
      setSummaries([]);
      return;
    }

    // Agrupar métricas por tabela
    const tableGroups: Record<string, QueryMetric[]> = {};
    metrics.forEach(metric => {
      if (!tableGroups[metric.tableName]) {
        tableGroups[metric.tableName] = [];
      }
      tableGroups[metric.tableName].push(metric);
    });

    // Calcular sumários para cada tabela
    const newSummaries: MetricSummary[] = Object.entries(tableGroups).map(([tableName, tableMetrics]) => {
      const totalQueries = tableMetrics.length;
      const totalDuration = tableMetrics.reduce((sum, metric) => sum + metric.duration, 0);
      const averageDuration = totalDuration / totalQueries;
      const slowQueries = tableMetrics.filter(metric => metric.duration > 500).length;
      const errorCount = tableMetrics.filter(metric => !metric.success).length;
      const cacheHits = tableMetrics.filter(metric => metric.fromCache).length;
      const cacheHitRate = (cacheHits / totalQueries) * 100;

      return {
        tableName,
        totalQueries,
        averageDuration,
        slowQueries,
        errorCount,
        cacheHitRate,
      };
    });

    // Ordenar sumários por número de consultas lentas (decrescente)
    newSummaries.sort((a, b) => b.slowQueries - a.slowQueries);

    setSummaries(newSummaries);
  };

  // Filtrar métricas com base nas seleções
  const getFilteredMetrics = (): QueryMetric[] => {
    let filtered = [...metrics];

    // Filtrar por tabela
    if (selectedTable) {
      filtered = filtered.filter(metric => metric.tableName === selectedTable);
    }

    // Filtrar por operação
    if (selectedOperation) {
      filtered = filtered.filter(metric => metric.operation === selectedOperation);
    }

    // Filtrar consultas lentas
    if (showSlowOnly) {
      filtered = filtered.filter(metric => metric.duration > 500);
    }

    // Filtrar erros
    if (showErrorsOnly) {
      filtered = filtered.filter(metric => !metric.success);
    }

    // Filtrar por intervalo de tempo
    const now = Date.now();
    let cutoffTime = now;
    if (timeRange === 'day') {
      cutoffTime = now - 24 * 60 * 60 * 1000; // 1 dia
    } else if (timeRange === 'week') {
      cutoffTime = now - 7 * 24 * 60 * 60 * 1000; // 1 semana
    } else if (timeRange === 'month') {
      cutoffTime = now - 30 * 24 * 60 * 60 * 1000; // 1 mês
    }
    filtered = filtered.filter(metric => metric.timestamp >= cutoffTime);

    return filtered;
  };

  // Limpar métricas
  const clearMetrics = async () => {
    Alert.alert(
      'Limpar Métricas',
      'Tem certeza que deseja limpar todas as métricas de desempenho?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: async () => {
            try {
              await queryPerformanceMonitor.clearMetrics();
              setMetrics([]);
              Alert.alert('Sucesso', 'Métricas de desempenho foram limpas.');
            } catch (error) {
              console.error('Erro ao limpar métricas:', error);
              Alert.alert('Erro', 'Não foi possível limpar as métricas de desempenho.');
            }
          },
        },
      ]
    );
  };

  // Enviar métricas para o servidor
  const uploadMetrics = async () => {
    try {
      await queryPerformanceMonitor.uploadMetrics();
      loadMetrics();
      Alert.alert('Sucesso', 'Métricas de desempenho foram enviadas para o servidor.');
    } catch (error) {
      console.error('Erro ao enviar métricas:', error);
      Alert.alert('Erro', 'Não foi possível enviar as métricas de desempenho.');
    }
  };

  // Renderizar item de métrica
  const renderMetricItem = ({ item }: { item: QueryMetric }) => (
    <GlassCard style={styles.metricCard}>
      <View style={styles.metricHeader}>
        <Text style={styles.metricOperation}>{item.operation}</Text>
        <Text style={styles.metricTimestamp}>
          {formatDistanceToNow(new Date(item.timestamp), { addSuffix: true, locale: ptBR })}
        </Text>
      </View>
      <View style={styles.metricDetails}>
        <Text style={styles.metricTable}>Tabela: {item.tableName}</Text>
        <Text style={[
          styles.metricDuration,
          item.duration > 500 ? styles.slowQuery : null
        ]}>
          {item.duration.toFixed(2)}ms
        </Text>
      </View>
      {item.fromCache && (
        <View style={styles.cacheIndicator}>
          <Text style={styles.cacheText}>Cache</Text>
        </View>
      )}
      {!item.success && (
        <View style={styles.errorContainer}>
          <AlertTriangle size={16} color={colors.error} />
          <Text style={styles.errorText}>{item.error || 'Erro desconhecido'}</Text>
        </View>
      )}
    </GlassCard>
  );

  // Renderizar componente
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Dashboard de Desempenho</Text>
        <View style={styles.actions}>
          <Pressable style={styles.actionButton} onPress={loadMetrics}>
            <RefreshCw size={20} color={colors.primary} />
          </Pressable>
          <Pressable style={styles.actionButton} onPress={uploadMetrics}>
            <Upload size={20} color={colors.primary} />
          </Pressable>
          <Pressable style={styles.actionButton} onPress={clearMetrics}>
            <Trash2 size={20} color={colors.error} />
          </Pressable>
        </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Carregando métricas...</Text>
        </View>
      ) : (
        <ScrollView style={styles.content}>
          {/* Sumário */}
          <GlassCard style={styles.summaryCard}>
            <Text style={styles.sectionTitle}>Resumo</Text>
            <View style={styles.summaryStats}>
              <View style={styles.statItem}>
                <Database size={24} color={colors.primary} />
                <Text style={styles.statValue}>{metrics.length}</Text>
                <Text style={styles.statLabel}>Consultas</Text>
              </View>
              <View style={styles.statItem}>
                <Clock size={24} color={colors.primary} />
                <Text style={styles.statValue}>
                  {metrics.length > 0
                    ? (metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length).toFixed(0)
                    : '0'}ms
                </Text>
                <Text style={styles.statLabel}>Média</Text>
              </View>
              <View style={styles.statItem}>
                <AlertTriangle size={24} color={colors.warning} />
                <Text style={styles.statValue}>
                  {metrics.filter(m => m.duration > 500).length}
                </Text>
                <Text style={styles.statLabel}>Lentas</Text>
              </View>
              <View style={styles.statItem}>
                <X size={24} color={colors.error} />
                <Text style={styles.statValue}>
                  {metrics.filter(m => !m.success).length}
                </Text>
                <Text style={styles.statLabel}>Erros</Text>
              </View>
            </View>
          </GlassCard>

          {/* Filtros */}
          <GlassCard style={styles.filtersCard}>
            <Text style={styles.sectionTitle}>Filtros</Text>
            <View style={styles.filters}>
              <Pressable
                style={[styles.filterButton, timeRange === 'day' && styles.activeFilter]}
                onPress={() => setTimeRange('day')}
              >
                <Text style={styles.filterText}>Hoje</Text>
              </Pressable>
              <Pressable
                style={[styles.filterButton, timeRange === 'week' && styles.activeFilter]}
                onPress={() => setTimeRange('week')}
              >
                <Text style={styles.filterText}>Semana</Text>
              </Pressable>
              <Pressable
                style={[styles.filterButton, timeRange === 'month' && styles.activeFilter]}
                onPress={() => setTimeRange('month')}
              >
                <Text style={styles.filterText}>Mês</Text>
              </Pressable>
              <Pressable
                style={[styles.filterButton, showSlowOnly && styles.activeFilter]}
                onPress={() => setShowSlowOnly(!showSlowOnly)}
              >
                <Text style={styles.filterText}>Lentas</Text>
              </Pressable>
              <Pressable
                style={[styles.filterButton, showErrorsOnly && styles.activeFilter]}
                onPress={() => setShowErrorsOnly(!showErrorsOnly)}
              >
                <Text style={styles.filterText}>Erros</Text>
              </Pressable>
            </View>
          </GlassCard>

          {/* Lista de métricas */}
          <Text style={styles.sectionTitle}>Métricas Recentes</Text>
          <FlatList
            data={getFilteredMetrics().slice(0, 50)}
            renderItem={renderMetricItem}
            keyExtractor={item => item.id}
            style={styles.metricsList}
            ListEmptyComponent={
              <Text style={styles.emptyText}>Nenhuma métrica encontrada.</Text>
            }
            scrollEnabled={false}
          />
        </ScrollView>
      )}
    </View>
  );
};

// Estilos
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.text,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  summaryCard: {
    padding: 16,
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  filtersCard: {
    padding: 16,
    marginBottom: 16,
  },
  filters: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.card,
    marginRight: 8,
    marginBottom: 8,
  },
  activeFilter: {
    backgroundColor: colors.primary,
  },
  filterText: {
    color: colors.text,
    fontSize: 14,
  },
  metricsList: {
    marginTop: 8,
  },
  metricCard: {
    padding: 12,
    marginBottom: 8,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  metricOperation: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  metricTimestamp: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  metricDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metricTable: {
    fontSize: 14,
    color: colors.text,
  },
  metricDuration: {
    fontSize: 14,
    color: colors.success,
  },
  slowQuery: {
    color: colors.warning,
  },
  cacheIndicator: {
    backgroundColor: colors.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  cacheText: {
    fontSize: 10,
    color: colors.white,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: colors.error,
    marginLeft: 4,
  },
  emptyText: {
    textAlign: 'center',
    color: colors.textSecondary,
    marginTop: 16,
  },
});

export default PerformanceDashboard;
