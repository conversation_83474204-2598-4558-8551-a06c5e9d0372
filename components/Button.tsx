import React from "react";
import { Text, StyleSheet, Pressable, ActivityIndicator, ViewStyle, TextStyle, View } from "react-native";
import { theme } from "@/constants/theme";
import { colors } from "@/constants/colors";
import { LinearGradient } from "expo-linear-gradient";
import type { LucideIcon } from "lucide-react-native";
import { Animated } from "react-native";

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "success" | "error" | "warning" | "info" | "glass";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ComponentType<any>;
  iconPosition?: "left" | "right";
  elevation?: "none" | "low" | "medium" | "high";
  rounded?: boolean;
  animatePress?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
  iconPosition = "left",
  elevation = "medium",
  rounded = false,
  animatePress = true,
}) => {
  // Animação para o efeito de pressionar
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const getGradientColors = () => {
    if (disabled) return ["#D1D5DB", "#9CA3AF"];

    switch (variant) {
      case "primary":
        return colors.primaryGradient;
      case "secondary":
        return colors.secondaryGradient;
      case "success":
        return colors.successGradient;
      case "error":
        return colors.errorGradient;
      case "warning":
        return colors.warningGradient || colors.secondaryGradient;
      case "info":
        return colors.infoGradient || colors.primaryGradient;
      case "glass":
        return ["rgba(255, 255, 255, 0.8)", "rgba(255, 255, 255, 0.6)"];
      default:
        return ["transparent", "transparent"];
    }
  };

  const getContainerStyle = () => {
    let containerStyle = [styles.container];

    // Size
    if (size === "small") {
      containerStyle.push(styles.smallContainer);
    } else if (size === "large") {
      containerStyle.push(styles.largeContainer);
    } else {
      containerStyle.push(styles.mediumContainer);
    }

    // Width
    if (fullWidth) {
      containerStyle.push(styles.fullWidth);
    }

    // Rounded corners
    if (rounded) {
      containerStyle.push(styles.rounded);
    }

    // Elevation (shadow)
    if (elevation === "none") {
      containerStyle.push(styles.elevationNone);
    } else if (elevation === "low") {
      containerStyle.push(styles.elevationLow);
    } else if (elevation === "high") {
      containerStyle.push(styles.elevationHigh);
    } else {
      containerStyle.push(styles.elevationMedium);
    }

    // Variant specific styles
    if (variant === "outline") {
      containerStyle.push(styles.outlineContainer);
      if (disabled) {
        containerStyle.push(styles.disabledOutlineContainer);
      }
    } else if (variant === "glass") {
      containerStyle.push(styles.glassContainer);
    }

    return containerStyle;
  };

  const getTextStyle = () => {
    let textStyleArray = [styles.text];

    // Variant
    if (variant === "outline") {
      textStyleArray.push(styles.outlineText);
      if (disabled) {
        textStyleArray.push(styles.disabledOutlineText);
      }
    } else if (variant === "glass") {
      textStyleArray.push(styles.glassText);
    } else {
      textStyleArray.push(styles.gradientText);
    }

    // Size
    if (size === "small") {
      textStyleArray.push(styles.smallText);
    } else if (size === "large") {
      textStyleArray.push(styles.largeText);
    } else {
      textStyleArray.push(styles.mediumText);
    }

    return textStyleArray;
  };

  const getIconColor = () => {
    if (disabled) {
      return variant === "outline" ? colors.textLight : "#fff";
    }

    switch (variant) {
      case "outline":
        return colors.primary;
      case "glass":
        return colors.text;
      default:
        return "#fff";
    }
  };

  // Funções para animação de pressionar
  const handlePressIn = () => {
    if (animatePress && !disabled) {
      Animated.spring(scaleAnim, {
        toValue: 0.96,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animatePress && !disabled) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }).start();
    }
  };

  if (variant === "outline" || variant === "glass") {
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <Pressable
          style={[
            ...getContainerStyle(),
            style,
          ]}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || loading}
        >
          {loading ? (
            <ActivityIndicator
              color={disabled ? colors.textLight : colors.primary}
              size="small"
            />
          ) : (
            <View style={[
              styles.buttonContent,
              iconPosition === "right" && styles.buttonContentReverse
            ]}>
              {icon && iconPosition === "left" && (
                <View style={styles.iconContainer}>
                  {React.createElement(icon, {
                    size: size === 'small' ? theme.sizes.icon.xs : size === 'large' ? theme.sizes.icon.md : theme.sizes.icon.sm,
                    color: getIconColor(),
                    strokeWidth: 2
                  })}
                </View>
              )}
              <Text style={[...getTextStyle(), icon && styles.textWithIcon, textStyle]}>{title}</Text>
              {icon && iconPosition === "right" && (
                <View style={styles.iconContainer}>
                  {React.createElement(icon, {
                    size: size === 'small' ? theme.sizes.icon.xs : size === 'large' ? theme.sizes.icon.md : theme.sizes.icon.sm,
                    color: getIconColor(),
                    strokeWidth: 2
                  })}
                </View>
              )}
            </View>
          )}
        </Pressable>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <Pressable
        style={[
          ...getContainerStyle(),
          style,
        ]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
      >
        <LinearGradient
          colors={getGradientColors()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <View style={[
              styles.buttonContent,
              iconPosition === "right" && styles.buttonContentReverse
            ]}>
              {icon && iconPosition === "left" && (
                <View style={styles.iconContainer}>
                  {React.createElement(icon, {
                    size: size === 'small' ? theme.sizes.icon.xs : size === 'large' ? theme.sizes.icon.md : theme.sizes.icon.sm,
                    color: getIconColor(),
                    strokeWidth: 2
                  })}
                </View>
              )}
              <Text style={[...getTextStyle(), icon && styles.textWithIcon, textStyle]}>{title}</Text>
              {icon && iconPosition === "right" && (
                <View style={styles.iconContainer}>
                  {React.createElement(icon, {
                    size: size === 'small' ? theme.sizes.icon.xs : size === 'large' ? theme.sizes.icon.md : theme.sizes.icon.sm,
                    color: getIconColor(),
                    strokeWidth: 2
                  })}
                </View>
              )}
            </View>
          )}
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: "hidden",
  },
  gradient: {
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  },
  outlineContainer: {
    backgroundColor: "transparent",
    borderWidth: 1.5,
    borderColor: colors.primary,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  glassContainer: {
    backgroundColor: colors.glass,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  disabledOutlineContainer: {
    borderColor: colors.textLight,
  },
  smallContainer: {
    height: theme.sizes.button.height.sm,
  },
  mediumContainer: {
    height: theme.sizes.button.height.md,
  },
  largeContainer: {
    height: theme.sizes.button.height.lg,
  },
  fullWidth: {
    width: "100%",
  },
  rounded: {
    borderRadius: 9999,
  },
  elevationNone: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  elevationLow: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  elevationMedium: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  elevationHigh: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  text: {
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
  },
  gradientText: {
    color: colors.white,
  },
  outlineText: {
    color: colors.primary,
  },
  glassText: {
    color: colors.text,
  },
  disabledOutlineText: {
    color: colors.textLight,
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  buttonContentReverse: {
    flexDirection: "row-reverse",
  },
  textWithIcon: {
    marginLeft: 8,
  },
  iconContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

