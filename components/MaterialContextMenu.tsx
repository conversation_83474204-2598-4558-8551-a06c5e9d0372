import React, { useState } from 'react';
import { View, Text, StyleSheet, Pressable, Modal } from 'react-native';
import { colors } from '@/constants/colors';
import { MoreVertical, Edit, Trash2, Copy, Share2 } from 'lucide-react-native';
import { StudyGroupMaterial } from '@/types';

interface MaterialContextMenuProps {
  material: StudyGroupMaterial;
  isOwner: boolean;
  isAdmin: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onShare: () => void;
}

export const MaterialContextMenu: React.FC<MaterialContextMenuProps> = ({
  material,
  isOwner,
  isAdmin,
  onEdit,
  onDelete,
  onShare,
}) => {
  const [menuVisible, setMenuVisible] = useState(false);

  const toggleMenu = () => {
    setMenuVisible(!menuVisible);
  };

  const handleEdit = () => {
    setMenuVisible(false);
    onEdit();
  };

  const handleDelete = () => {
    setMenuVisible(false);
    onDelete();
  };

  const handleShare = () => {
    setMenuVisible(false);
    onShare();
  };

  return (
    <View>
      <Pressable
        style={styles.menuButton}
        onPress={toggleMenu}
        hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
      >
        <MoreVertical size={20} color={colors.textMedium} />
      </Pressable>

      <Modal
        visible={menuVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setMenuVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setMenuVisible(false)}
        >
          <View style={styles.menuContainer}>
            <Text style={styles.menuTitle}>{material.title}</Text>
            
            <Pressable style={styles.menuItem} onPress={handleShare}>
              <Share2 size={20} color={colors.textDark} />
              <Text style={styles.menuItemText}>Compartilhar</Text>
            </Pressable>
            
            {isOwner && (
              <Pressable style={styles.menuItem} onPress={handleEdit}>
                <Edit size={20} color={colors.textDark} />
                <Text style={styles.menuItemText}>Editar</Text>
              </Pressable>
            )}
            
            {(isOwner || isAdmin) && (
              <Pressable style={styles.menuItem} onPress={handleDelete}>
                <Trash2 size={20} color={colors.danger} />
                <Text style={[styles.menuItemText, styles.dangerText]}>Excluir</Text>
              </Pressable>
            )}
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  menuButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    width: '80%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.textDark,
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.textDark,
    marginLeft: 12,
  },
  dangerText: {
    color: colors.danger,
  },
});
