import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, Switch, FlatList, Pressable, Alert, TextInput } from "react-native";
import { colors } from "@/constants/colors";
import { Button } from "@/components/Button";
import { BlockedApp, StudyGroupSettings } from "@/types";
import { Smartphone, Plus, Trash2, Search, XCircle } from "lucide-react-native";
import { useStudyGroupStore } from "@/store/studyGroupStore";

interface AppBlockingSettingsProps {
  groupId: string;
  settings: StudyGroupSettings;
  onSettingsUpdated?: (settings: StudyGroupSettings) => void;
}

// Lista de aplicativos populares que podem ser bloqueados
const POPULAR_APPS: BlockedApp[] = [
  { id: "instagram", name: "Instagram", packageName: "com.instagram.android", icon: "instagram" },
  { id: "whatsapp", name: "WhatsApp", packageName: "com.whatsapp", icon: "whatsapp" },
  { id: "twitter", name: "<PERSON> (Twitter)", packageName: "com.twitter.android", icon: "twitter" },
  { id: "facebook", name: "Facebook", packageName: "com.facebook.katana", icon: "facebook" },
  { id: "tiktok", name: "TikTok", packageName: "com.zhiliaoapp.musically", icon: "tiktok" },
  { id: "youtube", name: "YouTube", packageName: "com.google.android.youtube", icon: "youtube" },
  { id: "netflix", name: "Netflix", packageName: "com.netflix.mediaclient", icon: "netflix" },
  { id: "spotify", name: "Spotify", packageName: "com.spotify.music", icon: "spotify" },
  { id: "telegram", name: "Telegram", packageName: "org.telegram.messenger", icon: "telegram" },
];

export const AppBlockingSettings: React.FC<AppBlockingSettingsProps> = ({
  groupId,
  settings,
  onSettingsUpdated,
}) => {
  const { toggleAppBlocking, updateBlockedApps } = useStudyGroupStore();
  
  const [isEnabled, setIsEnabled] = useState(settings.enableAppBlocking || false);
  const [blockedApps, setBlockedApps] = useState<BlockedApp[]>(settings.blockedApps || []);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddAppModal, setShowAddAppModal] = useState(false);
  const [customAppName, setCustomAppName] = useState("");
  const [customAppPackage, setCustomAppPackage] = useState("");
  
  // Filtrar aplicativos populares que ainda não foram bloqueados
  const filteredApps = POPULAR_APPS.filter(app => 
    !blockedApps.some(blockedApp => blockedApp.id === app.id) &&
    app.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const handleToggleAppBlocking = async (value: boolean) => {
    try {
      setIsEnabled(value);
      const updatedSettings = await toggleAppBlocking(groupId, value);
      
      if (onSettingsUpdated) {
        onSettingsUpdated(updatedSettings);
      }
    } catch (error) {
      console.error('Error toggling app blocking:', error);
      Alert.alert('Erro', 'Não foi possível atualizar as configurações de bloqueio de aplicativos.');
      setIsEnabled(!value); // Reverter o estado em caso de erro
    }
  };
  
  const handleAddApp = async (app: BlockedApp) => {
    try {
      const newBlockedApps = [...blockedApps, app];
      setBlockedApps(newBlockedApps);
      
      const updatedSettings = await updateBlockedApps(groupId, newBlockedApps);
      
      if (onSettingsUpdated) {
        onSettingsUpdated(updatedSettings);
      }
      
      setSearchQuery("");
    } catch (error) {
      console.error('Error adding blocked app:', error);
      Alert.alert('Erro', 'Não foi possível adicionar o aplicativo à lista de bloqueio.');
    }
  };
  
  const handleRemoveApp = async (appId: string) => {
    try {
      const newBlockedApps = blockedApps.filter(app => app.id !== appId);
      setBlockedApps(newBlockedApps);
      
      const updatedSettings = await updateBlockedApps(groupId, newBlockedApps);
      
      if (onSettingsUpdated) {
        onSettingsUpdated(updatedSettings);
      }
    } catch (error) {
      console.error('Error removing blocked app:', error);
      Alert.alert('Erro', 'Não foi possível remover o aplicativo da lista de bloqueio.');
    }
  };
  
  const handleAddCustomApp = async () => {
    if (!customAppName.trim() || !customAppPackage.trim()) {
      Alert.alert('Erro', 'Nome e pacote do aplicativo são obrigatórios.');
      return;
    }
    
    const customApp: BlockedApp = {
      id: `custom_${Date.now()}`,
      name: customAppName.trim(),
      packageName: customAppPackage.trim(),
    };
    
    await handleAddApp(customApp);
    setCustomAppName("");
    setCustomAppPackage("");
    setShowAddAppModal(false);
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Smartphone size={24} color={colors.primary} />
          <Text style={styles.title}>Bloqueio de Aplicativos</Text>
        </View>
        <Switch
          value={isEnabled}
          onValueChange={handleToggleAppBlocking}
          trackColor={{ false: colors.textLight, true: `${colors.primary}80` }}
          thumbColor={isEnabled ? colors.primary : colors.white}
        />
      </View>
      
      <Text style={styles.description}>
        Quando ativado, os aplicativos selecionados serão bloqueados durante as sessões de estudo do grupo.
        Os membros não poderão acessar esses aplicativos até que a sessão de estudo seja finalizada.
      </Text>
      
      {isEnabled && (
        <>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Aplicativos Bloqueados</Text>
            <Button
              title="Adicionar"
              onPress={() => setShowAddAppModal(true)}
              variant="secondary"
              size="small"
              icon={Plus}
            />
          </View>
          
          {blockedApps.length === 0 ? (
            <View style={styles.emptyState}>
              <Smartphone size={40} color={`${colors.primary}50`} />
              <Text style={styles.emptyStateText}>
                Nenhum aplicativo bloqueado. Adicione aplicativos que deseja bloquear durante as sessões de estudo.
              </Text>
            </View>
          ) : (
            <FlatList
              data={blockedApps}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View style={styles.appItem}>
                  <View style={styles.appInfo}>
                    <View style={styles.appIconPlaceholder}>
                      <Smartphone size={20} color={colors.white} />
                    </View>
                    <View style={styles.appDetails}>
                      <Text style={styles.appName}>{item.name}</Text>
                      <Text style={styles.appPackage}>{item.packageName}</Text>
                    </View>
                  </View>
                  <Pressable
                    style={styles.removeButton}
                    onPress={() => handleRemoveApp(item.id)}
                  >
                    <Trash2 size={20} color={colors.danger} />
                  </Pressable>
                </View>
              )}
              style={styles.appList}
            />
          )}
          
          {showAddAppModal && (
            <View style={styles.addAppContainer}>
              <View style={styles.addAppHeader}>
                <Text style={styles.addAppTitle}>Adicionar Aplicativo</Text>
                <Pressable onPress={() => setShowAddAppModal(false)}>
                  <XCircle size={24} color={colors.textMedium} />
                </Pressable>
              </View>
              
              <View style={styles.searchContainer}>
                <Search size={20} color={colors.textMedium} style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Buscar aplicativo..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholderTextColor={colors.textLight}
                />
                {searchQuery ? (
                  <Pressable onPress={() => setSearchQuery("")} style={styles.clearButton}>
                    <XCircle size={16} color={colors.textMedium} />
                  </Pressable>
                ) : null}
              </View>
              
              <Text style={styles.sectionSubtitle}>Aplicativos Populares</Text>
              
              {filteredApps.length > 0 ? (
                <FlatList
                  data={filteredApps}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <Pressable
                      style={styles.popularAppItem}
                      onPress={() => handleAddApp(item)}
                    >
                      <View style={styles.appInfo}>
                        <View style={styles.appIconPlaceholder}>
                          <Smartphone size={20} color={colors.white} />
                        </View>
                        <Text style={styles.appName}>{item.name}</Text>
                      </View>
                      <Plus size={20} color={colors.primary} />
                    </Pressable>
                  )}
                  style={styles.popularAppsList}
                />
              ) : (
                <View style={styles.noResultsContainer}>
                  <Text style={styles.noResultsText}>
                    {searchQuery
                      ? `Nenhum resultado para "${searchQuery}"`
                      : "Todos os aplicativos populares já foram adicionados"}
                  </Text>
                </View>
              )}
              
              <Text style={styles.sectionSubtitle}>Adicionar Aplicativo Personalizado</Text>
              
              <View style={styles.customAppForm}>
                <TextInput
                  style={styles.customAppInput}
                  placeholder="Nome do aplicativo"
                  value={customAppName}
                  onChangeText={setCustomAppName}
                  placeholderTextColor={colors.textLight}
                />
                
                <TextInput
                  style={styles.customAppInput}
                  placeholder="Pacote do aplicativo (ex: com.instagram.android)"
                  value={customAppPackage}
                  onChangeText={setCustomAppPackage}
                  placeholderTextColor={colors.textLight}
                />
                
                <Button
                  title="Adicionar Aplicativo Personalizado"
                  onPress={handleAddCustomApp}
                  variant="primary"
                  size="medium"
                  icon={Plus}
                  style={styles.customAppButton}
                />
              </View>
            </View>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.textDark,
  },
  description: {
    fontSize: 14,
    color: colors.textMedium,
    marginBottom: 16,
    lineHeight: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.textDark,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.textDark,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: colors.textMedium,
    textAlign: "center",
    marginTop: 12,
  },
  appList: {
    maxHeight: 200,
  },
  appItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  appInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  appIconPlaceholder: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  appDetails: {
    flex: 1,
  },
  appName: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.textDark,
  },
  appPackage: {
    fontSize: 12,
    color: colors.textLight,
  },
  removeButton: {
    padding: 8,
  },
  addAppContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  addAppHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  addAppTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: colors.textDark,
  },
  clearButton: {
    padding: 8,
  },
  popularAppsList: {
    maxHeight: 150,
  },
  popularAppItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  noResultsContainer: {
    padding: 16,
    alignItems: "center",
  },
  noResultsText: {
    fontSize: 14,
    color: colors.textMedium,
    textAlign: "center",
  },
  customAppForm: {
    marginTop: 8,
  },
  customAppInput: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 12,
    color: colors.textDark,
  },
  customAppButton: {
    marginTop: 8,
  },
});
