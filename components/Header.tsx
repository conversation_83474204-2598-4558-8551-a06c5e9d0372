import React from "react";
import { View, Text, StyleSheet, Pressable, SafeAreaView, Image } from "react-native";
import { colors } from "@/constants/colors";
import { images } from "@/constants/images";
import { useUserStore } from "@/store/userStore";
import { User, Flame } from "lucide-react-native";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";

interface HeaderProps {
  title?: string;
  rightComponent?: React.ReactNode;
}

export const Header: React.FC<HeaderProps> = ({
  title = "LIA",
  rightComponent,
}) => {
  const { user } = useUserStore();
  const router = useRouter();

  const handleProfilePress = () => {
    router.push("/profile");
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.headerGradient}>
        <View style={styles.container}>
          <View style={styles.titleContainer}>
            {title === "LIA" ? (
              <View style={styles.logoContainer}>
                <Image
                  source={images.logo}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>
            ) : (
              <Text style={styles.title}>{title}</Text>
            )}
          </View>

          <View style={styles.rightContainer}>
            {rightComponent ? (
              rightComponent
            ) : (
              <Pressable
                style={({ pressed }) => [
                  styles.profileButton,
                  pressed && styles.pressed,
                ]}
                onPress={handleProfilePress}
              >
                <GlassCard style={styles.profileButtonInner} gradient gradientColors={["rgba(255,255,255,0.9)", "rgba(255,255,255,0.7)"]}>
                  <View style={styles.streakContainer}>
                    <Flame size={16} color={colors.secondary} />
                    <Text style={styles.streakText}>{user.streak || 0}</Text>
                  </View>
                  <View style={styles.avatar}>
                    <LinearGradient
                      colors={colors.primaryGradient}
                      style={styles.avatarGradient}
                    >
                      <User size={20} color="#fff" />
                    </LinearGradient>
                  </View>
                  <View style={styles.userInfo}>
                    <Text style={styles.userName} numberOfLines={1}>
                      {user.name}
                    </Text>
                    <Text style={styles.userLevel}>Nível {user.level || 1}</Text>
                  </View>
                </GlassCard>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: colors.white,
  },
  headerGradient: {
    width: '100%',
    backgroundColor: colors.white,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.white,
  },
  titleContainer: {
    flex: 1,
  },
  logoContainer: {
    alignSelf: "flex-start",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: 'transparent',
    borderRadius: 12,
    padding: 4,
  },
  logoImage: {
    width: 65,
    height: 40,
  },
  logo: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.white,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
  },
  rightContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconButton: {
    marginRight: 8,
  },
  iconButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  profileButton: {
    borderRadius: 20,
  },
  profileButtonInner: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 6,
    minHeight: 44,
    maxHeight: 60,
  },
  pressed: {
    opacity: 0.8,
    transform: [{ scale: 0.98 }],
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 150, 0, 0.15)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 6,
  },
  streakText: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.secondary,
    marginLeft: 2,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    overflow: "hidden",
  },
  avatarGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  userInfo: {
    marginLeft: 8,
    maxWidth: 100,
  },
  userName: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
  },
  userLevel: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.primary,
  },
});