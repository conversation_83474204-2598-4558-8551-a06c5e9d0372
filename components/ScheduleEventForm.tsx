import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Switch,
  Modal,
  Platform,
  Alert
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO, addHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarEvent } from '@/types';
import { useStudyStore } from '@/store/studyStore';
import { Calendar, Clock, BookOpen, Bell, Repeat, X, AlarmClock, ClipboardList } from 'lucide-react-native';

interface ScheduleEventFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialEvent?: Partial<CalendarEvent>;
  selectedDate?: string;
  scheduleTitle?: string;
}

export const ScheduleEventForm: React.FC<ScheduleEventFormProps> = ({
  visible,
  onClose,
  onSave,
  initialEvent,
  selectedDate,
  scheduleTitle
}) => {
  const { subjects } = useStudyStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(new Date().setHours(new Date().getHours() + 1)));
  const [allDay, setAllDay] = useState(false);
  const [color, setColor] = useState(colors.primary);
  const [subject, setSubject] = useState<string | null>(null);
  const [subjectId, setSubjectId] = useState<string | null>(null);
  const [type, setType] = useState<'study' | 'exam' | 'assignment' | 'meeting' | 'other'>('study');
  const [reminder, setReminder] = useState(true);
  const [reminderTime, setReminderTime] = useState(new Date(new Date().setMinutes(new Date().getMinutes() - 30)));
  const [recurrence, setRecurrence] = useState<'none' | 'daily' | 'weekly' | 'monthly'>('none');

  // Date/time picker states
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  useEffect(() => {
    if (visible) {
      // Initialize form with initial values or defaults
      if (initialEvent) {
        setTitle(initialEvent.title || '');
        setDescription(initialEvent.description || '');
        setStartDate(initialEvent.startDate ? parseISO(initialEvent.startDate) : new Date());
        setEndDate(initialEvent.endDate ? parseISO(initialEvent.endDate) : addHours(new Date(), 1));
        setAllDay(initialEvent.allDay || false);
        setColor(initialEvent.color || colors.primary);
        setSubject(initialEvent.subject || null);
        setSubjectId(initialEvent.subject_id || null);
        setType(initialEvent.type || 'study');
        setReminder(initialEvent.reminder || true);
        setReminderTime(initialEvent.reminderTime ? parseISO(initialEvent.reminderTime) : new Date(new Date().setMinutes(new Date().getMinutes() - 30)));
        setRecurrence(initialEvent.recurrence || 'none');
      } else {
        // Default values for new event
        const defaultDate = selectedDate ? parseISO(selectedDate) : new Date();
        defaultDate.setHours(9, 0, 0, 0); // Default to 9:00 AM

        // Prefill title and description with schedule info if available
        setTitle(subject ? `Estudar ${subject}` : 'Sessão de estudo');
        setDescription(scheduleTitle ? `Sessão de estudo do cronograma: ${scheduleTitle}` : '');

        // Se não houver uma matéria selecionada e houver matérias disponíveis, selecione a primeira
        if (!subject && subjects.length > 0) {
          const firstSubject = subjects[0];
          setSubject(firstSubject.title);
          setSubjectId(firstSubject.id);
          setColor(firstSubject.color);
          setTitle(`Estudar ${firstSubject.title}`);
          setDescription(scheduleTitle ? `Sessão de estudo de ${firstSubject.title} (Cronograma: ${scheduleTitle})` : `Sessão de estudo de ${firstSubject.title}`);
        }
        setStartDate(defaultDate);
        setEndDate(addHours(defaultDate, 1));
        setAllDay(false);
        setColor(colors.primary);
        setSubject(null);
        setSubjectId(null);
        setType('study');
        setReminder(true);
        setReminderTime(new Date(defaultDate.getTime() - 30 * 60000)); // 30 minutes before
        setRecurrence('none');
      }
    }
  }, [visible, initialEvent, selectedDate, scheduleTitle, subject]);

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título do evento é obrigatório.');
      return;
    }

    const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      allDay,
      color,
      subject: subject || undefined,
      subject_id: subjectId || undefined,
      type,
      completed: false,
      reminder,
      reminderTime: reminder ? reminderTime.toISOString() : undefined,
      recurrence,
    };

    onSave(event);
    onClose();
  };

  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      // Preserve the time from the current startDate
      const newDate = new Date(selectedDate);
      newDate.setHours(
        startDate.getHours(),
        startDate.getMinutes(),
        startDate.getSeconds(),
        startDate.getMilliseconds()
      );
      setStartDate(newDate);

      // If end date is before start date, update it
      if (endDate < newDate) {
        setEndDate(addHours(newDate, 1));
      }
    }
  };

  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
    setShowStartTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      // Preserve the date from the current startDate
      const newDate = new Date(startDate);
      newDate.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        selectedTime.getSeconds(),
        selectedTime.getMilliseconds()
      );
      setStartDate(newDate);

      // If end time is before start time on the same day, update it
      if (
        endDate.getDate() === newDate.getDate() &&
        endDate.getMonth() === newDate.getMonth() &&
        endDate.getFullYear() === newDate.getFullYear() &&
        endDate < newDate
      ) {
        setEndDate(addHours(newDate, 1));
      }
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      // Preserve the time from the current endDate
      const newDate = new Date(selectedDate);
      newDate.setHours(
        endDate.getHours(),
        endDate.getMinutes(),
        endDate.getSeconds(),
        endDate.getMilliseconds()
      );

      // If end date is before start date, don't update
      if (newDate < startDate) {
        Alert.alert('Erro', 'A data de término deve ser após a data de início.');
        return;
      }

      setEndDate(newDate);
    }
  };

  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
    setShowEndTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      // Preserve the date from the current endDate
      const newDate = new Date(endDate);
      newDate.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        selectedTime.getSeconds(),
        selectedTime.getMilliseconds()
      );

      // If end time is before start time on the same day, don't update
      if (
        startDate.getDate() === newDate.getDate() &&
        startDate.getMonth() === newDate.getMonth() &&
        startDate.getFullYear() === newDate.getFullYear() &&
        newDate < startDate
      ) {
        Alert.alert('Erro', 'O horário de término deve ser após o horário de início.');
        return;
      }

      setEndDate(newDate);
    }
  };

  const handleReminderTimeChange = (event: any, selectedTime?: Date) => {
    setShowReminderTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      setReminderTime(selectedTime);
    }
  };

  const handleSubjectSelect = (subjectId: string, subjectTitle: string, subjectColor: string) => {
    setSubjectId(subjectId);
    setSubject(subjectTitle);
    setColor(subjectColor);
    setTitle(`Estudar ${subjectTitle}`);
    setDescription(scheduleTitle ? `Sessão de estudo de ${subjectTitle} (Cronograma: ${scheduleTitle})` : `Sessão de estudo de ${subjectTitle}`);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {initialEvent?.id ? 'Editar Evento' : 'Novo Evento do Cronograma'}
            </Text>
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Título</Text>
              <TextInput
                style={styles.input}
                value={title}
                onChangeText={setTitle}
                placeholder="Título do evento"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Descrição do evento"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Matéria</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.subjectsScrollView}
              >
                {subjects.map((s) => (
                  <Pressable
                    key={s.id}
                    style={[
                      styles.subjectItem,
                      { borderColor: s.color },
                      subjectId === s.id && { backgroundColor: `${s.color}20` }
                    ]}
                    onPress={() => handleSubjectSelect(s.id, s.title, s.color)}
                  >
                    <Text style={[
                      styles.subjectItemText,
                      subjectId === s.id && { color: s.color, fontWeight: 'bold' }
                    ]}>
                      {s.title}
                    </Text>
                  </Pressable>
                ))}
              </ScrollView>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tipo</Text>
              <View style={styles.typeContainer}>
                {[
                  { value: 'study', label: 'Estudo', icon: BookOpen },
                  { value: 'exam', label: 'Prova', icon: ClipboardList },
                  { value: 'assignment', label: 'Tarefa', icon: BookOpen },
                  { value: 'meeting', label: 'Reunião', icon: Calendar },
                  { value: 'other', label: 'Outro', icon: Calendar }
                ].map((item) => (
                  <Pressable
                    key={item.value}
                    style={[
                      styles.typeItem,
                      type === item.value && styles.typeItemActive
                    ]}
                    onPress={() => setType(item.value as any)}
                  >
                    <item.icon
                      size={16}
                      color={type === item.value ? colors.white : colors.text}
                    />
                    <Text
                      style={[
                        styles.typeItemText,
                        type === item.value && styles.typeItemTextActive
                      ]}
                    >
                      {item.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Dia inteiro</Text>
                <Switch
                  value={allDay}
                  onValueChange={setAllDay}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={allDay ? colors.primary : colors.white}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Data e hora de início</Text>
              <View style={styles.dateTimeContainer}>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowStartDatePicker(true)}
                >
                  <Calendar size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
                  </Text>
                </Pressable>

                {!allDay && (
                  <Pressable
                    style={styles.timePickerButton}
                    onPress={() => setShowStartTimePicker(true)}
                  >
                    <Clock size={20} color={colors.primary} />
                    <Text style={styles.timePickerButtonText}>
                      {format(startDate, 'HH:mm')}
                    </Text>
                  </Pressable>
                )}
              </View>

              {showStartDatePicker && (
                <DateTimePicker
                  value={startDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleStartDateChange}
                />
              )}

              {showStartTimePicker && (
                <DateTimePicker
                  value={startDate}
                  mode="time"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleStartTimeChange}
                  minuteInterval={5}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Data e hora de término</Text>
              <View style={styles.dateTimeContainer}>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowEndDatePicker(true)}
                >
                  <Calendar size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(endDate, 'dd/MM/yyyy', { locale: ptBR })}
                  </Text>
                </Pressable>

                {!allDay && (
                  <Pressable
                    style={styles.timePickerButton}
                    onPress={() => setShowEndTimePicker(true)}
                  >
                    <Clock size={20} color={colors.primary} />
                    <Text style={styles.timePickerButtonText}>
                      {format(endDate, 'HH:mm')}
                    </Text>
                  </Pressable>
                )}
              </View>

              {showEndDatePicker && (
                <DateTimePicker
                  value={endDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleEndDateChange}
                />
              )}

              {showEndTimePicker && (
                <DateTimePicker
                  value={endDate}
                  mode="time"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleEndTimeChange}
                  minuteInterval={5}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Lembrete</Text>
                <Switch
                  value={reminder}
                  onValueChange={setReminder}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={reminder ? colors.primary : colors.white}
                />
              </View>

              {reminder && (
                <View style={styles.reminderContainer}>
                  <Text style={styles.reminderLabel}>Lembrar</Text>
                  <View style={styles.reminderOptions}>
                    {[
                      { value: 5, label: '5 min' },
                      { value: 15, label: '15 min' },
                      { value: 30, label: '30 min' },
                      { value: 60, label: '1 hora' }
                    ].map((option) => (
                      <Pressable
                        key={option.value}
                        style={[
                          styles.reminderOption,
                          reminderTime &&
                          Math.round((startDate.getTime() - reminderTime.getTime()) / 60000) === option.value &&
                          styles.reminderOptionActive
                        ]}
                        onPress={() => {
                          const newReminderTime = new Date(startDate);
                          newReminderTime.setMinutes(newReminderTime.getMinutes() - option.value);
                          setReminderTime(newReminderTime);
                        }}
                      >
                        <Text style={[
                          styles.reminderOptionText,
                          reminderTime &&
                          Math.round((startDate.getTime() - reminderTime.getTime()) / 60000) === option.value &&
                          styles.reminderOptionTextActive
                        ]}>
                          {option.label}
                        </Text>
                      </Pressable>
                    ))}
                  </View>
                </View>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Repetir</Text>
              <View style={styles.recurrenceContainer}>
                {[
                  { value: 'none', label: 'Não repetir' },
                  { value: 'daily', label: 'Diariamente' },
                  { value: 'weekly', label: 'Semanalmente' },
                  { value: 'monthly', label: 'Mensalmente' }
                ].map((option) => (
                  <Pressable
                    key={option.value}
                    style={[
                      styles.recurrenceOption,
                      recurrence === option.value && styles.recurrenceOptionActive
                    ]}
                    onPress={() => setRecurrence(option.value as any)}
                  >
                    <Text style={[
                      styles.recurrenceOptionText,
                      recurrence === option.value && styles.recurrenceOptionTextActive
                    ]}>
                      {option.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Cor</Text>
              <View style={styles.colorContainer}>
                {[
                  colors.primary,
                  '#F97316',
                  '#10B981',
                  '#8B5CF6',
                  '#EC4899',
                  '#F59E0B',
                  '#EF4444',
                  '#3B82F6'
                ].map((colorOption) => (
                  <Pressable
                    key={colorOption}
                    style={[
                      styles.colorOption,
                      { backgroundColor: colorOption },
                      color === colorOption && styles.colorOptionSelected
                    ]}
                    onPress={() => setColor(colorOption)}
                  />
                ))}
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="outline"
                size="medium"
                style={styles.cancelButton}
              />
              <Button
                title="Salvar"
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={styles.saveButton}
                loading={false}
              />
            </View>
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
    zIndex: 9999,
  },
  formContainer: {
    width: '100%',
    maxHeight: '90%',
    backgroundColor: colors.background,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  scrollView: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  subjectsScrollView: {
    flexGrow: 0,
    marginBottom: 8,
  },
  subjectItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  subjectItemText: {
    fontSize: 14,
    color: colors.text,
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  typeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: 8,
    marginBottom: 8,
  },
  typeItemActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeItemText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: 4,
  },
  typeItemTextActive: {
    color: colors.white,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  datePickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    marginRight: 8,
  },
  datePickerButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.text,
  },
  timePickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
  },
  timePickerButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.text,
  },
  reminderContainer: {
    marginTop: 8,
  },
  reminderLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
  reminderOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  reminderOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: 8,
    marginBottom: 8,
  },
  reminderOptionActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  reminderOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  reminderOptionTextActive: {
    color: colors.white,
  },
  recurrenceContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  recurrenceOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: 8,
    marginBottom: 8,
  },
  recurrenceOptionActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  recurrenceOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  recurrenceOptionTextActive: {
    color: colors.white,
  },
  colorContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
    marginBottom: 8,
  },
  colorOptionSelected: {
    borderWidth: 3,
    borderColor: colors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
  },
});
