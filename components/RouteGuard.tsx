import React, { useEffect, useState, useRef, useCallback, memo } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { colors } from '@/constants/colors';
import { getAuthenticatedUser, checkResourcePermission, checkGroupMembership, showPermissionError } from '@/middleware/authMiddleware';
import { supabase } from '@/lib/supabase';
import { measureFunction } from '@/services/performanceMonitor';
import { GlassCard } from './GlassCard';
import { Lock, AlertTriangle, ShieldAlert } from 'lucide-react-native';

interface RouteGuardProps {
  children: React.ReactNode;
  resourceId?: string;
  tableName?: string;
  groupId?: string;
  requireAdmin?: boolean;
  fallback?: React.ReactNode;
  cachePermission?: boolean;
  redirectTo?: string;
  showErrorMessage?: boolean;
  errorMessage?: string;
  onPermissionDenied?: () => void;
  onPermissionGranted?: () => void;
}

// Cache de permissões para evitar verificações repetidas
const permissionCache: Record<string, {
  hasPermission: boolean;
  timestamp: number;
  expiresAt: number;
}> = {};

// Tempo de expiração do cache de permissões (5 minutos)
const PERMISSION_CACHE_TIME = 5 * 60 * 1000;

/**
 * Componente otimizado para proteger rotas e recursos, garantindo que apenas usuários autorizados possam acessar
 * Implementa cache de permissões e verificações otimizadas para alta escala
 *
 * @param children Conteúdo a ser renderizado quando a autorização for bem-sucedida
 * @param resourceId ID do recurso a ser verificado (opcional)
 * @param tableName Nome da tabela do recurso (obrigatório se resourceId for fornecido)
 * @param groupId ID do grupo a ser verificado (opcional)
 * @param requireAdmin Se true, verifica se o usuário é administrador do grupo (opcional, padrão: false)
 * @param fallback Conteúdo a ser renderizado enquanto verifica a autorização (opcional)
 * @param cachePermission Se true, armazena o resultado da verificação em cache (opcional, padrão: true)
 * @param redirectTo Rota para redirecionar em caso de acesso negado (opcional, padrão: volta para a tela anterior)
 * @param showErrorMessage Se true, exibe uma mensagem de erro em caso de acesso negado (opcional, padrão: true)
 * @param errorMessage Mensagem de erro personalizada (opcional)
 * @param onPermissionDenied Função a ser chamada quando o acesso for negado (opcional)
 * @param onPermissionGranted Função a ser chamada quando o acesso for permitido (opcional)
 */
export const RouteGuard: React.FC<RouteGuardProps> = memo(({
  children,
  resourceId,
  tableName,
  groupId,
  requireAdmin = false,
  fallback,
  cachePermission = true,
  redirectTo,
  showErrorMessage = true,
  errorMessage,
  onPermissionDenied,
  onPermissionGranted,
}) => {
  const { user, loading } = useAuthStore();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const checkTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Gerar uma chave única para o cache de permissões
  const getCacheKey = useCallback(() => {
    if (!user) return '';

    const parts = [
      user.id,
      resourceId || '',
      tableName || '',
      groupId || '',
      requireAdmin ? 'admin' : 'member',
    ];

    return parts.join('_');
  }, [user, resourceId, tableName, groupId, requireAdmin]);

  // Verificar se o usuário está realmente autenticado
  const verifyAuthentication = useCallback(async () => {
    if (!user || !user.id) {
      console.warn('Usuário não autenticado ou sem ID');
      return false;
    }

    try {
      // Verificar se a sessão ainda é válida
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Erro ao verificar sessão:', error);
        return false;
      }

      if (!session || !session.user || session.user.id !== user.id) {
        console.warn('Sessão inválida ou usuário não corresponde');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro na verificação de autenticação:', error);
      return false;
    }
  }, [user]);

  // Verificar permissão com cache
  const checkPermissionWithCache = useCallback(async () => {
    if (!user) return false;

    // Primeiro verificar se o usuário está realmente autenticado
    const isAuthenticated = await verifyAuthentication();
    if (!isAuthenticated) {
      setError('Sessão expirada. Faça login novamente.');
      return false;
    }

    const cacheKey = getCacheKey();

    // Verificar se a permissão está em cache e não expirou
    if (cachePermission && cacheKey && permissionCache[cacheKey]) {
      const cachedPermission = permissionCache[cacheKey];
      const now = Date.now();

      if (now < cachedPermission.expiresAt) {
        return cachedPermission.hasPermission;
      }
    }

    // Verificar permissão para recurso específico
    if (resourceId && tableName) {
      try {
        const hasPermission = await measureFunction(
          'check_resource_permission',
          async () => checkResourcePermission(resourceId, tableName, user.id),
          { resourceId, tableName }
        );

        // Armazenar resultado em cache
        if (cachePermission && cacheKey) {
          const now = Date.now();
          permissionCache[cacheKey] = {
            hasPermission,
            timestamp: now,
            expiresAt: now + PERMISSION_CACHE_TIME,
          };
        }

        return hasPermission;
      } catch (err) {
        console.error('Erro ao verificar permissão:', err);
        setError('Erro ao verificar permissão. Tente novamente.');
        return false;
      }
    }

    // Verificar associação a grupo
    if (groupId) {
      try {
        const { isMember, isAdmin } = await measureFunction(
          'check_group_membership',
          async () => checkGroupMembership(groupId, user.id),
          { groupId }
        );

        // Se requer admin, verificar se o usuário é admin
        if (requireAdmin && !isAdmin) {
          setError('Apenas administradores podem acessar esta área.');
          return false;
        }

        // Se não é membro, negar acesso
        if (!isMember) {
          setError('Você não é membro deste grupo.');
          return false;
        }

        // Armazenar resultado em cache
        if (cachePermission && cacheKey) {
          const now = Date.now();
          permissionCache[cacheKey] = {
            hasPermission: true,
            timestamp: now,
            expiresAt: now + PERMISSION_CACHE_TIME,
          };
        }

        return true;
      } catch (err) {
        console.error('Erro ao verificar associação ao grupo:', err);
        setError('Erro ao verificar associação ao grupo. Tente novamente.');
        return false;
      }
    }

    // Se não houver recursos específicos para verificar, o usuário está autorizado
    return true;
  }, [user, resourceId, tableName, groupId, requireAdmin, cachePermission, getCacheKey]);

  // Verificar autorização
  const checkAuthorization = useCallback(async () => {
    setIsChecking(true);
    setError(null);

    // Clear any existing timeout
    if (checkTimeoutRef.current) {
      clearTimeout(checkTimeoutRef.current);
    }

    // Set a timeout for the permission check to prevent hanging
    checkTimeoutRef.current = setTimeout(() => {
      console.warn('Permission check timed out');
      setError('Verificação de permissão expirou. Tente novamente.');
      setIsChecking(false);
      setIsAuthorized(false);
    }, 10000); // 10 second timeout

    try {
      const hasPermission = await Promise.race([
        checkPermissionWithCache(),
        new Promise<boolean>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 8000)
        )
      ]);

      // Clear timeout on successful completion
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
        checkTimeoutRef.current = null;
      }

      setIsAuthorized(hasPermission);

      if (hasPermission && onPermissionGranted) {
        onPermissionGranted();
      } else if (!hasPermission && onPermissionDenied) {
        onPermissionDenied();
      }
    } catch (err: any) {
      // Clear timeout on error
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
        checkTimeoutRef.current = null;
      }

      console.error('Erro na verificação de autorização:', err);

      if (err.message === 'Timeout') {
        setError('Verificação de permissão expirou. Verifique sua conexão.');
      } else {
        setError('Erro ao verificar permissões. Tente novamente.');
      }

      setIsAuthorized(false);

      if (onPermissionDenied) {
        onPermissionDenied();
      }
    } finally {
      setIsChecking(false);
    }
  }, [checkPermissionWithCache, onPermissionGranted, onPermissionDenied]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (checkTimeoutRef.current) {
        clearTimeout(checkTimeoutRef.current);
      }
    };
  }, []);

  // Verificar autorização quando necessário
  useEffect(() => {
    if (loading) return;

    if (!user) {
      setIsAuthorized(false);
      setIsChecking(false);
      setError('Usuário não autenticado.');
      return;
    }

    checkAuthorization();
  }, [user, loading, checkAuthorization]);

  // Redirecionar em caso de acesso negado
  useEffect(() => {
    if (!isChecking && !isAuthorized && !loading) {
      if (showErrorMessage && error) {
        showPermissionError(errorMessage || error);
      }

      if (redirectTo) {
        router.replace(redirectTo);
      } else {
        // Delay to allow error message to show
        setTimeout(() => {
          router.back();
        }, 1500);
      }
    }
  }, [isChecking, isAuthorized, loading, error, showErrorMessage, errorMessage, redirectTo, router]);

  // Renderizar loading enquanto verifica
  if (loading || isChecking) {
    return fallback || (
      <View style={styles.container}>
        <GlassCard style={styles.loadingCard}>
          <View style={styles.loadingContent}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Verificando permissões...</Text>
            <Text style={styles.loadingSubtext}>
              Aguarde enquanto validamos seu acesso
            </Text>
          </View>
        </GlassCard>
      </View>
    );
  }

  // Renderizar erro se não autorizado
  if (!isAuthorized) {
    return (
      <View style={styles.container}>
        <GlassCard style={styles.errorCard}>
          <View style={styles.errorContent}>
            <View style={styles.errorIconContainer}>
              {error?.includes('admin') ? (
                <ShieldAlert size={48} color={colors.error} />
              ) : error?.includes('membro') ? (
                <Lock size={48} color={colors.error} />
              ) : (
                <AlertTriangle size={48} color={colors.error} />
              )}
            </View>
            <Text style={styles.errorTitle}>Acesso Restrito</Text>
            <Text style={styles.errorMessage}>
              {error || 'Você não tem permissão para acessar este conteúdo.'}
            </Text>
          </View>
        </GlassCard>
      </View>
    );
  }

  // Renderizar conteúdo autorizado
  return <>{children}</>;
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.background,
  },
  loadingCard: {
    width: '100%',
    maxWidth: 320,
    padding: 32,
  },
  loadingContent: {
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textDark,
    marginTop: 16,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: 14,
    color: colors.textMedium,
    marginTop: 8,
    textAlign: 'center',
  },
  errorCard: {
    width: '100%',
    maxWidth: 320,
    padding: 32,
  },
  errorContent: {
    alignItems: 'center',
  },
  errorIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: `${colors.error}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textDark,
    marginBottom: 12,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default RouteGuard;
