import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Pressable, Modal } from 'react-native';
import { colors } from '@/constants/colors';
import { getPerformanceMetrics, getPerformanceLogs } from '@/services/performanceMonitor';
import { GlassCard } from './GlassCard';
import { X, BarChart2 } from 'lucide-react-native';

interface PerformanceStatsProps {
  visible: boolean;
  onClose: () => void;
}

export const PerformanceStats: React.FC<PerformanceStatsProps> = ({ visible, onClose }) => {
  const [metrics, setMetrics] = useState<any>(null);
  const [logs, setLogs] = useState<any[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (visible) {
      loadData();
    }
  }, [visible, refreshKey]);

  const loadData = async () => {
    try {
      const performanceMetrics = await getPerformanceMetrics();
      setMetrics(performanceMetrics);

      const performanceLogs = getPerformanceLogs();
      // Ordenar logs por duração (mais lentos primeiro)
      const sortedLogs = [...performanceLogs].sort((a, b) => b.duration - a.duration);
      // Limitar a 20 logs para não sobrecarregar a UI
      setLogs(sortedLogs.slice(0, 20));
    } catch (error) {
      console.error('Erro ao carregar métricas de desempenho:', error);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <GlassCard style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <View style={styles.modalTitleContainer}>
              <BarChart2 size={24} color={colors.primary} />
              <Text style={styles.modalTitle}>Estatísticas de Desempenho</Text>
            </View>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <View style={styles.content}>
            {metrics ? (
              <>
                <View style={styles.metricsContainer}>
                  <View style={styles.metricCard}>
                    <Text style={styles.metricLabel}>Tempo Médio</Text>
                    <Text style={styles.metricValue}>{metrics.averageLoadTime.toFixed(2)} ms</Text>
                  </View>
                  
                  <View style={styles.metricCard}>
                    <Text style={styles.metricLabel}>Operação Mais Lenta</Text>
                    <Text style={styles.metricValue}>{metrics.slowestOperation.operation}</Text>
                    <Text style={styles.metricSubvalue}>{metrics.slowestOperation.duration.toFixed(2)} ms</Text>
                  </View>
                  
                  <View style={styles.metricCard}>
                    <Text style={styles.metricLabel}>Operação Mais Rápida</Text>
                    <Text style={styles.metricValue}>{metrics.fastestOperation.operation}</Text>
                    <Text style={styles.metricSubvalue}>{metrics.fastestOperation.duration.toFixed(2)} ms</Text>
                  </View>
                  
                  <View style={styles.metricCard}>
                    <Text style={styles.metricLabel}>Total de Operações</Text>
                    <Text style={styles.metricValue}>{metrics.totalOperations}</Text>
                  </View>
                </View>

                <Text style={styles.sectionTitle}>Operações Mais Lentas</Text>
                
                {logs.length > 0 ? (
                  <View style={styles.logsContainer}>
                    {logs.map((log, index) => (
                      <View key={`${log.operation}_${index}`} style={styles.logItem}>
                        <Text style={styles.logOperation}>{log.operation}</Text>
                        <Text style={styles.logDuration}>{log.duration.toFixed(2)} ms</Text>
                      </View>
                    ))}
                  </View>
                ) : (
                  <Text style={styles.emptyText}>Nenhuma operação registrada</Text>
                )}
              </>
            ) : (
              <Text style={styles.loadingText}>Carregando métricas...</Text>
            )}

            <Pressable style={styles.refreshButton} onPress={handleRefresh}>
              <Text style={styles.refreshButtonText}>Atualizar Métricas</Text>
            </Pressable>
          </View>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: `${colors.border}50`,
  },
  modalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 8,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  metricCard: {
    width: '48%',
    backgroundColor: `${colors.primary}10`,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
  },
  metricLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  metricSubvalue: {
    fontSize: 14,
    color: colors.primary,
    marginTop: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  logsContainer: {
    marginBottom: 20,
  },
  logItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: `${colors.border}20`,
  },
  logOperation: {
    fontSize: 14,
    color: colors.text,
    flex: 1,
  },
  logDuration: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
    marginLeft: 8,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
    marginVertical: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
    marginVertical: 40,
  },
  refreshButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  refreshButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
});
