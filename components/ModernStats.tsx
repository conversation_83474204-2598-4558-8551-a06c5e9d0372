import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet, Animated, Dimensions } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import * as Icons from "lucide-react-native";

const { width } = Dimensions.get("window");

interface StatItemProps {
  label: string;
  value: string | number;
  icon?: keyof typeof Icons;
  gradientColors?: string[];
  change?: {
    value: number;
    type: "increase" | "decrease";
  };
  animated?: boolean;
  delay?: number;
}

interface ModernStatsProps {
  stats: StatItemProps[];
  title?: string;
  subtitle?: string;
  layout?: "grid" | "horizontal" | "vertical";
  animated?: boolean;
  style?: any;
}

const StatItem: React.FC<StatItemProps & { index: number }> = ({
  label,
  value,
  icon,
  gradientColors = colors.primaryGradient,
  change,
  animated = true,
  delay = 0,
  index,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const countAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      const animationDelay = delay + (index * 100);
      
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: theme.animation.timing.normal,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: theme.animation.timing.normal,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            ...theme.animation.easing.spring,
            useNativeDriver: true,
          }),
          Animated.timing(countAnim, {
            toValue: 1,
            duration: theme.animation.timing.slow,
            useNativeDriver: false,
          }),
        ]).start();
      }, animationDelay);
    } else {
      fadeAnim.setValue(1);
      slideAnim.setValue(0);
      scaleAnim.setValue(1);
      countAnim.setValue(1);
    }
  }, [animated, delay, index]);

  const IconComponent = icon ? Icons[icon] : null;

  const animatedValue = countAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, typeof value === 'number' ? value : 0],
  });

  return (
    <Animated.View
      style={[
        styles.statItem,
        {
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        },
      ]}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.statGradient}
      >
        <View style={styles.statContent}>
          {IconComponent && (
            <View style={styles.statIconContainer}>
              <IconComponent
                size={theme.sizes.icon.xs}
                color="rgba(255, 255, 255, 0.9)"
              />
            </View>
          )}
          
          <View style={styles.statTextContainer}>
            <Text style={styles.statValue}>
              {typeof value === 'number' ? (
                <Animated.Text>
                  {animatedValue}
                </Animated.Text>
              ) : (
                value
              )}
            </Text>
            
            <Text style={styles.statLabel} numberOfLines={1}>
              {label}
            </Text>
            
            {change && (
              <View style={styles.changeContainer}>
                <Icons.TrendingUp 
                  size={12} 
                  color={change.type === "increase" ? colors.successLight : colors.errorLight} 
                />
                <Text 
                  style={[
                    styles.changeText,
                    { color: change.type === "increase" ? colors.successLight : colors.errorLight }
                  ]}
                >
                  {change.value > 0 ? '+' : ''}{change.value}%
                </Text>
              </View>
            )}
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

export const ModernStats: React.FC<ModernStatsProps> = ({
  stats,
  title,
  subtitle,
  layout = "grid",
  animated = true,
  style,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-20)).current;

  useEffect(() => {
    if (animated) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: theme.animation.timing.normal,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: theme.animation.timing.normal,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(1);
      slideAnim.setValue(0);
    }
  }, [animated]);

  const getContainerStyle = () => {
    switch (layout) {
      case "horizontal":
        return styles.horizontalContainer;
      case "vertical":
        return styles.verticalContainer;
      default:
        return styles.gridContainer;
    }
  };

  return (
    <View style={[styles.container, style]}>
      {(title || subtitle) && (
        <Animated.View
          style={[
            styles.headerContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {title && <Text style={styles.title}>{title}</Text>}
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </Animated.View>
      )}
      
      <View style={getContainerStyle()}>
        {stats.map((stat, index) => (
          <StatItem
            key={`${stat.label}-${index}`}
            {...stat}
            index={index}
            animated={animated}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  headerContainer: {
    marginBottom: 12,
    paddingHorizontal: theme.spacing.md,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.textDark,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textMedium,
  },
  gridContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: theme.spacing.md,
    gap: 10,
  },
  horizontalContainer: {
    flexDirection: "row",
    paddingHorizontal: theme.spacing.md,
    gap: 10,
  },
  verticalContainer: {
    paddingHorizontal: theme.spacing.md,
    gap: 10,
  },
  statItem: {
    flex: 1,
    minWidth: (width - 32 - 10) / 2 - 5,
    maxWidth: (width - 32 - 10) / 2,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#3399FF",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statGradient: {
    width: "100%",
    height: "100%",
  },
  statContent: {
    padding: 14,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 80,
    maxHeight: 95,
  },
  statIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 2,
  },
  statTextContainer: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 16,
    fontWeight: "700",
    color: colors.white,
    marginBottom: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  statLabel: {
    fontSize: 11,
    fontWeight: "600",
    color: "rgba(255, 255, 255, 0.9)",
    textAlign: "center",
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  changeContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: theme.spacing.xxs,
    gap: 2,
  },
  changeText: {
    fontSize: 10,
    fontWeight: "600",
  },
});

export default ModernStats;
