import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  Pressable,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { X, MessageSquare, Bo<PERSON>, User } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { Button } from './Button';
import { generateStudyConversation } from '@/services/openai';
import { hasApiKey } from '@/services/openai';
import { APIKeyModal } from './APIKeyModal';
import { LinearGradient } from 'expo-linear-gradient';

interface FlashcardConversationModalProps {
  visible: boolean;
  onClose: () => void;
  flashcard: {
    question: string;
    answer: string;
  };
}

export const FlashcardConversationModal: React.FC<FlashcardConversationModalProps> = ({
  visible,
  onClose,
  flashcard,
}) => {
  const [conversation, setConversation] = useState<{ role: string; content: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [apiKeyConfigured, setApiKeyConfigured] = useState(false);

  useEffect(() => {
    if (visible) {
      checkApiKey();
      if (flashcard) {
        generateConversation();
      }
    } else {
      setConversation([]);
    }
  }, [visible, flashcard]);

  const checkApiKey = async () => {
    const hasKey = await hasApiKey();
    setApiKeyConfigured(hasKey);
  };

  const generateConversation = async () => {
    if (!flashcard) return;

    const hasKey = await hasApiKey();
    if (!hasKey) {
      setShowApiKeyModal(true);
      return;
    }

    setIsLoading(true);
    try {
      const generatedConversation = await generateStudyConversation(flashcard);
      if (generatedConversation && generatedConversation.length > 0) {
        setConversation(generatedConversation);
      } else {
        // Fallback to a simple conversation if generation fails
        setConversation([
          { role: 'student', content: `Pode me explicar melhor sobre "${flashcard.question}"?` },
          { role: 'tutor', content: flashcard.answer },
          { role: 'student', content: 'Obrigado pela explicação!' },
          { role: 'tutor', content: 'De nada! Alguma outra dúvida sobre este tema?' },
        ]);
      }
    } catch (error) {
      console.error('Error generating conversation:', error);
      Alert.alert(
        'Erro',
        'Não foi possível gerar a conversa. Por favor, verifique sua conexão e tente novamente.'
      );
      // Set fallback conversation
      setConversation([
        { role: 'student', content: `Pode me explicar melhor sobre "${flashcard.question}"?` },
        { role: 'tutor', content: flashcard.answer },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApiKeySuccess = () => {
    setApiKeyConfigured(true);
    setShowApiKeyModal(false);
    generateConversation();
  };

  return (
    <>
      <Modal
        visible={visible}
        transparent
        animationType="slide"
        onRequestClose={onClose}
      >
        <View style={styles.modalOverlay}>
          <GlassCard style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <View style={styles.headerContent}>
                <MessageSquare size={24} color={colors.primary} style={styles.headerIcon} />
                <Text style={styles.modalTitle}>Conversa de Estudo</Text>
              </View>
              <Pressable style={styles.closeButton} onPress={onClose}>
                <X size={24} color={colors.text} />
              </Pressable>
            </View>

            <View style={styles.questionContainer}>
              <Text style={styles.questionLabel}>Flashcard:</Text>
              <Text style={styles.questionText}>{flashcard?.question}</Text>
            </View>

            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Gerando conversa de estudo...</Text>
              </View>
            ) : (
              <ScrollView style={styles.conversationContainer}>
                {conversation.map((message, index) => (
                  <View
                    key={index}
                    style={[
                      styles.messageContainer,
                      message.role === 'student' ? styles.studentMessage : styles.tutorMessage,
                    ]}
                  >
                    <View style={styles.avatarContainer}>
                      <LinearGradient
                        colors={
                          message.role === 'student'
                            ? ['#64748B', '#94A3B8']
                            : colors.primaryGradient
                        }
                        style={styles.avatarGradient}
                      >
                        {message.role === 'student' ? (
                          <User size={20} color="#fff" />
                        ) : (
                          <Bot size={20} color="#fff" />
                        )}
                      </LinearGradient>
                    </View>

                    <View
                      style={[
                        styles.messageBubble,
                        message.role === 'student'
                          ? styles.studentBubble
                          : styles.tutorBubble,
                      ]}
                    >
                      <Text
                        style={[
                          styles.messageText,
                          message.role === 'student'
                            ? styles.studentText
                            : styles.tutorText,
                        ]}
                      >
                        {message.content}
                      </Text>
                    </View>
                  </View>
                ))}
              </ScrollView>
            )}

            <View style={styles.modalFooter}>
              <Button
                title="Fechar"
                onPress={onClose}
                variant="outline"
                size="medium"
              />
              <Button
                title="Gerar Nova Conversa"
                onPress={generateConversation}
                variant="primary"
                size="medium"
                disabled={isLoading}
              />
            </View>
          </GlassCard>
        </View>
      </Modal>

      <APIKeyModal
        visible={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        onSuccess={handleApiKeySuccess}
      />
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 600,
    maxHeight: '90%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  questionContainer: {
    padding: 16,
    backgroundColor: `${colors.primary}15`,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  conversationContainer: {
    padding: 16,
    maxHeight: 400,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  studentMessage: {
    justifyContent: 'flex-end',
  },
  tutorMessage: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    overflow: 'hidden',
    marginRight: 8,
  },
  avatarGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageBubble: {
    maxWidth: '80%',
    borderRadius: 16,
    padding: 12,
  },
  studentBubble: {
    backgroundColor: colors.primary,
    borderBottomRightRadius: 4,
  },
  tutorBubble: {
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  studentText: {
    color: '#fff',
  },
  tutorText: {
    color: colors.text,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
});
