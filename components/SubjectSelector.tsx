import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  ActivityIndicator,
  TextInput,
  Alert
} from 'react-native';
import { colors } from '@/constants/colors';
import { useStudyStore } from '@/store/studyStore';
import { ChevronDown, Search, X, Check, Plus, BookOpen } from 'lucide-react-native';

interface SubjectSelectorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SubjectSelector: React.FC<SubjectSelectorProps> = ({
  value,
  onChange,
  placeholder = 'Selecione uma matéria'
}) => {
  const { subjects, fetchSubjects, loading, addSubject } = useStudyStore();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSubjects, setFilteredSubjects] = useState(subjects);
  const [creatingSubject, setCreatingSubject] = useState(false);

  useEffect(() => {
    // Carregar matérias quando o componente for montado
    const loadSubjects = async () => {
      try {
        console.log('Carregando matérias no SubjectSelector...');
        await fetchSubjects();
        console.log('Matérias carregadas com sucesso');
      } catch (error) {
        console.error('Erro ao carregar matérias:', error);
      }
    };

    loadSubjects();
  }, []);

  useEffect(() => {
    // Filtrar matérias com base na busca
    if (searchQuery.trim() === '') {
      setFilteredSubjects(subjects);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredSubjects(
        subjects.filter(subject =>
          subject.title.toLowerCase().includes(query)
        )
      );
    }
  }, [searchQuery, subjects]);

  const handleSelectSubject = (subjectTitle: string) => {
    onChange(subjectTitle);
    setModalVisible(false);
    setSearchQuery('');
  };

  const handleCreateSubject = async () => {
    if (!searchQuery.trim()) return;

    try {
      setCreatingSubject(true);
      console.log('Criando nova matéria:', searchQuery);

      // Criar uma nova matéria
      const newSubject = {
        title: searchQuery.trim(),
        description: `Matéria de ${searchQuery.trim()}`,
        icon: 'book',
        color: '#' + Math.floor(Math.random()*16777215).toString(16), // Cor aleatória
      };

      const savedSubject = await addSubject(newSubject);
      console.log('Nova matéria criada:', savedSubject);

      // Selecionar a nova matéria
      onChange(savedSubject.title);
      setModalVisible(false);
      setSearchQuery('');

      // Recarregar a lista de matérias
      await fetchSubjects();

    } catch (error) {
      console.error('Erro ao criar matéria:', error);
      Alert.alert('Erro', 'Não foi possível criar a matéria. Por favor, tente novamente.');
    } finally {
      setCreatingSubject(false);
    }
  };

  const renderSubjectItem = ({ item }) => (
    <TouchableOpacity
      style={styles.subjectItem}
      onPress={() => handleSelectSubject(item.title)}
    >
      <View style={[styles.subjectIcon, { backgroundColor: item.color || colors.primary }]}>
        <Text style={styles.subjectIconText}>{item.title.charAt(0).toUpperCase()}</Text>
      </View>
      <Text style={styles.subjectTitle}>{item.title}</Text>
      {value === item.title && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setModalVisible(true)}
      >
        <Text style={value ? styles.selectorText : styles.placeholderText}>
          {value || placeholder}
        </Text>
        <ChevronDown size={20} color={colors.textMedium} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Selecionar Matéria</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Search size={20} color={colors.textMedium} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Buscar matérias..."
                placeholderTextColor={colors.textMedium}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>

            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Carregando matérias...</Text>
              </View>
            ) : (
              <>
                <FlatList
                  data={filteredSubjects}
                  renderItem={renderSubjectItem}
                  keyExtractor={item => item.id}
                  style={styles.subjectList}
                  contentContainerStyle={styles.subjectListContent}
                  ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                      <Text style={styles.emptyText}>
                        {searchQuery.trim() !== ''
                          ? 'Nenhuma matéria encontrada com esse nome.'
                          : 'Você ainda não tem matérias cadastradas.'}
                      </Text>
                    </View>
                  }
                />

                {filteredSubjects.length === 0 && (
                  <View style={styles.modalFooter}>
                    <View style={styles.infoButton}>
                      <BookOpen size={20} color={colors.white} />
                      <Text style={styles.createButtonText}>
                        Você só pode usar matérias já cadastradas
                      </Text>
                    </View>
                  </View>
                )}
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  selectorText: {
    fontSize: 16,
    color: colors.text,
  },
  placeholderText: {
    fontSize: 16,
    color: colors.textMedium,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundLight,
    margin: 16,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.text,
  },
  subjectList: {
    flex: 1,
  },
  subjectListContent: {
    paddingHorizontal: 16,
  },
  subjectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  subjectIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  subjectIconText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
  },
  subjectTitle: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: 'center',
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textMedium,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
  },
  infoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.secondary,
    borderRadius: 8,
    paddingVertical: 12,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    marginLeft: 8,
  },
});
