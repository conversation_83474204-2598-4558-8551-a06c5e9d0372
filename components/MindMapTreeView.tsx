import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Animated,
  PanResponder,
  Dimensions,
  ScrollView,
} from 'react-native';
import { MindMap, MindMapNode, MindMapConnection } from '@/types';
import { colors } from '@/constants/colors';
import { Svg, Path, Polygon } from 'react-native-svg';

interface MindMapTreeViewProps {
  mindMap: MindMap;
  editMode: boolean;
  onNodePress?: (node: MindMapNode) => void;
  onConnectionPress?: (connection: MindMapConnection) => void;
  scale?: number;
}

interface TreeNode extends MindMapNode {
  children: TreeNode[];
  level: number;
  parent?: string;
}

export const MindMapTreeView: React.FC<MindMapTreeViewProps> = ({
  mindMap,
  editMode,
  onNodePress,
  onConnectionPress,
  scale = 1,
}) => {
  const [treeNodes, setTreeNodes] = useState<TreeNode[]>([]);
  const [rootNode, setRootNode] = useState<TreeNode | null>(null);
  const [mapWidth, setMapWidth] = useState(0);
  const [mapHeight, setMapHeight] = useState(0);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [initialPan, setInitialPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  const panAnim = useRef(new Animated.ValueXY()).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  // Configuração do PanResponder para permitir arrastar o mapa
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        setInitialPan({ x: pan.x, y: pan.y });
        setIsDragging(true);
      },
      onPanResponderMove: (_, gestureState) => {
        setPan({
          x: initialPan.x + gestureState.dx,
          y: initialPan.y + gestureState.dy,
        });
        panAnim.setValue({
          x: initialPan.x + gestureState.dx,
          y: initialPan.y + gestureState.dy,
        });
      },
      onPanResponderRelease: () => {
        setIsDragging(false);
      },
    })
  ).current;

  // Função para construir a árvore a partir dos nós e conexões
  const buildTree = () => {
    const { nodes, connections } = mindMap;

    if (!nodes.length) return;

    // Encontrar o nó raiz (geralmente o primeiro nó ou o nó sem conexões de entrada)
    let rootNodeId = nodes[0].id;
    const incomingConnections = new Set(connections.map(conn => conn.target));

    // Encontrar um nó que não seja alvo de nenhuma conexão (raiz)
    const possibleRoots = nodes.filter(node => !incomingConnections.has(node.id));
    if (possibleRoots.length > 0) {
      rootNodeId = possibleRoots[0].id;
    }

    // Criar mapa de nós para facilitar o acesso
    const nodeMap = new Map<string, TreeNode>();
    nodes.forEach(node => {
      nodeMap.set(node.id, {
        ...node,
        children: [],
        level: node.id === rootNodeId ? 0 : -1, // -1 significa que o nível ainda não foi determinado
      });
    });

    // Construir a árvore usando as conexões
    connections.forEach(conn => {
      const sourceNode = nodeMap.get(conn.source);
      const targetNode = nodeMap.get(conn.target);

      if (sourceNode && targetNode) {
        sourceNode.children.push(targetNode);
        targetNode.parent = sourceNode.id;
      }
    });

    // Definir níveis para todos os nós
    const assignLevels = (nodeId: string, level: number) => {
      const node = nodeMap.get(nodeId);
      if (node) {
        node.level = level;
        node.children.forEach(child => {
          assignLevels(child.id, level + 1);
        });
      }
    };

    assignLevels(rootNodeId, 0);

    // Converter o mapa de volta para uma lista
    const treeNodesList = Array.from(nodeMap.values());

    // Encontrar o nó raiz
    const root = treeNodesList.find(node => node.id === rootNodeId) || null;

    setTreeNodes(treeNodesList);
    setRootNode(root);

    // Calcular as dimensões do mapa
    calculateMapDimensions(treeNodesList);
  };

  // Calcular as dimensões do mapa com base nos nós
  const calculateMapDimensions = (nodes: TreeNode[]) => {
    if (!nodes.length) return;

    const maxLevel = Math.max(...nodes.map(node => node.level));
    const levelCounts = new Array(maxLevel + 1).fill(0);

    // Contar quantos nós existem em cada nível
    nodes.forEach(node => {
      if (node.level >= 0) {
        levelCounts[node.level]++;
      }
    });

    // Calcular a largura e altura do mapa
    const nodeWidth = 180;
    const nodeHeight = 60;
    const horizontalSpacing = 60;
    const verticalSpacing = 100;

    // Calcular a largura com base no nível com mais nós
    const maxNodesInLevel = Math.max(...levelCounts);
    const width = Math.max(
      screenWidth * 1.5, // Pelo menos 1.5x a largura da tela
      maxNodesInLevel * (nodeWidth + horizontalSpacing)
    );

    // Calcular a altura com base no número de níveis
    const height = Math.max(
      screenHeight * 1.5, // Pelo menos 1.5x a altura da tela
      (maxLevel + 1) * (nodeHeight + verticalSpacing) + 100 // +100 para margem
    );

    setMapWidth(width);
    setMapHeight(height);
  };

  // Posicionar os nós em uma estrutura de árvore
  const positionNodes = () => {
    if (!rootNode || !treeNodes.length) return [];

    const nodeWidth = 180;
    const nodeHeight = 60;
    const horizontalSpacing = 60;
    const verticalSpacing = 100;

    // Calcular a posição de cada nó com base em seu nível e posição na árvore
    const positionedNodes = [...treeNodes];

    // Agrupar nós por nível
    const nodesByLevel = positionedNodes.reduce((acc, node) => {
      if (node.level >= 0) {
        if (!acc[node.level]) {
          acc[node.level] = [];
        }
        acc[node.level].push(node);
      }
      return acc;
    }, {} as Record<number, TreeNode[]>);

    // Posicionar nós por nível
    Object.entries(nodesByLevel).forEach(([levelStr, nodes]) => {
      const level = parseInt(levelStr);

      // Calcular a largura total necessária para este nível
      const levelWidth = nodes.length * (nodeWidth + horizontalSpacing);
      // Calcular o ponto inicial para centralizar os nós deste nível
      const startX = (screenWidth - levelWidth) / 2 + nodeWidth / 2;

      nodes.forEach((node, index) => {
        // Posicionar horizontalmente para distribuir os nós uniformemente e centralizados
        const x = startX + (index * (nodeWidth + horizontalSpacing));

        // Posicionar verticalmente com base no nível (do topo para baixo)
        const y = 100 + (level * (nodeHeight + verticalSpacing));

        node.x = x;
        node.y = y;
      });
    });

    return positionedNodes;
  };

  // Renderizar as conexões entre os nós
  const renderConnections = () => {
    if (!treeNodes.length) return null;

    // Calcular as dimensões necessárias para o SVG
    const svgWidth = Math.max(mapWidth, screenWidth * 2);
    const svgHeight = Math.max(mapHeight, screenHeight * 2);

    // Criar um único SVG para todas as conexões
    return (
      <View style={styles.connectionContainer}>
        <Svg height={svgHeight} width={svgWidth} style={styles.svg}>
          {mindMap.connections.map(connection => {
            const sourceNode = treeNodes.find(node => node.id === connection.source);
            const targetNode = treeNodes.find(node => node.id === connection.target);

            if (!sourceNode || !targetNode) return null;

            // Calcular pontos de início e fim da linha
            const startX = sourceNode.x;
            const startY = sourceNode.y;
            const endX = targetNode.x;
            const endY = targetNode.y;

            // Determinar se a conexão é vertical (entre níveis) ou horizontal (mesmo nível)
            const isVertical = sourceNode.level !== targetNode.level;

            // Calcular pontos de controle para a curva
            // Para conexões verticais, usamos uma curva mais suave
            const midY = (startY + endY) / 2;
            const controlX1 = isVertical ? startX : (startX + endX) / 2;
            const controlY1 = isVertical ? midY : startY;
            const controlX2 = isVertical ? endX : (startX + endX) / 2;
            const controlY2 = isVertical ? midY : endY;

            // Cor da linha
            const lineColor = connection.color || colors.text;

            return (
              <React.Fragment key={connection.id}>
                <Path
                  d={`M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`}
                  stroke={lineColor}
                  strokeWidth={connection.width || 2}
                  fill="none"
                  strokeDasharray={connection.style === 'dashed' ? '5,5' : connection.style === 'dotted' ? '2,2' : '0'}
                />
                {/* Seta na ponta da linha */}
                <Polygon
                  points={`${endX},${endY} ${endX-10},${endY-5} ${endX-10},${endY+5}`}
                  fill={lineColor}
                  stroke={lineColor}
                  strokeWidth="1"
                />
              </React.Fragment>
            );
          })}
        </Svg>
      </View>
    );
  };

  // Renderizar os nós do mapa mental
  const renderNodes = () => {
    const positionedNodes = positionNodes();

    return positionedNodes.map(node => {
      // Determinar o estilo do nó com base em suas propriedades
      const nodeStyle = node.style || 'filled';
      const nodeShape = node.shape || 'rectangle';
      const nodeColor = node.color || colors.primary;
      const nodeTextColor = nodeStyle === 'filled' ? '#fff' : colors.text;

      // Determinar o tamanho do nó com base no nível
      const nodeSize = node.level === 0 ? 'large' : node.level === 1 ? 'medium' : 'small';
      const nodeSizeValue = nodeSize === 'large' ? 220 : nodeSize === 'medium' ? 180 : 150;
      const nodeHeight = nodeSize === 'large' ? 70 : nodeSize === 'medium' ? 60 : 50;

      // Determinar o raio da borda com base na forma
      const nodeBorderRadius =
        nodeShape === 'circle' ? 50 :
        nodeShape === 'square' ? 4 :
        nodeShape === 'rectangle' ? 8 : 8;

      // Estilo de fundo para nós de nível 0 (raiz)
      const rootNodeStyle = node.level === 0 ? {
        backgroundColor: '#333',
        borderColor: '#333',
      } : {};

      return (
        <Pressable
          key={node.id}
          style={[
            styles.nodeContainer,
            {
              left: node.x - (nodeSizeValue / 2),
              top: node.y - (nodeHeight / 2),
              width: nodeSizeValue,
              height: nodeHeight,
              backgroundColor: nodeStyle === 'filled' ? nodeColor : colors.card,
              borderColor: nodeColor,
              borderRadius: nodeBorderRadius,
              borderWidth: nodeStyle === 'minimal' ? 0 : 2,
              ...rootNodeStyle,
            },
          ]}
          onPress={() => onNodePress && onNodePress(node)}
        >
          <Text
            style={[
              styles.nodeText,
              {
                color: node.level === 0 ? '#fff' : nodeTextColor,
                fontSize: nodeSize === 'large' ? 16 : nodeSize === 'medium' ? 14 : 12,
              },
            ]}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {node.text}
          </Text>
        </Pressable>
      );
    });
  };

  // Efeito para construir a árvore quando o mapa mental muda
  useEffect(() => {
    buildTree();
  }, [mindMap]);

  // Efeito para atualizar a animação quando o pan muda
  useEffect(() => {
    panAnim.setValue(pan);
  }, [pan]);

  // Efeito para atualizar a animação quando a escala muda
  useEffect(() => {
    Animated.timing(scaleAnim, {
      toValue: scale,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [scale]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.mapContainer,
          {
            width: mapWidth,
            height: mapHeight,
            transform: [
              { translateX: panAnim.x },
              { translateY: panAnim.y },
              { scale: scaleAnim },
            ],
          },
        ]}
        {...panResponder.panHandlers}
      >
        {renderConnections()}
        {renderNodes()}
      </Animated.View>
    </View>
  );
};

// Os componentes SVG são importados diretamente no topo do arquivo

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
  },
  mapContainer: {
    position: 'absolute',
    backgroundColor: 'transparent',
  },
  nodeContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nodeText: {
    textAlign: 'center',
    fontWeight: '500',
  },
  connectionContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  svg: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
});
