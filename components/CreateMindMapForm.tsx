import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, Alert, Pressable } from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from './Button';
import { GlassCard } from './GlassCard';
import { Save, X, Network, BrainCircuit, GitBranch, Circle, Square, Triangle, Hexagon, Palette } from 'lucide-react-native';
import { supabase } from '@/lib/supabase';
import { useUserStore } from '@/store/userStore';
import { LinearGradient } from 'expo-linear-gradient';
import { MindMapNode, MindMapConnection } from '@/types';

interface CreateMindMapFormProps {
  groupId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

type MapType = 'concept' | 'brain' | 'flow';

export const CreateMindMapForm: React.FC<CreateMindMapFormProps> = ({
  groupId,
  onCancel,
  onSuccess,
}) => {
  const { supabaseUser } = useUserStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [mapType, setMapType] = useState<MapType>('concept');
  const [centralTopic, setCentralTopic] = useState('');
  const [saving, setSaving] = useState(false);

  const getMapTypeIcon = (type: MapType) => {
    switch (type) {
      case 'concept':
        return <Network size={24} color={colors.white} />;
      case 'brain':
        return <BrainCircuit size={24} color={colors.white} />;
      case 'flow':
        return <GitBranch size={24} color={colors.white} />;
      default:
        return <Network size={24} color={colors.white} />;
    }
  };

  const getMapTypeColor = (type: MapType) => {
    switch (type) {
      case 'concept':
        return colors.accent1;
      case 'brain':
        return colors.primary;
      case 'flow':
        return colors.secondary;
      default:
        return colors.primary;
    }
  };

  const getMapTypeGradient = (type: MapType) => {
    switch (type) {
      case 'concept':
        return [colors.accent1, colors.accent1Dark || colors.accent1];
      case 'brain':
        return colors.primaryGradient;
      case 'flow':
        return colors.secondaryGradient;
      default:
        return colors.primaryGradient;
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'Por favor, insira um título para o mapa mental.');
      return;
    }

    if (!centralTopic.trim()) {
      Alert.alert('Erro', 'Por favor, insira um tópico central para o mapa mental.');
      return;
    }

    try {
      setSaving(true);

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) {
        Alert.alert('Erro', 'Você precisa estar logado para adicionar materiais.');
        return;
      }

      // Create initial nodes and connections
      const centralNode: MindMapNode = {
        id: 'node_1',
        text: centralTopic.trim(),
        x: 400,
        y: 300,
        color: getMapTypeColor(mapType),
        shape: 'circle',
        style: 'filled',
        size: 'large',
      };

      // Prepare material data
      const materialData = {
        group_id: groupId,
        user_id: authUser.id,
        title: title.trim(),
        description: description.trim() || 'Mapa mental compartilhado no grupo de estudos',
        type: 'mindmap',
        content: {
          mapType,
          nodes: [centralNode],
          connections: [],
        },
      };

      // Save to Supabase
      const { data, error } = await supabase
        .from('study_group_materials')
        .insert([materialData])
        .select()
        .single();

      if (error) {
        console.error('Erro ao salvar mapa mental:', error);
        Alert.alert('Erro', 'Não foi possível salvar o mapa mental. Por favor, tente novamente.');
        return;
      }

      Alert.alert('Sucesso', 'Mapa mental adicionado com sucesso!');
      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar mapa mental:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao salvar o mapa mental. Por favor, tente novamente.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <GlassCard style={styles.container}>
      <View style={styles.header}>
        <LinearGradient
          colors={getMapTypeGradient(mapType)}
          style={styles.iconContainer}
        >
          {getMapTypeIcon(mapType)}
        </LinearGradient>
        <Text style={styles.headerTitle}>Novo Mapa Mental</Text>
      </View>

      <ScrollView
        style={styles.formContainer}
        contentContainerStyle={styles.formContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Título do Mapa</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Digite o título do mapa mental"
            placeholderTextColor={colors.textLight}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Descrição (opcional)</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Digite uma descrição para o mapa mental"
            placeholderTextColor={colors.textLight}
            multiline
            textAlignVertical="top"
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Tópico Central</Text>
          <TextInput
            style={styles.input}
            value={centralTopic}
            onChangeText={setCentralTopic}
            placeholder="Digite o tópico central do mapa"
            placeholderTextColor={colors.textLight}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Tipo de Mapa</Text>
          <View style={styles.mapTypesContainer}>
            <Pressable
              style={[
                styles.mapTypeButton,
                mapType === 'concept' && styles.mapTypeButtonActive,
                { borderColor: colors.accent1 },
              ]}
              onPress={() => setMapType('concept')}
            >
              <Network
                size={24}
                color={mapType === 'concept' ? colors.accent1 : colors.textLight}
              />
              <Text
                style={[
                  styles.mapTypeText,
                  mapType === 'concept' && { color: colors.accent1 },
                ]}
              >
                Conceitual
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.mapTypeButton,
                mapType === 'brain' && styles.mapTypeButtonActive,
                { borderColor: colors.primary },
              ]}
              onPress={() => setMapType('brain')}
            >
              <BrainCircuit
                size={24}
                color={mapType === 'brain' ? colors.primary : colors.textLight}
              />
              <Text
                style={[
                  styles.mapTypeText,
                  mapType === 'brain' && { color: colors.primary },
                ]}
              >
                Mental
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.mapTypeButton,
                mapType === 'flow' && styles.mapTypeButtonActive,
                { borderColor: colors.secondary },
              ]}
              onPress={() => setMapType('flow')}
            >
              <GitBranch
                size={24}
                color={mapType === 'flow' ? colors.secondary : colors.textLight}
              />
              <Text
                style={[
                  styles.mapTypeText,
                  mapType === 'flow' && { color: colors.secondary },
                ]}
              >
                Fluxograma
              </Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>

      <View style={styles.buttonsContainer}>
        <Button
          title="Cancelar"
          onPress={onCancel}
          variant="secondary"
          size="medium"
          icon={X}
          style={styles.button}
        />
        <Button
          title="Criar"
          onPress={handleSave}
          variant="primary"
          size="medium"
          icon={Save}
          style={styles.button}
          loading={saving}
        />
      </View>
    </GlassCard>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  formContainer: {
    flex: 1,
  },
  formContent: {
    paddingBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.cardLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 80,
  },
  mapTypesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  mapTypeButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.border,
    marginHorizontal: 4,
    backgroundColor: colors.cardLight,
  },
  mapTypeButtonActive: {
    backgroundColor: colors.card,
    borderWidth: 2,
  },
  mapTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginTop: 4,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  button: {
    marginLeft: 12,
  },
});
