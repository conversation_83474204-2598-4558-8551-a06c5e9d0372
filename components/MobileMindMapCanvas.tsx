import React, { useRef, useState, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  PanResponder,
  Animated,
  Dimensions,
  Pressable,
  Alert,
  InteractionManager,
} from 'react-native';
import { colors } from '@/constants/colors';
import { MindMapNode, MindMapConnection } from '@/types';
import Svg, { Line, Circle, Rect, Polygon, Path } from 'react-native-svg';
import { throttle } from '@/utils/performance';

const { width, height } = Dimensions.get('window');

interface MobileMindMapCanvasProps {
  nodes: MindMapNode[];
  connections: MindMapConnection[];
  editMode: boolean;
  onNodePress: (node: MindMapNode) => void;
  onNodeMove: (nodeId: string, x: number, y: number) => void;
  onCanvasPress: (x: number, y: number) => void;
  selectedNodeId?: string;
  connectionPreview?: { start: { x: number; y: number }; end: { x: number; y: number } } | null;
}

// Memoized node component for better performance
const MemoizedNode = memo(({ node, panOffset, zoomLevel, selectedNodeId, getNodeSize }: {
  node: MindMapNode;
  panOffset: { x: number; y: number };
  zoomLevel: number;
  selectedNodeId?: string;
  getNodeSize: (size?: string) => number;
}) => {
  const nodeSize = getNodeSize(node.size);
  const x = (node.x + panOffset.x) * zoomLevel;
  const y = (node.y + panOffset.y) * zoomLevel;
  const isSelected = selectedNodeId === node.id;

  return (
    <View
      key={node.id}
      style={[
        styles.node,
        {
          left: x - nodeSize / 2,
          top: y - nodeSize / 2,
          width: nodeSize,
          height: nodeSize,
          backgroundColor: node.style === 'filled' ? node.color : 'transparent',
          borderColor: node.color,
          borderWidth: node.style === 'outline' ? 2 : 0,
          borderRadius: node.shape === 'circle' ? nodeSize / 2 : 8,
          transform: [{ scale: isSelected ? 1.1 : 1 }],
        },
      ]}
    >
      <Text
        style={[
          styles.nodeText,
          {
            color: node.style === 'filled' ? '#fff' : node.color,
            fontSize: node.size === 'small' ? 10 : node.size === 'large' ? 14 : 12,
          },
        ]}
        numberOfLines={2}
        adjustsFontSizeToFit
      >
        {node.text}
      </Text>
    </View>
  );
});

export const MobileMindMapCanvas: React.FC<MobileMindMapCanvasProps> = memo(({
  nodes,
  connections,
  editMode,
  onNodePress,
  onNodeMove,
  onCanvasPress,
  selectedNodeId,
  connectionPreview,
}) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);

  const panRef = useRef({ x: 0, y: 0 });
  const lastPanRef = useRef({ x: 0, y: 0 });
  const lastDistanceRef = useRef<number | null>(null);
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Memoized getNodeSize function
  const getNodeSize = useCallback((size?: string): number => {
    switch (size) {
      case 'small': return 60;
      case 'large': return 120;
      default: return 80;
    }
  }, []);

  // Throttled pan update for better performance
  const throttledPanUpdate = useCallback(
    throttle((newPanOffset: { x: number; y: number }) => {
      setPanOffset(newPanOffset);
    }, 16), // ~60fps
    []
  );

  // Throttled zoom update for better performance
  const throttledZoomUpdate = useCallback(
    throttle((newZoom: number) => {
      setZoomLevel(newZoom);
      Animated.spring(scaleAnimation, {
        toValue: newZoom,
        friction: 10,
        tension: 40,
        useNativeDriver: true,
      }).start();
    }, 16), // ~60fps
    []
  );

  // Pan responder for canvas interactions with performance optimizations
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: (event) => {
        lastPanRef.current = { x: panRef.current.x, y: panRef.current.y };
        lastDistanceRef.current = null;

        // Check if touch started on a node
        const touchX = event.nativeEvent.locationX;
        const touchY = event.nativeEvent.locationY;

        const touchedNode = findNodeAtPosition(touchX, touchY);
        if (touchedNode && editMode) {
          setDraggedNodeId(touchedNode.id);
          setIsDragging(true);
        }
      },
      onPanResponderMove: (event, gestureState) => {
        // Handle pinch zoom (two fingers)
        if (event.nativeEvent.touches && event.nativeEvent.touches.length === 2) {
          const touch1 = event.nativeEvent.touches[0];
          const touch2 = event.nativeEvent.touches[1];
          
          const dx = touch1.pageX - touch2.pageX;
          const dy = touch1.pageY - touch2.pageY;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (lastDistanceRef.current !== null) {
            const change = distance - lastDistanceRef.current;
            const zoomChange = change * 0.003;
            const newZoom = Math.max(0.5, Math.min(3, zoomLevel + zoomChange));

            // Use throttled zoom update for better performance
            throttledZoomUpdate(newZoom);
          }

          lastDistanceRef.current = distance;
        } else if (draggedNodeId && isDragging) {
          // Move node with throttled updates
          const newX = gestureState.dx / zoomLevel;
          const newY = gestureState.dy / zoomLevel;
          onNodeMove(draggedNodeId, newX, newY);
        } else {
          // Pan canvas with throttled updates
          const newPanOffset = {
            x: lastPanRef.current.x + gestureState.dx / zoomLevel,
            y: lastPanRef.current.y + gestureState.dy / zoomLevel,
          };
          panRef.current = newPanOffset;
          throttledPanUpdate(newPanOffset);
        }
      },
      onPanResponderRelease: (event) => {
        if (draggedNodeId) {
          setDraggedNodeId(null);
          setIsDragging(false);
        } else if (!isDragging) {
          // Handle canvas tap
          const touchX = event.nativeEvent.locationX;
          const touchY = event.nativeEvent.locationY;
          
          const touchedNode = findNodeAtPosition(touchX, touchY);
          if (touchedNode) {
            onNodePress(touchedNode);
          } else {
            onCanvasPress(touchX - panOffset.x, touchY - panOffset.y);
          }
        }
        
        lastDistanceRef.current = null;
      },
    })
  ).current;

  // Memoized findNodeAtPosition for better performance
  const findNodeAtPosition = useCallback((x: number, y: number): MindMapNode | null => {
    for (const node of nodes) {
      const nodeX = (node.x + panOffset.x) * zoomLevel;
      const nodeY = (node.y + panOffset.y) * zoomLevel;
      const nodeSize = getNodeSize(node.size);

      if (
        x >= nodeX - nodeSize / 2 &&
        x <= nodeX + nodeSize / 2 &&
        y >= nodeY - nodeSize / 2 &&
        y <= nodeY + nodeSize / 2
      ) {
        return node;
      }
    }
    return null;
  }, [nodes, panOffset, zoomLevel, getNodeSize]);

  const renderNode = (node: MindMapNode) => {
    const nodeSize = getNodeSize(node.size);
    const x = (node.x + panOffset.x) * zoomLevel;
    const y = (node.y + panOffset.y) * zoomLevel;
    const isSelected = selectedNodeId === node.id;

    return (
      <View
        key={node.id}
        style={[
          styles.node,
          {
            left: x - nodeSize / 2,
            top: y - nodeSize / 2,
            width: nodeSize,
            height: nodeSize,
            backgroundColor: node.style === 'filled' ? node.color : 'transparent',
            borderColor: node.color,
            borderWidth: node.style === 'outline' ? 2 : 0,
            borderRadius: node.shape === 'circle' ? nodeSize / 2 : 8,
            transform: [{ scale: isSelected ? 1.1 : 1 }],
          },
        ]}
      >
        <Text
          style={[
            styles.nodeText,
            {
              color: node.style === 'filled' ? '#fff' : node.color,
              fontSize: node.size === 'small' ? 10 : node.size === 'large' ? 14 : 12,
            },
          ]}
          numberOfLines={2}
          adjustsFontSizeToFit
        >
          {node.text}
        </Text>
      </View>
    );
  };

  // Memoized connections rendering for better performance
  const renderedConnections = useMemo(() => {
    return connections.map((connection) => {
      const sourceNode = nodes.find(n => n.id === connection.source);
      const targetNode = nodes.find(n => n.id === connection.target);

      if (!sourceNode || !targetNode) return null;

      const x1 = (sourceNode.x + panOffset.x) * zoomLevel;
      const y1 = (sourceNode.y + panOffset.y) * zoomLevel;
      const x2 = (targetNode.x + panOffset.x) * zoomLevel;
      const y2 = (targetNode.y + panOffset.y) * zoomLevel;

      return (
        <Line
          key={connection.id}
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
          stroke={connection.color || colors.primary}
          strokeWidth={connection.width || 2}
          strokeDasharray={connection.style === 'dashed' ? '5,5' : undefined}
        />
      );
    }).filter(Boolean);
  }, [connections, nodes, panOffset, zoomLevel]);

  // Memoized connection preview for better performance
  const connectionPreviewElement = useMemo(() => {
    if (!connectionPreview) return null;

    return (
      <Line
        x1={(connectionPreview.start.x + panOffset.x) * zoomLevel}
        y1={(connectionPreview.start.y + panOffset.y) * zoomLevel}
        x2={(connectionPreview.end.x + panOffset.x) * zoomLevel}
        y2={(connectionPreview.end.y + panOffset.y) * zoomLevel}
        stroke={colors.primary}
        strokeWidth={2}
        strokeDasharray="3,3"
        opacity={0.7}
      />
    );
  }, [connectionPreview, panOffset, zoomLevel]);

  const renderConnections = useCallback(() => {
    return (
      <Svg style={StyleSheet.absoluteFill} width={width} height={height}>
        {renderedConnections}
        {connectionPreviewElement}
      </Svg>
    );
  }, [renderedConnections, connectionPreviewElement]);

  // Memoized nodes rendering for better performance
  const renderedNodes = useMemo(() => {
    return nodes.map(node => (
      <MemoizedNode
        key={node.id}
        node={node}
        panOffset={panOffset}
        zoomLevel={zoomLevel}
        selectedNodeId={selectedNodeId}
        getNodeSize={getNodeSize}
      />
    ));
  }, [nodes, panOffset, zoomLevel, selectedNodeId, getNodeSize]);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnimation }],
        },
      ]}
      {...panResponder.panHandlers}
    >
      {renderConnections()}
      {renderedNodes}
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  node: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nodeText: {
    textAlign: 'center',
    fontWeight: '600',
    paddingHorizontal: 4,
  },
});
