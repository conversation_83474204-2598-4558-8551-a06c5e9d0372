import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  Pressable,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { colors } from "@/constants/colors";
import { Button } from "./Button";
import { Subject, Separator } from "@/types";
import * as Icons from "lucide-react-native";
import { X } from "lucide-react-native";

interface CreateSubjectModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateSubject: (subject: Subject) => void;
  separators?: Separator[];
}

const SUBJECT_COLORS = [
  "#3399FF", // Primary blue
  "#F97316", // Orange
  "#10B981", // Green
  "#8B5CF6", // Purple
  "#EC4899", // Pink
  "#EF4444", // Red
  "#F59E0B", // Amber
  "#6366F1", // Indigo
];

const SUBJECT_ICONS = [
  "Book",
  "BookOpen",
  "Calculator",
  "FileText",
  "Globe",
  "Microscope",
  "Atom",
  "Dna",
  "Beaker",
  "PenTool",
  "Palette",
  "Music",
  "Languages",
  "History",
  "Landmark",
  "Binary",
  "Code",
  "Database",
];

export const CreateSubjectModal: React.FC<CreateSubjectModalProps> = ({
  visible,
  onClose,
  onCreateSubject,
  separators = [],
}) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedColor, setSelectedColor] = useState(SUBJECT_COLORS[0]);
  const [selectedIcon, setSelectedIcon] = useState(SUBJECT_ICONS[0]);
  const [selectedSeparator, setSelectedSeparator] = useState<string | null>(null);

  const handleCreateSubject = () => {
    if (title.trim() === "") {
      return;
    }

    const newSubject: Subject = {
      id: `subject_${Date.now()}`,
      title: title.trim(),
      description: description.trim() || `Matéria de ${title.trim()}`,
      icon: selectedIcon,
      color: selectedColor,
      progress: 0,
      separator_id: selectedSeparator,
    };

    onCreateSubject(newSubject);
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setSelectedColor(SUBJECT_COLORS[0]);
    setSelectedIcon(SUBJECT_ICONS[0]);
    setSelectedSeparator(null);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Nova Matéria</Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.modalContent}>
            <Text style={styles.inputLabel}>Nome da Matéria</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Ex: Matemática, Física, História..."
              placeholderTextColor={colors.textLight}
              maxLength={30}
            />

            <Text style={styles.inputLabel}>Descrição (opcional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descreva brevemente o conteúdo desta matéria..."
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={3}
              maxLength={100}
            />

            <Text style={styles.inputLabel}>Cor</Text>
            <View style={styles.colorGrid}>
              {SUBJECT_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    selectedColor === color && styles.selectedColorOption,
                  ]}
                  onPress={() => setSelectedColor(color)}
                >
                  {selectedColor === color && (
                    <View style={styles.selectedColorIndicator} />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.inputLabel}>Ícone</Text>
            <View style={styles.iconGrid}>
              {SUBJECT_ICONS.map((iconName) => {
                const IconComponent = Icons[iconName as keyof typeof Icons];
                return (
                  <TouchableOpacity
                    key={iconName}
                    style={[
                      styles.iconOption,
                      selectedIcon === iconName && {
                        backgroundColor: `${selectedColor}20`,
                        borderColor: selectedColor,
                      },
                    ]}
                    onPress={() => setSelectedIcon(iconName)}
                  >
                    <IconComponent
                      size={24}
                      color={selectedIcon === iconName ? selectedColor : colors.textLight}
                    />
                  </TouchableOpacity>
                );
              })}
            </View>

            {separators.length > 0 && (
              <>
                <Text style={styles.inputLabel}>Separador (opcional)</Text>
                <View style={styles.separatorContainer}>
                  <TouchableOpacity
                    style={[
                      styles.separatorOption,
                      selectedSeparator === null && styles.selectedSeparatorOption,
                    ]}
                    onPress={() => setSelectedSeparator(null)}
                  >
                    <Text style={[
                      styles.separatorText,
                      selectedSeparator === null && styles.selectedSeparatorText,
                    ]}>Nenhum</Text>
                  </TouchableOpacity>

                  {separators.map((separator) => (
                    <TouchableOpacity
                      key={separator.id}
                      style={[
                        styles.separatorOption,
                        { borderLeftColor: separator.color },
                        selectedSeparator === separator.id && styles.selectedSeparatorOption,
                      ]}
                      onPress={() => setSelectedSeparator(separator.id)}
                    >
                      <Text style={[
                        styles.separatorText,
                        selectedSeparator === separator.id && styles.selectedSeparatorText,
                      ]}>{separator.title}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            )}

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="outline"
                size="medium"
                style={styles.cancelButton}
              />
              <Button
                title="Criar Matéria"
                onPress={handleCreateSubject}
                variant="primary"
                size="medium"
                disabled={!title.trim()}
                style={styles.createButton}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContainer: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: 24,
    maxHeight: "90%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  colorGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: "#fff",
  },
  selectedColorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  iconGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
  },
  iconOption: {
    width: 50,
    height: 50,
    borderRadius: 12,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  separatorContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  separatorOption: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.border,
  },
  selectedSeparatorOption: {
    backgroundColor: `${colors.primary}15`,
    borderLeftColor: colors.primary,
  },
  separatorText: {
    fontSize: 16,
    color: colors.text,
  },
  selectedSeparatorText: {
    fontWeight: "600",
    color: colors.primary,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 24,
    marginBottom: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
});
