import React from 'react';
import { View, Text, StyleSheet, Modal, Pressable } from 'react-native';
import { colors } from '@/constants/colors';

interface MenuItem {
  icon: React.ReactNode;
  title: string;
  onPress: () => void;
  destructive?: boolean;
}

interface MenuProps {
  visible: boolean;
  onClose: () => void;
  items: MenuItem[];
}

export const Menu: React.FC<MenuProps> = ({ visible, onClose, items }) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Pressable style={styles.overlay} onPress={onClose}>
        <View style={styles.menuContainer}>
          {items.map((item, index) => (
            <Pressable
              key={index}
              style={styles.menuItem}
              onPress={() => {
                onClose();
                item.onPress();
              }}
            >
              <View style={styles.iconContainer}>{item.icon}</View>
              <Text
                style={[
                  styles.menuItemText,
                  item.destructive && styles.destructiveText,
                ]}
              >
                {item.title}
              </Text>
            </Pressable>
          ))}
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 8,
    width: '80%',
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  iconContainer: {
    marginRight: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: colors.text,
  },
  destructiveText: {
    color: colors.error,
  },
});
