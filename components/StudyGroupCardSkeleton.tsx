import React, { useEffect, useRef } from "react";
import { View, StyleSheet, Animated } from "react-native";
import { colors } from "@/constants/colors";

export const StudyGroupCardSkeleton: React.FC = () => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <View style={styles.container}>
      <View style={styles.statusContainer}>
        <Animated.View style={[styles.badge, { opacity: shimmerOpacity }]} />
      </View>

      <View style={styles.cardHeader}>
        <Animated.View style={[styles.groupIconContainer, { opacity: shimmerOpacity }]} />
        <View style={styles.groupInfo}>
          <Animated.View style={[styles.groupName, { opacity: shimmerOpacity }]} />
          <Animated.View style={[styles.groupDescription, { opacity: shimmerOpacity }]} />
        </View>
      </View>

      <View style={styles.groupStats}>
        <View style={styles.statItem}>
          <Animated.View style={[styles.statIcon, { opacity: shimmerOpacity }]} />
          <Animated.View style={[styles.statText, { opacity: shimmerOpacity }]} />
        </View>
        <View style={styles.statItem}>
          <Animated.View style={[styles.statIcon, { opacity: shimmerOpacity }]} />
          <Animated.View style={[styles.statText, { opacity: shimmerOpacity }]} />
        </View>
        <Animated.View style={[styles.statBadge, { opacity: shimmerOpacity }]} />
      </View>

      <View style={styles.footer}>
        <View style={styles.datesContainer}>
          <Animated.View style={[styles.dateText, { opacity: shimmerOpacity }]} />
          <Animated.View style={[styles.dateText, { opacity: shimmerOpacity }]} />
        </View>
        <Animated.View style={[styles.inviteCode, { opacity: shimmerOpacity }]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    position: "relative",
    overflow: "hidden",
    borderWidth: 1,
    borderColor: `${colors.primary}10`,
  },
  statusContainer: {
    position: "absolute",
    top: 12,
    right: 12,
    zIndex: 1,
  },
  badge: {
    width: 60,
    height: 20,
    borderRadius: 12,
    backgroundColor: `${colors.primary}20`,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  groupIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: `${colors.primary}20`,
    marginRight: 16,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    height: 20,
    backgroundColor: `${colors.primary}20`,
    borderRadius: 4,
    marginBottom: 8,
    width: "70%",
  },
  groupDescription: {
    height: 16,
    backgroundColor: `${colors.primary}15`,
    borderRadius: 4,
    width: "90%",
  },
  groupStats: {
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: `${colors.border}80`,
    paddingTop: 14,
    marginTop: 4,
    alignItems: "center",
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  statIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: `${colors.primary}20`,
  },
  statText: {
    width: 60,
    height: 14,
    backgroundColor: `${colors.primary}15`,
    borderRadius: 4,
    marginLeft: 6,
  },
  statBadge: {
    width: 80,
    height: 24,
    backgroundColor: `${colors.secondary}15`,
    borderRadius: 12,
    marginLeft: "auto",
  },
  footer: {
    marginTop: 14,
    paddingTop: 14,
    borderTopWidth: 1,
    borderTopColor: `${colors.border}80`,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  datesContainer: {
    gap: 6,
  },
  dateText: {
    width: 100,
    height: 12,
    backgroundColor: `${colors.primary}15`,
    borderRadius: 4,
  },
  inviteCode: {
    width: 80,
    height: 24,
    backgroundColor: `${colors.primary}15`,
    borderRadius: 12,
  },
});
