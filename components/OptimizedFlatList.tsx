import React, { memo, useCallback, useMemo } from 'react';
import {
  FlatList,
  FlatListProps,
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors } from '@/constants/colors';

interface OptimizedFlatListProps<T> extends Omit<FlatListProps<T>, 'renderItem'> {
  data: ReadonlyArray<T>;
  renderItem: (item: T, index: number) => React.ReactElement;
  keyExtractor: (item: T, index: number) => string;
  loading?: boolean;
  emptyText?: string;
  emptySubtext?: string;
  emptyIcon?: React.ReactNode;
  contentContainerStyle?: ViewStyle;
  loadingContainerStyle?: ViewStyle;
  emptyContainerStyle?: ViewStyle;
  emptyTextStyle?: TextStyle;
  emptySubtextStyle?: TextStyle;
  initialNumToRender?: number;
  maxToRenderPerBatch?: number;
  windowSize?: number;
  updateCellsBatchingPeriod?: number;
  onEndReachedThreshold?: number;
  removeClippedSubviews?: boolean;
  maintainVisibleContentPosition?: {
    minIndexForVisible: number;
    autoscrollToTopThreshold?: number;
  };
}

function OptimizedFlatListComponent<T>({
  data,
  renderItem,
  keyExtractor,
  loading = false,
  emptyText = 'Nenhum item encontrado',
  emptySubtext,
  emptyIcon,
  contentContainerStyle,
  loadingContainerStyle,
  emptyContainerStyle,
  emptyTextStyle,
  emptySubtextStyle,
  initialNumToRender = 10,
  maxToRenderPerBatch = 5,
  windowSize = 10,
  updateCellsBatchingPeriod = 50,
  onEndReachedThreshold = 0.5,
  removeClippedSubviews = true,
  maintainVisibleContentPosition,
  ...rest
}: OptimizedFlatListProps<T>) {
  // Memoize the renderItem function to prevent unnecessary re-renders
  const renderItemMemoized = useCallback(
    ({ item, index }: { item: T; index: number }) => renderItem(item, index),
    [renderItem]
  );

  // Memoize the keyExtractor function
  const keyExtractorMemoized = useCallback(
    (item: T, index: number) => keyExtractor(item, index),
    [keyExtractor]
  );

  // Render loading state
  const renderLoading = useMemo(
    () => (
      <View style={[styles.loadingContainer, loadingContainerStyle]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Carregando...</Text>
      </View>
    ),
    [loadingContainerStyle]
  );

  // Render empty state
  const renderEmpty = useMemo(
    () =>
      !loading && (
        <View style={[styles.emptyContainer, emptyContainerStyle]}>
          {emptyIcon}
          <Text style={[styles.emptyText, emptyTextStyle]}>{emptyText}</Text>
          {emptySubtext && (
            <Text style={[styles.emptySubtext, emptySubtextStyle]}>
              {emptySubtext}
            </Text>
          )}
        </View>
      ),
    [
      loading,
      emptyIcon,
      emptyText,
      emptySubtext,
      emptyContainerStyle,
      emptyTextStyle,
      emptySubtextStyle,
    ]
  );

  // If loading and no data, show loading indicator
  if (loading && data.length === 0) {
    return renderLoading;
  }

  return (
    <FlatList
      data={data}
      renderItem={renderItemMemoized}
      keyExtractor={keyExtractorMemoized}
      ListEmptyComponent={renderEmpty}
      contentContainerStyle={[
        styles.contentContainer,
        data.length === 0 && styles.emptyContentContainer,
        contentContainerStyle,
      ]}
      initialNumToRender={initialNumToRender}
      maxToRenderPerBatch={maxToRenderPerBatch}
      windowSize={windowSize}
      updateCellsBatchingPeriod={updateCellsBatchingPeriod}
      onEndReachedThreshold={onEndReachedThreshold}
      removeClippedSubviews={removeClippedSubviews}
      maintainVisibleContentPosition={maintainVisibleContentPosition}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    flexGrow: 1,
  },
  emptyContentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.textLight,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 20,
  },
});

// Memoize the entire component to prevent unnecessary re-renders
export const OptimizedFlatList = memo(
  OptimizedFlatListComponent
) as typeof OptimizedFlatListComponent;
