import React from "react";
import { View, StyleSheet, ViewStyle } from "react-native";
import { colors } from "@/constants/colors";
import { LinearGradient } from "expo-linear-gradient";

interface ProgressBarProps {
  progress: number;
  color?: string;
  gradientColors?: string[];
  height?: number;
  backgroundColor?: string;
  borderRadius?: number;
  style?: ViewStyle;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  color = colors.primary,
  gradientColors,
  height = 8,
  backgroundColor = colors.backgroundDark,
  borderRadius = 4,
  style,
}) => {
  // Ensure progress is between 0 and 100
  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  return (
    <View
      style={[
        styles.container,
        { height, backgroundColor, borderRadius },
        style,
      ]}
    >
      <LinearGradient
        colors={gradientColors || [color, `${color}80`]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[
          styles.progress,
          {
            width: `${clampedProgress}%`,
            borderRadius,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    overflow: "hidden",
  },
  progress: {
    height: "100%",
  },
});