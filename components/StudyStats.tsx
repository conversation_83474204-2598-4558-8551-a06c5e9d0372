import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { colors } from '@/constants/colors';
import { theme } from '@/constants/theme';
import { useTimerStore } from '@/store/timerStore';
import { Clock, Calendar, Target, ChevronRight, Trophy, Flame, BookOpen } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { formatDateISO } from '@/utils/dateUtils';
import { GlassCard } from '@/components/GlassCard';

export const StudyStats: React.FC = () => {
  const router = useRouter();
  const { fetchTodayStudyTime } = useTimerStore();
  const [todayStudyTime, setTodayStudyTime] = useState(0);
  const [sessionsCompleted, setSessionsCompleted] = useState(0);
  const [weeklyStreak, setWeeklyStreak] = useState(0);
  const [totalFlashcards, setTotalFlashcards] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadStudyTime = async () => {
      setIsLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const today = formatDateISO(new Date());

        // Carregar dados de estudo de hoje
        const { data, error } = await supabase
          .from('study_sessions')
          .select('total_time, sessions_completed')
          .eq('user_id', user.id)
          .eq('date', today)
          .single();

        if (!error && data) {
          setTodayStudyTime(data.total_time || 0);
          setSessionsCompleted(data.sessions_completed || 0);
        } else {
          setTodayStudyTime(0);
          setSessionsCompleted(0);
        }

        // Carregar streak semanal (últimos 7 dias com estudo)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const { data: streakData } = await supabase
          .from('study_sessions')
          .select('date')
          .eq('user_id', user.id)
          .gte('date', formatDateISO(sevenDaysAgo))
          .gt('total_time', 0);

        setWeeklyStreak(streakData?.length || 0);

        // Carregar total de flashcards
        const { data: flashcardsData } = await supabase
          .from('flashcard_sets')
          .select('id')
          .eq('user_id', user.id);

        setTotalFlashcards(flashcardsData?.length || 0);

      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStudyTime();

    // Atualizar a cada 60 segundos
    const interval = setInterval(loadStudyTime, 60000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${mins}min`;
    }
    return `${mins} minutos`;
  };

  const handlePress = () => {
    router.push('/statistics');
  };

  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Suas Estatísticas</Text>
        <ChevronRight size={theme.sizes.icon.sm} color={colors.textLight} />
      </View>

      <View style={styles.statsContainer}>
        <GlassCard style={styles.statCard} gradient>
          <View style={styles.statIconContainer}>
            <Clock size={theme.sizes.icon.sm} color={colors.primary} />
          </View>
          <Text style={styles.statValue}>
            {isLoading ? '...' : formatTime(todayStudyTime)}
          </Text>
          <Text style={styles.statLabel}>Hoje</Text>
        </GlassCard>

        <GlassCard style={styles.statCard} gradient>
          <View style={styles.statIconContainer}>
            <Flame size={theme.sizes.icon.sm} color={colors.secondary} />
          </View>
          <Text style={styles.statValue}>
            {isLoading ? '...' : weeklyStreak}
          </Text>
          <Text style={styles.statLabel}>Dias Ativos</Text>
        </GlassCard>

        <GlassCard style={styles.statCard} gradient>
          <View style={styles.statIconContainer}>
            <BookOpen size={theme.sizes.icon.sm} color={colors.accent1} />
          </View>
          <Text style={styles.statValue}>
            {isLoading ? '...' : totalFlashcards}
          </Text>
          <Text style={styles.statLabel}>Flashcards</Text>
        </GlassCard>
      </View>
    </Pressable>
  );
};



const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8, // Reduzido de 12 para 8 para acomodar 3 cards
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 10, // Reduzido de 12 para 10
  },
  statIconContainer: {
    width: theme.sizes.iconContainer.sm,
    height: theme.sizes.iconContainer.sm,
    borderRadius: theme.sizes.iconContainer.sm / 2,
    backgroundColor: `${colors.white}80`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 16, // Reduzido de 18 para 16
    fontWeight: '700',
    color: colors.text,
    marginBottom: 2, // Reduzido de 4 para 2
  },
  statLabel: {
    fontSize: 11, // Reduzido de 12 para 11
    color: colors.textLight,
    textAlign: 'center',
  },
});
