import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useRouter, useSegments } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { theme } from '@/constants/theme';
import { colors } from '@/constants/colors';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

/**
 * Componente de segurança para proteger rotas que exigem autenticação
 *
 * @param children - Conteúdo a ser renderizado quando a autenticação for bem-sucedida
 * @param fallback - Conteúdo a ser renderizado enquanto verifica a autenticação (opcional)
 * @param requireAuth - Se true, redireciona para login quando não autenticado. Se false, redireciona para home quando autenticado. (padrão: true)
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  fallback,
  requireAuth = true,
}) => {
  const { user, session, loading } = useAuthStore();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (loading) return;

    const inAuthGroup = segments[0] === '(auth)';
    const isAuthRoute = segments[0] === 'login' || segments[0] === 'register' || segments[0] === 'forgot-password';

    if (requireAuth) {
      // Caso 1: Requer autenticação
      if (!session && !inAuthGroup && !isAuthRoute) {
        // Usuário não está autenticado e não está em uma rota de autenticação
        router.replace('/login');
      }
    } else {
      // Caso 2: Não requer autenticação (ex: páginas de login)
      if (session && (inAuthGroup || isAuthRoute)) {
        // Usuário já está autenticado mas está tentando acessar uma página de autenticação
        router.replace('/(tabs)');
      }
    }
  }, [session, segments, loading, requireAuth, router]);

  // Renderizar fallback enquanto verifica autenticação
  if (loading) {
    return fallback || (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Verificando autenticação...</Text>
      </View>
    );
  }

  // Verificar se o usuário pode acessar a rota
  const canAccess = requireAuth ? !!session : !session;

  if (!canAccess) {
    // Não renderizar nada enquanto redireciona
    return null;
  }

  // Renderizar o conteúdo protegido
  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...theme.typography.subtitle1,
    marginTop: theme.spacing.md,
  },
});

export default AuthGuard;
