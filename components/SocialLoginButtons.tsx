import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '@/constants/theme';
import Constants from 'expo-constants';

// Verificar se estamos no Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

interface SocialLoginButtonsProps {
  onGooglePress: () => void;
  onApplePress: () => void;
  loading?: boolean;
  disabled?: boolean;
}

export const SocialLoginButtons: React.FC<SocialLoginButtonsProps> = ({
  onGooglePress,
  onApplePress,
  loading = false,
  disabled = false,
}) => {
  const handleSocialLogin = (provider: 'google' | 'apple', callback: () => void) => {
    if (isExpoGo) {
      Alert.alert(
        'Login Social no Expo Go',
        `O login com ${provider === 'google' ? 'Google' : 'Apple'} não está disponível no Expo Go.\n\nPara testar o login social, você precisa:\n\n1. Fazer um build de desenvolvimento\n2. Ou usar o login com email/senha`,
        [
          { text: 'Entendi', style: 'default' },
          {
            text: 'Usar Email/Senha',
            style: 'default',
            onPress: () => {
              // Não fazer nada, deixar o usuário usar o login normal
            }
          }
        ]
      );
      return;
    }

    callback();
  };

  return (
    <View style={styles.container}>
      <View style={styles.dividerContainer}>
        <View style={styles.dividerLine} />
        <Text style={styles.dividerText}>ou continue com</Text>
        <View style={styles.dividerLine} />
      </View>

      {isExpoGo && (
        <View style={styles.expoGoNotice}>
          <Ionicons name="information-circle" size={16} color="rgba(255, 255, 255, 0.7)" />
          <Text style={styles.expoGoText}>
            Login social disponível apenas em builds de desenvolvimento
          </Text>
        </View>
      )}

      <View style={styles.buttonsContainer}>
        {/* Botão Google */}
        <TouchableOpacity
          style={[
            styles.socialButton,
            styles.googleButton,
            (loading || disabled || isExpoGo) && styles.disabledButton
          ]}
          onPress={() => handleSocialLogin('google', onGooglePress)}
          disabled={loading || disabled}
          activeOpacity={0.8}
        >
          {loading ? (
            <ActivityIndicator size="small" color={theme.colors.text} />
          ) : (
            <>
              <Ionicons name="logo-google" size={20} color="#DB4437" />
              <Text style={[styles.buttonText, styles.googleText]}>Google</Text>
            </>
          )}
        </TouchableOpacity>

        {/* Botão Apple - apenas no iOS */}
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            style={[
              styles.socialButton,
              styles.appleButton,
              (loading || disabled || isExpoGo) && styles.disabledButton
            ]}
            onPress={() => handleSocialLogin('apple', onApplePress)}
            disabled={loading || disabled}
            activeOpacity={0.8}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <Ionicons name="logo-apple" size={20} color="#FFFFFF" />
                <Text style={[styles.buttonText, styles.appleText]}>Apple</Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginVertical: theme.spacing.lg,
  },

  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },

  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },

  dividerText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: theme.typography.size.sm,
    fontWeight: theme.typography.weight.medium,
    marginHorizontal: theme.spacing.md,
  },

  expoGoNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: theme.borderRadius.sm,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    gap: theme.spacing.xs,
  },

  expoGoText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: theme.typography.size.xs,
    fontWeight: theme.typography.weight.medium,
    textAlign: 'center',
    flex: 1,
  },

  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.md,
  },
  
  socialButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
    minHeight: 50,

    // Sombra
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  
  googleButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  
  appleButton: {
    backgroundColor: '#000000',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  
  disabledButton: {
    opacity: 0.6,
  },
  
  buttonText: {
    fontSize: theme.typography.size.md,
    fontWeight: theme.typography.weight.semibold,
  },
  
  googleText: {
    color: '#333333',
  },
  
  appleText: {
    color: '#FFFFFF',
  },
});

export default SocialLoginButtons;
