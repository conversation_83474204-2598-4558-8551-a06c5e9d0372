import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Switch,
  Modal,
  Platform,
  Alert
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { TodoItem } from '@/types';
import { useStudyStore } from '@/store/studyStore';
import { Calendar, Clock, BookOpen, Bell, Tag, X, AlertTriangle } from 'lucide-react-native';
import { RecurrenceSelector, RecurrenceSettings } from '@/components/RecurrenceSelector';

interface TodoFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialTodo?: Partial<TodoItem>;
}

export const TodoForm: React.FC<TodoFormProps> = ({
  visible,
  onClose,
  onSave,
  initialTodo
}) => {
  const { subjects } = useStudyStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState<Date | null>(addDays(new Date(), 1));
  const [hasDueDate, setHasDueDate] = useState(true);
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium');
  const [subject, setSubject] = useState<string | null>(null);
  const [subjectId, setSubjectId] = useState<string | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [reminder, setReminder] = useState(false);
  const [reminderTime, setReminderTime] = useState<Date | null>(null);
  const [recurrenceSettings, setRecurrenceSettings] = useState<RecurrenceSettings>({
    type: 'none',
    endType: 'never',
    interval: 1,
    weekdays: [new Date().getDay()],
    endDate: (() => {
      const date = new Date();
      date.setMonth(date.getMonth() + 1);
      return date;
    })()
  });

  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);
  const [showSubjectPicker, setShowSubjectPicker] = useState(false);
  const [showPriorityPicker, setShowPriorityPicker] = useState(false);

  // Priority options with labels
  const priorityOptions = [
    { value: 'high', label: 'Alta', color: colors.error },
    { value: 'medium', label: 'Média', color: colors.warning },
    { value: 'low', label: 'Baixa', color: colors.success },
  ];

  useEffect(() => {
    if (visible) {
      // Initialize form with initial values or defaults
      if (initialTodo) {
        setTitle(initialTodo.title || '');
        setDescription(initialTodo.description || '');
        setDueDate(initialTodo.dueDate ? parseISO(initialTodo.dueDate) : null);
        setHasDueDate(!!initialTodo.dueDate);
        setPriority(initialTodo.priority || 'medium');
        setSubject(initialTodo.subject || null);
        setSubjectId(initialTodo.subject_id || null);
        setTags(initialTodo.tags || []);

        // Load recurrence settings if available
        if (initialTodo.recurrence) {
          const recSettings: RecurrenceSettings = {
            type: initialTodo.recurrence as any,
            endType: initialTodo.recurrenceSettings?.endType || 'never',
            interval: initialTodo.recurrenceSettings?.interval || 1,
            weekdays: initialTodo.recurrenceSettings?.weekdays || [new Date().getDay()],
            endDate: initialTodo.recurrenceEndDate ? new Date(initialTodo.recurrenceEndDate) : undefined,
            occurrences: initialTodo.recurrenceSettings?.occurrences
          };
          setRecurrenceSettings(recSettings);
        }
      } else {
        // Default values for new todo
        setTitle('');
        setDescription('');
        setDueDate(addDays(new Date(), 1));
        setHasDueDate(true);
        setPriority('medium');
        setSubject(null);
        setSubjectId(null);
        setTags([]);
      }

      setNewTag('');
      setReminder(false);
      setReminderTime(null);
      setRecurrenceSettings({
        type: 'none',
        endType: 'never',
        interval: 1,
        weekdays: [new Date().getDay()],
        endDate: (() => {
          const date = new Date();
          date.setMonth(date.getMonth() + 1);
          return date;
        })()
      });
    }
  }, [visible, initialTodo]);

  useEffect(() => {
    // Set reminder time to 9 AM on due date if due date exists and reminder is enabled
    if (reminder && dueDate && !reminderTime) {
      const reminderDate = new Date(dueDate);
      reminderDate.setHours(9, 0, 0, 0);
      setReminderTime(reminderDate);
    }
  }, [reminder, dueDate]);

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título da tarefa é obrigatório.');
      return;
    }

    const todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      dueDate: hasDueDate && dueDate ? dueDate.toISOString() : undefined,
      priority,
      completed: false,
      subject: subject || undefined,
      subject_id: subjectId || undefined,
      tags,
      reminderTime: reminder && reminderTime ? reminderTime.toISOString() : undefined,
      recurrence: recurrenceSettings.type !== 'none' ? recurrenceSettings.type : undefined,
      recurrenceEndDate: recurrenceSettings.type !== 'none' && recurrenceSettings.endType === 'on_date' && recurrenceSettings.endDate
        ? recurrenceSettings.endDate.toISOString()
        : undefined,
      recurrenceSettings: recurrenceSettings.type !== 'none' ? {
        interval: recurrenceSettings.interval,
        weekdays: recurrenceSettings.weekdays,
        monthDay: recurrenceSettings.monthDay,
        endType: recurrenceSettings.endType,
        occurrences: recurrenceSettings.occurrences
      } : undefined,
    };

    onSave(todo);
    onClose();
  };

  const handleDueDateChange = (event: any, selectedDate?: Date) => {
    setShowDueDatePicker(false);
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };

  const handleReminderTimeChange = (event: any, selectedTime?: Date) => {
    setShowReminderTimePicker(false);
    if (selectedTime) {
      setReminderTime(selectedTime);
    }
  };

  const handleSubjectSelect = (subjectTitle: string, subjectId: string) => {
    setSubject(subjectTitle);
    setSubjectId(subjectId);
    setShowSubjectPicker(false);
  };

  const handlePrioritySelect = (selectedPriority: 'high' | 'medium' | 'low') => {
    setPriority(selectedPriority);
    setShowPriorityPicker(false);
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const getPriorityLabel = (priorityValue: string) => {
    const priority = priorityOptions.find(p => p.value === priorityValue);
    return priority ? priority.label : 'Média';
  };

  const getPriorityColor = (priorityValue: string) => {
    const priority = priorityOptions.find(p => p.value === priorityValue);
    return priority ? priority.color : colors.warning;
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {initialTodo?.id ? 'Editar Tarefa' : 'Nova Tarefa'}
            </Text>
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Título</Text>
              <TextInput
                style={styles.input}
                value={title}
                onChangeText={setTitle}
                placeholder="Título da tarefa"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Descrição da tarefa"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Data de vencimento</Text>
                <Switch
                  value={hasDueDate}
                  onValueChange={setHasDueDate}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={hasDueDate ? colors.primary : colors.white}
                />
              </View>
              {hasDueDate && (
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowDueDatePicker(true)}
                >
                  <Calendar size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {dueDate ? format(dueDate, 'dd/MM/yyyy', { locale: ptBR }) : 'Selecionar data'}
                  </Text>
                </Pressable>
              )}
              {showDueDatePicker && (
                <DateTimePicker
                  value={dueDate || new Date()}
                  mode="date"
                  display="default"
                  onChange={handleDueDateChange}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Prioridade</Text>
              <Pressable
                style={[styles.priorityButton, { borderColor: getPriorityColor(priority) }]}
                onPress={() => setShowPriorityPicker(true)}
              >
                <AlertTriangle size={20} color={getPriorityColor(priority)} />
                <Text style={[styles.priorityButtonText, { color: getPriorityColor(priority) }]}>
                  {getPriorityLabel(priority)}
                </Text>
              </Pressable>
              <Modal
                visible={showPriorityPicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowPriorityPicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Prioridade</Text>
                      <Pressable onPress={() => setShowPriorityPicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <ScrollView style={styles.pickerScrollView}>
                      {priorityOptions.map((option) => (
                        <Pressable
                          key={option.value}
                          style={[styles.priorityItem, { borderLeftColor: option.color }]}
                          onPress={() => handlePrioritySelect(option.value as any)}
                        >
                          <AlertTriangle size={20} color={option.color} />
                          <Text style={styles.priorityItemText}>{option.label}</Text>
                        </Pressable>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              </Modal>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Matéria</Text>
              <Pressable
                style={styles.pickerButton}
                onPress={() => setShowSubjectPicker(true)}
              >
                <BookOpen size={20} color={colors.primary} />
                <Text style={styles.pickerButtonText}>
                  {subject || 'Selecionar matéria'}
                </Text>
              </Pressable>
              <Modal
                visible={showSubjectPicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowSubjectPicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Matéria</Text>
                      <Pressable onPress={() => setShowSubjectPicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <ScrollView style={styles.pickerScrollView}>
                      <Pressable
                        style={styles.pickerItem}
                        onPress={() => {
                          setSubject(null);
                          setSubjectId(null);
                          setShowSubjectPicker(false);
                        }}
                      >
                        <Text style={styles.pickerItemText}>Nenhuma</Text>
                      </Pressable>
                      {subjects.map((subject) => (
                        <Pressable
                          key={subject.id}
                          style={styles.pickerItem}
                          onPress={() => handleSubjectSelect(subject.title, subject.id)}
                        >
                          <Text style={styles.pickerItemText}>{subject.title}</Text>
                        </Pressable>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              </Modal>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tags</Text>
              <View style={styles.tagInputContainer}>
                <TextInput
                  style={styles.tagInput}
                  value={newTag}
                  onChangeText={setNewTag}
                  placeholder="Adicionar tag"
                  placeholderTextColor={colors.textLight}
                  onSubmitEditing={addTag}
                />
                <Pressable style={styles.addTagButton} onPress={addTag}>
                  <Text style={styles.addTagButtonText}>Adicionar</Text>
                </Pressable>
              </View>
              <View style={styles.tagsContainer}>
                {tags.map((tag) => (
                  <View key={tag} style={styles.tag}>
                    <Tag size={16} color={colors.primary} />
                    <Text style={styles.tagText}>{tag}</Text>
                    <Pressable style={styles.removeTagButton} onPress={() => removeTag(tag)}>
                      <X size={16} color={colors.textLight} />
                    </Pressable>
                  </View>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Lembrete</Text>
                <Switch
                  value={reminder}
                  onValueChange={setReminder}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={reminder ? colors.primary : colors.white}
                />
              </View>
              {reminder && (
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowReminderTimePicker(true)}
                >
                  <Bell size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {reminderTime
                      ? format(reminderTime, 'dd/MM/yyyy HH:mm', { locale: ptBR })
                      : 'Selecionar horário do lembrete'}
                  </Text>
                </Pressable>
              )}
              {showReminderTimePicker && (
                <DateTimePicker
                  value={reminderTime || new Date()}
                  mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                  display="default"
                  onChange={handleReminderTimeChange}
                />
              )}
            </View>

            <RecurrenceSelector
              value={recurrenceSettings}
              onChange={setRecurrenceSettings}
            />

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="secondary"
                size="medium"
                style={styles.button}
              />
              <Button
                title="Salvar"
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={styles.button}
              />
            </View>
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
    zIndex: 9999,
  },
  formContainer: {
    width: '100%',
    maxHeight: '90%',
    borderRadius: 16,
    padding: 20,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
  },
  closeButton: {
    padding: 10,
    backgroundColor: colors.card,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  scrollView: {
    flex: 1,
  },
  formGroup: {
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  priorityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderLeftWidth: 4,
  },
  priorityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  pickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  pickerModalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    maxHeight: '70%',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  pickerScrollView: {
    maxHeight: 300,
  },
  pickerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  pickerItemText: {
    fontSize: 16,
    color: colors.text,
  },
  priorityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    borderLeftWidth: 4,
  },
  priorityItemText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagInput: {
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: 8,
  },
  addTagButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 12,
  },
  addTagButtonText: {
    color: colors.white,
    fontWeight: '600',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}20`,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    margin: 4,
  },
  tagText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 4,
    marginRight: 4,
  },
  removeTagButton: {
    padding: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});
