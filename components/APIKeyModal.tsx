import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Pressable,
  Modal,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { X, Key, Check } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { setOpenAIApiKey } from '@/services/openai';
import { hasApiKey, initializeOpenAIAssistants } from '@/services/openaiAssistants';

interface APIKeyModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const APIKeyModal: React.FC<APIKeyModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [apiKey, setApiKey] = useState('');
  const [loading, setLoading] = useState(false);
  const [hasKey, setHasKey] = useState(false);

  useEffect(() => {
    if (visible) {
      checkApiKey();
      initializeOpenAIAssistants().then(initialized => {
        if (initialized) {
          setHasKey(true);
          if (onSuccess) onSuccess();
          onClose();
        }
      });
    }
  }, [visible]);

  const checkApiKey = async () => {
    const keyExists = await hasApiKey();
    setHasKey(keyExists);
  };

  const handleSave = async () => {
    if (!apiKey.trim()) {
      Alert.alert('Erro', 'Por favor, insira uma chave de API válida.');
      return;
    }

    setLoading(true);
    try {
      const success = await setOpenAIApiKey(apiKey);
      if (success) {
        setHasKey(true);
        Alert.alert('Sucesso', 'Chave de API salva com sucesso!');
        if (onSuccess) onSuccess();
        onClose();
      } else {
        Alert.alert('Erro', 'Não foi possível salvar a chave de API.');
      }
    } catch (error) {
      Alert.alert('Erro', 'Ocorreu um erro ao salvar a chave de API.');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <GlassCard style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Configurar API OpenAI</Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.description}>
              Para usar os recursos de IA, você precisa fornecer sua chave de API da OpenAI.
              Você pode obter uma chave em{' '}
              <Text style={styles.link}>https://platform.openai.com/api-keys</Text>
            </Text>

            <View style={styles.inputContainer}>
              <Key size={20} color={colors.textLight} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Insira sua chave de API OpenAI"
                placeholderTextColor={colors.textMedium}
                value={apiKey}
                onChangeText={setApiKey}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <Text style={styles.securityNote}>
              Sua chave será armazenada apenas no seu dispositivo e nunca será compartilhada.
            </Text>

            {hasKey && (
              <View style={styles.successMessage}>
                <Check size={16} color={colors.success} />
                <Text style={styles.successText}>
                  Você já tem uma chave configurada. Insira uma nova para substituí-la.
                </Text>
              </View>
            )}
          </View>

          <View style={styles.modalFooter}>
            <Pressable
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.buttonText}>Cancelar</Text>
            </Pressable>
            <Pressable
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Salvar</Text>
              )}
            </Pressable>
          </View>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 500,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: colors.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  description: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
    lineHeight: 22,
  },
  link: {
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  inputIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: colors.text,
  },
  securityNote: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 16,
    fontStyle: 'italic',
    backgroundColor: `${colors.primary}10`,
    padding: 10,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
  },
  successMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  successText: {
    fontSize: 14,
    color: colors.success,
    marginLeft: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 12,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
});
