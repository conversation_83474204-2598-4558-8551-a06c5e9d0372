import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, Pressable, ScrollView } from 'react-native';
import { colors } from '@/constants/colors';
import { X, Check } from 'lucide-react-native';
import { Button } from './Button';
import { Subject, Separator } from '@/types';

interface AssignSeparatorModalProps {
  visible: boolean;
  onClose: () => void;
  onAssign: (separatorId: string | null) => Promise<void>;
  separators: Separator[];
  subject: Subject | null;
}

export const AssignSeparatorModal: React.FC<AssignSeparatorModalProps> = ({
  visible,
  onClose,
  onAssign,
  separators,
  subject,
}) => {
  const [selectedSeparator, setSelectedSeparator] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (subject) {
      setSelectedSeparator(subject.separator_id || null);
    }
  }, [subject]);

  // Reset state when modal is closed
  useEffect(() => {
    if (!visible) {
      setIsSaving(false);
    }
  }, [visible]);

  const handleSave = async () => {
    if (!subject) return;
    if (isSaving) return; // Prevent double clicks
    
    setIsSaving(true);
    try {
      await onAssign(selectedSeparator);
      onClose();
    } catch (error) {
      console.error('Error assigning separator:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (!subject) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Atribuir Separador</Text>
            <Pressable onPress={onClose} hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <Text style={styles.subjectTitle}>{subject.title}</Text>
          <Text style={styles.instructionText}>Selecione um separador para esta matéria:</Text>

          <ScrollView style={styles.separatorsList}>
            <Pressable
              style={[
                styles.separatorOption,
                selectedSeparator === null && styles.selectedSeparatorOption,
              ]}
              onPress={() => setSelectedSeparator(null)}
            >
              <Text style={styles.separatorText}>Nenhum separador</Text>
              {selectedSeparator === null && (
                <Check size={20} color={colors.primary} />
              )}
            </Pressable>

            {separators.map((separator) => (
              <Pressable
                key={separator.id}
                style={[
                  styles.separatorOption,
                  { borderLeftColor: separator.color, borderLeftWidth: 4 },
                  selectedSeparator === separator.id && styles.selectedSeparatorOption,
                ]}
                onPress={() => setSelectedSeparator(separator.id)}
              >
                <Text style={styles.separatorText}>{separator.title}</Text>
                {selectedSeparator === separator.id && (
                  <Check size={20} color={colors.primary} />
                )}
              </Pressable>
            ))}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <Button
              title="Cancelar"
              onPress={onClose}
              variant="outline"
              size="medium"
            />
            <Button
              title="Salvar"
              onPress={handleSave}
              variant="primary"
              size="medium"
              loading={isSaving}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  subjectTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 12,
  },
  separatorsList: {
    maxHeight: 200,
    marginBottom: 24,
  },
  separatorOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
  },
  selectedSeparatorOption: {
    backgroundColor: `${colors.primary}15`,
  },
  separatorText: {
    fontSize: 16,
    color: colors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
});
