import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { colors } from "@/constants/colors";
import { StudyGroupMaterial } from "@/types";
import { FileText, BookOpen, Link, File, ChevronRight } from "lucide-react-native";

interface StudyGroupMaterialCardProps {
  material: StudyGroupMaterial;
  onPress: (material: StudyGroupMaterial) => void;
}

export const StudyGroupMaterialCard: React.FC<StudyGroupMaterialCardProps> = ({
  material,
  onPress,
}) => {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(material)}
    >
      <View style={styles.materialIconContainer}>
        {material.type === 'note' && <FileText size={20} color={colors.white} />}
        {material.type === 'flashcard' && <BookOpen size={20} color={colors.white} />}
        {material.type === 'document' && <File size={20} color={colors.white} />}
        {material.type === 'link' && <Link size={20} color={colors.white} />}
      </View>
      <View style={styles.materialInfo}>
        <Text style={styles.materialTitle}>{material.title}</Text>
        <Text style={styles.materialDescription} numberOfLines={1}>
          {material.description || `Compartilhado por ${material.userName}`}
        </Text>
      </View>
      <ChevronRight size={20} color={colors.textLight} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  materialIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  materialInfo: {
    flex: 1,
  },
  materialTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 2,
  },
  materialDescription: {
    fontSize: 14,
    color: colors.textMedium,
  },
});
