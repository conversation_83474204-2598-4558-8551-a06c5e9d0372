import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { StudyPlan } from "@/types";
import { colors } from "@/constants/colors";
import { Calendar, Clock } from "lucide-react-native";
import { ProgressBar } from "./ProgressBar";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";

interface StudyPlanCardProps {
  plan: StudyPlan;
  onPress: (plan: StudyPlan) => void;
}

export const StudyPlanCard: React.FC<StudyPlanCardProps> = ({ plan, onPress }) => {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(plan)}
    >
      <LinearGradient
        colors={["rgba(255,255,255,0.8)", "rgba(255,255,255,0.5)"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.cardGradient}
      >
        <Text style={styles.title}>{plan.title}</Text>
        
        <View style={styles.infoContainer}>
          <View style={styles.infoItem}>
            <Calendar size={16} color={colors.primary} />
            <Text style={styles.infoText}>{plan.duration}</Text>
          </View>
          <View style={styles.infoItem}>
            <Clock size={16} color={colors.primary} />
            <Text style={styles.infoText}>
              {plan.sessions.reduce(
                (total, session) =>
                  total +
                  session.activities.reduce(
                    (sessionTotal, activity) => sessionTotal + activity.duration,
                    0
                  ),
                0
              )}{" "}
              min/semana
            </Text>
          </View>
        </View>
        
        <View style={styles.subjectsContainer}>
          {plan.subjects.slice(0, 3).map((subject, index) => (
            <GlassCard key={index} style={styles.subjectTag}>
              <Text style={styles.subjectText}>{subject}</Text>
            </GlassCard>
          ))}
          {plan.subjects.length > 3 && (
            <GlassCard style={styles.subjectTag}>
              <Text style={styles.subjectText}>+{plan.subjects.length - 3}</Text>
            </GlassCard>
          )}
        </View>
        
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>Progresso: {plan.progress}%</Text>
          <ProgressBar 
            progress={plan.progress} 
            gradientColors={colors.primaryGradient}
            height={8}
            backgroundColor={`${colors.primary}20`}
          />
        </View>
      </LinearGradient>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardGradient: {
    padding: 16,
    borderRadius: 20,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 12,
  },
  infoContainer: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 16,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 6,
  },
  subjectsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
    gap: 8,
  },
  subjectTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  subjectText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: "500",
  },
  progressContainer: {
    marginTop: 4,
  },
  progressText: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
});