import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { MindMap } from "@/types";
import { colors } from "@/constants/colors";
import { Network, Clock, FileText, BrainCircuit, GitBranch, Circle, Square, Triangle, Hexagon } from "lucide-react-native";
import { formatDistanceToNow } from "@/utils/dateUtils";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";

interface MindMapCardProps {
  mindMap: MindMap;
  onPress: (mindMap: MindMap) => void;
}

export const MindMapCard: React.FC<MindMapCardProps> = ({ mindMap, onPress }) => {
  // Determine icon based on map type
  const getIcon = () => {
    switch (mindMap.type) {
      case "concept":
        return <Network size={20} color="#fff" />;
      case "flow":
        return <GitBranch size={20} color="#fff" />;
      case "brain":
        return <BrainCircuit size={20} color="#fff" />;
      default:
        return <Network size={20} color="#fff" />;
    }
  };

  // Determine color based on map type or use the one from the mindMap
  const getColor = () => {
    if (mindMap.color) return mindMap.color;
    
    switch (mindMap.type) {
      case "concept":
        return colors.accent1;
      case "flow":
        return colors.secondary;
      case "brain":
        return colors.primary;
      default:
        return colors.primary;
    }
  };
  
  // Get node shapes count
  const getNodeShapesCount = () => {
    const shapes = {
      circle: 0,
      square: 0,
      triangle: 0,
      hexagon: 0,
    };
    
    mindMap.nodes?.forEach(node => {
      if (node.shape) {
        shapes[node.shape]++;
      } else {
        shapes.circle++; // Default shape
      }
    });
    
    return shapes;
  };
  
  const nodeShapes = getNodeShapesCount();
  
  // Get node styles count
  const getNodeStylesCount = () => {
    const styles = {
      filled: 0,
      outline: 0,
      minimal: 0,
    };
    
    mindMap.nodes?.forEach(node => {
      if (node.style) {
        styles[node.style]++;
      } else {
        styles.filled++; // Default style
      }
    });
    
    return styles;
  };
  
  const nodeStyles = getNodeStylesCount();
  
  // Get connection styles count
  const getConnectionStylesCount = () => {
    const styles = {
      solid: 0,
      dashed: 0,
      dotted: 0,
    };
    
    mindMap.connections?.forEach(conn => {
      if (conn.style) {
        styles[conn.style]++;
      } else {
        styles.solid++; // Default style
      }
    });
    
    return styles;
  };
  
  const connectionStyles = getConnectionStylesCount();

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(mindMap)}
    >
      <LinearGradient
        colors={["rgba(255,255,255,0.8)", "rgba(255,255,255,0.5)"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.cardGradient}
      >
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: getColor() }]}>
            {getIcon()}
          </View>
          <Text style={styles.subject}>{mindMap.subject}</Text>
        </View>
        
        <Text style={styles.title}>{mindMap.title}</Text>
        
        <View style={styles.statsContainer}>
          <GlassCard style={styles.stat} gradient>
            <Text style={styles.statValue}>{mindMap.nodes?.length || 0}</Text>
            <Text style={styles.statLabel}>Nós</Text>
          </GlassCard>
          <GlassCard style={styles.stat} gradient>
            <Text style={styles.statValue}>{mindMap.connections?.length || 0}</Text>
            <Text style={styles.statLabel}>Conexões</Text>
          </GlassCard>
        </View>
        
        {/* Node shapes preview */}
        {mindMap.nodes && mindMap.nodes.length > 0 && (
          <View style={styles.shapesPreview}>
            {nodeShapes.circle > 0 && (
              <View style={styles.shapePreviewItem}>
                <Circle size={16} color={colors.textLight} />
                <Text style={styles.shapePreviewCount}>{nodeShapes.circle}</Text>
              </View>
            )}
            {nodeShapes.square > 0 && (
              <View style={styles.shapePreviewItem}>
                <Square size={16} color={colors.textLight} />
                <Text style={styles.shapePreviewCount}>{nodeShapes.square}</Text>
              </View>
            )}
            {nodeShapes.triangle > 0 && (
              <View style={styles.shapePreviewItem}>
                <Triangle size={16} color={colors.textLight} />
                <Text style={styles.shapePreviewCount}>{nodeShapes.triangle}</Text>
              </View>
            )}
            {nodeShapes.hexagon > 0 && (
              <View style={styles.shapePreviewItem}>
                <Hexagon size={16} color={colors.textLight} />
                <Text style={styles.shapePreviewCount}>{nodeShapes.hexagon}</Text>
              </View>
            )}
          </View>
        )}
        
        <View style={styles.footer}>
          <View style={styles.footerItem}>
            <Clock size={14} color={colors.textLight} />
            <Text style={styles.footerText}>
              {mindMap.lastEdited ? formatDistanceToNow(new Date(mindMap.lastEdited)) : "Novo"}
            </Text>
          </View>
          {mindMap.description && (
            <View style={styles.footerItem}>
              <FileText size={14} color={colors.textLight} />
              <Text style={styles.footerText} numberOfLines={1}>
                {mindMap.description}
              </Text>
            </View>
          )}
        </View>
      </LinearGradient>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardGradient: {
    padding: 16,
    borderRadius: 20,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  subject: {
    fontSize: 14,
    color: colors.textLight,
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 12,
  },
  stat: {
    flex: 1,
    alignItems: "center",
    padding: 12,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
  },
  shapesPreview: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 16,
    justifyContent: "center",
  },
  shapePreviewItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  shapePreviewCount: {
    fontSize: 12,
    color: colors.textLight,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  footerItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  footerText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
});