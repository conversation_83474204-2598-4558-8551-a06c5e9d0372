import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Switch,
  Modal,
  Platform,
  Alert,
  FlatList
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO, addHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarEvent, TodoItem } from '@/types';
import { useCalendarStore } from '@/store/calendarStore';
import { useStudyStore } from '@/store/studyStore';
import { Calendar, Clock, BookOpen, Bell, Repeat, X, CheckSquare, Plus, Edit2 } from 'lucide-react-native';

interface EventFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialEvent?: Partial<CalendarEvent>;
  selectedDate?: string;
  onAddTodo?: (eventId: string, todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt' | 'event_id'>) => Promise<TodoItem | null>;
}

export const EventForm: React.FC<EventFormProps> = ({
  visible,
  onClose,
  onSave,
  initialEvent,
  selectedDate,
  onAddTodo
}) => {
  const { subjects } = useStudyStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(new Date().setHours(new Date().getHours() + 1)));
  const [allDay, setAllDay] = useState(false);
  const [color, setColor] = useState(colors.primary);
  const [subject, setSubject] = useState<string | null>(null);
  const [subjectId, setSubjectId] = useState<string | null>(null);
  const [type, setType] = useState<'study' | 'exam' | 'assignment' | 'meeting' | 'other'>('study');
  const [reminder, setReminder] = useState(false);
  const [reminderTime, setReminderTime] = useState(new Date(new Date().setMinutes(new Date().getMinutes() - 30)));
  const [recurrence, setRecurrence] = useState<'none' | 'daily' | 'weekly' | 'monthly'>('none');

  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  // Estado para gerenciar tarefas associadas ao evento
  const [showTodoForm, setShowTodoForm] = useState(false);
  const [todoTitle, setTodoTitle] = useState('');
  const [todoDescription, setTodoDescription] = useState('');
  const [todoPriority, setTodoPriority] = useState<'high' | 'medium' | 'low'>('medium');
  const [eventTodos, setEventTodos] = useState<TodoItem[]>([]);
  const [editingTodo, setEditingTodo] = useState<TodoItem | null>(null);
  const [showSubjectPicker, setShowSubjectPicker] = useState(false);
  const [showTypePicker, setShowTypePicker] = useState(false);
  const [showRecurrencePicker, setShowRecurrencePicker] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);

  // Predefined colors
  const colorOptions = [
    colors.primary,
    colors.secondary,
    colors.accent1,
    colors.accent2,
    '#F97316', // Orange
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#10B981', // Green
    '#3B82F6', // Blue
    '#F59E0B', // Amber
  ];

  // Event types with labels
  const eventTypes = [
    { value: 'study', label: 'Estudo' },
    { value: 'exam', label: 'Prova' },
    { value: 'assignment', label: 'Tarefa' },
    { value: 'meeting', label: 'Reunião' },
    { value: 'other', label: 'Outro' },
  ];

  // Recurrence options with labels
  const recurrenceOptions = [
    { value: 'none', label: 'Não se repete' },
    { value: 'daily', label: 'Diariamente' },
    { value: 'weekly', label: 'Semanalmente' },
    { value: 'monthly', label: 'Mensalmente' },
  ];

  useEffect(() => {
    if (visible) {
      try {
        // Initialize form with initial values or defaults
        if (initialEvent && Object.keys(initialEvent).length > 0) {
          console.log('Inicializando com evento existente:', initialEvent);
          setTitle(initialEvent.title || '');
          setDescription(initialEvent.description || '');
          setStartDate(initialEvent.startDate ? new Date(initialEvent.startDate) : new Date());
          setEndDate(initialEvent.endDate ? new Date(initialEvent.endDate) : addHours(new Date(), 1));
          setAllDay(initialEvent.allDay || false);
          setColor(initialEvent.color || colors.primary);
          setSubject(initialEvent.subject || null);
          setSubjectId(initialEvent.subject_id || null);
          setType(initialEvent.type || 'study');
          setReminder(initialEvent.reminder || false);
          setReminderTime(initialEvent.reminderTime ? new Date(initialEvent.reminderTime) : new Date(new Date().setMinutes(new Date().getMinutes() - 30)));
          setRecurrence(initialEvent.recurrence || 'none');
        } else {
          console.log('Inicializando com valores padrão');
          // Default values for new event
          const defaultDate = selectedDate ? new Date(selectedDate) : new Date();
          defaultDate.setHours(9, 0, 0, 0); // Default to 9:00 AM

          setTitle('');
          setDescription('');
          setStartDate(defaultDate);
          setEndDate(addHours(defaultDate, 1));
          setAllDay(false);
          setColor(colors.primary);
          setSubject(null);
          setSubjectId(null);
          setType('study');
          setReminder(false);
          setReminderTime(new Date(defaultDate.getTime() - 30 * 60000)); // 30 minutes before
          setRecurrence('none');
          setEventTodos([]);
        }
      } catch (error) {
        console.error('Erro ao inicializar formulário:', error);
        // Fallback para valores padrão em caso de erro
        const defaultDate = new Date();
        defaultDate.setHours(9, 0, 0, 0);

        setTitle('');
        setDescription('');
        setStartDate(defaultDate);
        setEndDate(addHours(defaultDate, 1));
        setAllDay(false);
        setColor(colors.primary);
        setSubject(null);
        setSubjectId(null);
        setType('study');
        setReminder(false);
        setReminderTime(new Date(defaultDate.getTime() - 30 * 60000));
        setRecurrence('none');
        setEventTodos([]);
      }
    }
  }, [visible, initialEvent, selectedDate]);

  // Carregar tarefas do evento se estiver editando
  useEffect(() => {
    if (initialEvent?.id) {
      const todos = useCalendarStore.getState().getEventTodos(initialEvent.id);
      setEventTodos(todos);
    }
  }, [initialEvent?.id]);

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título do evento é obrigatório.');
      return;
    }

    const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      allDay,
      color,
      subject: subject || undefined,
      subject_id: subjectId || undefined,
      type,
      completed: false,
      reminder,
      reminderTime: reminder ? reminderTime.toISOString() : undefined,
      recurrence,
      has_todos: eventTodos.length > 0
    };

    onSave(event);
    onClose();
  };

  const handleAddTodo = async () => {
    if (!todoTitle.trim()) {
      Alert.alert('Erro', 'O título da tarefa é obrigatório.');
      return;
    }

    if (editingTodo) {
      // Atualizar tarefa existente (apenas na interface, pois o evento ainda não foi salvo)
      setEventTodos(eventTodos.map(todo =>
        todo === editingTodo ? {
          ...todo,
          title: todoTitle,
          description: todoDescription,
          priority: todoPriority
        } : todo
      ));
    } else {
      // Se o evento já existe e temos a função onAddTodo, adicionar a tarefa ao evento
      if (initialEvent?.id && onAddTodo) {
        const todoData: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt' | 'event_id'> = {
          title: todoTitle,
          description: todoDescription,
          priority: todoPriority,
          completed: false,
          subject: subject || undefined,
          subject_id: subjectId || undefined,
          dueDate: new Date().toISOString()
        };

        try {
          const savedTodo = await onAddTodo(initialEvent.id, todoData);
          if (savedTodo) {
            setEventTodos([...eventTodos, savedTodo]);
          }
        } catch (error) {
          console.error('Erro ao adicionar tarefa ao evento:', error);
          Alert.alert('Erro', 'Não foi possível adicionar a tarefa ao evento.');
        }
      } else {
        // Adicionar nova tarefa temporária
        const newTodo: TodoItem = {
          id: `temp-${Date.now()}`,
          title: todoTitle,
          description: todoDescription,
          priority: todoPriority,
          completed: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setEventTodos([...eventTodos, newTodo]);
      }
    }

    // Limpar formulário
    setTodoTitle('');
    setTodoDescription('');
    setTodoPriority('medium');
    setEditingTodo(null);
    setShowTodoForm(false);
  };

  const handleEditTodo = (todo: TodoItem) => {
    setTodoTitle(todo.title);
    setTodoDescription(todo.description || '');
    setTodoPriority(todo.priority);
    setEditingTodo(todo);
    setShowTodoForm(true);
  };

  const handleRemoveTodo = (todo: TodoItem) => {
    setEventTodos(eventTodos.filter(t => t !== todo));
  };

  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      // Preserve the time from the current startDate
      newDate.setHours(
        startDate.getHours(),
        startDate.getMinutes(),
        startDate.getSeconds(),
        startDate.getMilliseconds()
      );
      setStartDate(newDate);

      // If end date is before start date, update it
      if (endDate < newDate) {
        setEndDate(addHours(newDate, 1));
      }
    }
  };

  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
    setShowStartTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(startDate);
      newDate.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        selectedTime.getSeconds(),
        selectedTime.getMilliseconds()
      );
      setStartDate(newDate);

      // If end time is before start time on the same day, update it
      if (
        endDate.getDate() === newDate.getDate() &&
        endDate.getMonth() === newDate.getMonth() &&
        endDate.getFullYear() === newDate.getFullYear() &&
        endDate < newDate
      ) {
        setEndDate(addHours(newDate, 1));
      }
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      // Preserve the time from the current endDate
      newDate.setHours(
        endDate.getHours(),
        endDate.getMinutes(),
        endDate.getSeconds(),
        endDate.getMilliseconds()
      );

      // If end date is before start date, don't update
      if (newDate < startDate) {
        Alert.alert('Erro', 'A data de término deve ser após a data de início.');
        return;
      }

      setEndDate(newDate);
    }
  };

  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
    setShowEndTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(endDate);
      newDate.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        selectedTime.getSeconds(),
        selectedTime.getMilliseconds()
      );

      // If end time is before start time on the same day, don't update
      if (
        startDate.getDate() === newDate.getDate() &&
        startDate.getMonth() === newDate.getMonth() &&
        startDate.getFullYear() === newDate.getFullYear() &&
        newDate < startDate
      ) {
        Alert.alert('Erro', 'O horário de término deve ser após o horário de início.');
        return;
      }

      setEndDate(newDate);
    }
  };

  const handleReminderTimeChange = (event: any, selectedTime?: Date) => {
    setShowReminderTimePicker(false);
    if (selectedTime) {
      setReminderTime(selectedTime);
    }
  };

  const handleSubjectSelect = (subjectTitle: string, subjectId: string) => {
    setSubject(subjectTitle);
    setSubjectId(subjectId);
    setShowSubjectPicker(false);
  };

  const handleTypeSelect = (selectedType: 'study' | 'exam' | 'assignment' | 'meeting' | 'other') => {
    setType(selectedType);
    setShowTypePicker(false);
  };

  const handleRecurrenceSelect = (selectedRecurrence: 'none' | 'daily' | 'weekly' | 'monthly') => {
    setRecurrence(selectedRecurrence);
    setShowRecurrencePicker(false);
  };

  const handleColorSelect = (selectedColor: string) => {
    setColor(selectedColor);
    setShowColorPicker(false);
  };

  const getTypeLabel = (typeValue: string) => {
    const type = eventTypes.find(t => t.value === typeValue);
    return type ? type.label : 'Estudo';
  };

  const getRecurrenceLabel = (recurrenceValue: string) => {
    const recurrence = recurrenceOptions.find(r => r.value === recurrenceValue);
    return recurrence ? recurrence.label : 'Não se repete';
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {initialEvent?.id ? 'Editar Evento' : 'Novo Evento'}
            </Text>
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Título</Text>
              <TextInput
                style={styles.input}
                value={title}
                onChangeText={setTitle}
                placeholder="Título do evento"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Descrição do evento"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Dia inteiro</Text>
              <Switch
                value={allDay}
                onValueChange={setAllDay}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={allDay ? colors.primary : colors.white}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de início</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showStartDatePicker && (
                <DateTimePicker
                  value={startDate}
                  mode="date"
                  display="default"
                  onChange={handleStartDateChange}
                />
              )}
            </View>

            {!allDay && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Hora de início</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowStartTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(startDate, 'HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showStartTimePicker && (
                  <DateTimePicker
                    value={startDate}
                    mode="time"
                    display="default"
                    onChange={handleStartTimeChange}
                  />
                )}
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de término</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(endDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showEndDatePicker && (
                <DateTimePicker
                  value={endDate}
                  mode="date"
                  display="default"
                  onChange={handleEndDateChange}
                />
              )}
            </View>

            {!allDay && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Hora de término</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowEndTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(endDate, 'HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showEndTimePicker && (
                  <DateTimePicker
                    value={endDate}
                    mode="time"
                    display="default"
                    onChange={handleEndTimeChange}
                  />
                )}
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Matéria</Text>
              <Pressable
                style={styles.pickerButton}
                onPress={() => setShowSubjectPicker(true)}
              >
                <BookOpen size={20} color={colors.primary} />
                <Text style={styles.pickerButtonText}>
                  {subject || 'Selecionar matéria'}
                </Text>
              </Pressable>
              <Modal
                visible={showSubjectPicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowSubjectPicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Matéria</Text>
                      <Pressable onPress={() => setShowSubjectPicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <ScrollView style={styles.pickerScrollView}>
                      <Pressable
                        style={styles.pickerItem}
                        onPress={() => {
                          setSubject(null);
                          setSubjectId(null);
                          setShowSubjectPicker(false);
                        }}
                      >
                        <Text style={styles.pickerItemText}>Nenhuma</Text>
                      </Pressable>
                      {subjects.map((subject) => (
                        <Pressable
                          key={subject.id}
                          style={styles.pickerItem}
                          onPress={() => handleSubjectSelect(subject.title, subject.id)}
                        >
                          <Text style={styles.pickerItemText}>{subject.title}</Text>
                        </Pressable>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              </Modal>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tipo</Text>
              <Pressable
                style={styles.pickerButton}
                onPress={() => setShowTypePicker(true)}
              >
                <Text style={styles.pickerButtonText}>
                  {getTypeLabel(type)}
                </Text>
              </Pressable>
              <Modal
                visible={showTypePicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowTypePicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Tipo</Text>
                      <Pressable onPress={() => setShowTypePicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <ScrollView style={styles.pickerScrollView}>
                      {eventTypes.map((eventType) => (
                        <Pressable
                          key={eventType.value}
                          style={styles.pickerItem}
                          onPress={() => handleTypeSelect(eventType.value as any)}
                        >
                          <Text style={styles.pickerItemText}>{eventType.label}</Text>
                        </Pressable>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              </Modal>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Cor</Text>
              <Pressable
                style={styles.colorButton}
                onPress={() => setShowColorPicker(true)}
              >
                <View style={[styles.colorPreview, { backgroundColor: color }]} />
                <Text style={styles.pickerButtonText}>Selecionar cor</Text>
              </Pressable>
              <Modal
                visible={showColorPicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowColorPicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Cor</Text>
                      <Pressable onPress={() => setShowColorPicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <View style={styles.colorGrid}>
                      {colorOptions.map((colorOption) => (
                        <Pressable
                          key={colorOption}
                          style={[
                            styles.colorOption,
                            { backgroundColor: colorOption },
                            color === colorOption && styles.colorOptionSelected
                          ]}
                          onPress={() => handleColorSelect(colorOption)}
                        />
                      ))}
                    </View>
                  </View>
                </View>
              </Modal>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Lembrete</Text>
              <Switch
                value={reminder}
                onValueChange={setReminder}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={reminder ? colors.primary : colors.white}
              />
            </View>

            {reminder && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Horário do lembrete</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowReminderTimePicker(true)}
                >
                  <Bell size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(reminderTime, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showReminderTimePicker && (
                  <DateTimePicker
                    value={reminderTime}
                    mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                    display="default"
                    onChange={handleReminderTimeChange}
                  />
                )}
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Recorrência</Text>
              <Pressable
                style={styles.pickerButton}
                onPress={() => setShowRecurrencePicker(true)}
              >
                <Repeat size={20} color={colors.primary} />
                <Text style={styles.pickerButtonText}>
                  {getRecurrenceLabel(recurrence)}
                </Text>
              </Pressable>
              <Modal
                visible={showRecurrencePicker}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowRecurrencePicker(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>Selecionar Recorrência</Text>
                      <Pressable onPress={() => setShowRecurrencePicker(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>
                    <ScrollView style={styles.pickerScrollView}>
                      {recurrenceOptions.map((option) => (
                        <Pressable
                          key={option.value}
                          style={styles.pickerItem}
                          onPress={() => handleRecurrenceSelect(option.value as any)}
                        >
                          <Text style={styles.pickerItemText}>{option.label}</Text>
                        </Pressable>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              </Modal>
            </View>

            {/* Seção de Tarefas */}
            <View style={[styles.formGroup, styles.todosSection]}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Tarefas do Evento</Text>
                <Pressable
                  style={styles.addButton}
                  onPress={() => setShowTodoForm(true)}
                >
                  <Plus size={20} color={colors.white} />
                </Pressable>
              </View>

              {eventTodos.length > 0 ? (
                <View style={styles.todosList}>
                  {eventTodos.map((todo) => (
                    <View key={todo.id} style={styles.todoItem}>
                      <View style={styles.todoInfo}>
                        <Text style={styles.todoTitle}>{todo.title}</Text>
                        {todo.description && (
                          <Text style={styles.todoDescription}>{todo.description}</Text>
                        )}
                        <View style={styles.todoPriorityBadge}>
                          <Text style={styles.todoPriorityText}>
                            {todo.priority === 'high' ? 'Alta' : todo.priority === 'medium' ? 'Média' : 'Baixa'}
                          </Text>
                        </View>
                      </View>
                      <View style={styles.todoActions}>
                        <Pressable
                          style={styles.todoActionButton}
                          onPress={() => handleEditTodo(todo)}
                        >
                          <Edit2 size={16} color={colors.primary} />
                        </Pressable>
                        <Pressable
                          style={styles.todoActionButton}
                          onPress={() => handleRemoveTodo(todo)}
                        >
                          <X size={16} color={colors.error} />
                        </Pressable>
                      </View>
                    </View>
                  ))}
                </View>
              ) : (
                <View style={styles.emptyTodos}>
                  <Text style={styles.emptyTodosText}>Nenhuma tarefa adicionada</Text>
                  <Text style={styles.emptyTodosSubtext}>Adicione tarefas para este evento</Text>
                </View>
              )}

              {/* Modal para adicionar/editar tarefa */}
              <Modal
                visible={showTodoForm}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowTodoForm(false)}
              >
                <View style={styles.pickerModalContainer}>
                  <View style={styles.pickerContent}>
                    <View style={styles.pickerHeader}>
                      <Text style={styles.pickerTitle}>
                        {editingTodo ? 'Editar Tarefa' : 'Nova Tarefa'}
                      </Text>
                      <Pressable onPress={() => setShowTodoForm(false)}>
                        <X size={24} color={colors.text} />
                      </Pressable>
                    </View>

                    <View style={styles.formGroup}>
                      <Text style={styles.label}>Título</Text>
                      <TextInput
                        style={styles.input}
                        value={todoTitle}
                        onChangeText={setTodoTitle}
                        placeholder="Título da tarefa"
                        placeholderTextColor={colors.textLight}
                      />
                    </View>

                    <View style={styles.formGroup}>
                      <Text style={styles.label}>Descrição</Text>
                      <TextInput
                        style={[styles.input, styles.textArea]}
                        value={todoDescription}
                        onChangeText={setTodoDescription}
                        placeholder="Descrição da tarefa"
                        placeholderTextColor={colors.textLight}
                        multiline
                        numberOfLines={4}
                        textAlignVertical="top"
                      />
                    </View>

                    <View style={styles.formGroup}>
                      <Text style={styles.label}>Prioridade</Text>
                      <View style={styles.prioritySelector}>
                        <Pressable
                          style={[
                            styles.priorityButton,
                            todoPriority === 'low' && styles.priorityButtonActive,
                            { backgroundColor: todoPriority === 'low' ? `${colors.success}20` : colors.card }
                          ]}
                          onPress={() => setTodoPriority('low')}
                        >
                          <Text style={[
                            styles.priorityButtonText,
                            todoPriority === 'low' && { color: colors.success }
                          ]}>Baixa</Text>
                        </Pressable>

                        <Pressable
                          style={[
                            styles.priorityButton,
                            todoPriority === 'medium' && styles.priorityButtonActive,
                            { backgroundColor: todoPriority === 'medium' ? `${colors.warning}20` : colors.card }
                          ]}
                          onPress={() => setTodoPriority('medium')}
                        >
                          <Text style={[
                            styles.priorityButtonText,
                            todoPriority === 'medium' && { color: colors.warning }
                          ]}>Média</Text>
                        </Pressable>

                        <Pressable
                          style={[
                            styles.priorityButton,
                            todoPriority === 'high' && styles.priorityButtonActive,
                            { backgroundColor: todoPriority === 'high' ? `${colors.error}20` : colors.card }
                          ]}
                          onPress={() => setTodoPriority('high')}
                        >
                          <Text style={[
                            styles.priorityButtonText,
                            todoPriority === 'high' && { color: colors.error }
                          ]}>Alta</Text>
                        </Pressable>
                      </View>
                    </View>

                    <View style={styles.buttonContainer}>
                      <Button
                        title="Cancelar"
                        onPress={() => {
                          setTodoTitle('');
                          setTodoDescription('');
                          setTodoPriority('medium');
                          setEditingTodo(null);
                          setShowTodoForm(false);
                        }}
                        variant="secondary"
                        size="medium"
                        style={styles.button}
                      />
                      <Button
                        title="Salvar"
                        onPress={handleAddTodo}
                        variant="primary"
                        size="medium"
                        style={styles.button}
                      />
                    </View>
                  </View>
                </View>
              </Modal>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="secondary"
                size="medium"
                style={styles.button}
              />
              <Button
                title="Salvar"
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={styles.button}
              />
            </View>
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
    zIndex: 9999,
  },
  formContainer: {
    width: '100%',
    maxHeight: '90%',
    borderRadius: 16,
    padding: 20,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
  },
  closeButton: {
    padding: 10,
    backgroundColor: colors.card,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  scrollView: {
    flex: 1,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  pickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  pickerModalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    maxHeight: '70%',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  pickerScrollView: {
    maxHeight: 300,
  },
  pickerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  pickerItemText: {
    fontSize: 16,
    color: colors.text,
  },
  colorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  colorPreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 10,
  },
  colorOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    margin: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    borderColor: colors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
  todosSection: {
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  addButton: {
    backgroundColor: colors.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  todosList: {
    marginTop: 8,
  },
  todoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  todoInfo: {
    flex: 1,
  },
  todoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  todoDescription: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 4,
  },
  todoPriorityBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  todoPriorityText: {
    fontSize: 12,
    color: colors.textLight,
  },
  todoActions: {
    flexDirection: 'row',
  },
  todoActionButton: {
    padding: 8,
    marginLeft: 8,
  },
  emptyTodos: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: `${colors.primary}10`,
    borderRadius: 8,
  },
  emptyTodosText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  emptyTodosSubtext: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
  },
  prioritySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityButton: {
    flex: 1,
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  priorityButtonActive: {
    borderColor: 'transparent',
  },
  priorityButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textLight,
  },
});
