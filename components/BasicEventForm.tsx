import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Switch,
  Modal,
  Platform,
  Alert
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO, addHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarEvent } from '@/types';
import { Calendar, Clock, X } from 'lucide-react-native';

interface BasicEventFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialEvent?: Partial<CalendarEvent>;
  selectedDate?: string;
}

export const BasicEventForm: React.FC<BasicEventFormProps> = ({
  visible,
  onClose,
  onSave,
  initialEvent,
  selectedDate
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(new Date().setHours(new Date().getHours() + 1)));
  const [allDay, setAllDay] = useState(false);

  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // Inicializar o formulário quando o modal for aberto
  useEffect(() => {
    if (visible) {
      console.log('BasicEventForm: Inicializando formulário');
      
      try {
        if (initialEvent && Object.keys(initialEvent).length > 0) {
          console.log('BasicEventForm: Usando evento inicial:', initialEvent);
          
          setTitle(initialEvent.title || '');
          setDescription(initialEvent.description || '');
          
          if (initialEvent.startDate) {
            try {
              const parsedStartDate = new Date(initialEvent.startDate);
              if (!isNaN(parsedStartDate.getTime())) {
                setStartDate(parsedStartDate);
              }
            } catch (e) {
              console.error('Erro ao parsear startDate:', e);
            }
          }
          
          if (initialEvent.endDate) {
            try {
              const parsedEndDate = new Date(initialEvent.endDate);
              if (!isNaN(parsedEndDate.getTime())) {
                setEndDate(parsedEndDate);
              }
            } catch (e) {
              console.error('Erro ao parsear endDate:', e);
            }
          }
          
          setAllDay(initialEvent.allDay || false);
        } else {
          console.log('BasicEventForm: Usando valores padrão');
          
          // Valores padrão para novo evento
          let defaultDate = new Date();
          
          if (selectedDate) {
            try {
              const parsedDate = new Date(selectedDate);
              if (!isNaN(parsedDate.getTime())) {
                defaultDate = parsedDate;
              }
            } catch (e) {
              console.error('Erro ao parsear selectedDate:', e);
            }
          }
          
          defaultDate.setHours(10, 0, 0, 0); // 10:00 AM
          
          setTitle('');
          setDescription('');
          setStartDate(defaultDate);
          setEndDate(addHours(defaultDate, 1)); // 1 hora depois
          setAllDay(false);
        }
      } catch (error) {
        console.error('Erro ao inicializar formulário:', error);
        
        // Fallback para valores padrão em caso de erro
        const defaultDate = new Date();
        defaultDate.setHours(10, 0, 0, 0);
        
        setTitle('');
        setDescription('');
        setStartDate(defaultDate);
        setEndDate(addHours(defaultDate, 1));
        setAllDay(false);
      }
    }
  }, [visible, initialEvent, selectedDate]);

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título do evento é obrigatório.');
      return;
    }

    const event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      allDay,
      color: colors.primary,
      type: 'study',
      completed: false,
      reminder: false
    };

    console.log('BasicEventForm: Salvando evento:', event);
    onSave(event);
    onClose();
  };

  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      // Preservar o horário da data atual
      newDate.setHours(
        startDate.getHours(),
        startDate.getMinutes(),
        startDate.getSeconds(),
        startDate.getMilliseconds()
      );
      setStartDate(newDate);

      // Se a data de término for anterior à data de início, atualizá-la
      if (endDate < newDate) {
        setEndDate(addHours(newDate, 1));
      }
    }
  };

  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
    setShowStartTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(startDate);
      newDate.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        selectedTime.getSeconds(),
        selectedTime.getMilliseconds()
      );
      setStartDate(newDate);

      // Se o horário de término for anterior ao horário de início no mesmo dia, atualizá-lo
      if (
        endDate.getDate() === newDate.getDate() &&
        endDate.getMonth() === newDate.getMonth() &&
        endDate.getFullYear() === newDate.getFullYear() &&
        endDate < newDate
      ) {
        setEndDate(addHours(newDate, 1));
      }
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      // Preservar o horário da data atual
      newDate.setHours(
        endDate.getHours(),
        endDate.getMinutes(),
        endDate.getSeconds(),
        endDate.getMilliseconds()
      );
      setEndDate(newDate);
    }
  };

  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
    setShowEndTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(endDate);
      newDate.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        selectedTime.getSeconds(),
        selectedTime.getMilliseconds()
      );
      setEndDate(newDate);
    }
  };

  // Renderizar o modal apenas quando estiver visível
  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {initialEvent?.id ? 'Editar Evento' : 'Novo Evento'}
            </Text>
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Título</Text>
              <TextInput
                style={styles.input}
                value={title}
                onChangeText={setTitle}
                placeholder="Título do evento"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Descrição do evento"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Dia inteiro</Text>
              <Switch
                value={allDay}
                onValueChange={setAllDay}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={allDay ? colors.primary : colors.white}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de início</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(startDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showStartDatePicker && (
                <DateTimePicker
                  value={startDate}
                  mode="date"
                  display="default"
                  onChange={handleStartDateChange}
                />
              )}
            </View>

            {!allDay && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Hora de início</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowStartTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(startDate, 'HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showStartTimePicker && (
                  <DateTimePicker
                    value={startDate}
                    mode="time"
                    display="default"
                    onChange={handleStartTimeChange}
                  />
                )}
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Data de término</Text>
              <Pressable
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {format(endDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </Pressable>
              {showEndDatePicker && (
                <DateTimePicker
                  value={endDate}
                  mode="date"
                  display="default"
                  onChange={handleEndDateChange}
                />
              )}
            </View>

            {!allDay && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Hora de término</Text>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowEndTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {format(endDate, 'HH:mm', { locale: ptBR })}
                  </Text>
                </Pressable>
                {showEndTimePicker && (
                  <DateTimePicker
                    value={endDate}
                    mode="time"
                    display="default"
                    onChange={handleEndTimeChange}
                  />
                )}
              </View>
            )}

            <View style={styles.formActions}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="outline"
                size="medium"
                style={styles.cancelButton}
              />
              <Button
                title="Salvar"
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={styles.saveButton}
              />
            </View>
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 16,
  },
  formContainer: {
    width: '100%',
    maxHeight: '90%',
    borderRadius: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
  },
});
