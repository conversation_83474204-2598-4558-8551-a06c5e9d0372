import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Pressable, ScrollView, Dimensions, ActivityIndicator } from 'react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { Button } from './Button';
import { Plus, FileText, BookOpen, Network, FileQuestion, Search, Filter, SlidersHorizontal } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { StudyGroupMaterial } from '@/types';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface MaterialsTabViewProps {
  groupId: string;
  materials: StudyGroupMaterial[];
  loading: boolean;
  isAdmin?: boolean;
  onAddMaterial: (type: string) => void;
  onMaterialPress: (material: StudyGroupMaterial) => void;
}

type MaterialType = 'all' | 'note' | 'flashcard' | 'mindmap' | 'quiz';

export const MaterialsTabView: React.FC<MaterialsTabViewProps> = ({
  groupId,
  materials,
  loading,
  isAdmin = false,
  onAddMaterial,
  onMaterialPress,
}) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<MaterialType>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  const filteredMaterials = materials.filter((material) => {
    // Filtrar por tipo
    if (activeTab !== 'all') {
      if (activeTab === 'mindmap' && material.type !== 'mindmap') return false;
      if (activeTab === 'note' && material.type !== 'note') return false;
      if (activeTab === 'flashcard' && material.type !== 'flashcard') return false;
      if (activeTab === 'quiz' && material.type !== 'quiz') return false;
    }

    // Filtrar por busca
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        material.title.toLowerCase().includes(query) ||
        (material.description && material.description.toLowerCase().includes(query))
      );
    }

    return true;
  });

  const renderMaterialCard = (material: StudyGroupMaterial) => {
    let icon;
    let iconColor;
    let gradientColors;

    switch (material.type) {
      case 'note':
        icon = <FileText size={20} color="#fff" />;
        iconColor = colors.primary;
        gradientColors = colors.primaryGradient;
        break;
      case 'flashcard':
        icon = <BookOpen size={20} color="#fff" />;
        iconColor = colors.accent1;
        gradientColors = [colors.accent1, colors.accent1Dark || colors.accent1];
        break;
      case 'mindmap':
        icon = <Network size={20} color="#fff" />;
        iconColor = colors.secondary;
        gradientColors = colors.secondaryGradient;
        break;
      case 'quiz':
        icon = <FileQuestion size={20} color="#fff" />;
        iconColor = colors.accent2;
        gradientColors = [colors.accent2, colors.accent2Dark || colors.accent2];
        break;
      default:
        icon = <FileText size={20} color="#fff" />;
        iconColor = colors.primary;
        gradientColors = colors.primaryGradient;
    }

    return (
      <Pressable
        key={material.id}
        style={({ pressed }) => [styles.materialCard, pressed && styles.cardPressed]}
        onPress={() => onMaterialPress(material)}
      >
        <View style={styles.materialHeader}>
          <LinearGradient
            colors={gradientColors}
            style={styles.materialIconContainer}
          >
            {icon}
          </LinearGradient>
          <View style={styles.materialInfo}>
            <Text style={styles.materialTitle} numberOfLines={1}>
              {material.title}
            </Text>
            <Text style={styles.materialMeta}>
              {material.userName || 'Usuário'} • {new Date(material.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </View>
        {material.description && (
          <Text style={styles.materialDescription} numberOfLines={2}>
            {material.description}
          </Text>
        )}
      </Pressable>
    );
  };

  const renderEmptyState = () => {
    let message = 'Nenhum material encontrado';
    let icon;

    switch (activeTab) {
      case 'note':
        message = 'Nenhuma anotação encontrada';
        icon = <FileText size={48} color={colors.textLight} />;
        break;
      case 'flashcard':
        message = 'Nenhum flashcard encontrado';
        icon = <BookOpen size={48} color={colors.textLight} />;
        break;
      case 'mindmap':
        message = 'Nenhum mapa mental encontrado';
        icon = <Network size={48} color={colors.textLight} />;
        break;
      case 'quiz':
        message = 'Nenhum quiz encontrado';
        icon = <FileQuestion size={48} color={colors.textLight} />;
        break;
      default:
        message = 'Nenhum material encontrado';
        icon = <FileText size={48} color={colors.textLight} />;
    }

    return (
      <View style={styles.emptyContainer}>
        {icon}
        <Text style={styles.emptyText}>{message}</Text>
        <Button
          title={`Adicionar ${activeTab === 'all' ? 'material' : activeTab === 'note' ? 'anotação' : activeTab === 'flashcard' ? 'flashcard' : activeTab === 'mindmap' ? 'mapa mental' : 'quiz'}`}
          onPress={() => onAddMaterial(activeTab === 'all' ? 'note' : activeTab)}
          variant="primary"
          size="medium"
          icon={Plus}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContent}
        >
          <Pressable
            style={[styles.tab, activeTab === 'all' && styles.activeTab]}
            onPress={() => setActiveTab('all')}
          >
            <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
              Todos
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tab, activeTab === 'note' && styles.activeTab]}
            onPress={() => setActiveTab('note')}
          >
            <FileText
              size={16}
              color={activeTab === 'note' ? colors.primary : colors.textLight}
            />
            <Text style={[styles.tabText, activeTab === 'note' && styles.activeTabText]}>
              Anotações
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tab, activeTab === 'flashcard' && styles.activeTab]}
            onPress={() => setActiveTab('flashcard')}
          >
            <BookOpen
              size={16}
              color={activeTab === 'flashcard' ? colors.accent1 : colors.textLight}
            />
            <Text style={[styles.tabText, activeTab === 'flashcard' && styles.activeTabText]}>
              Flashcards
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tab, activeTab === 'mindmap' && styles.activeTab]}
            onPress={() => setActiveTab('mindmap')}
          >
            <Network
              size={16}
              color={activeTab === 'mindmap' ? colors.secondary : colors.textLight}
            />
            <Text style={[styles.tabText, activeTab === 'mindmap' && styles.activeTabText]}>
              Mapas Mentais
            </Text>
          </Pressable>
          <Pressable
            style={[styles.tab, activeTab === 'quiz' && styles.activeTab]}
            onPress={() => setActiveTab('quiz')}
          >
            <FileQuestion
              size={16}
              color={activeTab === 'quiz' ? colors.accent2 : colors.textLight}
            />
            <Text style={[styles.tabText, activeTab === 'quiz' && styles.activeTabText]}>
              Quizzes
            </Text>
          </Pressable>
        </ScrollView>
      </View>

      <View style={styles.actionsContainer}>
        <View style={styles.searchContainer}>
          {showSearch ? (
            <GlassCard style={styles.searchInputContainer}>
              <Search size={20} color={colors.textLight} />
              <input
                style={styles.searchInput}
                placeholder="Buscar materiais..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Pressable onPress={() => setShowSearch(false)}>
                <X size={20} color={colors.textLight} />
              </Pressable>
            </GlassCard>
          ) : (
            <Button
              title="Buscar"
              onPress={() => setShowSearch(true)}
              variant="secondary"
              size="small"
              icon={Search}
            />
          )}
        </View>
        <Button
          title={`Adicionar ${activeTab === 'all' ? '' : activeTab === 'note' ? 'Anotação' : activeTab === 'flashcard' ? 'Flashcard' : activeTab === 'mindmap' ? 'Mapa Mental' : 'Quiz'}`}
          onPress={() => onAddMaterial(activeTab === 'all' ? 'note' : activeTab)}
          variant="primary"
          size="small"
          icon={Plus}
        />
      </View>

      <View style={styles.materialsContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Carregando materiais...</Text>
          </View>
        ) : filteredMaterials.length > 0 ? (
          <ScrollView
            style={styles.materialsScrollView}
            contentContainerStyle={styles.materialsScrollContent}
            showsVerticalScrollIndicator={false}
          >
            {filteredMaterials.map(renderMaterialCard)}
          </ScrollView>
        ) : (
          renderEmptyState()
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  tabsContainer: {
    marginBottom: 16,
  },
  tabsScrollContent: {
    paddingHorizontal: 16,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: colors.cardLight,
  },
  activeTab: {
    backgroundColor: colors.card,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textLight,
    marginLeft: 4,
  },
  activeTabText: {
    color: colors.text,
    fontWeight: '600',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  searchContainer: {
    flex: 1,
    marginRight: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: colors.text,
    backgroundColor: 'transparent',
    border: 'none',
    outline: 'none',
  },
  materialsContainer: {
    flex: 1,
  },
  materialsScrollView: {
    flex: 1,
  },
  materialsScrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  materialCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardPressed: {
    opacity: 0.8,
  },
  materialHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  materialIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  materialInfo: {
    flex: 1,
  },
  materialTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  materialMeta: {
    fontSize: 12,
    color: colors.textLight,
  },
  materialDescription: {
    fontSize: 14,
    color: colors.text,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textLight,
    textAlign: 'center',
    marginVertical: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 16,
  },
});
