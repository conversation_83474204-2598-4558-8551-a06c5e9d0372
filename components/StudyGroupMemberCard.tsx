import React from "react";
import { View, Text, StyleSheet, Pressable, Image } from "react-native";
import { colors } from "@/constants/colors";
import { StudyGroupMember } from "@/types";
import { Users, Clock, BookOpen, Trophy, Star } from "lucide-react-native";

interface StudyGroupMemberCardProps {
  member: StudyGroupMember;
  onPress?: (member: StudyGroupMember) => void;
  isCurrentUser?: boolean;
}

export const StudyGroupMemberCard: React.FC<StudyGroupMemberCardProps> = ({
  member,
  onPress,
  isCurrentUser = false,
}) => {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        isCurrentUser && styles.currentUserContainer,
        pressed && styles.pressed,
      ]}
      onPress={onPress ? () => onPress(member) : undefined}
      disabled={!onPress}
    >
      <View style={styles.memberInfo}>
        <View style={styles.memberAvatar}>
          {member.userAvatar ? (
            <Image source={{ uri: member.userAvatar }} style={styles.avatarImage} />
          ) : (
            <Users size={20} color={colors.white} />
          )}
        </View>
        <View style={styles.memberDetails}>
          <Text style={styles.memberName}>{member.userName}</Text>
          <View style={styles.roleAndRankContainer}>
            <Text style={styles.memberRole}>
              {member.role === 'admin' ? 'Administrador' :
               member.role === 'moderator' ? 'Moderador' : 'Membro'}
            </Text>
            {member.rank > 0 && (
              <View style={styles.rankBadge}>
                <Trophy size={12} color={colors.white} />
                <Text style={styles.rankText}>{member.rank}º</Text>
              </View>
            )}
          </View>
        </View>
        <View style={styles.levelContainer}>
          <Star size={16} color={colors.primary} />
          <Text style={styles.levelText}>Nível {member.level || 1}</Text>
        </View>
      </View>
      <View style={styles.memberStats}>
        <View style={styles.statItem}>
          <Star size={14} color={colors.textMedium} />
          <Text style={styles.statText}>{member.flashcardsCreated * 10} XP</Text>
        </View>
        <View style={styles.statItem}>
          <BookOpen size={14} color={colors.textMedium} />
          <Text style={styles.statText}>{member.flashcardsCreated} recursos</Text>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  // Estilo para destacar o usuário atual
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  currentUserContainer: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    backgroundColor: `${colors.primary}05`,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  memberInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
    overflow: "hidden",
  },
  avatarImage: {
    width: 40,
    height: 40,
  },
  memberDetails: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.textDark,
    marginBottom: 2,
  },
  roleAndRankContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  memberRole: {
    fontSize: 14,
    color: colors.textMedium,
    marginRight: 8,
  },
  rankBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  rankText: {
    fontSize: 12,
    color: colors.white,
    fontWeight: "bold",
    marginLeft: 2,
  },
  levelContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.lightGray,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  levelText: {
    fontSize: 12,
    color: colors.textDark,
    fontWeight: "bold",
    marginLeft: 4,
  },
  memberStats: {
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 12,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  statText: {
    fontSize: 14,
    color: colors.textMedium,
    marginLeft: 6,
  },
});
