import React, { useState } from "react";
import { View, Text, StyleSheet, Pressable, Modal } from "react-native";
import { colors } from "@/constants/colors";
import { FilterIcon, ArrowUpDown, X, Check } from "lucide-react-native";
import { GlassCard } from "./GlassCard";
import { Button } from "./Button";

export type SortOption = "title" | "date" | "progress";
export type FilterOption = "all" | "mastered" | "learning" | "new";

interface FlashcardFiltersProps {
  onSortChange: (sort: SortOption) => void;
  onFilterChange: (filter: FilterOption) => void;
  currentSort: SortOption;
  currentFilter: FilterOption;
}

export const FlashcardFilters: React.FC<FlashcardFiltersProps> = ({
  onSortChange,
  onFilterChange,
  currentSort,
  currentFilter,
}) => {
  const [showSortModal, setShowSortModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);

  const sortOptions: { value: SortOption; label: string }[] = [
    { value: "title", label: "Título (A-Z)" },
    { value: "date", label: "Data (Mais recente)" },
    { value: "progress", label: "Progresso (Maior)" },
  ];

  const filterOptions: { value: FilterOption; label: string }[] = [
    { value: "all", label: "Todos os flashcards" },
    { value: "mastered", label: "Dominados" },
    { value: "learning", label: "Em aprendizado" },
    { value: "new", label: "Novos" },
  ];

  const getSortLabel = () => {
    return sortOptions.find((option) => option.value === currentSort)?.label || "Ordenar";
  };

  const getFilterLabel = () => {
    return filterOptions.find((option) => option.value === currentFilter)?.label || "Filtrar";
  };

  return (
    <View style={styles.container}>
      <Pressable
        style={styles.filterButton}
        onPress={() => setShowSortModal(true)}
      >
        <ArrowUpDown size={16} color={colors.primary} />
        <Text style={styles.filterButtonText}>{getSortLabel()}</Text>
      </Pressable>

      <Pressable
        style={styles.filterButton}
        onPress={() => setShowFilterModal(true)}
      >
        <FilterIcon size={16} color={colors.primary} />
        <Text style={styles.filterButtonText}>{getFilterLabel()}</Text>
      </Pressable>

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSortModal(false)}
      >
        <View style={styles.modalOverlay}>
          <GlassCard style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Ordenar por</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => setShowSortModal(false)}
              >
                <X size={24} color={colors.text} />
              </Pressable>
            </View>

            <View style={styles.modalContent}>
              {sortOptions.map((option) => (
                <Pressable
                  key={option.value}
                  style={styles.optionItem}
                  onPress={() => {
                    onSortChange(option.value);
                    setShowSortModal(false);
                  }}
                >
                  <Text style={styles.optionText}>{option.label}</Text>
                  {currentSort === option.value && (
                    <Check size={20} color={colors.primary} />
                  )}
                </Pressable>
              ))}
            </View>
          </GlassCard>
        </View>
      </Modal>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <GlassCard style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filtrar por</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => setShowFilterModal(false)}
              >
                <X size={24} color={colors.text} />
              </Pressable>
            </View>

            <View style={styles.modalContent}>
              {filterOptions.map((option) => (
                <Pressable
                  key={option.value}
                  style={styles.optionItem}
                  onPress={() => {
                    onFilterChange(option.value);
                    setShowFilterModal(false);
                  }}
                >
                  <Text style={styles.optionText}>{option.label}</Text>
                  {currentFilter === option.value && (
                    <Check size={20} color={colors.primary} />
                  )}
                </Pressable>
              ))}
            </View>
          </GlassCard>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginBottom: 16,
    gap: 8,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
  },
  filterButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.text,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalContainer: {
    width: "100%",
    maxWidth: 400,
    borderRadius: 16,
    overflow: "hidden",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  optionText: {
    fontSize: 16,
    color: colors.text,
  },
});
