import React from "react";
import { View, Text, StyleSheet, Pressable, Dimensions, Alert } from "react-native";
import { FlashcardSet } from "@/types";
import { colors } from "@/constants/colors";
import { BookOpen, Clock, Award, ArrowRight, Edit, Trash, MoreVertical } from "lucide-react-native";
import { formatDistanceToNow } from "@/utils/dateUtils";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";
import { ProgressBar } from "./ProgressBar";

interface FlashcardSetCardProps {
  set: FlashcardSet;
  onPress: (set: FlashcardSet) => void;
  onEdit?: (set: FlashcardSet) => void;
  onDelete?: (set: FlashcardSet) => void;
}

const { width } = Dimensions.get("window");

export const FlashcardSetCard: React.FC<FlashcardSetCardProps> = ({ set, onPress, onEdit, onDelete }) => {
  const masteredPercentage = Math.round((set.mastered / set.count) * 100);

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(set)}
    >
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <LinearGradient
            colors={colors.primaryGradient}
            style={styles.iconContainer}
          >
            <BookOpen size={24} color="#fff" />
          </LinearGradient>
          <View style={styles.headerTextContainer}>
            <Text style={styles.subject}>{set.subject}</Text>
            <Text style={styles.title}>{set.title}</Text>
          </View>
        </View>

        {(onEdit || onDelete) && (
          <View style={styles.actionsContainer}>
            {onEdit && (
              <Pressable
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onEdit(set);
                }}
              >
                <Edit size={18} color={colors.primary} />
              </Pressable>
            )}

            {onDelete && (
              <Pressable
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  Alert.alert(
                    "Excluir conjunto",
                    `Tem certeza que deseja excluir o conjunto "${set.title}"? Esta ação não pode ser desfeita.`,
                    [
                      {
                        text: "Cancelar",
                        style: "cancel"
                      },
                      {
                        text: "Excluir",
                        onPress: () => onDelete(set),
                        style: "destructive"
                      }
                    ]
                  );
                }}
              >
                <Trash size={18} color={colors.error} />
              </Pressable>
            )}
          </View>
        )}
      </View>

      <View style={styles.progressSection}>
        <View style={styles.progressLabelContainer}>
          <Text style={styles.progressLabel}>Progresso</Text>
          <Text style={styles.progressPercentage}>{masteredPercentage}%</Text>
        </View>
        <ProgressBar
          progress={masteredPercentage}
          height={10}
          gradientColors={["#60A5FA", "#2563EB"]}
          style={styles.progressBar}
        />
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <BookOpen size={18} color={colors.primary} />
          </View>
          <View>
            <Text style={styles.statValue}>{set.count}</Text>
            <Text style={styles.statLabel}>Cartões</Text>
          </View>
        </View>

        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <Award size={18} color={colors.success} />
          </View>
          <View>
            <Text style={styles.statValue}>{set.mastered}</Text>
            <Text style={styles.statLabel}>Dominados</Text>
          </View>
        </View>

        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <Clock size={18} color={colors.secondary} />
          </View>
          <View>
            <Text style={styles.statLabel}>Última revisão</Text>
            <Text style={styles.timeAgo}>{formatDistanceToNow(new Date(set.lastReviewed))}</Text>
          </View>
        </View>
      </View>

      <View style={styles.footer}>
        <LinearGradient
          colors={colors.primaryGradient}
          style={styles.studyButton}
        >
          <Text style={styles.studyButtonText}>Estudar agora</Text>
          <ArrowRight size={18} color="#fff" />
        </LinearGradient>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  actionsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  subject: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressLabelContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: colors.textLight,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
  },
  progressBar: {
    borderRadius: 6,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 12,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
  },
  timeAgo: {
    fontSize: 12,
    color: colors.textLight,
    fontWeight: "500",
  },
  footer: {
    alignItems: "flex-end",
  },
  studyButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
  },
  studyButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
});