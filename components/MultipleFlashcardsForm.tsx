import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  Pressable,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  FlatList,
  ActivityIndicator
} from "react-native";
import { colors } from "@/constants/colors";
import { Button } from "@/components/Button";
import { X, Check, Plus, Edit, Trash, ArrowLeft, ArrowRight } from "lucide-react-native";
import { useStudyStore } from "@/store/studyStore";
import { SubjectSelector } from "@/components/SubjectSelector";
import { GlassCard } from "@/components/GlassCard";

interface FlashcardItem {
  id: string;
  front: string;
  back: string;
}

interface MultipleFlashcardsFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (title: string, description: string, subject: string, subjectId: string | undefined, flashcards: FlashcardItem[]) => void;
  loading?: boolean;
}

export const MultipleFlashcardsForm: React.FC<MultipleFlashcardsFormProps> = ({
  visible,
  onClose,
  onSave,
  loading = false
}) => {
  const { subjects, fetchSubjects } = useStudyStore();
  
  // Conjunto de flashcards
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | undefined>();
  
  // Flashcards
  const [flashcards, setFlashcards] = useState<FlashcardItem[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState("");
  const [currentAnswer, setCurrentAnswer] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showFlashcardForm, setShowFlashcardForm] = useState(false);
  
  // Etapas do formulário
  const [currentStep, setCurrentStep] = useState(1); // 1: Informações do conjunto, 2: Adicionar flashcards, 3: Revisão
  
  useEffect(() => {
    if (visible) {
      fetchSubjects();
    }
  }, [visible]);
  
  const resetForm = () => {
    setTitle("");
    setDescription("");
    setSelectedSubject("");
    setSelectedSubjectId(undefined);
    setFlashcards([]);
    setCurrentQuestion("");
    setCurrentAnswer("");
    setEditingIndex(null);
    setCurrentStep(1);
  };
  
  const handleClose = () => {
    resetForm();
    onClose();
  };
  
  const handleAddFlashcard = () => {
    if (!currentQuestion.trim()) {
      Alert.alert("Erro", "Por favor, insira uma pergunta para o flashcard.");
      return;
    }
    
    if (!currentAnswer.trim()) {
      Alert.alert("Erro", "Por favor, insira uma resposta para o flashcard.");
      return;
    }
    
    const newFlashcard = {
      id: Date.now().toString(),
      front: currentQuestion,
      back: currentAnswer
    };
    
    if (editingIndex !== null) {
      // Editar flashcard existente
      const updatedFlashcards = [...flashcards];
      updatedFlashcards[editingIndex] = newFlashcard;
      setFlashcards(updatedFlashcards);
      setEditingIndex(null);
    } else {
      // Adicionar novo flashcard
      setFlashcards([...flashcards, newFlashcard]);
    }
    
    setCurrentQuestion("");
    setCurrentAnswer("");
    setShowFlashcardForm(false);
  };
  
  const handleEditFlashcard = (index: number) => {
    const flashcard = flashcards[index];
    setCurrentQuestion(flashcard.front);
    setCurrentAnswer(flashcard.back);
    setEditingIndex(index);
    setShowFlashcardForm(true);
  };
  
  const handleDeleteFlashcard = (index: number) => {
    Alert.alert(
      "Confirmar exclusão",
      "Tem certeza que deseja excluir este flashcard?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Excluir",
          onPress: () => {
            const updatedFlashcards = [...flashcards];
            updatedFlashcards.splice(index, 1);
            setFlashcards(updatedFlashcards);
          },
          style: "destructive"
        }
      ]
    );
  };
  
  const handleNextStep = () => {
    if (currentStep === 1) {
      if (!title.trim()) {
        Alert.alert("Erro", "Por favor, insira um título para o conjunto.");
        return;
      }
      setCurrentStep(2);
    } else if (currentStep === 2) {
      if (flashcards.length === 0) {
        Alert.alert("Erro", "Por favor, adicione pelo menos um flashcard.");
        return;
      }
      setCurrentStep(3);
    }
  };
  
  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const handleSaveSet = () => {
    if (!title.trim()) {
      Alert.alert("Erro", "Por favor, insira um título para o conjunto.");
      return;
    }
    
    if (flashcards.length === 0) {
      Alert.alert("Erro", "Por favor, adicione pelo menos um flashcard.");
      return;
    }
    
    onSave(title, description, selectedSubject, selectedSubjectId, flashcards);
  };
  
  const renderFlashcardItem = ({ item, index }: { item: FlashcardItem, index: number }) => (
    <GlassCard style={styles.flashcardItem}>
      <View style={styles.flashcardContent}>
        <View style={styles.flashcardHeader}>
          <Text style={styles.flashcardNumber}>Cartão {index + 1}</Text>
          <View style={styles.flashcardActions}>
            <Pressable
              style={styles.actionButton}
              onPress={() => handleEditFlashcard(index)}
            >
              <Edit size={18} color={colors.primary} />
            </Pressable>
            <Pressable
              style={styles.actionButton}
              onPress={() => handleDeleteFlashcard(index)}
            >
              <Trash size={18} color={colors.error} />
            </Pressable>
          </View>
        </View>
        <View style={styles.flashcardBody}>
          <View style={styles.flashcardSection}>
            <Text style={styles.flashcardLabel}>Pergunta:</Text>
            <Text style={styles.flashcardText}>{item.front}</Text>
          </View>
          <View style={styles.flashcardSection}>
            <Text style={styles.flashcardLabel}>Resposta:</Text>
            <Text style={styles.flashcardText}>{item.back}</Text>
          </View>
        </View>
      </View>
    </GlassCard>
  );
  
  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      <View style={[styles.stepDot, currentStep >= 1 && styles.activeStepDot]} />
      <View style={styles.stepLine} />
      <View style={[styles.stepDot, currentStep >= 2 && styles.activeStepDot]} />
      <View style={styles.stepLine} />
      <View style={[styles.stepDot, currentStep >= 3 && styles.activeStepDot]} />
    </View>
  );
  
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Informações do Conjunto</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Título</Text>
              <TextInput
                style={styles.input}
                placeholder="Título do conjunto"
                placeholderTextColor={colors.textMedium}
                value={title}
                onChangeText={setTitle}
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Matéria</Text>
              <SubjectSelector
                value={selectedSubject}
                onChange={(value) => {
                  setSelectedSubject(value);
                  // Encontrar o ID da matéria selecionada
                  const subject = subjects.find(s => s.title === value);
                  setSelectedSubjectId(subject?.id);
                }}
                placeholder="Selecione uma matéria"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Descrição do conjunto (opcional)"
                placeholderTextColor={colors.textMedium}
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        );
        
      case 2:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Adicionar Flashcards</Text>
            
            {flashcards.length > 0 ? (
              <FlatList
                data={flashcards}
                renderItem={renderFlashcardItem}
                keyExtractor={item => item.id}
                style={styles.flashcardList}
                contentContainerStyle={styles.flashcardListContent}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  Você ainda não adicionou nenhum flashcard.
                </Text>
              </View>
            )}
            
            <Button
              title="Adicionar Flashcard"
              onPress={() => {
                setCurrentQuestion("");
                setCurrentAnswer("");
                setEditingIndex(null);
                setShowFlashcardForm(true);
              }}
              variant="primary"
              icon={Plus}
              style={styles.addButton}
            />
          </View>
        );
        
      case 3:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Revisar e Confirmar</Text>
            
            <View style={styles.reviewSection}>
              <Text style={styles.reviewSectionTitle}>Informações do Conjunto</Text>
              <View style={styles.reviewItem}>
                <Text style={styles.reviewLabel}>Título:</Text>
                <Text style={styles.reviewValue}>{title}</Text>
              </View>
              {selectedSubject && (
                <View style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>Matéria:</Text>
                  <Text style={styles.reviewValue}>{selectedSubject}</Text>
                </View>
              )}
              {description && (
                <View style={styles.reviewItem}>
                  <Text style={styles.reviewLabel}>Descrição:</Text>
                  <Text style={styles.reviewValue}>{description}</Text>
                </View>
              )}
            </View>
            
            <View style={styles.reviewSection}>
              <Text style={styles.reviewSectionTitle}>Flashcards ({flashcards.length})</Text>
              <FlatList
                data={flashcards}
                renderItem={renderFlashcardItem}
                keyExtractor={item => item.id}
                style={styles.flashcardList}
                contentContainerStyle={styles.flashcardListContent}
              />
            </View>
          </View>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentStep === 1 ? "Novo Conjunto de Flashcards" : 
                 currentStep === 2 ? "Adicionar Flashcards" : 
                 "Revisar Conjunto"}
              </Text>
              <Pressable
                style={styles.closeButton}
                onPress={handleClose}
              >
                <X size={24} color={colors.text} />
              </Pressable>
            </View>
            
            {renderStepIndicator()}
            
            <ScrollView style={styles.modalContent}>
              {renderStepContent()}
            </ScrollView>
            
            <View style={styles.modalFooter}>
              {currentStep > 1 && (
                <Button
                  title="Voltar"
                  onPress={handlePreviousStep}
                  variant="secondary"
                  style={styles.footerButton}
                  icon={ArrowLeft}
                />
              )}
              
              {currentStep < 3 ? (
                <Button
                  title="Próximo"
                  onPress={handleNextStep}
                  variant="primary"
                  style={styles.footerButton}
                  icon={ArrowRight}
                  iconPosition="right"
                />
              ) : (
                <Button
                  title={loading ? "Salvando..." : "Criar Conjunto"}
                  onPress={handleSaveSet}
                  variant="primary"
                  style={styles.footerButton}
                  icon={Check}
                  disabled={loading}
                />
              )}
            </View>
          </View>
        </KeyboardAvoidingView>
      </View>
      
      {/* Modal para adicionar/editar flashcard */}
      <Modal
        visible={showFlashcardForm}
        transparent
        animationType="fade"
        onRequestClose={() => setShowFlashcardForm(false)}
      >
        <View style={styles.modalOverlay}>
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={styles.keyboardAvoidingView}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {editingIndex !== null ? "Editar Flashcard" : "Novo Flashcard"}
                </Text>
                <Pressable
                  style={styles.closeButton}
                  onPress={() => setShowFlashcardForm(false)}
                >
                  <X size={24} color={colors.text} />
                </Pressable>
              </View>
              
              <ScrollView style={styles.modalContent}>
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Pergunta</Text>
                  <TextInput
                    style={[styles.input, styles.textArea]}
                    placeholder="Digite a pergunta do flashcard"
                    placeholderTextColor={colors.textMedium}
                    value={currentQuestion}
                    onChangeText={setCurrentQuestion}
                    multiline
                    numberOfLines={3}
                  />
                </View>
                
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Resposta</Text>
                  <TextInput
                    style={[styles.input, styles.textArea]}
                    placeholder="Digite a resposta do flashcard"
                    placeholderTextColor={colors.textMedium}
                    value={currentAnswer}
                    onChangeText={setCurrentAnswer}
                    multiline
                    numberOfLines={5}
                  />
                </View>
              </ScrollView>
              
              <View style={styles.modalFooter}>
                <Button
                  title="Cancelar"
                  onPress={() => setShowFlashcardForm(false)}
                  variant="secondary"
                  style={styles.footerButton}
                  icon={X}
                />
                <Button
                  title={editingIndex !== null ? "Atualizar" : "Adicionar"}
                  onPress={handleAddFlashcard}
                  variant="primary"
                  style={styles.footerButton}
                  icon={editingIndex !== null ? Check : Plus}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </View>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  keyboardAvoidingView: {
    width: "100%",
    maxWidth: 500,
  },
  modalContainer: {
    width: "100%",
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
    maxHeight: "90%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  stepIndicator: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  stepDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.border,
  },
  activeStepDot: {
    backgroundColor: colors.primary,
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: colors.border,
    marginHorizontal: 8,
  },
  modalContent: {
    padding: 16,
    maxHeight: 400,
  },
  stepContent: {
    paddingBottom: 16,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  modalFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  flashcardList: {
    maxHeight: 300,
  },
  flashcardListContent: {
    paddingBottom: 16,
  },
  flashcardItem: {
    marginBottom: 12,
    padding: 12,
  },
  flashcardContent: {
    width: "100%",
  },
  flashcardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  flashcardNumber: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
  },
  flashcardActions: {
    flexDirection: "row",
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  flashcardBody: {
    paddingVertical: 4,
  },
  flashcardSection: {
    marginBottom: 8,
  },
  flashcardLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textMedium,
    marginBottom: 4,
  },
  flashcardText: {
    fontSize: 16,
    color: colors.text,
  },
  emptyContainer: {
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: "center",
  },
  addButton: {
    marginTop: 8,
  },
  reviewSection: {
    marginBottom: 24,
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 16,
  },
  reviewSectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  reviewItem: {
    marginBottom: 8,
  },
  reviewLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.textMedium,
    marginBottom: 4,
  },
  reviewValue: {
    fontSize: 16,
    color: colors.text,
  },
});
