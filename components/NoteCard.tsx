import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { Note } from "@/types";
import { colors } from "@/constants/colors";
import { FileText, Clock, Tag, BookOpen } from "lucide-react-native";
import { formatDistanceToNow } from "@/utils/dateUtils";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";

interface NoteCardProps {
  note: Note;
  onPress: (note: Note) => void;
  onConvert?: (note: Note) => void;
}

export const NoteCard: React.FC<NoteCardProps> = ({ note, onPress, onConvert }) => {
  // Safely handle content - ensure it's a string
  const contentString = typeof note.content === 'string' ? note.content : '';

  // Calculate a preview of the content (first 100 characters)
  const contentPreview = contentString.length > 100
    ? `${contentString.substring(0, 100)}...`
    : contentString;

  // Safely handle subject - ensure it's a string
  const subjectString = typeof note.subject === 'string' ? note.subject :
    (note.subject && typeof note.subject === 'object' && 'title' in note.subject) ?
    (note.subject as any).title : 'Sem matéria';

  // Safely handle tags - ensure it's an array
  const tagsArray = Array.isArray(note.tags) ? note.tags : [];

  // Safely handle title - ensure it's a string
  const titleString = typeof note.title === 'string' ? note.title : 'Sem título';

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(note)}
    >
      <LinearGradient
        colors={["rgba(255,255,255,0.8)", "rgba(255,255,255,0.5)"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.cardGradient}
      >
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <FileText size={20} color="#fff" />
          </View>
          <Text style={styles.subject}>{subjectString}</Text>
        </View>

        <Text style={styles.title}>{titleString}</Text>

        <Text style={styles.preview} numberOfLines={2}>
          {contentPreview}
        </Text>

        <View style={styles.footer}>
          <View style={styles.tagsContainer}>
            {tagsArray.length > 0 && (
              <View style={styles.tagRow}>
                <Tag size={14} color={colors.textLight} />
                <Text style={styles.tagsText} numberOfLines={1}>
                  {tagsArray.join(", ")}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.actionsContainer}>
            {onConvert && (
              <Pressable
                style={styles.convertButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onConvert(note);
                }}
              >
                <BookOpen size={16} color={colors.primary} />
                <Text style={styles.convertText}>Flashcards</Text>
              </Pressable>
            )}

            <View style={styles.timeContainer}>
              <Clock size={14} color={colors.textLight} />
              <Text style={styles.timeText}>
                {formatDistanceToNow(new Date(note.updatedAt || new Date()))}
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardGradient: {
    padding: 16,
    borderRadius: 20,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  subject: {
    fontSize: 14,
    color: colors.textLight,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
  },
  preview: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 16,
    lineHeight: 20,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  tagsContainer: {
    flex: 1,
    marginRight: 8,
  },
  tagRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  tagsText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  actionsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeText: {
    fontSize: 12,
    color: colors.textLight,
    marginLeft: 4,
  },
  convertButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  convertText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: "500",
    marginLeft: 4,
  },
});