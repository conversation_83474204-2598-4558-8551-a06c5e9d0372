import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from './Button';
import { GlassCard } from './GlassCard';
import { Save, X, FileText } from 'lucide-react-native';
import { supabase } from '@/lib/supabase';
import { useUserStore } from '@/store/userStore';

interface CreateNoteFormProps {
  groupId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

export const CreateNoteForm: React.FC<CreateNoteFormProps> = ({
  groupId,
  onCancel,
  onSuccess,
}) => {
  const { supabaseUser } = useUserStore();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'Por favor, insira um título para a anotação.');
      return;
    }

    if (!content.trim()) {
      Alert.alert('Erro', 'Por favor, insira o conteúdo da anotação.');
      return;
    }

    try {
      setSaving(true);

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) {
        Alert.alert('Erro', 'Você precisa estar logado para adicionar materiais.');
        return;
      }

      // Prepare material data
      const materialData = {
        group_id: groupId,
        user_id: authUser.id,
        title: title.trim(),
        description: 'Anotação compartilhada no grupo de estudos',
        type: 'note',
        content: { text: content.trim() },
      };

      // Save to Supabase
      const { data, error } = await supabase
        .from('study_group_materials')
        .insert([materialData])
        .select()
        .single();

      if (error) {
        console.error('Erro ao salvar anotação:', error);
        Alert.alert('Erro', 'Não foi possível salvar a anotação. Por favor, tente novamente.');
        return;
      }

      Alert.alert('Sucesso', 'Anotação adicionada com sucesso!');
      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar anotação:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao salvar a anotação. Por favor, tente novamente.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <GlassCard style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <FileText size={24} color={colors.white} />
        </View>
        <Text style={styles.headerTitle}>Nova Anotação</Text>
      </View>

      <ScrollView
        style={styles.formContainer}
        contentContainerStyle={styles.formContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Título</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Digite o título da anotação"
            placeholderTextColor={colors.textLight}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Conteúdo</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={content}
            onChangeText={setContent}
            placeholder="Digite o conteúdo da anotação"
            placeholderTextColor={colors.textLight}
            multiline
            textAlignVertical="top"
            numberOfLines={10}
          />
        </View>
      </ScrollView>

      <View style={styles.buttonsContainer}>
        <Button
          title="Cancelar"
          onPress={onCancel}
          variant="secondary"
          size="medium"
          icon={X}
          style={styles.button}
        />
        <Button
          title="Salvar"
          onPress={handleSave}
          variant="primary"
          size="medium"
          icon={Save}
          style={styles.button}
          loading={saving}
        />
      </View>
    </GlassCard>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  formContainer: {
    flex: 1,
  },
  formContent: {
    paddingBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.cardLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 150,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  button: {
    marginLeft: 12,
  },
});
