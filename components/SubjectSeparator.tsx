import React, { useState } from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { colors } from '@/constants/colors';
import { ChevronDown, ChevronRight, MoreVertical, Edit, Trash } from 'lucide-react-native';
import { Separator, Subject } from '@/types';
import { SubjectCard } from './SubjectCard';
import { Menu } from './Menu';

interface SubjectSeparatorProps {
  separator: Separator;
  subjects: Subject[];
  onEditSeparator: (separator: Separator) => void;
  onDeleteSeparator: (separator: Separator) => void;
  onSubjectPress: (subject: Subject) => void;
  onSubjectLongPress?: (subject: Subject) => void;
}

export const SubjectSeparator: React.FC<SubjectSeparatorProps> = ({
  separator,
  subjects,
  onEditSeparator,
  onDeleteSeparator,
  onSubjectPress,
  onSubjectLongPress,
}) => {
  const [expanded, setExpanded] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const handleEditSeparator = () => {
    setMenuVisible(false);
    onEditSeparator(separator);
  };

  const handleDeleteSeparator = () => {
    setMenuVisible(false);
    onDeleteSeparator(separator);
  };

  return (
    <View style={styles.container}>
      <Pressable
        style={[styles.header, { borderLeftColor: separator.color }]}
        onPress={toggleExpanded}
      >
        <View style={styles.titleContainer}>
          {expanded ? (
            <ChevronDown size={20} color={colors.text} />
          ) : (
            <ChevronRight size={20} color={colors.text} />
          )}
          <Text style={styles.title}>{separator.title}</Text>
          {separator.description && (
            <Text style={styles.count}>({subjects.length})</Text>
          )}
        </View>

        <Pressable
          style={styles.menuButton}
          onPress={() => setMenuVisible(true)}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <MoreVertical size={20} color={colors.text} />
        </Pressable>
      </Pressable>

      {expanded && (
        <View style={styles.content}>
          {subjects.length > 0 ? (
            subjects.map((subject) => (
              <SubjectCard
                key={subject.id}
                subject={subject}
                onPress={onSubjectPress}
                onLongPress={onSubjectLongPress ? () => onSubjectLongPress(subject) : undefined}
              />
            ))
          ) : (
            <Text style={styles.emptyText}>
              Nenhuma matéria neste separador. Adicione matérias a este separador ao criar ou editar uma matéria.
            </Text>
          )}
        </View>
      )}

      <Menu
        visible={menuVisible}
        onClose={() => setMenuVisible(false)}
        items={[
          {
            icon: <Edit size={20} color={colors.text} />,
            title: 'Editar Separador',
            onPress: handleEditSeparator,
          },
          {
            icon: <Trash size={20} color={colors.error} />,
            title: 'Excluir Separador',
            onPress: handleDeleteSeparator,
            destructive: true,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 12,
    borderLeftWidth: 4,
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 8,
  },
  count: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
  },
  menuButton: {
    padding: 4,
  },
  content: {
    paddingLeft: 8,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textLight,
    fontStyle: 'italic',
    padding: 8,
    textAlign: 'center',
  },
});
