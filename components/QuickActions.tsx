import React from 'react';
import { View, Text, StyleSheet, Pressable, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import {
  MessageCircle,
  BookOpen,
  FileQuestion,
  Calendar,
  BarChart,
  FileText,
  Brain,
  Zap
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

interface QuickActionProps {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  gradientColors: string[];
  onPress: () => void;
}

const QuickActionCard: React.FC<QuickActionProps> = ({
  title,
  subtitle,
  icon,
  gradientColors,
  onPress
}) => {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.actionCard,
        pressed && { transform: [{ scale: 0.95 }], opacity: 0.9 }
      ]}
      onPress={onPress}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.actionCardGradient}
      >
        <View style={styles.actionIconContainer}>
          {React.cloneElement(icon as React.ReactElement, { 
            color: "#fff", 
            size: 24 
          })}
        </View>
        <View style={styles.actionContent}>
          <Text style={styles.actionTitle}>{title}</Text>
          <Text style={styles.actionSubtitle}>{subtitle}</Text>
        </View>
        <View style={styles.actionArrow}>
          <View style={styles.arrowIcon} />
        </View>
      </LinearGradient>
    </Pressable>
  );
};

export const QuickActions: React.FC = () => {
  const router = useRouter();

  const actions = [
    {
      title: "Chat com IA",
      subtitle: "Tire dúvidas instantaneamente",
      icon: <MessageCircle />,
      gradientColors: [colors.primary, colors.primaryLight],
      onPress: () => router.push('/chat')
    },
    {
      title: "Flashcards",
      subtitle: "Memorize conceitos",
      icon: <BookOpen />,
      gradientColors: [colors.secondary, '#FF8A65'],
      onPress: () => router.push('/flashcards')
    },
    {
      title: "Quiz Rápido",
      subtitle: "Teste seus conhecimentos",
      icon: <FileQuestion />,
      gradientColors: [colors.accent1, '#42A5F5'],
      onPress: () => router.push('/quizzes')
    },
    {
      title: "Anotações",
      subtitle: "Organize suas ideias",
      icon: <FileText />,
      gradientColors: [colors.accent2, '#AB47BC'],
      onPress: () => router.push('/notes')
    }
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>Ações Rápidas</Text>
        <View style={styles.badge}>
          <Zap size={16} color={colors.primary} />
          <Text style={styles.badgeText}>Rápido</Text>
        </View>
      </View>
      
      <View style={styles.actionsGrid}>
        {actions.map((action, index) => (
          <QuickActionCard
            key={index}
            title={action.title}
            subtitle={action.subtitle}
            icon={action.icon}
            gradientColors={action.gradientColors}
            onPress={action.onPress}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    gap: 4,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: colors.primary,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionCard: {
    width: '48%',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  actionCardGradient: {
    padding: 20,
    minHeight: 120,
    justifyContent: 'space-between',
  },
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 18,
  },
  actionArrow: {
    alignSelf: 'flex-end',
    marginTop: 8,
  },
  arrowIcon: {
    width: 6,
    height: 6,
    borderTopWidth: 2,
    borderRightWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
    transform: [{ rotate: '45deg' }],
  },
});
