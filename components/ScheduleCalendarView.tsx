import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Dimensions
} from 'react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isToday, isSameDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ScheduleItem, Schedule } from '@/types';
import { Clock, BookOpen, ChevronLeft, ChevronRight, Calendar } from 'lucide-react-native';

interface ScheduleCalendarViewProps {
  schedule: Schedule;
  items: ScheduleItem[];
  onItemPress?: (item: ScheduleItem) => void;
}

export const ScheduleCalendarView: React.FC<ScheduleCalendarViewProps> = ({
  schedule,
  items,
  onItemPress
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [visibleItems, setVisibleItems] = useState<ScheduleItem[]>([]);

  useEffect(() => {
    // Generate week days
    const start = startOfWeek(currentDate, { weekStartsOn: 0 }); // Sunday
    const end = endOfWeek(currentDate, { weekStartsOn: 0 }); // Saturday
    const days = eachDayOfInterval({ start, end });
    setWeekDays(days);

    // Set selected date to today if it's in the current week, otherwise to the first day of the week
    const today = new Date();
    const dayInWeek = days.find(day => isSameDay(day, today));
    setSelectedDate(dayInWeek || days[0]);
  }, [currentDate]);

  useEffect(() => {
    // Filter items for the selected date
    filterItemsForSelectedDate();
  }, [selectedDate, items]);

  const filterItemsForSelectedDate = () => {
    if (!selectedDate || !items.length) {
      setVisibleItems([]);
      return;
    }

    const dayOfWeek = selectedDate.getDay();
    const dayOfMonth = selectedDate.getDate();

    const filtered = items.filter(item => {
      // Check for specific date
      if (item.specificDate && isSameDay(new Date(item.specificDate), selectedDate)) {
        return true;
      }

      // Check for day of week (for weekly schedules)
      if (schedule.type === 'weekly' && item.dayOfWeek === dayOfWeek) {
        return true;
      }

      // Check for day of month (for monthly schedules)
      if (schedule.type === 'monthly' && item.dayOfMonth === dayOfMonth) {
        return true;
      }

      return false;
    });

    setVisibleItems(filtered);
  };

  const handlePreviousWeek = () => {
    setCurrentDate(addDays(currentDate, -7));
  };

  const handleNextWeek = () => {
    setCurrentDate(addDays(currentDate, 7));
  };

  const handleDayPress = (day: Date) => {
    setSelectedDate(day);
  };

  const getTimeText = (item: ScheduleItem) => {
    if (!item.startTime || !item.endTime) return '';

    let startTimeStr = '';
    let endTimeStr = '';

    try {
      // Handle ISO string format
      if (typeof item.startTime === 'string') {
        if (item.startTime.includes('T')) {
          startTimeStr = format(new Date(item.startTime), 'HH:mm');
        } else if (item.startTime.includes(':')) {
          // Handle time-only format (HH:MM:SS)
          startTimeStr = item.startTime.substring(0, 5); // Extract HH:MM
        } else {
          // Fallback
          startTimeStr = item.startTime;
        }
      }

      if (typeof item.endTime === 'string') {
        if (item.endTime.includes('T')) {
          endTimeStr = format(new Date(item.endTime), 'HH:mm');
        } else if (item.endTime.includes(':')) {
          // Handle time-only format (HH:MM:SS)
          endTimeStr = item.endTime.substring(0, 5); // Extract HH:MM
        } else {
          // Fallback
          endTimeStr = item.endTime;
        }
      }
    } catch (error) {
      console.error('Error formatting time:', error);
      // Fallback to raw values if formatting fails
      startTimeStr = typeof item.startTime === 'string' ? item.startTime : 'Indefinido';
      endTimeStr = typeof item.endTime === 'string' ? item.endTime : 'Indefinido';
    }

    return `${startTimeStr} - ${endTimeStr}`;
  };

  return (
    <View style={styles.container}>
      <GlassCard style={styles.calendarCard}>
        <View style={styles.calendarHeader}>
          <Pressable style={styles.navButton} onPress={handlePreviousWeek}>
            <ChevronLeft size={24} color={colors.primary} />
          </Pressable>

          <Text style={styles.monthText}>
            {format(startOfWeek(currentDate, { weekStartsOn: 0 }), 'd', { locale: ptBR })} - {format(endOfWeek(currentDate, { weekStartsOn: 0 }), 'd', { locale: ptBR })} {format(currentDate, 'MMMM yyyy', { locale: ptBR })}
          </Text>

          <Pressable style={styles.navButton} onPress={handleNextWeek}>
            <ChevronRight size={24} color={colors.primary} />
          </Pressable>
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.weekDaysContainer}>
            {weekDays.map((day) => (
              <Pressable
                key={day.toISOString()}
                style={[
                  styles.dayItem,
                  isToday(day) && styles.todayItem,
                  isSameDay(day, selectedDate) && styles.selectedDayItem
                ]}
                onPress={() => handleDayPress(day)}
                hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
              >
                <Text style={[
                  styles.dayName,
                  isSameDay(day, selectedDate) && styles.selectedDayText
                ]}>
                  {format(day, 'EEE', { locale: ptBR })}
                </Text>
                <Text style={[
                  styles.dayNumber,
                  isSameDay(day, selectedDate) && styles.selectedDayText
                ]}>
                  {format(day, 'd')}
                </Text>
              </Pressable>
            ))}
          </View>
        </ScrollView>

        <View style={styles.scheduleContainer}>
          <Text style={styles.dateText}>
            {format(selectedDate, "EEEE, d 'de' MMMM", { locale: ptBR })}
          </Text>

          {visibleItems.length > 0 ? (
            <ScrollView style={styles.itemsContainer}>
              {visibleItems.map((item) => (
                <Pressable
                  key={item.id}
                  style={[styles.itemCard, { borderLeftColor: item.color || colors.primary }]}
                  onPress={() => onItemPress && onItemPress(item)}
                >
                  <Text style={styles.itemTitle}>{item.subjectTitle}</Text>

                  <View style={styles.itemDetails}>
                    <View style={styles.itemDetail}>
                      <Clock size={16} color={colors.textLight} />
                      <Text style={styles.itemDetailText}>{getTimeText(item)}</Text>
                    </View>

                    <View style={styles.itemDetail}>
                      <BookOpen size={16} color={colors.textLight} />
                      <Text style={styles.itemDetailText}>Estudo</Text>
                    </View>
                  </View>
                </Pressable>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.emptyContainer}>
              <Calendar size={48} color={colors.textLight} />
              <Text style={styles.emptyText}>
                Nenhuma atividade programada para este dia.
              </Text>
            </View>
          )}
      </View>
    </GlassCard>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  calendarCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: `${colors.border}50`,
    backgroundColor: `${colors.primary}05`,
  },
  navButton: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: `${colors.primary}10`,
  },
  navButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  monthText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    textTransform: 'capitalize',
    textAlign: 'center',
    flex: 1,
  },
  weekDaysContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 16,
    marginBottom: 8,
  },
  dayItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 4,
    minWidth: 60,
  },
  todayItem: {
    backgroundColor: `${colors.primary}15`,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  selectedDayItem: {
    backgroundColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  dayName: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textMedium,
    textTransform: 'uppercase',
    marginBottom: 6,
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  selectedDayText: {
    color: colors.white,
  },
  scheduleContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  dateText: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 16,
    textTransform: 'capitalize',
    textAlign: 'center',
    backgroundColor: `${colors.primary}10`,
    paddingVertical: 8,
    borderRadius: 8,
  },
  itemsContainer: {
    flex: 1,
  },
  itemCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 10,
  },
  itemDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: `${colors.backgroundLight}50`,
    padding: 10,
    borderRadius: 8,
  },
  itemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 6,
  },
  itemDetailText: {
    fontSize: 14,
    color: colors.textMedium,
    marginLeft: 6,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
    marginTop: 20,
    backgroundColor: `${colors.backgroundLight}50`,
    borderRadius: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 22,
  },
});
