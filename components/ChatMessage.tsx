import React, { memo, useMemo } from "react";
import { View, Text, StyleSheet, Image } from "react-native";
import { colors } from "@/constants/colors";
import { images } from "@/constants/images";
import { User } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";
import Markdown from 'react-native-markdown-display';

// Componente memoizado para o avatar da Lia
const LiaAvatar = memo(() => {
  // Usar useMemo para garantir que a imagem seja carregada apenas uma vez
  const imageSource = useMemo(() => images.logo, []);

  return (
    <Image
      source={imageSource}
      style={styles.logoImage}
      resizeMode="contain"
      fadeDuration={0}
      cachePolicy="memory"
    />
  );
});

interface ChatMessageProps {
  message: {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at?: string;
  };
}

// Usar memo para evitar re-renderizações desnecessárias
export const ChatMessage: React.FC<ChatMessageProps> = memo(({ message }) => {
  const isUser = message.role === "user";
  const isAssistant = message.role === "assistant" || message.role === "system";

  return (
    <View
      style={[
        styles.container,
        isUser ? styles.userContainer : styles.systemContainer,
      ]}
    >
      <View style={isUser ? styles.userAvatarContainer : styles.liaAvatarContainer}>
        {isUser ? (
          <LinearGradient
            colors={["#64748B", "#94A3B8"]}
            style={styles.avatarGradient}
          >
            <User size={20} color="#fff" />
          </LinearGradient>
        ) : (
          <LiaAvatar />
        )}
      </View>

      {isUser ? (
        <LinearGradient
          colors={colors.primaryGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.messageContainer, styles.userMessageContainer]}
        >
          <Text style={styles.userMessageText}>{message.content}</Text>
        </LinearGradient>
      ) : isAssistant ? (
        <GlassCard style={[styles.messageContainer, styles.systemMessageContainer]}>
          <Markdown style={markdownStyles}>
            {message.content}
          </Markdown>
        </GlassCard>
      ) : null}
    </View>
  );
}, (prevProps, nextProps) => {
  // Comparar apenas o ID e o conteúdo da mensagem para determinar se precisa re-renderizar
  return prevProps.message.id === nextProps.message.id &&
         prevProps.message.content === nextProps.message.content;
});

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  userContainer: {
    justifyContent: "flex-end",
  },
  systemContainer: {
    justifyContent: "flex-start",
  },
  userAvatarContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    overflow: "hidden",
    marginRight: 8,
  },
  liaAvatarContainer: {
    width: 36,
    height: 36,
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  logoImage: {
    width: 36,
    height: 36,
  },
  messageContainer: {
    maxWidth: "80%",
    borderRadius: 20,
    padding: 12,
  },
  userMessageContainer: {
    borderBottomRightRadius: 4,
  },
  systemMessageContainer: {
    borderBottomLeftRadius: 4,
    padding: 16,
  },
  userMessageText: {
    fontSize: 16,
    lineHeight: 22,
    color: "#fff",
  },
  systemMessageText: {
    fontSize: 16,
    lineHeight: 22,
    color: colors.text,
  },
});

const markdownStyles = StyleSheet.create({
  body: {
    color: colors.text,
    fontSize: 16,
    lineHeight: 22,
  },
  heading1: {
    color: colors.text,
    fontWeight: 'bold',
    fontSize: 20,
    marginTop: 8,
    marginBottom: 4,
  },
  heading2: {
    color: colors.text,
    fontWeight: 'bold',
    fontSize: 18,
    marginTop: 8,
    marginBottom: 4,
  },
  heading3: {
    color: colors.text,
    fontWeight: 'bold',
    fontSize: 16,
    marginTop: 8,
    marginBottom: 4,
  },
  paragraph: {
    color: colors.text,
    fontSize: 16,
    lineHeight: 22,
    marginTop: 4,
    marginBottom: 8,
  },
  link: {
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  blockquote: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    paddingLeft: 8,
    marginLeft: 8,
    marginVertical: 8,
  },
  code_block: {
    backgroundColor: `${colors.primary}10`,
    padding: 8,
    borderRadius: 4,
    fontFamily: 'monospace',
    marginVertical: 8,
  },
  code_inline: {
    backgroundColor: `${colors.primary}10`,
    padding: 2,
    borderRadius: 4,
    fontFamily: 'monospace',
  },
  list_item: {
    flexDirection: 'row',
    marginVertical: 2,
  },
  bullet_list: {
    marginLeft: 8,
  },
  ordered_list: {
    marginLeft: 8,
  },
});