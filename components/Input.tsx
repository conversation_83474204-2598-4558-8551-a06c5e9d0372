import React, { useState, useRef, forwardRef, useImperativeHandle } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  TextInputProps,
  ViewStyle,
  TextStyle,
  Animated,
  Platform,
  KeyboardTypeOptions,
} from "react-native";
import { theme } from "@/constants/theme";
import { colors } from "@/constants/colors";
import { Eye, EyeOff, AlertCircle, Check } from "lucide-react-native";

export interface InputProps extends Omit<TextInputProps, "style"> {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  success?: boolean;
  helperText?: string;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  secureTextEntry?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  multiline?: boolean;
  numberOfLines?: number;
  keyboardType?: KeyboardTypeOptions;
  autoCapitalize?: "none" | "sentences" | "words" | "characters";
  autoCorrect?: boolean;
  maxLength?: number;
  editable?: boolean;
  required?: boolean;
  onBlur?: () => void;
  onFocus?: () => void;
  validator?: (value: string) => boolean;
}

export interface InputRef {
  focus: () => void;
  blur: () => void;
  clear: () => void;
  isFocused: () => boolean;
}

export const Input = forwardRef<InputRef, InputProps>(
  (
    {
      label,
      placeholder,
      value,
      onChangeText,
      error,
      success,
      helperText,
      containerStyle,
      inputStyle,
      labelStyle,
      secureTextEntry = false,
      icon,
      iconPosition = "left",
      multiline = false,
      numberOfLines = 1,
      keyboardType,
      autoCapitalize = "none",
      autoCorrect = false,
      maxLength,
      editable = true,
      required = false,
      onBlur,
      onFocus,
      validator,
      ...rest
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState(false);
    const [isPasswordVisible, setIsPasswordVisible] = useState(false);
    const [isValid, setIsValid] = useState<boolean | null>(null);
    const inputRef = useRef<TextInput>(null);
    const focusAnim = useRef(new Animated.Value(0)).current;

    // Expor métodos do input para o componente pai
    useImperativeHandle(ref, () => ({
      focus: () => {
        inputRef.current?.focus();
      },
      blur: () => {
        inputRef.current?.blur();
      },
      clear: () => {
        inputRef.current?.clear();
      },
      isFocused: () => {
        return inputRef.current?.isFocused() || false;
      },
    }));

    // Animar o foco
    const handleFocus = () => {
      setIsFocused(true);
      Animated.timing(focusAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
      onFocus && onFocus();
    };

    // Animar a perda de foco e validar
    const handleBlur = () => {
      setIsFocused(false);
      Animated.timing(focusAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();

      // Validar o campo se houver um validador
      if (validator && value) {
        setIsValid(validator(value));
      }

      onBlur && onBlur();
    };

    // Alternar visibilidade da senha
    const togglePasswordVisibility = () => {
      setIsPasswordVisible(!isPasswordVisible);
    };

    // Calcular a cor da borda com base no estado
    const getBorderColor = () => {
      if (error) return colors.error;
      if (success || isValid) return colors.success;
      if (isFocused) return colors.primary;
      return colors.border;
    };

    // Calcular a altura do input
    const getInputHeight = () => {
      if (multiline) {
        return numberOfLines * 24;
      }
      return 56;
    };

    return (
      <View style={[styles.container, containerStyle]}>
        {label && (
          <View style={styles.labelContainer}>
            <Text style={[styles.label, labelStyle]}>
              {label}
              {required && <Text style={styles.required}> *</Text>}
            </Text>
          </View>
        )}

        <View
          style={[
            styles.inputContainer,
            {
              borderColor: getBorderColor(),
              height: getInputHeight(),
              borderWidth: isFocused ? 2 : 1,
            },
            !editable && styles.disabledInput,
          ]}
        >
          {icon && iconPosition === "left" && (
            <View style={styles.iconContainer}>{icon}</View>
          )}

          <TextInput
            ref={inputRef}
            style={[
              styles.input,
              multiline && styles.multilineInput,
              iconPosition === "left" && styles.inputWithLeftIcon,
              (secureTextEntry || iconPosition === "right") && styles.inputWithRightIcon,
              inputStyle,
            ]}
            placeholder={placeholder}
            placeholderTextColor={colors.textMedium}
            value={value}
            onChangeText={(text) => {
              onChangeText(text);
              if (validator && text) {
                setIsValid(validator(text));
              }
            }}
            secureTextEntry={secureTextEntry && !isPasswordVisible}
            multiline={multiline}
            numberOfLines={multiline ? numberOfLines : undefined}
            keyboardType={keyboardType}
            autoCapitalize={autoCapitalize}
            autoCorrect={autoCorrect}
            maxLength={maxLength}
            editable={editable}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...rest}
          />

          {secureTextEntry && (
            <Pressable
              style={styles.iconContainer}
              onPress={togglePasswordVisibility}
            >
              {isPasswordVisible ? (
                <EyeOff size={20} color={colors.textMedium} />
              ) : (
                <Eye size={20} color={colors.textMedium} />
              )}
            </Pressable>
          )}

          {!secureTextEntry && iconPosition === "right" && icon && (
            <View style={styles.iconContainer}>{icon}</View>
          )}

          {isValid !== null && !error && (
            <View style={styles.validationIconContainer}>
              {isValid ? (
                <Check size={20} color={colors.success} />
              ) : (
                <AlertCircle size={20} color={colors.error} />
              )}
            </View>
          )}
        </View>

        {(error || helperText) && (
          <Text
            style={[
              styles.helperText,
              error ? styles.errorText : styles.infoText,
            ]}
          >
            {error || helperText}
          </Text>
        )}
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
    width: "100%",
  },
  labelContainer: {
    marginBottom: theme.spacing.xs,
    flexDirection: "row",
  },
  label: {
    fontSize: theme.typography.size.sm,
    fontWeight: theme.typography.weight.medium,
    color: colors.text,
  },
  required: {
    color: colors.error,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: colors.backgroundLight,
    paddingHorizontal: theme.spacing.md,
  },
  input: {
    flex: 1,
    fontSize: theme.typography.size.md,
    color: colors.text,
    paddingVertical: Platform.OS === "ios" ? 16 : 12,
  },
  multilineInput: {
    textAlignVertical: "top",
    paddingTop: 16,
  },
  inputWithLeftIcon: {
    paddingLeft: theme.spacing.sm,
  },
  inputWithRightIcon: {
    paddingRight: theme.spacing.sm,
  },
  iconContainer: {
    paddingHorizontal: theme.spacing.xs,
  },
  validationIconContainer: {
    paddingHorizontal: theme.spacing.xs,
  },
  helperText: {
    fontSize: theme.typography.size.xs,
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.xs,
  },
  errorText: {
    color: colors.error,
  },
  infoText: {
    color: colors.textMedium,
  },
  disabledInput: {
    backgroundColor: colors.backgroundDark,
    opacity: 0.7,
  },
});

export default Input;
