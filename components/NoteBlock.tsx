import React, { useState, useRef } from "react";
import { View, Text, StyleSheet, Pressable, TextInput, Image, Platform, NativeSyntheticEvent, TextInputSelectionChangeEventData } from "react-native";
import { colors } from "@/constants/colors";
import { NoteBlock as NoteBlockType } from "@/types";
import {
  GripVertical,
  Edit2,
  Trash2,
  Plus,
  Image as ImageIcon,
  Table,
  List,
  Code,
  Quote,
  Minus,
  File
} from "lucide-react-native";

interface NoteBlockProps {
  block: NoteBlockType;
  isEditing: boolean;
  onUpdate: (updatedBlock: NoteBlockType) => void;
  onDelete: (blockId: string) => void;
  onAddBlockAfter: (blockId: string) => void;
  onMoveBlock?: (blockId: string, direction: 'up' | 'down') => void;
}

export const NoteBlock: React.FC<NoteBlockProps> = ({
  block,
  isEditing,
  onUpdate,
  onDelete,
  onAddBlockAfter,
  onMoveBlock,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [content, setContent] = useState(block.content);
  const [selection, setSelection] = useState<{start: number, end: number} | null>(null);
  const inputRef = useRef<TextInput>(null);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    // Only update the block if it's the current block being edited
    // This prevents issues with deleting content affecting other blocks
    onUpdate({ ...block, content: newContent });
  };

  const renderBlockContent = () => {
    if (isEditing) {
      switch (block.type) {
        case "heading":
          return (
            <TextInput
              ref={inputRef}
              style={styles.headingInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Título"
              multiline
              onSelectionChange={(event: NativeSyntheticEvent<TextInputSelectionChangeEventData>) => {
                setSelection(event.nativeEvent.selection);
                // Atualiza o bloco com a posição atual do cursor
                onUpdate({
                  ...block,
                  selection: event.nativeEvent.selection
                });
              }}
            />
          );
        case "subheading":
          return (
            <TextInput
              ref={inputRef}
              style={styles.subheadingInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Subtítulo"
              multiline
              onSelectionChange={(event: NativeSyntheticEvent<TextInputSelectionChangeEventData>) => {
                setSelection(event.nativeEvent.selection);
                onUpdate({
                  ...block,
                  selection: event.nativeEvent.selection
                });
              }}
            />
          );
        case "text":
          return (
            <TextInput
              style={styles.textInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Digite seu texto aqui..."
              multiline
            />
          );
        case "image":
          return (
            <View style={styles.imageContainer}>
              {block.metadata?.url ? (
                <View>
                  <Image
                    source={{ uri: block.metadata.url }}
                    style={styles.image}
                    resizeMode="contain"
                  />
                  <TextInput
                    style={styles.imageCaption}
                    value={content}
                    onChangeText={handleContentChange}
                    placeholder="Adicionar legenda..."
                  />
                </View>
              ) : (
                <Pressable style={styles.imagePlaceholder}>
                  <ImageIcon size={32} color={colors.textLight} />
                  <Text style={styles.imagePlaceholderText}>Adicionar imagem</Text>
                </Pressable>
              )}
            </View>
          );
        case "table":
          return (
            <View style={styles.tablePlaceholder}>
              <Table size={32} color={colors.textLight} />
              <Text style={styles.placeholderText}>Tabela</Text>
              <Text style={styles.placeholderSubtext}>
                Toque para editar a tabela
              </Text>
            </View>
          );
        case "list":
          return (
            <TextInput
              style={styles.textInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Lista de itens (um por linha)"
              multiline
            />
          );
        case "code":
          return (
            <TextInput
              style={styles.codeInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Código"
              multiline
            />
          );
        case "quote":
          return (
            <TextInput
              style={styles.quoteInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Citação"
              multiline
            />
          );
        case "divider":
          return <View style={styles.divider} />;
        case "file":
          return (
            <View style={styles.filePlaceholder}>
              <File size={32} color={colors.textLight} />
              <Text style={styles.placeholderText}>Arquivo</Text>
              <Text style={styles.placeholderSubtext}>
                Toque para adicionar um arquivo
              </Text>
            </View>
          );
        default:
          return (
            <TextInput
              ref={inputRef}
              style={styles.textInput}
              value={content}
              onChangeText={handleContentChange}
              placeholder="Digite seu texto aqui..."
              multiline
              onSelectionChange={(event: NativeSyntheticEvent<TextInputSelectionChangeEventData>) => {
                setSelection(event.nativeEvent.selection);
                onUpdate({
                  ...block,
                  selection: event.nativeEvent.selection
                });
              }}
            />
          );
      }
    } else {
      // View mode
      switch (block.type) {
        case "heading":
          return <Text style={styles.heading}>{content}</Text>;
        case "subheading":
          return <Text style={styles.subheading}>{content}</Text>;
        case "text":
          return <Text style={styles.text}>{content}</Text>;
        case "image":
          return (
            <View style={styles.imageContainer}>
              {block.metadata?.url && (
                <View>
                  <Image
                    source={{ uri: block.metadata.url }}
                    style={styles.image}
                    resizeMode="contain"
                  />
                  {content && <Text style={styles.imageCaption}>{content}</Text>}
                </View>
              )}
            </View>
          );
        case "table":
          return (
            <View style={styles.tablePlaceholder}>
              <Table size={32} color={colors.textLight} />
              <Text style={styles.placeholderText}>Tabela</Text>
            </View>
          );
        case "list":
          return (
            <View style={styles.listContainer}>
              {content.split("\n").map((item, index) => (
                <View key={index} style={styles.listItem}>
                  <View style={styles.listBullet} />
                  <Text style={styles.listItemText}>{item}</Text>
                </View>
              ))}
            </View>
          );
        case "code":
          return <Text style={styles.code}>{content}</Text>;
        case "quote":
          return <Text style={styles.quote}>{content}</Text>;
        case "divider":
          return <View style={styles.divider} />;
        case "file":
          return (
            <View style={styles.fileContainer}>
              <File size={24} color={colors.primary} />
              <Text style={styles.fileName}>
                {block.metadata?.name || "Arquivo"}
              </Text>
            </View>
          );
        default:
          return <Text style={styles.text}>{content}</Text>;
      }
    }
  };

  return (
    <View
      style={[
        styles.blockContainer,
        isHovered && isEditing && styles.hoveredBlock,
      ]}
      onTouchStart={() => setIsHovered(true)}
      onTouchEnd={() => setIsHovered(false)}
    >
      <View style={styles.blockContent}>{renderBlockContent()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  blockContainer: {
    marginBottom: 16,
    borderRadius: 8,
  },
  hoveredBlock: {
    backgroundColor: `${colors.primary}05`,
  },
  // Removed block controls
  blockContent: {
    flex: 1,
    paddingVertical: 8,
  },
  headingInput: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    padding: 8,
  },
  subheadingInput: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
    padding: 8,
  },
  textInput: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text,
    padding: 8,
  },
  codeInput: {
    fontSize: 14,
    fontFamily: Platform.OS === "ios" ? "Menlo" : "monospace",
    backgroundColor: colors.backgroundDark,
    color: colors.textLight,
    padding: 16,
    borderRadius: 8,
  },
  quoteInput: {
    fontSize: 16,
    fontStyle: "italic",
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    paddingLeft: 16,
    paddingVertical: 8,
    marginLeft: 8,
  },
  heading: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
  },
  subheading: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text,
  },
  imageContainer: {
    marginVertical: 8,
  },
  image: {
    width: "100%",
    height: 200,
    borderRadius: 8,
  },
  imageCaption: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: "center",
    marginTop: 8,
  },
  imagePlaceholder: {
    width: "100%",
    height: 150,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: "dashed",
  },
  imagePlaceholderText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 8,
  },
  tablePlaceholder: {
    width: "100%",
    height: 120,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
    marginVertical: 8,
  },
  placeholderText: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 8,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: colors.textLight,
    marginTop: 4,
  },
  listContainer: {
    marginVertical: 8,
  },
  listItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  listBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 8,
    marginRight: 8,
  },
  listItemText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 24,
    color: colors.text,
  },
  code: {
    fontSize: 14,
    fontFamily: Platform.OS === "ios" ? "Menlo" : "monospace",
    backgroundColor: colors.backgroundDark,
    color: colors.textLight,
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  quote: {
    fontSize: 16,
    fontStyle: "italic",
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    paddingLeft: 16,
    paddingVertical: 8,
    marginLeft: 8,
    marginVertical: 8,
    color: colors.text,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 16,
  },
  fileContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
  },
  fileName: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 12,
  },
  filePlaceholder: {
    width: "100%",
    height: 100,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: "dashed",
    marginVertical: 8,
  },
});
