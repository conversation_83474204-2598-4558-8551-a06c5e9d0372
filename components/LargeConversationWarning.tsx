import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Alert,
} from 'react-native';
import { AlertTriangle, X } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useAssistantChatStore } from '@/store/assistantChatStore';

interface LargeConversationWarningProps {
  onNewChat: () => void;
}

export const LargeConversationWarning: React.FC<LargeConversationWarningProps> = ({
  onNewChat,
}) => {
  const { dismissLargeConversationWarning } = useAssistantChatStore();

  const handleNewChat = () => {
    dismissLargeConversationWarning();
    onNewChat();
  };

  const handleDismiss = () => {
    dismissLargeConversationWarning();
  };

  return (
    <View style={styles.container}>
      <View style={styles.warningContent}>
        <AlertTriangle size={20} color={colors.warning} style={styles.icon} />
        <Text style={styles.warningText}>
          Esta conversa está ficando grande. Para melhor desempenho, considere iniciar uma nova conversa.
        </Text>
        <Pressable style={styles.closeButton} onPress={handleDismiss}>
          <X size={18} color={colors.textLight} />
        </Pressable>
      </View>
      <View style={styles.buttonContainer}>
        <Pressable style={styles.newChatButton} onPress={handleNewChat}>
          <Text style={styles.newChatButtonText}>Iniciar Nova Conversa</Text>
        </Pressable>
        <Pressable style={styles.continueButton} onPress={handleDismiss}>
          <Text style={styles.continueButtonText}>Continuar Nesta Conversa</Text>
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: `${colors.warning}15`,
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 12,
    borderWidth: 1,
    borderColor: `${colors.warning}30`,
  },
  warningContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
  },
  closeButton: {
    padding: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
  },
  newChatButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  newChatButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  continueButton: {
    backgroundColor: 'transparent',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.textLight,
  },
  continueButtonText: {
    color: colors.text,
    fontSize: 14,
    fontWeight: '500',
  },
});
