import React from 'react';
import { View, Text, StyleSheet, Pressable, Alert } from 'react-native';
import { colors } from '@/constants/colors';
import { useTimerStore, TimerMode } from '@/store/timerStore';
import { Clock, Pause, Play, X } from 'lucide-react-native';

export const TimerOverlay: React.FC = () => {
  const {
    isActive,
    mode,
    timeRemaining,
    pauseTimer,
    resumeTimer,
    stopTimer
  } = useTimerStore();

  // Não mostrar o overlay se o timer não estiver ativo ou estiver no modo idle
  if (mode === 'idle' || !isActive) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = (mode: TimerMode): string => {
    switch (mode) {
      case 'focus':
        return colors.primary;
      case 'break':
        return colors.success;
      default:
        return colors.textLight;
    }
  };

  const getTimerLabel = (mode: TimerMode): string => {
    switch (mode) {
      case 'focus':
        return 'Foco';
      case 'break':
        return 'Descanso';
      default:
        return 'Pomodoro';
    }
  };

  const handleToggleTimer = () => {
    if (isActive) {
      pauseTimer();
    } else {
      resumeTimer();
    }
  };

  const handleStop = () => {
    Alert.alert(
      'Cancelar Temporizador',
      'Tem certeza que deseja cancelar o temporizador? Seu progresso será salvo, mas a sessão atual será interrompida.',
      [
        {
          text: 'Continuar',
          style: 'cancel',
        },
        {
          text: 'Cancelar Temporizador',
          style: 'destructive',
          onPress: () => stopTimer(),
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={[styles.content, { borderColor: getTimerColor(mode) }]}>
        <View style={styles.infoContainer}>
          <Clock size={16} color={getTimerColor(mode)} />
          <Text style={[styles.label, { color: getTimerColor(mode) }]}>
            {getTimerLabel(mode)}
          </Text>
          <Text style={styles.time}>{formatTime(timeRemaining)}</Text>
        </View>

        <View style={styles.controls}>
          <Pressable
            style={styles.controlButton}
            onPress={handleToggleTimer}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            {isActive ? (
              <Pause size={16} color={colors.text} />
            ) : (
              <Play size={16} color={colors.text} />
            )}
          </Pressable>

          <Pressable
            style={styles.controlButton}
            onPress={handleStop}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <X size={16} color={colors.error} />
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50, // Posicionado abaixo do header
    left: 0,
    right: 0,
    zIndex: 1000,
    alignItems: 'center',
    paddingTop: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.card,
    borderRadius: 20,
    borderWidth: 2,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    width: '90%',
    maxWidth: 300,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
  },
  time: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  controlButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.backgroundLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
