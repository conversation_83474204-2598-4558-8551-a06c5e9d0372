import React, { useState } from "react";
import { View, Text, StyleSheet, Pressable, Animated } from "react-native";
import { colors } from "@/constants/colors";
import { 
  Plus, 
  Calendar,
  ClipboardList,
  X
} from "lucide-react-native";

interface CalendarFABProps {
  onAddEvent: () => void;
  onAddTask: () => void;
}

export const CalendarFAB: React.FC<CalendarFABProps> = ({
  onAddEvent,
  onAddTask,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const animation = useState(new Animated.Value(0))[0];

  const toggleMenu = () => {
    const toValue = isOpen ? 0 : 1;
    
    Animated.spring(animation, {
      toValue,
      friction: 5,
      useNativeDriver: true,
    }).start();
    
    setIsOpen(!isOpen);
  };

  // Animation for background overlay
  const bgOpacity = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5],
  });

  // Animation for rotation of the main button
  const rotation = animation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  // Animation for the task button
  const taskButtonTranslateY = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -100],
  });

  // Animation for the event button
  const eventButtonTranslateY = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -180],
  });

  return (
    <>
      {/* Background overlay when FAB is open */}
      <Animated.View
        style={[styles.fabOverlay, { opacity: bgOpacity }]}
        pointerEvents={isOpen ? 'auto' : 'none'}
      >
        <Pressable style={styles.fabOverlayTouchable} onPress={toggleMenu} />
      </Animated.View>

      <View style={styles.fabContainer}>
        {/* Event Button */}
        <Animated.View 
          style={[
            styles.fabButton, 
            styles.fabSecondary, 
            { 
              transform: [{ translateY: eventButtonTranslateY }], 
              opacity: animation 
            }
          ]}
        >
          <Pressable 
            onPress={() => {
              toggleMenu();
              onAddEvent();
            }} 
            style={styles.fabButtonInner}
          >
            <Calendar size={24} color={colors.white} />
          </Pressable>
          {isOpen && (
            <Animated.View style={[styles.fabLabel, { opacity: animation }]}>
              <Text style={styles.fabLabelText}>Novo Evento</Text>
            </Animated.View>
          )}
        </Animated.View>

        {/* Task Button */}
        <Animated.View 
          style={[
            styles.fabButton, 
            styles.fabPrimary, 
            { 
              transform: [{ translateY: taskButtonTranslateY }], 
              opacity: animation 
            }
          ]}
        >
          <Pressable 
            onPress={() => {
              toggleMenu();
              onAddTask();
            }} 
            style={styles.fabButtonInner}
          >
            <ClipboardList size={24} color={colors.white} />
          </Pressable>
          {isOpen && (
            <Animated.View style={[styles.fabLabel, { opacity: animation }]}>
              <Text style={styles.fabLabelText}>Nova Tarefa</Text>
            </Animated.View>
          )}
        </Animated.View>

        {/* Main FAB Button */}
        <View style={styles.fabMain}>
          <Pressable onPress={toggleMenu} style={styles.fabMainButton}>
            <Animated.View style={{ transform: [{ rotate: rotation }] }}>
              <Plus size={24} color={colors.white} />
            </Animated.View>
          </Pressable>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  fabContainer: {
    position: 'absolute',
    right: 20,
    bottom: 140,
    alignItems: 'center',
    zIndex: 10,
  },
  fabOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 1,
  },
  fabOverlayTouchable: {
    width: '100%',
    height: '100%',
  },
  fabMain: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 1,
  },
  fabMainButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fabButton: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  fabButtonInner: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fabPrimary: {
    backgroundColor: colors.primary,
  },
  fabSecondary: {
    backgroundColor: colors.secondary,
  },
  fabLabel: {
    position: 'absolute',
    right: 60,
    backgroundColor: colors.background,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  fabLabelText: {
    color: colors.text,
    fontSize: 14,
    fontWeight: '500',
  },
});
