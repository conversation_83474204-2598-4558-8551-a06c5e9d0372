import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal as RNModal,
  Pressable,
  ViewStyle,
  TextStyle,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { theme } from "@/constants/theme";
import { X } from "lucide-react-native";
import { Button } from "./Button";

const { height } = Dimensions.get("window");

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  animationType?: "none" | "slide" | "fade";
  position?: "center" | "bottom";
  fullScreen?: boolean;
  avoidKeyboard?: boolean;
  scrollable?: boolean;
  maxHeight?: number | string;
  width?: number | string;
  footer?: React.ReactNode;
  testID?: string;
  primaryButton?: {
    title: string;
    onPress: () => void;
    loading?: boolean;
    disabled?: boolean;
    variant?: "primary" | "secondary" | "success" | "error" | "warning";
  };
  secondaryButton?: {
    title: string;
    onPress: () => void;
    loading?: boolean;
    disabled?: boolean;
    variant?: "outline" | "glass";
  };
}

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  contentStyle,
  titleStyle,
  showCloseButton = true,
  closeOnBackdropPress = true,
  animationType = "fade",
  position = "center",
  fullScreen = false,
  avoidKeyboard = true,
  scrollable = false,
  maxHeight = "80%",
  width = "90%",
  footer,
  testID,
  primaryButton,
  secondaryButton,
}) => {
  // Animação para entrada do modal
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(height)).current;

  useEffect(() => {
    if (visible) {
      // Animar entrada
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animar saída
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, slideAnim]);

  // Determinar estilos com base na posição
  const getPositionStyle = (): ViewStyle => {
    if (position === "bottom") {
      return {
        justifyContent: "flex-end",
        paddingBottom: 20,
      };
    }
    return {
      justifyContent: "center",
      paddingHorizontal: 20, // Add horizontal padding for center modals
    };
  };

  // Determinar estilos do container do modal
  const getModalContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.lg,
      ...theme.shadows.lg,
    };

    if (fullScreen) {
      return {
        ...baseStyle,
        width: "100%",
        height: "100%",
        borderRadius: 0,
      };
    }

    if (position === "bottom") {
      return {
        ...baseStyle,
        width: "100%",
        maxHeight: "85%", // Limit height to maintain context
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        borderTopLeftRadius: theme.borderRadius.xl,
        borderTopRightRadius: theme.borderRadius.xl,
        paddingTop: theme.spacing.md,
      };
    }

    return {
      ...baseStyle,
      width,
      maxHeight: "90%", // Limit height to maintain context
    };
  };

  // Renderizar o conteúdo do modal
  const renderContent = () => (
    <View style={[styles.modalContainer, getModalContainerStyle()]}>
      {/* Cabeçalho */}
      {(title || showCloseButton) && (
        <View style={styles.header}>
          {title && (
            <Text style={[styles.title, titleStyle]} numberOfLines={2}>
              {title}
            </Text>
          )}
          {showCloseButton && (
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <X size={24} color={theme.colors.text} />
            </Pressable>
          )}
        </View>
      )}

      {/* Conteúdo */}
      {scrollable ? (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.contentContainer, contentStyle]}
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
      ) : (
        <View style={[styles.contentContainer, contentStyle]}>{children}</View>
      )}

      {/* Rodapé */}
      {(footer || primaryButton || secondaryButton) && (
        <View style={styles.footer}>
          {footer || (
            <View style={styles.buttonContainer}>
              {secondaryButton && (
                <Button
                  title={secondaryButton.title}
                  onPress={secondaryButton.onPress}
                  variant={secondaryButton.variant || "outline"}
                  loading={secondaryButton.loading}
                  disabled={secondaryButton.disabled}
                  style={styles.secondaryButton}
                />
              )}
              {primaryButton && (
                <Button
                  title={primaryButton.title}
                  onPress={primaryButton.onPress}
                  variant={primaryButton.variant || "primary"}
                  loading={primaryButton.loading}
                  disabled={primaryButton.disabled}
                  style={styles.primaryButton}
                />
              )}
            </View>
          )}
        </View>
      )}
    </View>
  );

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      testID={testID}
    >
      <Animated.View
        style={[
          styles.overlay,
          getPositionStyle(),
          { opacity: fadeAnim },
        ]}
      >
        <Pressable
          style={styles.backdrop}
          onPress={closeOnBackdropPress ? onClose : undefined}
        />

        {avoidKeyboard ? (
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={styles.keyboardAvoidingView}
          >
            <Animated.View
              style={[
                position === "bottom" && {
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {renderContent()}
            </Animated.View>
          </KeyboardAvoidingView>
        ) : (
          <Animated.View
            style={[
              position === "bottom" && {
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {renderContent()}
          </Animated.View>
        )}
      </Animated.View>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "transparent",
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  keyboardAvoidingView: {
    width: "100%",
    alignItems: "center",
  },
  modalContainer: {
    overflow: "hidden",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    ...theme.typography.heading3,
    flex: 1,
  },
  closeButton: {
    padding: theme.spacing.xs,
    marginLeft: theme.spacing.md,
  },
  scrollView: {
    maxHeight: "100%",
  },
  contentContainer: {
    padding: theme.spacing.lg,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    padding: theme.spacing.md,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  primaryButton: {
    minWidth: 120,
  },
  secondaryButton: {
    minWidth: 120,
    marginRight: theme.spacing.md,
  },
});

export default Modal;
