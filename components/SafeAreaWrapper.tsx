import React from 'react';
import {
  SafeAreaView,
  StyleSheet,
  ViewStyle,
  StatusBar,
  View,
  Platform,
  useColorScheme
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import { colors } from '@/constants/colors';

interface SafeAreaWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
  edges?: Array<'top' | 'right' | 'bottom' | 'left'>;
  backgroundColor?: string;
  useSafeArea?: boolean;
}

/**
 * A wrapper component that provides consistent safe area handling across the app
 */
export const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({
  children,
  style,
  edges,
  backgroundColor = colors.background,
  useSafeArea = true,
}) => {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // If we're not using safe area, just render the children with the background color
  if (!useSafeArea) {
    return (
      <View style={[
        styles.container,
        { backgroundColor },
        style
      ]}>
        {children}
      </View>
    );
  }

  // Calculate padding based on insets and edges
  const padding = {
    paddingTop: edges?.includes('top') ? insets.top : 0,
    paddingRight: edges?.includes('right') ? insets.right : 0,
    paddingBottom: edges?.includes('bottom') ? insets.bottom : 0,
    paddingLeft: edges?.includes('left') ? insets.left : 0,
  };

  // On iOS, use SafeAreaView
  if (Platform.OS === 'ios') {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor },
          style
        ]}
      >
        {children}
      </SafeAreaView>
    );
  }

  // On Android, manually apply padding based on insets
  return (
    <View
      style={[
        styles.container,
        padding,
        { backgroundColor },
        style
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SafeAreaWrapper;
