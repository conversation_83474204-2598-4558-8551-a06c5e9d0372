import React, { useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Dimensions,
  Animated,
  Platform
} from "react-native";
import { Flashcard as FlashcardType } from "@/types";
import { colors } from "@/constants/colors";
import { ArrowRight, Check, X, <PERSON>ota<PERSON><PERSON><PERSON>, BookOpen } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { GlassCard } from "./GlassCard";

interface FlashcardProps {
  flashcard: FlashcardType;
  onResult: (flashcard: FlashcardType, result: "easy" | "medium" | "hard") => void;
  onNext: () => void;
}

const { width } = Dimensions.get("window");

export const Flashcard: React.FC<FlashcardProps> = ({
  flashcard,
  onResult,
  onNext,
}) => {
  const [flipped, setFlipped] = useState(false);
  const [answered, setAnswered] = useState(false);

  // Animation values
  const flipAnimation = useRef(new Animated.Value(0)).current;

  const handleFlip = () => {
    if (!answered) {
      if (!flipped) {
        // Prevent multiple taps during animation
        setFlipped(true);
        Animated.timing(flipAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
          // Adding easing to make animation smoother
        }).start();
      } else {
        // Prevent multiple taps during animation
        setFlipped(false);
        Animated.timing(flipAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
          // Adding easing to make animation smoother
        }).start();
      }
    }
  };

  const handleResult = (result: "easy" | "medium" | "hard") => {
    setAnswered(true);
    onResult(flashcard, result);
  };

  const handleNext = () => {
    // Reset the card state
    setAnswered(false);

    // Reset animation first
    Animated.timing(flipAnimation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setFlipped(false);
      onNext();
    });
  };

  // Interpolate rotation based on flip animation
  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['180deg', '360deg'],
  });

  // Improved opacity transitions with smoother values
  const frontOpacity = flipAnimation.interpolate({
    inputRange: [0.85, 0.86],  // Narrower range for sharper transition
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  const backOpacity = flipAnimation.interpolate({
    inputRange: [0.14, 0.15],  // Narrower range for sharper transition
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      <View style={styles.cardContainer}>
        {/* Using a wrapper to stabilize the animation */}
        <View style={styles.cardWrapper}>
          <Animated.View
            style={[
              styles.card,
              {
                transform: [{ rotateY: frontInterpolate }],
                opacity: frontOpacity,
                // Adding perspective to fix the 3D effect
                zIndex: flipped ? 0 : 1,
              },
            ]}
          >
            <GlassCard style={styles.cardInner} gradient>
              <Pressable
                style={styles.cardContent}
                onPress={handleFlip}
              >
                <BookOpen size={28} color={colors.primary} style={styles.cardIcon} />
                <Text style={styles.questionText}>{flashcard.question}</Text>
                <View style={styles.tapHint}>
                  <Text style={styles.tapHintText}>Toque para virar</Text>
                </View>
              </Pressable>
            </GlassCard>
          </Animated.View>

          <Animated.View
            style={[
              styles.card,
              styles.cardBack,
              {
                transform: [{ rotateY: backInterpolate }],
                opacity: backOpacity,
                // Adding perspective to fix the 3D effect
                zIndex: flipped ? 1 : 0,
              },
            ]}
          >
            <GlassCard style={styles.cardInner} gradient>
              <Pressable
                style={styles.cardContent}
                onPress={handleFlip}
              >
                <Text style={styles.answerText}>{flashcard.answer}</Text>
                <View style={styles.tapHint}>
                  <Text style={styles.tapHintText}>Toque para virar</Text>
                </View>
              </Pressable>
            </GlassCard>
          </Animated.View>
        </View>
      </View>

      {!answered ? (
        <View style={styles.actionsContainer}>
          {flipped ? (
            <View style={styles.difficultyButtons}>
              <Pressable
                style={styles.difficultyButton}
                onPress={() => handleResult("hard")}
              >
                <LinearGradient
                  colors={colors.errorGradient}
                  style={styles.difficultyButtonGradient}
                >
                  <X size={24} color="#fff" />
                  <Text style={styles.difficultyButtonText}>Difícil</Text>
                </LinearGradient>
              </Pressable>

              <Pressable
                style={styles.difficultyButton}
                onPress={() => handleResult("medium")}
              >
                <LinearGradient
                  colors={colors.secondaryGradient}
                  style={styles.difficultyButtonGradient}
                >
                  <ArrowRight size={24} color="#fff" />
                  <Text style={styles.difficultyButtonText}>Médio</Text>
                </LinearGradient>
              </Pressable>

              <Pressable
                style={styles.difficultyButton}
                onPress={() => handleResult("easy")}
              >
                <LinearGradient
                  colors={colors.successGradient}
                  style={styles.difficultyButtonGradient}
                >
                  <Check size={24} color="#fff" />
                  <Text style={styles.difficultyButtonText}>Fácil</Text>
                </LinearGradient>
              </Pressable>
            </View>
          ) : (
            <Pressable
              style={styles.flipButtonContainer}
              onPress={handleFlip}
            >
              <LinearGradient
                colors={colors.primaryGradient}
                style={styles.flipButton}
              >
                <RotateCw size={28} color="#fff" />
                <Text style={styles.flipButtonText}>Virar cartão</Text>
              </LinearGradient>
            </Pressable>
          )}
        </View>
      ) : (
        <Pressable style={styles.nextButton} onPress={handleNext}>
          <LinearGradient
            colors={colors.primaryGradient}
            style={styles.nextButtonGradient}
          >
            <Text style={styles.nextButtonText}>Próximo</Text>
            <ArrowRight size={24} color="#fff" />
          </LinearGradient>
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    width: width - 32,
    marginBottom: 16,
  },
  cardContainer: {
    width: "100%",
    height: 350,
    marginBottom: 24,
    // Adding a fixed perspective to the container
    perspective: 1500,
  },
  // Adding a wrapper to stabilize the animation
  cardWrapper: {
    width: "100%",
    height: "100%",
    position: "relative",
  },
  card: {
    width: "100%",
    height: "100%",
    position: "absolute",
    backfaceVisibility: "hidden",
    // Ensuring the card doesn't shift during animation
    left: 0,
    top: 0,
    // Solid background to prevent transparency issues
    backgroundColor: colors.white,
    // Add border radius to match container
    borderRadius: 16,
    // Add shadow for better depth perception
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardBack: {
    transform: [{ rotateY: "180deg" }],
  },
  cardInner: {
    width: "100%",
    height: "100%",
    padding: 24,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 16,
  },
  cardContent: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  cardIcon: {
    marginBottom: 16,
    opacity: 0.8,
  },
  questionText: {
    fontSize: 26,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
  },
  answerText: {
    fontSize: 22,
    color: colors.text,
    textAlign: "center",
    lineHeight: 32,
  },
  tapHint: {
    position: "absolute",
    bottom: 16,
    alignItems: "center",
  },
  tapHintText: {
    fontSize: 14,
    color: colors.textLight,
  },
  actionsContainer: {
    width: "100%",
  },
  difficultyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    gap: 12,
  },
  difficultyButton: {
    flex: 1,
    height: 70, // Increased from 60 to 70 for better touch targets
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  difficultyButtonGradient: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    padding: 8, // Added padding for better touch area
  },
  difficultyButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  flipButtonContainer: {
    width: "100%",
    height: 70, // Increased from 60 to 70 for better touch targets
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  flipButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
  },
  flipButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 18,
  },
  nextButton: {
    width: "100%",
    height: 70, // Increased from 60 to 70 for better touch targets
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  nextButtonGradient: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
  },
  nextButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 18,
  },
});