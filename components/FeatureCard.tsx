import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import * as Icons from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

interface FeatureCardProps {
  title: string;
  description: string;
  icon: keyof typeof Icons;
  color?: string;
  gradientColors?: string[];
  onPress: () => void;
  style?: any;
  new?: boolean;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  color = colors.primary,
  gradientColors,
  onPress,
  style,
  new: isNew,
}) => {
  // Dynamically get the icon component
  const IconComponent = Icons[icon] || Icons.Sparkles;

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
        style,
      ]}
      onPress={onPress}
    >
      <LinearGradient
        colors={gradientColors || [color, `${color}80`]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.iconContainer}
      >
        <IconComponent size={theme.sizes.icon.sm} color="#fff" />
      </LinearGradient>
      <View style={styles.content}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
          {isNew && <View style={styles.newBadge}><Text style={styles.newText}>Novo</Text></View>}
        </View>
        <Text style={styles.description} numberOfLines={2}>
          {description}
        </Text>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  iconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 15,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 2,
  },
  description: {
    fontSize: 12,
    color: colors.textLight,
    lineHeight: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  newBadge: {
    backgroundColor: colors.accent1,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
  },
  newText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});