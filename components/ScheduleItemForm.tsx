import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  Pressable,
  ScrollView,
  Switch,
  Alert,
  Platform
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from './Button';
import { GlassCard } from './GlassCard';
import {
  X, Calendar, Clock, BookOpen, Check, BookMarked, Pencil,
  GraduationCap, Brain, Calculator, FileText, Lightbulb,
  Microscope, Atom, Globe, Palette, Music, Code, Dumbbell
} from 'lucide-react-native';
import { useStudyStore } from '@/store/studyStore';
import { ScheduleItem, Subject, Schedule } from '@/types';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ScheduleItemFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (item: Omit<ScheduleItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialItem?: ScheduleItem;
  schedule: Schedule;
}

export const ScheduleItemForm: React.FC<ScheduleItemFormProps> = ({
  visible,
  onClose,
  onSave,
  initialItem,
  schedule
}) => {
  const { subjects } = useStudyStore();
  const [subjectId, setSubjectId] = useState<string | undefined>(undefined);
  const [subjectTitle, setSubjectTitle] = useState('');
  const [dayOfWeek, setDayOfWeek] = useState<number | undefined>(undefined);
  const [dayOfMonth, setDayOfMonth] = useState<number | undefined>(undefined);
  const [specificDate, setSpecificDate] = useState<string | undefined>(undefined);
  const [startTime, setStartTime] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date(new Date().setHours(new Date().getHours() + 1)));
  const [createEvent, setCreateEvent] = useState(true);
  const [createTodo, setCreateTodo] = useState(true);
  const [color, setColor] = useState(colors.primary);
  const [icon, setIcon] = useState<string | undefined>('book-open');

  // Date/time picker states
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Day of week options
  const daysOfWeek = [
    { value: 0, label: 'Domingo' },
    { value: 1, label: 'Segunda-feira' },
    { value: 2, label: 'Terça-feira' },
    { value: 3, label: 'Quarta-feira' },
    { value: 4, label: 'Quinta-feira' },
    { value: 5, label: 'Sexta-feira' },
    { value: 6, label: 'Sábado' }
  ];

  // Days of month options (1-31)
  const daysOfMonth = Array.from({ length: 31 }, (_, i) => ({ value: i + 1, label: `Dia ${i + 1}` }));

  useEffect(() => {
    if (visible) {
      try {
        if (initialItem) {
          // Initialize form with initial values
          setSubjectId(initialItem.subjectId);
          setSubjectTitle(initialItem.subjectTitle);
          setDayOfWeek(initialItem.dayOfWeek);
          setDayOfMonth(initialItem.dayOfMonth);
          setSpecificDate(initialItem.specificDate);

          // Validar e processar a hora de início
          if (initialItem.startTime) {
            try {
              const parsedStartTime = parseISO(initialItem.startTime);
              // Verificar se a data é válida
              if (!isNaN(parsedStartTime.getTime())) {
                setStartTime(parsedStartTime);
              } else {
                console.warn('Data de início inválida:', initialItem.startTime);
                setStartTime(new Date());
              }
            } catch (error) {
              console.error('Erro ao processar hora de início:', error);
              setStartTime(new Date());
            }
          } else {
            setStartTime(new Date());
          }

          // Validar e processar a hora de término
          if (initialItem.endTime) {
            try {
              const parsedEndTime = parseISO(initialItem.endTime);
              // Verificar se a data é válida
              if (!isNaN(parsedEndTime.getTime())) {
                setEndTime(parsedEndTime);
              } else {
                console.warn('Data de término inválida:', initialItem.endTime);
                const defaultEndTime = new Date();
                defaultEndTime.setHours(defaultEndTime.getHours() + 1);
                setEndTime(defaultEndTime);
              }
            } catch (error) {
              console.error('Erro ao processar hora de término:', error);
              const defaultEndTime = new Date();
              defaultEndTime.setHours(defaultEndTime.getHours() + 1);
              setEndTime(defaultEndTime);
            }
          } else {
            const defaultEndTime = new Date();
            defaultEndTime.setHours(defaultEndTime.getHours() + 1);
            setEndTime(defaultEndTime);
          }

          setCreateEvent(initialItem.createEvent);
          setCreateTodo(initialItem.createTodo);
          setColor(initialItem.color || colors.primary);
          setIcon(initialItem.icon || 'book-open');
        } else {
          // Default values for new item
          resetForm();
        }
      } catch (error) {
        console.error('Erro ao inicializar formulário:', error);
        // Em caso de erro, resetar para valores padrão
        resetForm();
      }
    }
  }, [visible, initialItem]);

  const resetForm = () => {
    setSubjectId(undefined);
    setSubjectTitle('');

    // Set default day based on schedule type
    if (schedule.type === 'weekly') {
      setDayOfWeek(new Date().getDay());
      setDayOfMonth(undefined);
      setSpecificDate(undefined);
    } else if (schedule.type === 'monthly') {
      setDayOfWeek(undefined);
      setDayOfMonth(new Date().getDate());
      setSpecificDate(undefined);
    } else if (schedule.type === 'custom') {
      setDayOfWeek(undefined);
      setDayOfMonth(undefined);
      setSpecificDate(new Date().toISOString());
    } else {
      setDayOfWeek(undefined);
      setDayOfMonth(undefined);
      setSpecificDate(undefined);
    }

    // Set default times (current hour to next hour)
    const now = new Date();
    now.setMinutes(0, 0, 0); // Reset minutes and seconds
    setStartTime(now);

    const oneHourLater = new Date(now);
    oneHourLater.setHours(now.getHours() + 1);
    setEndTime(oneHourLater);

    setCreateEvent(true);
    setCreateTodo(true);
    setColor(colors.primary);
    setIcon('book-open');
  };

  const handleSave = () => {
    // Validate form
    if (!subjectTitle.trim()) {
      Alert.alert('Erro', 'Selecione uma matéria para o cronograma.');
      return;
    }

    // Validate day selection based on schedule type
    if (schedule.type === 'weekly' && dayOfWeek === undefined) {
      Alert.alert('Erro', 'Selecione um dia da semana.');
      return;
    }

    if (schedule.type === 'monthly' && dayOfMonth === undefined) {
      Alert.alert('Erro', 'Selecione um dia do mês.');
      return;
    }

    if (schedule.type === 'custom' && !specificDate) {
      Alert.alert('Erro', 'Selecione uma data específica.');
      return;
    }

    // Create schedule item object
    const item: Omit<ScheduleItem, 'id' | 'createdAt' | 'updatedAt' | 'order'> = {
      scheduleId: schedule.id,
      subjectId,
      subjectTitle,
      dayOfWeek,
      dayOfMonth,
      specificDate,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      createEvent,
      createTodo,
      color,
      icon
    };

    onSave(item);
    onClose();
  };

  const handleSubjectSelect = (subject: Subject) => {
    setSubjectId(subject.id);
    setSubjectTitle(subject.title);
    setColor(subject.color);
  };

  const handleDayOfWeekSelect = (day: number) => {
    setDayOfWeek(day);
    setDayOfMonth(undefined);
    setSpecificDate(undefined);
  };

  const handleDayOfMonthSelect = (day: number) => {
    setDayOfMonth(day);
    setDayOfWeek(undefined);
    setSpecificDate(undefined);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setSpecificDate(selectedDate.toISOString());
      setDayOfWeek(undefined);
      setDayOfMonth(undefined);
    }
  };

  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
    setShowStartTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      setStartTime(selectedTime);

      // If end time is before start time, adjust it
      if (selectedTime >= endTime) {
        const newEndTime = new Date(selectedTime);
        newEndTime.setHours(selectedTime.getHours() + 1);
        setEndTime(newEndTime);
      }
    }
  };

  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
    setShowEndTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      // Ensure end time is after start time
      if (selectedTime > startTime) {
        setEndTime(selectedTime);
      } else {
        Alert.alert('Erro', 'O horário de término deve ser após o horário de início.');
      }
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {initialItem ? 'Editar Item do Cronograma' : 'Novo Item do Cronograma'}
            </Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Subject Selection */}
            <Text style={styles.sectionTitle}>Matéria</Text>
            <View style={styles.subjectsContainer}>
              {subjects.map((subject) => (
                <Pressable
                  key={subject.id}
                  style={[
                    styles.subjectItem,
                    { borderColor: subject.color },
                    subjectId === subject.id && { backgroundColor: `${subject.color}20` }
                  ]}
                  onPress={() => handleSubjectSelect(subject)}
                >
                  <Text style={styles.subjectTitle}>{subject.title}</Text>
                  {subjectId === subject.id && (
                    <Check size={16} color={subject.color} />
                  )}
                </Pressable>
              ))}
            </View>

            {/* Day Selection based on schedule type */}
            <Text style={styles.sectionTitle}>Dia</Text>
            {schedule.type === 'weekly' && (
              <View style={styles.daysContainer}>
                {daysOfWeek.map((day) => (
                  <Pressable
                    key={day.value}
                    style={[
                      styles.dayItem,
                      dayOfWeek === day.value && styles.selectedDay
                    ]}
                    onPress={() => handleDayOfWeekSelect(day.value)}
                  >
                    <Text style={[
                      styles.dayText,
                      dayOfWeek === day.value && styles.selectedDayText
                    ]}>
                      {day.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            )}

            {schedule.type === 'monthly' && (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.daysScrollView}
              >
                <View style={styles.daysContainer}>
                  {daysOfMonth.map((day) => (
                    <Pressable
                      key={day.value}
                      style={[
                        styles.dayOfMonthItem,
                        dayOfMonth === day.value && styles.selectedDay
                      ]}
                      onPress={() => handleDayOfMonthSelect(day.value)}
                    >
                      <Text style={[
                        styles.dayText,
                        dayOfMonth === day.value && styles.selectedDayText
                      ]}>
                        {day.value}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </ScrollView>
            )}

            {schedule.type === 'custom' && (
              <View style={styles.datePickerContainer}>
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Calendar size={20} color={colors.primary} />
                  <Text style={styles.datePickerText}>
                    {specificDate
                      ? (() => {
                          try {
                            const date = parseISO(specificDate);
                            if (isNaN(date.getTime())) {
                              return 'Selecionar data específica';
                            }
                            return format(date, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
                          } catch (error) {
                            console.error('Erro ao formatar data específica:', error);
                            return 'Selecionar data específica';
                          }
                        })()
                      : 'Selecionar data específica'}
                  </Text>
                </Pressable>

                {showDatePicker && (
                  <DateTimePicker
                    value={(() => {
                      try {
                        if (specificDate) {
                          const date = parseISO(specificDate);
                          if (!isNaN(date.getTime())) {
                            return date;
                          }
                        }
                        return new Date();
                      } catch (error) {
                        console.error('Erro ao processar data para DateTimePicker:', error);
                        return new Date();
                      }
                    })()}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={handleDateChange}
                  />
                )}
              </View>
            )}

            {/* Time Selection */}
            <Text style={styles.sectionTitle}>Horário</Text>
            <View style={styles.timePickersContainer}>
              <View style={styles.timePicker}>
                <Text style={styles.timePickerLabel}>Início</Text>
                <Pressable
                  style={styles.timePickerButton}
                  onPress={() => setShowStartTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.timePickerText}>
                    {format(startTime, 'HH:mm')}
                  </Text>
                </Pressable>

                {showStartTimePicker && (
                  <DateTimePicker
                    value={startTime}
                    mode="time"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={handleStartTimeChange}
                    minuteInterval={5}
                  />
                )}
              </View>

              <View style={styles.timePicker}>
                <Text style={styles.timePickerLabel}>Término</Text>
                <Pressable
                  style={styles.timePickerButton}
                  onPress={() => setShowEndTimePicker(true)}
                >
                  <Clock size={20} color={colors.primary} />
                  <Text style={styles.timePickerText}>
                    {format(endTime, 'HH:mm')}
                  </Text>
                </Pressable>

                {showEndTimePicker && (
                  <DateTimePicker
                    value={endTime}
                    mode="time"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={handleEndTimeChange}
                    minuteInterval={5}
                  />
                )}
              </View>
            </View>

            {/* Options */}
            <Text style={styles.sectionTitle}>Ícone</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.iconsScrollView}>
              {[
                { name: 'book-open', component: BookOpen },
                { name: 'book-marked', component: BookMarked },
                { name: 'pencil', component: Pencil },
                { name: 'graduation-cap', component: GraduationCap },
                { name: 'brain', component: Brain },
                { name: 'calculator', component: Calculator },
                { name: 'file-text', component: FileText },
                { name: 'lightbulb', component: Lightbulb },
                { name: 'microscope', component: Microscope },
                { name: 'atom', component: Atom },
                { name: 'globe', component: Globe },
                { name: 'palette', component: Palette },
                { name: 'music', component: Music },
                { name: 'code', component: Code },
                { name: 'dumbbell', component: Dumbbell }
              ].map((iconOption) => (
                <Pressable
                  key={iconOption.name}
                  style={[
                    styles.iconItem,
                    icon === iconOption.name && { backgroundColor: `${color}20`, borderColor: color }
                  ]}
                  onPress={() => setIcon(iconOption.name)}
                >
                  <iconOption.component
                    size={24}
                    color={icon === iconOption.name ? color : colors.text}
                  />
                </Pressable>
              ))}
            </ScrollView>

            <Text style={styles.sectionTitle}>Opções</Text>
            <GlassCard style={styles.optionsCard}>
              <View style={styles.optionItem}>
                <Text style={styles.optionText}>Criar evento no calendário</Text>
                <Switch
                  value={createEvent}
                  onValueChange={setCreateEvent}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={createEvent ? colors.primary : colors.white}
                />
              </View>

              <View style={styles.optionItem}>
                <Text style={styles.optionText}>Criar tarefa de revisão</Text>
                <Switch
                  value={createTodo}
                  onValueChange={setCreateTodo}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={createTodo ? colors.primary : colors.white}
                />
              </View>
            </GlassCard>

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="outline"
                size="medium"
                style={styles.cancelButton}
              />
              <Button
                title={initialItem ? 'Salvar Alterações' : 'Adicionar Item'}
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={styles.saveButton}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '90%',
    backgroundColor: colors.background,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 15,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 5,
  },
  modalContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 15,
    marginBottom: 10,
  },
  subjectsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  subjectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    borderWidth: 1,
    borderRadius: 10,
    margin: 5,
    minWidth: '45%',
  },
  subjectTitle: {
    fontSize: 14,
    color: colors.text,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  daysScrollView: {
    marginBottom: 10,
  },
  dayItem: {
    padding: 10,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    margin: 5,
    minWidth: '45%',
    alignItems: 'center',
  },
  dayOfMonthItem: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 20,
    margin: 5,
  },
  selectedDay: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  dayText: {
    fontSize: 14,
    color: colors.text,
  },
  selectedDayText: {
    color: colors.white,
  },
  datePickerContainer: {
    marginBottom: 10,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
  },
  datePickerText: {
    marginLeft: 10,
    fontSize: 14,
    color: colors.text,
  },
  timePickersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  timePicker: {
    width: '48%',
  },
  timePickerLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 5,
  },
  timePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
  },
  timePickerText: {
    marginLeft: 10,
    fontSize: 14,
    color: colors.text,
  },
  optionsCard: {
    padding: 15,
    marginBottom: 20,
  },
  iconsScrollView: {
    marginBottom: 20,
  },
  iconItem: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    marginRight: 10,
    marginBottom: 10,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  optionText: {
    fontSize: 14,
    color: colors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    marginBottom: 30,
  },
  cancelButton: {
    flex: 1,
    marginRight: 10,
  },
  saveButton: {
    flex: 1,
    marginLeft: 10,
  },
});
