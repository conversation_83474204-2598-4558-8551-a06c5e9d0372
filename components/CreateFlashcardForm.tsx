import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, Alert, ActivityIndicator, Pressable } from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from './Button';
import { GlassCard } from './GlassCard';
import { Save, X, BookOpen, Plus, Trash2 } from 'lucide-react-native';
import { supabase } from '@/lib/supabase';
import { useUserStore } from '@/store/userStore';
import { LinearGradient } from 'expo-linear-gradient';

interface CreateFlashcardFormProps {
  groupId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

interface FlashcardItem {
  front: string;
  back: string;
}

export const CreateFlashcardForm: React.FC<CreateFlashcardFormProps> = ({
  groupId,
  onCancel,
  onSuccess,
}) => {
  const { supabaseUser } = useUserStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [flashcards, setFlashcards] = useState<FlashcardItem[]>([
    { front: '', back: '' },
  ]);
  const [saving, setSaving] = useState(false);

  const handleAddFlashcard = () => {
    setFlashcards([...flashcards, { front: '', back: '' }]);
  };

  const handleRemoveFlashcard = (index: number) => {
    if (flashcards.length <= 1) {
      Alert.alert('Erro', 'Você precisa ter pelo menos um flashcard.');
      return;
    }
    
    const newFlashcards = [...flashcards];
    newFlashcards.splice(index, 1);
    setFlashcards(newFlashcards);
  };

  const handleFlashcardChange = (index: number, field: 'front' | 'back', value: string) => {
    const newFlashcards = [...flashcards];
    newFlashcards[index][field] = value;
    setFlashcards(newFlashcards);
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'Por favor, insira um título para o conjunto de flashcards.');
      return;
    }

    // Verificar se todos os flashcards têm frente e verso
    const emptyFlashcards = flashcards.filter(card => !card.front.trim() || !card.back.trim());
    if (emptyFlashcards.length > 0) {
      Alert.alert('Erro', 'Todos os flashcards devem ter frente e verso preenchidos.');
      return;
    }

    try {
      setSaving(true);

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) {
        Alert.alert('Erro', 'Você precisa estar logado para adicionar materiais.');
        return;
      }

      // Prepare material data
      const materialData = {
        group_id: groupId,
        user_id: authUser.id,
        title: title.trim(),
        description: description.trim() || 'Conjunto de flashcards compartilhado no grupo de estudos',
        type: 'flashcard',
        content: { cards: flashcards },
      };

      // Save to Supabase
      const { data, error } = await supabase
        .from('study_group_materials')
        .insert([materialData])
        .select()
        .single();

      if (error) {
        console.error('Erro ao salvar flashcards:', error);
        Alert.alert('Erro', 'Não foi possível salvar os flashcards. Por favor, tente novamente.');
        return;
      }

      Alert.alert('Sucesso', 'Flashcards adicionados com sucesso!');
      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar flashcards:', error);
      Alert.alert('Erro', 'Ocorreu um erro ao salvar os flashcards. Por favor, tente novamente.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <GlassCard style={styles.container}>
      <View style={styles.header}>
        <LinearGradient
          colors={colors.primaryGradient}
          style={styles.iconContainer}
        >
          <BookOpen size={24} color={colors.white} />
        </LinearGradient>
        <Text style={styles.headerTitle}>Novos Flashcards</Text>
      </View>

      <ScrollView
        style={styles.formContainer}
        contentContainerStyle={styles.formContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Título do Conjunto</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Digite o título do conjunto de flashcards"
            placeholderTextColor={colors.textLight}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Descrição (opcional)</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Digite uma descrição para o conjunto de flashcards"
            placeholderTextColor={colors.textLight}
            multiline
            textAlignVertical="top"
            numberOfLines={3}
          />
        </View>

        <Text style={[styles.label, { marginTop: 16 }]}>Flashcards</Text>
        
        {flashcards.map((card, index) => (
          <View key={index} style={styles.flashcardContainer}>
            <View style={styles.flashcardHeader}>
              <Text style={styles.flashcardTitle}>Flashcard {index + 1}</Text>
              <Pressable
                style={styles.removeButton}
                onPress={() => handleRemoveFlashcard(index)}
              >
                <Trash2 size={20} color={colors.error} />
              </Pressable>
            </View>
            
            <View style={styles.inputContainer}>
              <Text style={styles.sublabel}>Frente</Text>
              <TextInput
                style={styles.input}
                value={card.front}
                onChangeText={(value) => handleFlashcardChange(index, 'front', value)}
                placeholder="Digite a pergunta ou termo"
                placeholderTextColor={colors.textLight}
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Text style={styles.sublabel}>Verso</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={card.back}
                onChangeText={(value) => handleFlashcardChange(index, 'back', value)}
                placeholder="Digite a resposta ou definição"
                placeholderTextColor={colors.textLight}
                multiline
                textAlignVertical="top"
                numberOfLines={3}
              />
            </View>
          </View>
        ))}
        
        <Button
          title="Adicionar Flashcard"
          onPress={handleAddFlashcard}
          variant="secondary"
          size="medium"
          icon={Plus}
          style={styles.addButton}
        />
      </ScrollView>

      <View style={styles.buttonsContainer}>
        <Button
          title="Cancelar"
          onPress={onCancel}
          variant="secondary"
          size="medium"
          icon={X}
          style={styles.button}
        />
        <Button
          title="Salvar"
          onPress={handleSave}
          variant="primary"
          size="medium"
          icon={Save}
          style={styles.button}
          loading={saving}
        />
      </View>
    </GlassCard>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  formContainer: {
    flex: 1,
  },
  formContent: {
    paddingBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  sublabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.cardLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 80,
  },
  flashcardContainer: {
    backgroundColor: colors.cardLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  flashcardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  flashcardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  removeButton: {
    padding: 4,
  },
  addButton: {
    marginTop: 8,
    marginBottom: 24,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  button: {
    marginLeft: 12,
  },
});
