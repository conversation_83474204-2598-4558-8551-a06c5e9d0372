import React, { useEffect, useRef, memo } from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import { colors } from '@/constants/colors';
import { GlassCard } from './GlassCard';

export const TypingIndicator = memo(() => {
  // Animated values for each dot
  const dot1Opacity = useRef(new Animated.Value(0.3)).current;
  const dot2Opacity = useRef(new Animated.Value(0.3)).current;
  const dot3Opacity = useRef(new Animated.Value(0.3)).current;

  // Animation sequence - otimizada para ser mais rápida
  const animateDots = () => {
    // Animação paralela mais eficiente
    Animated.loop(
      Animated.stagger(150, [
        Animated.sequence([
          Animated.timing(dot1Opacity, {
            toValue: 1,
            duration: 250, // Duração reduzida
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(dot1Opacity, {
            toValue: 0.3,
            duration: 250, // Duração reduzida
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(dot2Opacity, {
            toValue: 1,
            duration: 250, // Duração reduzida
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(dot2Opacity, {
            toValue: 0.3,
            duration: 250, // Duração reduzida
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(dot3Opacity, {
            toValue: 1,
            duration: 250, // Duração reduzida
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(dot3Opacity, {
            toValue: 0.3,
            duration: 250, // Duração reduzida
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]),
      ])
    ).start();
  };

  // Start animation on mount
  useEffect(() => {
    animateDots();

    // Clean up animations on unmount
    return () => {
      dot1Opacity.stopAnimation();
      dot2Opacity.stopAnimation();
      dot3Opacity.stopAnimation();
    };
  }, []);

  return (
    <View style={styles.container}>
      <GlassCard style={styles.typingContainer}>
        <Text style={styles.typingText}>Lia está digitando</Text>
        <View style={styles.dotsContainer}>
          <Animated.View style={[styles.dot, { opacity: dot1Opacity }]} />
          <Animated.View style={[styles.dot, { opacity: dot2Opacity }]} />
          <Animated.View style={[styles.dot, { opacity: dot3Opacity }]} />
        </View>
      </GlassCard>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    height: 50, // Altura fixa para evitar saltos na rolagem
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxWidth: '80%',
    alignSelf: 'flex-start',
    height: 42, // Altura fixa para evitar saltos na rolagem
  },
  typingText: {
    fontSize: 14,
    color: colors.text,
    marginRight: 8,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginHorizontal: 2,
  },
});
