import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TextInput, Pressable, Alert } from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from './Button';
import { X, Check } from 'lucide-react-native';

interface CreateSeparatorModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateSeparator: (separator: { title: string; description: string; color: string }) => void;
}

const colorOptions = [
  '#3399FF', // Azul (cor padrão do app)
  '#FF6B6B', // Vermelho
  '#4CAF50', // Verde
  '#FFA726', // Laranja
  '#9C27B0', // Roxo
  '#00BCD4', // Ciano
  '#607D8B', // Azul acinzentado
  '#795548', // Marrom
];

export const CreateSeparatorModal: React.FC<CreateSeparatorModalProps> = ({
  visible,
  onClose,
  onCreateSeparator,
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedColor, setSelectedColor] = useState(colorOptions[0]);

  const handleCreateSeparator = () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'Por favor, insira um título para o separador.');
      return;
    }

    onCreateSeparator({
      title: title.trim(),
      description: description.trim(),
      color: selectedColor,
    });

    // Limpar os campos
    setTitle('');
    setDescription('');
    setSelectedColor(colorOptions[0]);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Novo Separador</Text>
            <Pressable onPress={onClose} hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <Text style={styles.inputLabel}>Título</Text>
          <TextInput
            style={styles.input}
            placeholder="Ex: Faculdade, Concurso, Idiomas..."
            placeholderTextColor={colors.textLight}
            value={title}
            onChangeText={setTitle}
            autoFocus
          />

          <Text style={styles.inputLabel}>Descrição (opcional)</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Descrição do separador..."
            placeholderTextColor={colors.textLight}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={3}
          />

          <Text style={styles.inputLabel}>Cor</Text>
          <View style={styles.colorOptions}>
            {colorOptions.map((color) => (
              <Pressable
                key={color}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  selectedColor === color && styles.selectedColorOption,
                ]}
                onPress={() => setSelectedColor(color)}
              >
                {selectedColor === color && (
                  <Check size={16} color="#fff" />
                )}
              </Pressable>
            ))}
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title="Cancelar"
              onPress={onClose}
              variant="outline"
              size="medium"
            />
            <Button
              title="Criar Separador"
              onPress={handleCreateSeparator}
              variant="primary"
              size="medium"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 24,
    gap: 12,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
});
