import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  Pressable,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { colors } from "@/constants/colors";
import { Button } from "./Button";
import { Subject, Separator } from "@/types";
import * as Icons from "lucide-react-native";
import { X } from "lucide-react-native";

interface EditSubjectModalProps {
  visible: boolean;
  onClose: () => void;
  onUpdateSubject: (id: string, updates: Partial<Subject>) => void;
  subject: Subject;
  separators?: Separator[];
}

const SUBJECT_COLORS = [
  "#3399FF", // Primary blue
  "#F97316", // Orange
  "#10B981", // Green
  "#8B5CF6", // Purple
  "#EC4899", // Pink
  "#EF4444", // Red
  "#F59E0B", // Amber
  "#6366F1", // Indigo
];

const SUBJECT_ICONS = [
  "Book",
  "BookOpen",
  "Calculator",
  "FileText",
  "Globe",
  "Microscope",
  "Atom",
  "Dna",
  "Beaker",
  "PenTool",
  "Palette",
  "Music",
  "Languages",
  "History",
  "Landmark",
  "Binary",
  "Code",
  "Database",
];

export const EditSubjectModal: React.FC<EditSubjectModalProps> = ({
  visible,
  onClose,
  onUpdateSubject,
  subject,
  separators = [],
}) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedColor, setSelectedColor] = useState("");
  const [selectedIcon, setSelectedIcon] = useState("");
  const [selectedSeparator, setSelectedSeparator] = useState<string | null>(null);

  useEffect(() => {
    if (visible && subject) {
      setTitle(subject.title);
      setDescription(subject.description || "");
      setSelectedColor(subject.color);
      setSelectedIcon(subject.icon);
      setSelectedSeparator(subject.separator_id || null);
    }
  }, [visible, subject]);

  const handleUpdateSubject = () => {
    if (title.trim() === "") {
      return;
    }

    const updates: Partial<Subject> = {
      title: title.trim(),
      description: description.trim() || `Matéria de ${title.trim()}`,
      icon: selectedIcon,
      color: selectedColor,
      separator_id: selectedSeparator,
    };

    onUpdateSubject(subject.id, updates);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Editar Matéria</Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.modalContent}>
            <Text style={styles.inputLabel}>Nome da Matéria</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Ex: Matemática, Física, História..."
              placeholderTextColor={colors.textLight}
              maxLength={30}
            />

            <Text style={styles.inputLabel}>Descrição (opcional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Descreva brevemente o conteúdo desta matéria..."
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={3}
              maxLength={100}
            />

            <Text style={styles.inputLabel}>Cor</Text>
            <View style={styles.colorGrid}>
              {SUBJECT_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    selectedColor === color && styles.selectedColorOption,
                  ]}
                  onPress={() => setSelectedColor(color)}
                >
                  {selectedColor === color && (
                    <View style={styles.selectedColorIndicator} />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.inputLabel}>Ícone</Text>
            <View style={styles.iconGrid}>
              {SUBJECT_ICONS.map((iconName) => {
                const IconComponent = Icons[iconName as keyof typeof Icons];
                return (
                  <TouchableOpacity
                    key={iconName}
                    style={[
                      styles.iconOption,
                      selectedIcon === iconName && {
                        backgroundColor: `${selectedColor}20`,
                        borderColor: selectedColor,
                      },
                    ]}
                    onPress={() => setSelectedIcon(iconName)}
                  >
                    <IconComponent
                      size={24}
                      color={selectedIcon === iconName ? selectedColor : colors.textLight}
                    />
                  </TouchableOpacity>
                );
              })}
            </View>

            {separators.length > 0 && (
              <>
                <Text style={styles.inputLabel}>Separador (opcional)</Text>
                <View style={styles.separatorContainer}>
                  <TouchableOpacity
                    style={[
                      styles.separatorOption,
                      selectedSeparator === null && styles.selectedSeparatorOption,
                    ]}
                    onPress={() => setSelectedSeparator(null)}
                  >
                    <Text style={styles.separatorText}>Nenhum</Text>
                  </TouchableOpacity>
                  {separators.map((separator) => (
                    <TouchableOpacity
                      key={separator.id}
                      style={[
                        styles.separatorOption,
                        { borderLeftColor: separator.color },
                        selectedSeparator === separator.id && styles.selectedSeparatorOption,
                      ]}
                      onPress={() => setSelectedSeparator(separator.id)}
                    >
                      <Text style={styles.separatorText}>{separator.title}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            )}

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="outline"
                size="medium"
                style={styles.cancelButton}
              />
              <Button
                title="Salvar Alterações"
                onPress={handleUpdateSubject}
                variant="primary"
                size="medium"
                disabled={!title.trim()}
                style={styles.createButton}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  modalContainer: {
    width: "100%",
    maxHeight: "90%",
    backgroundColor: colors.card,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  colorGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  selectedColorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  iconGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  iconOption: {
    width: 48,
    height: 48,
    borderRadius: 12,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.backgroundLight,
    borderWidth: 1,
    borderColor: colors.border,
  },
  separatorContainer: {
    marginBottom: 16,
  },
  separatorOption: {
    backgroundColor: colors.backgroundLight,
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.border,
  },
  selectedSeparatorOption: {
    backgroundColor: `${colors.primary}15`,
    borderLeftColor: colors.primary,
  },
  separatorText: {
    fontSize: 16,
    color: colors.text,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
});
