import React, { useRef } from 'react';
import { View, Text, StyleSheet, Pressable, Dimensions, ScrollView, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { theme } from '@/constants/theme';
import {
  MessageCircle,
  BookOpen,
  FileQuestion,
  Calendar,
  BarChart,
  FileText,
  Brain,
  Zap,
  Users,
  Clock,
  Trophy,
  Target,
  Sparkles,
  Star
} from 'lucide-react-native';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.55; // Mais compacto
const CARD_MARGIN = 10; // Margem reduzida

interface QuickActionProps {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  gradientColors: string[];
  onPress: () => void;
  badge?: string;
}

const QuickActionCard: React.FC<QuickActionProps> = ({
  title,
  subtitle,
  icon,
  gradientColors,
  onPress,
  badge
}) => {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.actionCard,
        { width: CARD_WIDTH },
        pressed && { transform: [{ scale: 0.98 }], opacity: 0.9 }
      ]}
      onPress={onPress}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.actionCardGradient}
      >
        <View style={styles.actionHeader}>
          <View style={styles.actionIconContainer}>
            {React.cloneElement(icon as React.ReactElement, {
              color: "#fff",
              size: theme.sizes.icon.md // Mudado de lg para md (24px)
            })}
          </View>
          {badge && (
            <View style={styles.badgeContainer}>
              <Text style={styles.badgeText}>{badge}</Text>
            </View>
          )}
        </View>

        <View style={styles.actionContent}>
          <Text style={styles.actionTitle}>{title}</Text>
          <Text style={styles.actionSubtitle}>{subtitle}</Text>
        </View>

        <View style={styles.actionFooter}>
          <View style={styles.actionArrow}>
            <View style={styles.arrowIcon} />
          </View>
        </View>
      </LinearGradient>
    </Pressable>
  );
};

export const QuickActionsCarousel: React.FC = () => {
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView>(null);

  const actions = [
    {
      title: "Chat com IA",
      subtitle: "Tire dúvidas instantaneamente com nossa IA especializada",
      icon: <MessageCircle />,
      gradientColors: colors.cardGradient1,
      badge: "Novo",
      onPress: () => router.push('/chat')
    },
    {
      title: "Flashcards",
      subtitle: "Memorize conceitos importantes de forma eficiente",
      icon: <BookOpen />,
      gradientColors: colors.cardGradient2,
      onPress: () => router.push('/flashcards')
    },
    {
      title: "Quiz Rápido",
      subtitle: "Teste seus conhecimentos com questões personalizadas",
      icon: <FileQuestion />,
      gradientColors: colors.cardGradient3,
      badge: "Popular",
      onPress: () => router.push('/quizzes')
    },
    {
      title: "Grupos de Estudo",
      subtitle: "Estude em grupo e compartilhe conhecimento",
      icon: <Users />,
      gradientColors: colors.cardGradient4,
      onPress: () => router.push('/study-groups')
    },
    {
      title: "Cronograma",
      subtitle: "Organize seus estudos com planejamento inteligente",
      icon: <Calendar />,
      gradientColors: colors.cardGradient5,
      onPress: () => router.push('/schedules')
    },
    {
      title: "Estatísticas",
      subtitle: "Acompanhe seu progresso e desempenho",
      icon: <BarChart />,
      gradientColors: colors.cardGradient1,
      onPress: () => router.push('/statistics')
    },
    {
      title: "Anotações",
      subtitle: "Organize suas ideias e conhecimentos",
      icon: <FileText />,
      gradientColors: colors.cardGradient2,
      onPress: () => router.push('/notes')
    },
    {
      title: "Mapas Mentais",
      subtitle: "Visualize conexões entre conceitos",
      icon: <Brain />,
      gradientColors: colors.cardGradient3,
      onPress: () => router.push('/mind-maps')
    }
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Zap size={theme.sizes.icon.sm} color={colors.primary} />
          <Text style={styles.sectionTitle}>Ações Rápidas</Text>
        </View>
        <View style={styles.badge}>
          <Sparkles size={theme.sizes.icon.xs} color={colors.primary} />
          <Text style={styles.badgeHeaderText}>Produtivo</Text>
        </View>
      </View>

      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
        snapToInterval={CARD_WIDTH + CARD_MARGIN}
        decelerationRate="fast"
        pagingEnabled={false}
      >
        {actions.map((action, index) => (
          <View key={index} style={styles.cardContainer}>
            <QuickActionCard
              title={action.title}
              subtitle={action.subtitle}
              icon={action.icon}
              gradientColors={action.gradientColors}
              onPress={action.onPress}
              badge={action.badge}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
    maxHeight: 200, // Limitar altura máxima
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textDark,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 4,
  },
  badgeHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
  },
  scrollContainer: {
    paddingLeft: 16,
    paddingRight: 16,
  },
  cardContainer: {
    marginRight: CARD_MARGIN,
  },
  actionCard: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: "#3399FF",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
  },
  actionCardGradient: {
    padding: 18,
    minHeight: 110,
    maxHeight: 130,
    justifyContent: 'space-between',
  },
  actionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  actionIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 3,
  },
  badgeContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: colors.primary,
  },
  actionContent: {
    flex: 1,
    justifyContent: 'center',
    marginVertical: 16,
  },
  actionTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 3,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  actionFooter: {
    alignItems: 'flex-end',
  },
  actionArrow: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowIcon: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 0,
    borderTopWidth: 4,
    borderBottomWidth: 4,
    borderLeftColor: '#fff',
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
  },
});
