import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { ProgressBar } from "./ProgressBar";
import { Subject } from "@/types";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import * as Icons from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

interface SubjectCardProps {
  subject: Subject;
  onPress: (subject: Subject) => void;
  onLongPress?: (subject: Subject) => void;
}

export const SubjectCard: React.FC<SubjectCardProps> = ({ subject, onPress, onLongPress }) => {
  // Dynamically get the icon component
  const IconComponent = Icons[subject.icon as keyof typeof Icons] || Icons.Book;

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(subject)}
      onLongPress={onLongPress ? () => onLongPress(subject) : undefined}
      delayLongPress={500}
    >
      <LinearGradient
        colors={[subject.color, `${subject.color}80`]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.iconContainer}
      >
        <IconComponent size={theme.sizes.icon.sm} color="#fff" />
      </LinearGradient>
      <View style={styles.contentContainer}>
        <Text style={styles.title}>{subject.title}</Text>
        <View style={styles.progressContainer}>
          <ProgressBar
            progress={subject.progress}
            color={subject.color}
            height={8}
            backgroundColor={`${subject.color}20`}
            style={styles.progressBar}
          />
          <Text style={[styles.progressText, { color: subject.color }]}>
            {subject.progress}%
          </Text>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  iconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 15,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  progressBar: {
    flex: 1,
    marginRight: 12,
  },
  progressText: {
    fontSize: 12,
    fontWeight: "600",
  },
});