import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, Pressable, ScrollView, Alert } from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from './Button';
import { X, Check } from 'lucide-react-native';
import { Subject, Separator } from '@/types';
import { useStudyStore } from '@/store/studyStore';

interface AssignSubjectModalProps {
  visible: boolean;
  onClose: () => void;
  subject: Subject | null;
  separators: Separator[];
}

export const AssignSubjectModal: React.FC<AssignSubjectModalProps> = ({
  visible,
  onClose,
  subject,
  separators,
}) => {
  const { updateSubject } = useStudyStore();
  const [selectedSeparator, setSelectedSeparator] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (subject) {
      setSelectedSeparator(subject.separator_id || null);
    }
  }, [subject]);

  // Resetar o estado quando o modal é fechado
  useEffect(() => {
    if (!visible) {
      setIsSaving(false);
    }
  }, [visible]);

  const handleSave = async () => {
    if (!subject) return;
    if (isSaving) return; // Evitar cliques duplos

    try {
      // Registrar o estado atual e a mudança pretendida
      console.log('Estado atual da matéria:', {
        subject_id: subject.id,
        subject_title: subject.title,
        current_separator_id: subject.separator_id,
        new_separator_id: selectedSeparator,
        moving_from: subject.separator_id ? 'separador' : 'nenhum',
        moving_to: selectedSeparator ? 'separador' : 'nenhum'
      });

      // Desabilitar o botão de salvar durante a operação
      setIsSaving(true);

      // Criar uma cópia do objeto de atualização para evitar problemas de referência
      const updateData = { separator_id: selectedSeparator };
      console.log('Dados de atualização:', updateData);

      // Usar um timeout para evitar que a UI trave durante a operação
      setTimeout(async () => {
        try {
          // Chamar a função de atualização com os dados corretos
          await updateSubject(subject.id, updateData);

          console.log('Matéria movida com sucesso!');
          Alert.alert('Sucesso', 'Matéria movida com sucesso!');

          // Fechar o modal após um pequeno delay para garantir que a UI seja atualizada
          setTimeout(() => {
            onClose();
            setIsSaving(false);
          }, 300);
        } catch (error) {
          console.error('Erro ao mover matéria:', error);
          Alert.alert(
            'Erro',
            'Não foi possível mover a matéria. Por favor, tente novamente mais tarde.'
          );
          setIsSaving(false);
        }
      }, 100);
    } catch (error) {
      console.error('Erro ao preparar atualização:', error);
      Alert.alert(
        'Erro',
        'Não foi possível preparar a atualização. Por favor, tente novamente mais tarde.'
      );
      setIsSaving(false);
    }
  };

  if (!subject) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {
        if (!isSaving) {
          onClose();
        }
      }}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Mover Matéria</Text>
            <Pressable
              onPress={() => {
                if (!isSaving) {
                  onClose();
                }
              }}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              disabled={isSaving}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <Text style={styles.subjectTitle}>{subject.title}</Text>
          <Text style={styles.instructionText}>Selecione um separador para esta matéria:</Text>

          <ScrollView style={styles.separatorsList}>
            <Pressable
              style={[
                styles.separatorOption,
                selectedSeparator === null && styles.selectedSeparatorOption,
              ]}
              onPress={() => setSelectedSeparator(null)}
            >
              <Text style={styles.separatorText}>Nenhum separador</Text>
              {selectedSeparator === null && (
                <Check size={20} color={colors.primary} />
              )}
            </Pressable>

            {separators.map((separator) => (
              <Pressable
                key={separator.id}
                style={[
                  styles.separatorOption,
                  { borderLeftColor: separator.color },
                  selectedSeparator === separator.id && styles.selectedSeparatorOption,
                ]}
                onPress={() => setSelectedSeparator(separator.id)}
              >
                <Text style={styles.separatorText}>{separator.title}</Text>
                {selectedSeparator === separator.id && (
                  <Check size={20} color={colors.primary} />
                )}
              </Pressable>
            ))}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <Button
              title="Cancelar"
              onPress={() => {
                if (!isSaving) {
                  onClose();
                }
              }}
              variant="outline"
              size="medium"
              style={styles.button}
              disabled={isSaving}
            />
            <Button
              title={isSaving ? "Salvando..." : "Salvar"}
              onPress={handleSave}
              variant="primary"
              size="medium"
              style={styles.button}
              disabled={isSaving}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
  },
  subjectTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 16,
  },
  separatorsList: {
    maxHeight: 300,
    marginBottom: 16,
  },
  separatorOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.border,
  },
  selectedSeparatorOption: {
    backgroundColor: `${colors.primary}15`,
    borderLeftColor: colors.primary,
  },
  separatorText: {
    fontSize: 16,
    color: colors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});
