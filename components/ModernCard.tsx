import React from "react";
import { View, Text, StyleSheet, Pressable, Animated, ViewStyle, TextStyle } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";

interface ModernCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  variant?: "default" | "glass" | "gradient" | "elevated" | "outlined" | "hero";
  gradientColors?: string[];
  gradientDirection?: "horizontal" | "vertical" | "diagonal";
  borderRadius?: number;
  disabled?: boolean;
  animatePress?: boolean;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  shadow?: "none" | "xs" | "sm" | "md" | "lg" | "xl" | "xxl";
  testID?: string;
}

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  title,
  subtitle,
  onPress,
  onLongPress,
  style,
  contentStyle,
  titleStyle,
  subtitleStyle,
  variant = "default",
  gradientColors,
  gradientDirection = "diagonal",
  borderRadius = theme.borderRadius.card,
  disabled = false,
  animatePress = true,
  header,
  footer,
  shadow = "md",
  testID,
}) => {
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (animatePress && !disabled && (onPress || onLongPress)) {
      Animated.spring(scaleAnim, {
        toValue: 0.98,
        ...theme.animation.easing.spring,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animatePress && !disabled && (onPress || onLongPress)) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        ...theme.animation.easing.spring,
        useNativeDriver: true,
      }).start();
    }
  };

  const getShadowStyle = () => {
    if (shadow === "none") return {};

    const blueShadow = {
      shadowColor: "#3399FF",
      shadowOpacity: 0.12,
    };

    switch (shadow) {
      case "xs":
        return { ...blueShadow, shadowOffset: { width: 0, height: 2 }, shadowRadius: 4, elevation: 2 };
      case "sm":
        return { ...blueShadow, shadowOffset: { width: 0, height: 4 }, shadowRadius: 6, elevation: 3 };
      case "md":
        return { ...blueShadow, shadowOffset: { width: 0, height: 6 }, shadowRadius: 8, elevation: 4 };
      case "lg":
        return { ...blueShadow, shadowOffset: { width: 0, height: 8 }, shadowRadius: 12, elevation: 6 };
      case "xl":
        return { ...blueShadow, shadowOffset: { width: 0, height: 10 }, shadowRadius: 16, elevation: 8 };
      case "xxl":
        return { ...blueShadow, shadowOffset: { width: 0, height: 12 }, shadowRadius: 20, elevation: 10 };
      default:
        return { ...blueShadow, shadowOffset: { width: 0, height: 6 }, shadowRadius: 8, elevation: 4 };
    }
  };

  const getGradientStart = () => {
    switch (gradientDirection) {
      case "horizontal": return { x: 0, y: 0.5 };
      case "vertical": return { x: 0.5, y: 0 };
      case "diagonal": return { x: 0, y: 0 };
      default: return { x: 0, y: 0 };
    }
  };

  const getGradientEnd = () => {
    switch (gradientDirection) {
      case "horizontal": return { x: 1, y: 0.5 };
      case "vertical": return { x: 0.5, y: 1 };
      case "diagonal": return { x: 1, y: 1 };
      default: return { x: 1, y: 1 };
    }
  };

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius,
      ...getShadowStyle(),
    };

    switch (variant) {
      case "glass":
        return {
          ...baseStyle,
          backgroundColor: colors.glassBlur,
          borderWidth: 1,
          borderColor: "rgba(255, 255, 255, 0.3)",
          backdropFilter: "blur(10px)",
        };
      case "elevated":
        return {
          ...baseStyle,
          backgroundColor: colors.cardElevated,
          ...theme.shadows.lg,
        };
      case "outlined":
        return {
          ...baseStyle,
          backgroundColor: colors.background,
          borderWidth: 1.5,
          borderColor: colors.borderLight,
        };
      case "hero":
        return {
          ...baseStyle,
          overflow: "hidden",
          ...theme.shadows.xl,
        };
      case "gradient":
        return {
          ...baseStyle,
          overflow: "hidden",
        };
      case "default":
      default:
        return {
          ...baseStyle,
          backgroundColor: colors.card,
        };
    }
  };

  const renderContent = () => (
    <View style={[styles.content, contentStyle]}>
      {header && <View style={styles.header}>{header}</View>}
      
      {(title || subtitle) && (
        <View style={styles.titleContainer}>
          {title && (
            <Text style={[styles.title, titleStyle]}>{title}</Text>
          )}
          {subtitle && (
            <Text style={[styles.subtitle, subtitleStyle]}>{subtitle}</Text>
          )}
        </View>
      )}
      
      {children}
      
      {footer && <View style={styles.footer}>{footer}</View>}
    </View>
  );

  const cardStyle = [styles.container, getCardStyle(), style];

  if (variant === "gradient" || variant === "hero") {
    const gradientColorValues = gradientColors || colors.primaryGradient;

    return (
      <Animated.View
        style={[
          { transform: [{ scale: scaleAnim }] },
          disabled && styles.disabled,
        ]}
        testID={testID}
      >
        <Pressable
          style={cardStyle}
          onPress={onPress}
          onLongPress={onLongPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || (!onPress && !onLongPress)}
        >
          <LinearGradient
            colors={gradientColorValues}
            start={getGradientStart()}
            end={getGradientEnd()}
            style={styles.gradient}
          >
            {renderContent()}
          </LinearGradient>
        </Pressable>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      style={[
        { transform: [{ scale: scaleAnim }] },
        disabled && styles.disabled,
      ]}
      testID={testID}
    >
      <Pressable
        style={cardStyle}
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || (!onPress && !onLongPress)}
      >
        {renderContent()}
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: "hidden",
    marginBottom: theme.spacing.md,
  },
  gradient: {
    width: "100%",
    height: "100%",
  },
  header: {
    width: "100%",
    marginBottom: theme.spacing.sm,
  },
  titleContainer: {
    marginBottom: theme.spacing.sm,
  },
  title: {
    ...theme.typography.heading3,
    marginBottom: theme.spacing.xxs,
  },
  subtitle: {
    ...theme.typography.subtitle1,
    color: colors.textMedium,
  },
  content: {
    padding: theme.spacing.cardPadding,
  },
  footer: {
    width: "100%",
    marginTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.dividerLight,
    paddingTop: theme.spacing.sm,
  },
  disabled: {
    opacity: 0.6,
  },
});

export default ModernCard;
