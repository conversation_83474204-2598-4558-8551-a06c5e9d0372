import React, { useState } from "react";
import { View, Text, StyleSheet, TextInput, Pressable, ScrollView } from "react-native";
import { colors } from "@/constants/colors";
import { Plus, Trash2 } from "lucide-react-native";
import { Button } from "./Button";

interface TableEditorProps {
  initialData?: string[][];
  onSave: (data: string[][]) => void;
  onCancel: () => void;
}

export const TableEditor: React.FC<TableEditorProps> = ({
  initialData = [["", ""], ["", ""]],
  onSave,
  onCancel,
}) => {
  const [tableData, setTableData] = useState<string[][]>(initialData);

  const updateCell = (rowIndex: number, colIndex: number, value: string) => {
    const newData = [...tableData];
    newData[rowIndex][colIndex] = value;
    setTableData(newData);
  };

  const addRow = () => {
    const newRow = Array(tableData[0].length).fill("");
    setTableData([...tableData, newRow]);
  };

  const addColumn = () => {
    const newData = tableData.map(row => [...row, ""]);
    setTableData(newData);
  };

  const removeRow = (rowIndex: number) => {
    if (tableData.length <= 1) return;
    const newData = tableData.filter((_, index) => index !== rowIndex);
    setTableData(newData);
  };

  const removeColumn = (colIndex: number) => {
    if (tableData[0].length <= 1) return;
    const newData = tableData.map(row => row.filter((_, index) => index !== colIndex));
    setTableData(newData);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Editor de Tabela</Text>
      
      <ScrollView horizontal style={styles.tableContainer}>
        <View>
          <View style={styles.tableHeader}>
            {tableData[0].map((_, colIndex) => (
              <View key={`header-${colIndex}`} style={styles.headerCell}>
                <Pressable
                  style={styles.removeButton}
                  onPress={() => removeColumn(colIndex)}
                >
                  <Trash2 size={16} color={colors.error} />
                </Pressable>
              </View>
            ))}
            <Pressable style={styles.addButton} onPress={addColumn}>
              <Plus size={20} color={colors.primary} />
            </Pressable>
          </View>
          
          <ScrollView style={styles.tableBody}>
            {tableData.map((row, rowIndex) => (
              <View key={`row-${rowIndex}`} style={styles.tableRow}>
                {row.map((cell, colIndex) => (
                  <View key={`cell-${rowIndex}-${colIndex}`} style={styles.tableCell}>
                    <TextInput
                      style={styles.cellInput}
                      value={cell}
                      onChangeText={(text) => updateCell(rowIndex, colIndex, text)}
                      placeholder="Célula"
                    />
                  </View>
                ))}
                <Pressable
                  style={styles.removeRowButton}
                  onPress={() => removeRow(rowIndex)}
                >
                  <Trash2 size={16} color={colors.error} />
                </Pressable>
              </View>
            ))}
            <Pressable style={styles.addRowButton} onPress={addRow}>
              <Plus size={20} color={colors.primary} />
              <Text style={styles.addRowText}>Adicionar linha</Text>
            </Pressable>
          </ScrollView>
        </View>
      </ScrollView>
      
      <View style={styles.buttonContainer}>
        <Button
          title="Cancelar"
          onPress={onCancel}
          variant="outline"
          size="medium"
          style={styles.button}
        />
        <Button
          title="Salvar"
          onPress={() => onSave(tableData)}
          variant="primary"
          size="medium"
          style={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    maxHeight: 500,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
  },
  tableContainer: {
    maxHeight: 400,
  },
  tableHeader: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerCell: {
    width: 120,
    padding: 8,
    alignItems: "center",
  },
  tableBody: {
    maxHeight: 350,
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tableCell: {
    width: 120,
    padding: 8,
  },
  cellInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  addButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  removeButton: {
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  removeRowButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  addRowButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
  },
  addRowText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 16,
  },
  button: {
    marginLeft: 8,
  },
});
