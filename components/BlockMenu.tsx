import React from "react";
import { View, Text, StyleSheet, Pressable, ScrollView } from "react-native";
import { colors } from "@/constants/colors";
import { 
  Type, 
  Heading, 
  Image, 
  Table, 
  List, 
  Code, 
  Quote, 
  Minus, 
  File, 
  Plus,
  FileText,
  CheckSquare,
  FileSpreadsheet
} from "lucide-react-native";
import { NoteBlockType } from "@/types";

interface BlockMenuProps {
  onSelectBlock: (type: NoteBlockType) => void;
  onClose: () => void;
}

export const BlockMenu: React.FC<BlockMenuProps> = ({ onSelectBlock, onClose }) => {
  const blockTypes = [
    { type: "text" as NoteBlockType, icon: Type, label: "Texto" },
    { type: "heading" as NoteBlockType, icon: Heading, label: "Título" },
    { type: "subheading" as NoteBlockType, icon: Heading, label: "Subtítulo" },
    { type: "image" as NoteBlockType, icon: Image, label: "Imagem" },
    { type: "table" as NoteBlockType, icon: Table, label: "Tabela" },
    { type: "list" as NoteBlockType, icon: List, label: "Lista" },
    { type: "code" as NoteBlockType, icon: Code, label: "Código" },
    { type: "quote" as NoteBlockType, icon: Quote, label: "Citação" },
    { type: "divider" as NoteBlockType, icon: Minus, label: "Divisor" },
    { type: "file" as NoteBlockType, icon: File, label: "Arquivo" },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Adicionar bloco</Text>
        <Pressable onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>×</Text>
        </Pressable>
      </View>
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.blockGrid}>
          {blockTypes.map((block) => (
            <Pressable
              key={block.type}
              style={styles.blockItem}
              onPress={() => {
                onSelectBlock(block.type);
                onClose();
              }}
            >
              <View style={styles.blockIcon}>
                <block.icon size={24} color={colors.primary} />
              </View>
              <Text style={styles.blockLabel}>{block.label}</Text>
            </Pressable>
          ))}
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Modelos</Text>
          <View style={styles.blockGrid}>
            <Pressable
              style={styles.blockItem}
              onPress={() => {
                // Add a checklist template
                onClose();
              }}
            >
              <View style={styles.blockIcon}>
                <CheckSquare size={24} color={colors.primary} />
              </View>
              <Text style={styles.blockLabel}>Checklist</Text>
            </Pressable>
            
            <Pressable
              style={styles.blockItem}
              onPress={() => {
                // Add a note template
                onClose();
              }}
            >
              <View style={styles.blockIcon}>
                <FileText size={24} color={colors.primary} />
              </View>
              <Text style={styles.blockLabel}>Nota</Text>
            </Pressable>
            
            <Pressable
              style={styles.blockItem}
              onPress={() => {
                // Add a spreadsheet template
                onClose();
              }}
            >
              <View style={styles.blockIcon}>
                <FileSpreadsheet size={24} color={colors.primary} />
              </View>
              <Text style={styles.blockLabel}>Planilha</Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    maxHeight: 400,
    width: "100%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
  },
  closeButtonText: {
    fontSize: 20,
    color: colors.text,
    fontWeight: "600",
  },
  scrollView: {
    padding: 16,
  },
  blockGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  blockItem: {
    width: "33.33%",
    padding: 8,
    alignItems: "center",
  },
  blockIcon: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: `${colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  blockLabel: {
    fontSize: 14,
    color: colors.text,
    textAlign: "center",
  },
  section: {
    marginTop: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
});
