import React from 'react';
import { View, Text, StyleSheet, ScrollView, Pressable, Image } from 'react-native';
import { colors } from '@/constants/colors';
import { theme } from '@/constants/theme';
import { PomodoroTimer } from './PomodoroTimer';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassCard } from './GlassCard';
import {
  Clock,
  BookOpen,
  FileQuestion,
  BrainCircuit,
  BarChart,
  Lightbulb,
  FileText,
  Calendar,
  Target,
  Award,
  Brain
} from 'lucide-react-native';

interface ResourceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onPress: () => void;
}

const ResourceCard: React.FC<ResourceCardProps> = ({ title, description, icon, onPress }) => {
  // Determinar a cor de fundo com base no título
  const getBackgroundColor = () => {
    switch (title) {
      case "Flashcards":
        return colors.primary;
      case "Quizzes":
        return colors.secondary;
      case "Mapas Mentais":
        return colors.accent1;
      case "Calendário":
        return colors.primary;
      case "Estatísticas":
        return colors.accent1;
      case "Anotações":
        return colors.accent2;
      default:
        return colors.primary;
    }
  };

  return (
    <Pressable
      style={({ pressed }) => [
        styles.resourceCard,
        pressed && { transform: [{ scale: 0.98 }], opacity: 0.9 }
      ]}
      onPress={onPress}
    >
      <LinearGradient
        colors={[getBackgroundColor(), `${getBackgroundColor()}80`]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.resourceCardInner}
      >
        <View style={styles.resourceIconContainer}>
          {React.cloneElement(icon as React.ReactElement, { color: "#fff", size: theme.sizes.icon.sm })}
        </View>
        <View style={styles.resourceContent}>
          <Text style={styles.resourceTitle}>{title}</Text>
          <Text style={styles.resourceDescription} numberOfLines={2}>
            {description}
          </Text>
        </View>
      </LinearGradient>
    </Pressable>
  );
};

export const MainResources: React.FC = () => {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionTitle}>Recursos de Estudo</Text>
        <Pressable
          style={styles.viewAllButton}
          onPress={() => router.push('/tools')}
        >
          <Text style={styles.viewAllText}>Ver todos</Text>
        </Pressable>
      </View>

      <PomodoroTimer />

      <View style={styles.resourcesGrid}>
        <ResourceCard
          title="Flashcards"
          description="Memorize conceitos importantes"
          icon={<BookOpen size={theme.sizes.icon.md} color={colors.primary} />}
          onPress={() => router.push('/flashcards')}
        />

        <ResourceCard
          title="Quizzes"
          description="Teste seu conhecimento"
          icon={<FileQuestion size={theme.sizes.icon.md} color={colors.accent1} />}
          onPress={() => router.push('/quizzes')}
        />

        <ResourceCard
          title="Mapas Mentais"
          description="Organize suas ideias visualmente"
          icon={<BrainCircuit size={theme.sizes.icon.md} color={colors.accent2} />}
          onPress={() => router.push('/mind-maps')}
        />

        <ResourceCard
          title="Calendário"
          description="Planeje seus estudos"
          icon={<Calendar size={theme.sizes.icon.md} color={colors.primary} />}
          onPress={() => router.push('/calendar')}
        />

        <ResourceCard
          title="Estatísticas"
          description="Acompanhe seu progresso"
          icon={<BarChart size={theme.sizes.icon.md} color={colors.accent1} />}
          onPress={() => router.push('/statistics')}
        />

        <ResourceCard
          title="Anotações"
          description="Organize seu conhecimento"
          icon={<FileText size={theme.sizes.icon.md} color={colors.accent2} />}
          onPress={() => router.push('/notes')}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(51, 153, 255, 0.1)',
    padding: 12,
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
  },
  viewAllButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.primary,
    borderRadius: 20,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
  resourcesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  resourceCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  resourceCardInner: {
    padding: 14, // Reduzido de 16 para 14
    borderRadius: 16,
    height: 110, // Reduzido de 130 para 110
  },
  resourceIconContainer: {
    width: theme.sizes.iconContainer.sm, // Mudado de md para sm (40px)
    height: theme.sizes.iconContainer.sm, // Mudado de md para sm (40px)
    borderRadius: theme.sizes.iconContainer.sm / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10, // Reduzido de 12 para 10
  },
  resourceContent: {
    flex: 1,
  },
  resourceTitle: {
    fontSize: 14, // Reduzido de 16 para 14
    fontWeight: '700',
    color: colors.white,
    marginBottom: 3, // Reduzido de 4 para 3
  },
  resourceDescription: {
    fontSize: 11, // Reduzido de 12 para 11
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 15, // Reduzido de 16 para 15
  },
});
