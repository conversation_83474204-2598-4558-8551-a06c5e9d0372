import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";
import { Activity } from "@/types";
import { colors } from "@/constants/colors";
import { theme } from "@/constants/theme";
import { Book, BookOpen, MessageSquare, BrainCircuit, FileText } from "lucide-react-native";
import { formatDistanceToNow } from "@/utils/dateUtils";
import { LinearGradient } from "expo-linear-gradient";

interface ActivityCardProps {
  activity: Activity;
  onPress: (activity: Activity) => void;
}

export const ActivityCard: React.FC<ActivityCardProps> = ({ activity, onPress }) => {
  const getIconAndColors = () => {
    switch (activity.type) {
      case "flashcards":
        return {
          icon: <BookOpen size={theme.sizes.icon.sm} color="#fff" />,
          colors: colors.primaryGradient
        };
      case "quiz":
        return {
          icon: <FileText size={theme.sizes.icon.sm} color="#fff" />,
          colors: colors.secondaryGradient
        };
      case "notes":
        return {
          icon: <Book size={theme.sizes.icon.sm} color="#fff" />,
          colors: colors.successGradient
        };
      case "chat":
        return {
          icon: <MessageSquare size={theme.sizes.icon.sm} color="#fff" />,
          colors: colors.primaryGradient
        };
      case "mindmap":
        return {
          icon: <BrainCircuit size={theme.sizes.icon.sm} color="#fff" />,
          colors: [colors.accent1, `${colors.accent1}80`]
        };
      default:
        return {
          icon: <Book size={theme.sizes.icon.sm} color="#fff" />,
          colors: colors.primaryGradient
        };
    }
  };

  const getTypeLabel = () => {
    switch (activity.type) {
      case "flashcards":
        return "Flashcards";
      case "quiz":
        return "Quiz";
      case "notes":
        return "Anotações";
      case "chat":
        return "Chat com IA";
      case "mindmap":
        return "Mapa Mental";
      default:
        return activity.type;
    }
  };

  const { icon, colors: gradientColors } = getIconAndColors();

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress(activity)}
    >
      <View style={styles.content}>
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.iconContainer}
        >
          {icon}
        </LinearGradient>
        <View style={styles.textContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {activity.title}
          </Text>
          <View style={styles.detailsContainer}>
            <Text style={styles.subject}>{activity.subject}</Text>
            <Text style={styles.dot}>•</Text>
            <Text style={styles.type}>{getTypeLabel()}</Text>
          </View>
        </View>
      </View>
      <Text style={styles.date}>{formatDistanceToNow(new Date(activity.date))}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    minHeight: 80,
    maxHeight: 120,
  },
  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: theme.sizes.iconContainer.md,
    height: theme.sizes.iconContainer.md,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  detailsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  subject: {
    fontSize: 14,
    color: colors.textLight,
  },
  dot: {
    fontSize: 14,
    color: colors.textLight,
    marginHorizontal: 4,
  },
  type: {
    fontSize: 14,
    color: colors.textLight,
  },
  date: {
    fontSize: 12,
    color: colors.textLight,
  },
});