import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import { colors } from '@/constants/colors';
import { Calendar, Clock, Repeat, Calendar as CalendarIcon } from 'lucide-react-native';
import { SelectModal } from './SelectModal';
import { WeekdaySelector } from './WeekdaySelector';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export type RecurrenceType = 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
export type RecurrenceEndType = 'never' | 'on_date' | 'after_occurrences';

export interface RecurrenceSettings {
  type: RecurrenceType;
  interval?: number;
  weekdays?: number[];
  monthDay?: number;
  endType: RecurrenceEndType;
  endDate?: Date;
  occurrences?: number;
}

interface RecurrenceSelectorProps {
  value: RecurrenceSettings;
  onChange: (settings: RecurrenceSettings) => void;
}

export const RecurrenceSelector: React.FC<RecurrenceSelectorProps> = ({
  value,
  onChange,
}) => {
  const [showTypeModal, setShowTypeModal] = useState(false);
  const [showEndTypeModal, setShowEndTypeModal] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const recurrenceTypes = [
    { value: 'none', label: 'Não repetir' },
    { value: 'daily', label: 'Diariamente' },
    { value: 'weekly', label: 'Semanalmente' },
    { value: 'monthly', label: 'Mensalmente' },
    { value: 'yearly', label: 'Anualmente' },
    { value: 'custom', label: 'Personalizado' },
  ];

  const endTypes = [
    { value: 'never', label: 'Nunca' },
    { value: 'on_date', label: 'Em uma data' },
    { value: 'after_occurrences', label: 'Após um número de ocorrências' },
  ];

  const handleTypeChange = (type: string) => {
    onChange({
      ...value,
      type: type as RecurrenceType,
      // Reset weekdays when changing type
      weekdays: type === 'weekly' ? [new Date().getDay()] : undefined,
    });
  };

  const handleEndTypeChange = (endType: string) => {
    onChange({
      ...value,
      endType: endType as RecurrenceEndType,
    });
  };

  const handleIntervalChange = (text: string) => {
    const interval = parseInt(text);
    if (!isNaN(interval) && interval > 0) {
      onChange({
        ...value,
        interval,
      });
    }
  };

  const handleWeekdaysChange = (weekdays: number[]) => {
    onChange({
      ...value,
      weekdays,
    });
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      onChange({
        ...value,
        endDate: selectedDate,
      });
    }
  };

  const handleOccurrencesChange = (text: string) => {
    const occurrences = parseInt(text);
    if (!isNaN(occurrences) && occurrences > 0) {
      onChange({
        ...value,
        occurrences,
      });
    }
  };

  const getTypeLabel = () => {
    const type = recurrenceTypes.find(t => t.value === value.type);
    return type ? type.label : 'Não repetir';
  };

  const getEndTypeLabel = () => {
    const endType = endTypes.find(t => t.value === value.endType);
    return endType ? endType.label : 'Nunca';
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Repetição</Text>

      <TouchableOpacity
        style={styles.selectorButton}
        onPress={() => setShowTypeModal(true)}
      >
        <Repeat size={20} color={colors.primary} />
        <Text style={styles.selectorButtonText}>{getTypeLabel()}</Text>
      </TouchableOpacity>

      {value.type !== 'none' && (
        <>
          {(value.type === 'daily' || value.type === 'weekly' || value.type === 'monthly' || value.type === 'yearly' || value.type === 'custom') && (
            <View style={styles.intervalContainer}>
              <Text style={styles.label}>Repetir a cada</Text>
              <View style={styles.intervalInputContainer}>
                <TextInput
                  style={styles.intervalInput}
                  value={value.interval?.toString() || '1'}
                  onChangeText={handleIntervalChange}
                  keyboardType="number-pad"
                />
                <Text style={styles.intervalText}>
                  {value.type === 'daily' && ' dia(s)'}
                  {value.type === 'weekly' && ' semana(s)'}
                  {value.type === 'monthly' && ' mês(es)'}
                  {value.type === 'yearly' && ' ano(s)'}
                  {value.type === 'custom' && ' unidade(s)'}
                </Text>
              </View>
            </View>
          )}

          {value.type === 'weekly' && (
            <WeekdaySelector
              selectedDays={value.weekdays || []}
              onChange={handleWeekdaysChange}
            />
          )}

          <View style={styles.endTypeContainer}>
            <Text style={styles.label}>Terminar</Text>
            <TouchableOpacity
              style={styles.selectorButton}
              onPress={() => setShowEndTypeModal(true)}
            >
              <CalendarIcon size={20} color={colors.primary} />
              <Text style={styles.selectorButtonText}>{getEndTypeLabel()}</Text>
            </TouchableOpacity>

            {value.endType === 'on_date' && (
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Calendar size={20} color={colors.primary} />
                <Text style={styles.datePickerButtonText}>
                  {value.endDate
                    ? format(value.endDate, 'dd/MM/yyyy', { locale: ptBR })
                    : 'Selecionar data'}
                </Text>
              </TouchableOpacity>
            )}

            {value.endType === 'after_occurrences' && (
              <View style={styles.occurrencesContainer}>
                <TextInput
                  style={styles.occurrencesInput}
                  value={value.occurrences?.toString() || '10'}
                  onChangeText={handleOccurrencesChange}
                  keyboardType="number-pad"
                />
                <Text style={styles.occurrencesText}>ocorrências</Text>
              </View>
            )}
          </View>
        </>
      )}

      <SelectModal
        visible={showTypeModal}
        onClose={() => setShowTypeModal(false)}
        options={recurrenceTypes}
        selectedValue={value.type}
        onSelect={handleTypeChange}
        title="Tipo de repetição"
      />

      <SelectModal
        visible={showEndTypeModal}
        onClose={() => setShowEndTypeModal(false)}
        options={endTypes}
        selectedValue={value.endType}
        onSelect={handleEndTypeChange}
        title="Terminar repetição"
      />

      {showEndDatePicker && (
        <DateTimePicker
          value={value.endDate || new Date()}
          mode="date"
          display="default"
          onChange={handleEndDateChange}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  selectorButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  intervalContainer: {
    marginBottom: 12,
  },
  intervalInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  intervalInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    width: 80,
    fontSize: 16,
    color: colors.text,
  },
  intervalText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  endTypeContainer: {
    marginTop: 12,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
  occurrencesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  occurrencesInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    width: 80,
    fontSize: 16,
    color: colors.text,
  },
  occurrencesText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 8,
  },
});
