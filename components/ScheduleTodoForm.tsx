import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Switch,
  Modal,
  Platform,
  Alert
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '@/components/Button';
import { GlassCard } from '@/components/GlassCard';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { TodoItem } from '@/types';
import { useStudyStore } from '@/store/studyStore';
import { Calendar, Clock, BookOpen, Bell, Tag, X, ClipboardList, AlarmClock } from 'lucide-react-native';

interface ScheduleTodoFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialTodo?: Partial<TodoItem>;
  scheduleTitle?: string;
  eventDate?: Date;
}

export const ScheduleTodoForm: React.FC<ScheduleTodoFormProps> = ({
  visible,
  onClose,
  onSave,
  initialTodo,
  scheduleTitle,
  eventDate
}) => {
  const { subjects } = useStudyStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState<Date | null>(addDays(new Date(), 1));
  const [hasDueDate, setHasDueDate] = useState(true);
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium');
  const [subject, setSubject] = useState<string | null>(null);
  const [subjectId, setSubjectId] = useState<string | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [reminder, setReminder] = useState(true);
  const [reminderTime, setReminderTime] = useState<Date | null>(null);

  // Date picker state
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  useEffect(() => {
    if (visible) {
      // Initialize form with initial values or defaults
      if (initialTodo) {
        setTitle(initialTodo.title || '');
        setDescription(initialTodo.description || '');
        setDueDate(initialTodo.dueDate ? parseISO(initialTodo.dueDate) : null);
        setHasDueDate(!!initialTodo.dueDate);
        setPriority(initialTodo.priority || 'medium');
        setSubject(initialTodo.subject || null);
        setSubjectId(initialTodo.subject_id || null);
        setTags(initialTodo.tags || []);
        setReminder(!!initialTodo.reminderTime);
        setReminderTime(initialTodo.reminderTime ? parseISO(initialTodo.reminderTime) : null);
      } else {
        // Default values for new todo
        // Use event date + 1 day if provided, otherwise tomorrow
        const defaultDueDate = eventDate ? addDays(eventDate, 1) : addDays(new Date(), 1);

        // Prefill title and description with schedule info if available
        setTitle(subject ? `Revisar ${subject}` : 'Revisão de conteúdo');
        setDescription(scheduleTitle ? `Revisão do conteúdo estudado (Cronograma: ${scheduleTitle})` : 'Revisão do conteúdo estudado');

        // Se não houver uma matéria selecionada e houver matérias disponíveis, selecione a primeira
        if (!subject && subjects.length > 0) {
          const firstSubject = subjects[0];
          setSubject(firstSubject.title);
          setSubjectId(firstSubject.id);
          setTitle(`Revisar ${firstSubject.title}`);
          setDescription(scheduleTitle ? `Revisão do conteúdo estudado em ${firstSubject.title} (Cronograma: ${scheduleTitle})` : `Revisão do conteúdo estudado em ${firstSubject.title}`);
        }
        setDueDate(defaultDueDate);
        setHasDueDate(true);
        setPriority('medium');
        setSubject(null);
        setSubjectId(null);
        setTags(['revisão', 'cronograma']);
        setReminder(true);

        // Set reminder time to 9 AM on due date
        const reminderDate = new Date(defaultDueDate);
        reminderDate.setHours(9, 0, 0, 0);
        setReminderTime(reminderDate);
      }

      setNewTag('');
    }
  }, [visible, initialTodo, scheduleTitle, subject, eventDate]);

  useEffect(() => {
    // Set reminder time to 9 AM on due date if due date exists and reminder is enabled
    if (reminder && dueDate && !reminderTime) {
      const reminderDate = new Date(dueDate);
      reminderDate.setHours(9, 0, 0, 0);
      setReminderTime(reminderDate);
    }
  }, [reminder, dueDate]);

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Erro', 'O título da tarefa é obrigatório.');
      return;
    }

    const todo: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      dueDate: hasDueDate && dueDate ? dueDate.toISOString() : undefined,
      priority,
      completed: false,
      subject: subject || undefined,
      subject_id: subjectId || undefined,
      tags,
      reminderTime: reminder && reminderTime ? reminderTime.toISOString() : undefined,
    };

    onSave(todo);
    onClose();
  };

  const handleDueDateChange = (event: any, selectedDate?: Date) => {
    setShowDueDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDueDate(selectedDate);

      // Update reminder time to 9 AM on the new due date
      if (reminder) {
        const reminderDate = new Date(selectedDate);
        reminderDate.setHours(9, 0, 0, 0);
        setReminderTime(reminderDate);
      }
    }
  };

  const handleReminderTimeChange = (event: any, selectedTime?: Date) => {
    setShowReminderTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      setReminderTime(selectedTime);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const handleSubjectSelect = (subjectId: string, subjectTitle: string) => {
    setSubjectId(subjectId);
    setSubject(subjectTitle);
    setTitle(`Revisar ${subjectTitle}`);
    setDescription(scheduleTitle ? `Revisão do conteúdo estudado em ${subjectTitle} (Cronograma: ${scheduleTitle})` : `Revisão do conteúdo estudado em ${subjectTitle}`);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <GlassCard style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {initialTodo?.id ? 'Editar Tarefa' : 'Nova Tarefa de Revisão'}
            </Text>
            <Pressable
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            >
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Título</Text>
              <TextInput
                style={styles.input}
                value={title}
                onChangeText={setTitle}
                placeholder="Título da tarefa"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Descrição</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Descrição da tarefa"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Matéria</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.subjectsScrollView}
              >
                {subjects.map((s) => (
                  <Pressable
                    key={s.id}
                    style={[
                      styles.subjectItem,
                      { borderColor: s.color },
                      subjectId === s.id && { backgroundColor: `${s.color}20` }
                    ]}
                    onPress={() => handleSubjectSelect(s.id, s.title)}
                  >
                    <Text style={[
                      styles.subjectItemText,
                      subjectId === s.id && { color: s.color, fontWeight: 'bold' }
                    ]}>
                      {s.title}
                    </Text>
                  </Pressable>
                ))}
              </ScrollView>
            </View>

            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Data de vencimento</Text>
                <Switch
                  value={hasDueDate}
                  onValueChange={setHasDueDate}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={hasDueDate ? colors.primary : colors.white}
                />
              </View>

              {hasDueDate && (
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowDueDatePicker(true)}
                >
                  <Calendar size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {dueDate ? format(dueDate, 'dd/MM/yyyy', { locale: ptBR }) : 'Selecionar data'}
                  </Text>
                </Pressable>
              )}

              {showDueDatePicker && dueDate && (
                <DateTimePicker
                  value={dueDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleDueDateChange}
                  minimumDate={new Date()}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Prioridade</Text>
              <View style={styles.priorityContainer}>
                {[
                  { value: 'low', label: 'Baixa', color: colors.success },
                  { value: 'medium', label: 'Média', color: colors.warning },
                  { value: 'high', label: 'Alta', color: colors.error }
                ].map((option) => (
                  <Pressable
                    key={option.value}
                    style={[
                      styles.priorityOption,
                      { borderColor: option.color },
                      priority === option.value && { backgroundColor: `${option.color}20` }
                    ]}
                    onPress={() => setPriority(option.value as any)}
                  >
                    <View style={[styles.priorityDot, { backgroundColor: option.color }]} />
                    <Text style={styles.priorityOptionText}>
                      {option.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.label}>Lembrete</Text>
                <Switch
                  value={reminder}
                  onValueChange={setReminder}
                  trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                  thumbColor={reminder ? colors.primary : colors.white}
                />
              </View>

              {reminder && dueDate && (
                <Pressable
                  style={styles.datePickerButton}
                  onPress={() => setShowReminderTimePicker(true)}
                >
                  <Bell size={20} color={colors.primary} />
                  <Text style={styles.datePickerButtonText}>
                    {reminderTime
                      ? format(reminderTime, 'dd/MM/yyyy HH:mm', { locale: ptBR })
                      : 'Selecionar horário do lembrete'}
                  </Text>
                </Pressable>
              )}

              {showReminderTimePicker && reminderTime && (
                <DateTimePicker
                  value={reminderTime}
                  mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleReminderTimeChange}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tags</Text>
              <View style={styles.tagsContainer}>
                {tags.map((tag) => (
                  <View key={tag} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                    <Pressable
                      style={styles.removeTagButton}
                      onPress={() => handleRemoveTag(tag)}
                    >
                      <X size={16} color={colors.white} />
                    </Pressable>
                  </View>
                ))}
              </View>

              <View style={styles.addTagContainer}>
                <TextInput
                  style={styles.addTagInput}
                  value={newTag}
                  onChangeText={setNewTag}
                  placeholder="Nova tag"
                  placeholderTextColor={colors.textLight}
                  onSubmitEditing={handleAddTag}
                />
                <Pressable
                  style={styles.addTagButton}
                  onPress={handleAddTag}
                  disabled={!newTag.trim()}
                >
                  <Tag size={20} color={newTag.trim() ? colors.primary : colors.textLight} />
                </Pressable>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title="Cancelar"
                onPress={onClose}
                variant="outline"
                size="medium"
                style={styles.cancelButton}
              />
              <Button
                title="Salvar"
                onPress={handleSave}
                variant="primary"
                size="medium"
                style={styles.saveButton}
                loading={false}
              />
            </View>
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
    zIndex: 9999,
  },
  formContainer: {
    width: '100%',
    maxHeight: '90%',
    backgroundColor: colors.background,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  scrollView: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  subjectsScrollView: {
    flexGrow: 0,
    marginBottom: 8,
  },
  subjectItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  subjectItemText: {
    fontSize: 14,
    color: colors.text,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    marginTop: 8,
  },
  datePickerButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.text,
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderWidth: 1,
    borderRadius: 10,
    marginHorizontal: 4,
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  priorityOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
    color: colors.white,
    marginRight: 4,
  },
  removeTagButton: {
    padding: 2,
  },
  addTagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addTagInput: {
    flex: 1,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    marginRight: 8,
  },
  addTagButton: {
    padding: 12,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
  },
});
