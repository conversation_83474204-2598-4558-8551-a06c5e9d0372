import React from "react";
import { View, StyleSheet, ViewProps } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";

interface GlassCardProps extends ViewProps {
  intensity?: number;
  tint?: "light" | "dark";
  gradient?: boolean;
  gradientColors?: string[];
}

export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  style,
  intensity = 80,
  tint = "light",
  gradient = false,
  gradientColors,
  ...props
}) => {
  const backgroundColor = tint === "light" ? colors.glass : colors.glassDark;

  if (gradient) {
    const hasCustomColors = gradientColors && gradientColors.length > 0;
    const defaultColors = hasCustomColors ? gradientColors : ["rgba(255,255,255,0.8)", "rgba(255,255,255,0.5)"];

    return (
      <LinearGradient
        colors={defaultColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.container, style]}
        {...props}
      >
        {children}
      </LinearGradient>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor },
        style,
      ]}
      {...props}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    alignSelf: 'stretch',
  },
});