import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { colors } from "@/constants/colors";
import { GlassCard } from "./GlassCard";
import { ProgressBar } from "./ProgressBar";
import { <PERSON><PERSON><PERSON>, Clock, Award, Zap, BarChart2, Brain } from "lucide-react-native";
import { formatDistanceToNow } from "@/utils/dateUtils";
import { Flashcard } from "@/types";
import { calculateAllStats } from "@/utils/flashcardStats";

interface FlashcardStatsProps {
  flashcards?: Flashcard[];
  totalCards?: number;
  masteredCards?: number;
  lastReviewed?: string | null;
  reviewCount?: number;
  averageScore?: number;
  streakDays?: number;
}

export const FlashcardStats: React.FC<FlashcardStatsProps> = ({
  flashcards = [],
  totalCards: propsTotalCards,
  masteredCards: propsMasteredCards,
  lastReviewed: propsLastReviewed,
  reviewCount: propsReviewCount,
  averageScore: propsAverageScore,
  streakDays: propsStreakDays,
}) => {
  // Calcular estatísticas a partir dos flashcards, se fornecidos
  const stats = flashcards.length > 0 ? calculateAllStats(flashcards) : null;

  // Usar valores calculados ou props
  const totalCards = propsTotalCards ?? (stats ? stats.progress.total : 0);
  const masteredCards = propsMasteredCards ?? (stats ? stats.progress.mastered : 0);
  const reviewCount = propsReviewCount ?? (stats ? stats.activity.totalReviews : 0);
  const streakDays = propsStreakDays ?? (stats ? stats.activity.streakDays : 0);
  const lastReviewed = propsLastReviewed ?? null;
  const averageScore = propsAverageScore ?? (stats ? 100 - stats.difficulty.hardPercentage : 0);

  const masteredPercentage = totalCards > 0 ? Math.round((masteredCards / totalCards) * 100) : 0;

  return (
    <GlassCard style={styles.container}>
      <Text style={styles.title}>Estatísticas de Estudo</Text>

      <View style={styles.progressSection}>
        <View style={styles.progressLabelContainer}>
          <Text style={styles.progressLabel}>Progresso</Text>
          <Text style={styles.progressPercentage}>{masteredPercentage}%</Text>
        </View>
        <ProgressBar
          progress={masteredPercentage}
          height={10}
          gradientColors={["#60A5FA", "#2563EB"]}
          style={styles.progressBar}
        />
      </View>

      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <BookOpen size={18} color={colors.primary} />
          </View>
          <View>
            <Text style={styles.statValue}>{totalCards}</Text>
            <Text style={styles.statLabel}>Cartões</Text>
          </View>
        </View>

        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <Award size={18} color={colors.success} />
          </View>
          <View>
            <Text style={styles.statValue}>{masteredCards}</Text>
            <Text style={styles.statLabel}>Dominados</Text>
          </View>
        </View>

        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <BarChart2 size={18} color={colors.info} />
          </View>
          <View>
            <Text style={styles.statValue}>{reviewCount}</Text>
            <Text style={styles.statLabel}>Revisões</Text>
          </View>
        </View>

        <View style={styles.statItem}>
          <View style={styles.statIconContainer}>
            <Zap size={18} color={colors.warning} />
          </View>
          <View>
            <Text style={styles.statValue}>{streakDays}</Text>
            <Text style={styles.statLabel}>Dias seguidos</Text>
          </View>
        </View>
      </View>

      {lastReviewed && (
        <View style={styles.lastReviewedContainer}>
          <Clock size={16} color={colors.textLight} />
          <Text style={styles.lastReviewedText}>
            Última revisão: {formatDistanceToNow(new Date(lastReviewed))}
          </Text>
        </View>
      )}

      <View style={styles.averageScoreContainer}>
        <Text style={styles.averageScoreLabel}>Pontuação média:</Text>
        <View style={styles.scoreBar}>
          <View
            style={[
              styles.scoreBarFill,
              {
                width: `${averageScore}%`,
                backgroundColor:
                  averageScore >= 80
                    ? colors.success
                    : averageScore >= 60
                    ? colors.warning
                    : colors.error,
              },
            ]}
          />
        </View>
        <Text style={styles.averageScoreValue}>{averageScore}%</Text>
      </View>
    </GlassCard>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 16,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressLabelContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: colors.textLight,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.primary,
  },
  progressBar: {
    borderRadius: 6,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    width: "48%",
    marginBottom: 12,
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 12,
    backgroundColor: colors.backgroundLight,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textLight,
  },
  lastReviewedContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  lastReviewedText: {
    fontSize: 14,
    color: colors.textLight,
    marginLeft: 8,
  },
  averageScoreContainer: {
    marginTop: 8,
  },
  averageScoreLabel: {
    fontSize: 14,
    color: colors.textLight,
    marginBottom: 8,
  },
  scoreBar: {
    height: 8,
    backgroundColor: colors.backgroundLight,
    borderRadius: 4,
    overflow: "hidden",
    marginBottom: 4,
  },
  scoreBarFill: {
    height: "100%",
    borderRadius: 4,
  },
  averageScoreValue: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text,
    textAlign: "right",
  },
});
