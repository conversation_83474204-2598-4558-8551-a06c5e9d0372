import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  Pressable,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Image,
  TouchableOpacity
} from "react-native";
import { colors } from "@/constants/colors";
import { Button } from "@/components/Button";
import { X, Check, BookOpen, Image as ImageIcon, Camera, Upload } from "lucide-react-native";
import { useStudyStore } from "@/store/studyStore";
import { SubjectSelector } from "@/components/SubjectSelector";
import * as ImagePicker from 'expo-image-picker';

interface FlashcardFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (question: string, answer: string, subjectId?: string, imageUri?: string) => void;
  loading?: boolean;
  setId?: string;
}

export const FlashcardForm: React.FC<FlashcardFormProps> = ({
  visible,
  onClose,
  onSave,
  loading = false,
  setId
}) => {
  const { subjects, fetchSubjects, loading: subjectsLoading } = useStudyStore();
  const [question, setQuestion] = useState("");
  const [answer, setAnswer] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | undefined>();
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [showImageOptions, setShowImageOptions] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchSubjects();
    }
  }, [visible]);

  const handleSave = () => {
    if (!question.trim()) {
      Alert.alert("Erro", "Por favor, insira uma pergunta para o flashcard.");
      return;
    }

    if (!answer.trim()) {
      Alert.alert("Erro", "Por favor, insira uma resposta para o flashcard.");
      return;
    }

    onSave(question, answer, selectedSubjectId, imageUri || undefined);
    resetForm();
  };

  const pickImage = async () => {
    try {
      // Solicitar permissões
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permissão negada', 'Precisamos de permissão para acessar sua galeria de fotos.');
        return;
      }

      // Abrir seletor de imagens
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
        setShowImageOptions(false);
      }
    } catch (error) {
      console.error('Erro ao selecionar imagem:', error);
      Alert.alert('Erro', 'Não foi possível selecionar a imagem. Por favor, tente novamente.');
    }
  };

  const takePhoto = async () => {
    try {
      // Solicitar permissões
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permissão negada', 'Precisamos de permissão para acessar sua câmera.');
        return;
      }

      // Abrir câmera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setImageUri(result.assets[0].uri);
        setShowImageOptions(false);
      }
    } catch (error) {
      console.error('Erro ao tirar foto:', error);
      Alert.alert('Erro', 'Não foi possível tirar a foto. Por favor, tente novamente.');
    }
  };

  const removeImage = () => {
    setImageUri(null);
    setShowImageOptions(false);
  };

  const resetForm = () => {
    setQuestion("");
    setAnswer("");
    setSelectedSubject("");
    setSelectedSubjectId(undefined);
    setImageUri(null);
    setShowImageOptions(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Novo Flashcard</Text>
              <Pressable
                style={styles.closeButton}
                onPress={handleClose}
              >
                <X size={24} color={colors.text} />
              </Pressable>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Matéria</Text>
                <SubjectSelector
                  value={selectedSubject}
                  onChange={(value) => {
                    setSelectedSubject(value);
                    // Encontrar o ID da matéria selecionada
                    const subject = subjects.find(s => s.title === value);
                    setSelectedSubjectId(subject?.id);
                  }}
                  placeholder="Selecione uma matéria"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Pergunta</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  placeholder="Digite a pergunta do flashcard"
                  placeholderTextColor={colors.textMedium}
                  value={question}
                  onChangeText={setQuestion}
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Resposta</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  placeholder="Digite a resposta do flashcard"
                  placeholderTextColor={colors.textMedium}
                  value={answer}
                  onChangeText={setAnswer}
                  multiline
                  numberOfLines={5}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Imagem (opcional)</Text>

                {imageUri ? (
                  <View style={styles.imagePreviewContainer}>
                    <Image source={{ uri: imageUri }} style={styles.imagePreview} />
                    <Pressable style={styles.removeImageButton} onPress={removeImage}>
                      <X size={20} color={colors.white} />
                    </Pressable>
                  </View>
                ) : (
                  <Pressable
                    style={styles.imagePickerButton}
                    onPress={() => setShowImageOptions(true)}
                  >
                    <ImageIcon size={24} color={colors.primary} />
                    <Text style={styles.imagePickerText}>Adicionar imagem</Text>
                  </Pressable>
                )}

                {showImageOptions && (
                  <View style={styles.imageOptionsContainer}>
                    <Pressable style={styles.imageOption} onPress={pickImage}>
                      <Upload size={24} color={colors.primary} />
                      <Text style={styles.imageOptionText}>Galeria</Text>
                    </Pressable>

                    <Pressable style={styles.imageOption} onPress={takePhoto}>
                      <Camera size={24} color={colors.primary} />
                      <Text style={styles.imageOptionText}>Câmera</Text>
                    </Pressable>

                    <Pressable style={styles.imageOption} onPress={() => setShowImageOptions(false)}>
                      <X size={24} color={colors.error} />
                      <Text style={[styles.imageOptionText, { color: colors.error }]}>Cancelar</Text>
                    </Pressable>
                  </View>
                )}
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                title="Cancelar"
                onPress={handleClose}
                variant="secondary"
                style={styles.modalButton}
                icon={X}
              />
              <Button
                title={loading ? "Salvando..." : "Salvar"}
                onPress={handleSave}
                variant="primary"
                style={styles.modalButton}
                icon={Check}
                disabled={loading}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  keyboardAvoidingView: {
    width: "100%",
    maxWidth: 500,
  },
  modalContainer: {
    width: "100%",
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
    maxHeight: 400,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.text,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  imagePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    borderStyle: "dashed",
  },
  imagePickerText: {
    marginLeft: 8,
    fontSize: 16,
    color: colors.primary,
    fontWeight: "500",
  },
  imagePreviewContainer: {
    position: "relative",
    borderRadius: 8,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  imagePreview: {
    width: "100%",
    height: 200,
    resizeMode: "cover",
  },
  removeImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: colors.error,
    borderRadius: 20,
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  imageOptionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 16,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 16,
  },
  imageOption: {
    alignItems: "center",
    padding: 8,
  },
  imageOptionText: {
    marginTop: 4,
    fontSize: 14,
    color: colors.text,
  },
  modalFooter: {
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  modalButton: {
    minWidth: 100,
    marginLeft: 12,
  },
});
