import React from 'react';
import { View, Text, StyleSheet, Pressable, Switch } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { colors } from '@/constants/colors';
import { Card } from './Card';
import { Sun, Moon, Smartphone } from 'lucide-react-native';

interface ThemeSettingsProps {
  showTitle?: boolean;
  style?: any;
}

export const ThemeSettings: React.FC<ThemeSettingsProps> = ({
  showTitle = true,
  style,
}) => {
  const { theme, themeMode, setThemeMode, isDark } = useTheme();

  // Função para renderizar o ícone do tema
  const renderThemeIcon = () => {
    switch (themeMode) {
      case 'light':
        return <Sun size={24} color={colors.secondary} />;
      case 'dark':
        return <Moon size={24} color={colors.accent1} />;
      case 'system':
        return <Smartphone size={24} color={colors.primary} />;
    }
  };

  // Função para obter o texto do tema atual
  const getThemeModeText = () => {
    switch (themeMode) {
      case 'light':
        return 'Tema Claro';
      case 'dark':
        return 'Tema Escuro';
      case 'system':
        return 'Tema do Sistema';
    }
  };

  return (
    <Card
      style={[styles.container, style]}
      variant="default"
      elevation="low"
    >
      {showTitle && (
        <Text style={[styles.title, { color: colors.text }]}>
          Configurações de Tema
        </Text>
      )}

      {/* Opção de tema claro */}
      <Pressable
        style={[
          styles.option,
          themeMode === 'light' && { backgroundColor: colors.backgroundLight },
        ]}
        onPress={() => setThemeMode('light')}
      >
        <View style={styles.optionContent}>
          <View style={[styles.iconContainer, { backgroundColor: colors.backgroundLight }]}>
            <Sun size={24} color={colors.secondary} />
          </View>
          <Text style={[styles.optionText, { color: colors.text }]}>
            Tema Claro
          </Text>
        </View>
        <View style={styles.radioButton}>
          {themeMode === 'light' && (
            <View style={[styles.radioButtonInner, { backgroundColor: colors.primary }]} />
          )}
        </View>
      </Pressable>

      {/* Opção de tema escuro */}
      <Pressable
        style={[
          styles.option,
          themeMode === 'dark' && { backgroundColor: colors.backgroundLight },
        ]}
        onPress={() => setThemeMode('dark')}
      >
        <View style={styles.optionContent}>
          <View style={[styles.iconContainer, { backgroundColor: colors.backgroundLight }]}>
            <Moon size={24} color={colors.accent1} />
          </View>
          <Text style={[styles.optionText, { color: colors.text }]}>
            Tema Escuro
          </Text>
        </View>
        <View style={styles.radioButton}>
          {themeMode === 'dark' && (
            <View style={[styles.radioButtonInner, { backgroundColor: colors.primary }]} />
          )}
        </View>
      </Pressable>

      {/* Opção de tema do sistema */}
      <Pressable
        style={[
          styles.option,
          themeMode === 'system' && { backgroundColor: colors.backgroundLight },
        ]}
        onPress={() => setThemeMode('system')}
      >
        <View style={styles.optionContent}>
          <View style={[styles.iconContainer, { backgroundColor: colors.backgroundLight }]}>
            <Smartphone size={24} color={colors.primary} />
          </View>
          <Text style={[styles.optionText, { color: colors.text }]}>
            Tema do Sistema
          </Text>
        </View>
        <View style={styles.radioButton}>
          {themeMode === 'system' && (
            <View style={[styles.radioButtonInner, { backgroundColor: colors.primary }]} />
          )}
        </View>
      </Pressable>

      {/* Informação sobre o tema atual */}
      <View style={styles.infoContainer}>
        <View style={styles.infoIconContainer}>
          {renderThemeIcon()}
        </View>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          Tema atual: {getThemeModeText()}
          {themeMode === 'system' && ` (${isDark ? 'Escuro' : 'Claro'})`}
        </Text>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: 0,
    overflow: 'hidden',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ccc',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  infoIconContainer: {
    marginRight: 12,
  },
  infoText: {
    fontSize: 14,
  },
});

export default ThemeSettings;
