import React, { useState, useEffect, memo } from 'react';
import {
  Image,
  ImageProps,
  View,
  StyleSheet,
  ActivityIndicator,
  Platform,
  Animated,
} from 'react-native';
import { getCachedImage } from '@/utils/imageCache';
import { colors } from '@/constants/colors';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  source: { uri: string } | number;
  width?: number;
  height?: number;
  thumbnail?: boolean;
  quality?: number;
  placeholderColor?: string;
  showLoadingIndicator?: boolean;
  fadeDuration?: number;
  priority?: 'high' | 'normal' | 'low';
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

const OptimizedImageComponent: React.FC<OptimizedImageProps> = ({
  source,
  width,
  height,
  thumbnail = false,
  quality = 80,
  placeholderColor = colors.backgroundDark,
  showLoadingIndicator = false,
  fadeDuration = 300,
  priority = 'normal',
  style,
  onLoad,
  onError,
  ...props
}) => {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const opacity = useState(new Animated.Value(0))[0];

  useEffect(() => {
    let isMounted = true;
    
    const loadImage = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // If source is a number (local image), use it directly
        if (typeof source === 'number') {
          setImageUri(Image.resolveAssetSource(source).uri);
          setLoading(false);
          
          if (isMounted) {
            Animated.timing(opacity, {
              toValue: 1,
              duration: fadeDuration,
              useNativeDriver: true,
            }).start();
          }
          
          return;
        }
        
        // For remote images, use the cache
        if (source.uri) {
          const cachedUri = await getCachedImage(source.uri, {
            width,
            height,
            quality,
            thumbnail,
          });
          
          if (isMounted) {
            setImageUri(cachedUri || source.uri);
            setLoading(false);
            
            Animated.timing(opacity, {
              toValue: 1,
              duration: fadeDuration,
              useNativeDriver: true,
            }).start();
          }
        }
      } catch (err) {
        if (isMounted) {
          const error = err instanceof Error ? err : new Error('Failed to load image');
          setError(error);
          setLoading(false);
          if (onError) onError(error);
        }
      }
    };
    
    loadImage();
    
    return () => {
      isMounted = false;
    };
  }, [source, width, height, thumbnail, quality, fadeDuration]);

  const handleLoad = () => {
    if (onLoad) onLoad();
  };

  const handleError = (err: any) => {
    const error = new Error('Image loading failed');
    setError(error);
    if (onError) onError(error);
  };

  // Calculate dimensions for the placeholder
  const styleObj = StyleSheet.flatten(style || {});
  const placeholderWidth = width || styleObj.width || 100;
  const placeholderHeight = height || styleObj.height || 100;

  return (
    <View style={[styles.container, { width: placeholderWidth, height: placeholderHeight }]}>
      {/* Placeholder */}
      {loading && (
        <View
          style={[
            styles.placeholder,
            {
              width: placeholderWidth,
              height: placeholderHeight,
              backgroundColor: placeholderColor,
            },
          ]}
        >
          {showLoadingIndicator && (
            <ActivityIndicator size="small" color={colors.primary} />
          )}
        </View>
      )}
      
      {/* Actual image */}
      {imageUri && (
        <Animated.Image
          source={{ uri: imageUri }}
          style={[
            styles.image,
            style,
            { opacity },
            width && { width },
            height && { height },
          ]}
          onLoad={handleLoad}
          onError={handleError}
          fadeDuration={0} // We handle fading ourselves
          {...props}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
});

// Memoize the component to prevent unnecessary re-renders
export const OptimizedImage = memo(OptimizedImageComponent);
