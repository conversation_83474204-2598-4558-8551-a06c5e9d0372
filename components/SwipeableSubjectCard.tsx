import React, { useRef, useState } from 'react';
import { View, Text, StyleSheet, Animated, Alert, Pressable } from 'react-native';
import { Swipeable, RectButton } from 'react-native-gesture-handler';
import { SubjectCard } from './SubjectCard';
import { Subject } from '@/types';
import { colors } from '@/constants/colors';
import { Edit, Trash2 } from 'lucide-react-native';

interface SwipeableSubjectCardProps {
  subject: Subject;
  onPress: (subject: Subject) => void;
  onEdit: (subject: Subject) => void;
  onDelete: (subject: Subject) => void;
}

export const SwipeableSubjectCard: React.FC<SwipeableSubjectCardProps> = ({
  subject,
  onPress,
  onEdit,
  onDelete,
}) => {
  const swipeableRef = useRef<Swipeable>(null);

  const closeSwipeable = () => {
    swipeableRef.current?.close();
  };

  const handleEdit = () => {
    closeSwipeable();
    onEdit(subject);
  };

  const handleDelete = () => {
    closeSwipeable();
    Alert.alert(
      'Excluir Matéria',
      `Tem certeza que deseja excluir a matéria "${subject.title}"? Esta ação não pode ser desfeita.`,
      [
        { text: 'Cancelar', style: 'cancel', onPress: closeSwipeable },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: () => onDelete(subject),
        },
      ]
    );
  };

  const renderRightActions = (progress: Animated.AnimatedInterpolation<number>) => {
    const trans = progress.interpolate({
      inputRange: [0, 1],
      outputRange: [64, 0],
    });

    return (
      <View style={styles.rightActionsContainer}>
        <Animated.View style={{ transform: [{ translateX: trans }] }}>
          <RectButton style={[styles.rightAction, styles.editAction]} onPress={handleEdit}>
            <Edit size={24} color="#fff" />
            <Text style={styles.actionText}>Editar</Text>
          </RectButton>
        </Animated.View>
        <Animated.View style={{ transform: [{ translateX: trans }] }}>
          <RectButton style={[styles.rightAction, styles.deleteAction]} onPress={handleDelete}>
            <Trash2 size={24} color="#fff" />
            <Text style={styles.actionText}>Excluir</Text>
          </RectButton>
        </Animated.View>
      </View>
    );
  };

  return (
    <Swipeable
      ref={swipeableRef}
      friction={2}
      rightThreshold={40}
      renderRightActions={renderRightActions}
    >
      <SubjectCard
        subject={subject}
        onPress={onPress}
      />
    </Swipeable>
  );
};

const styles = StyleSheet.create({
  rightActionsContainer: {
    width: 192,
    flexDirection: 'row',
  },
  rightAction: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    marginBottom: 16,
    marginLeft: 8,
  },
  editAction: {
    backgroundColor: colors.primary,
  },
  deleteAction: {
    backgroundColor: colors.error,
  },
  actionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
});
