import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  Pressable,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { X, MessageSquare, Plus, Trash2, AlertCircle } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { images } from '@/constants/images';
import { GlassCard } from './GlassCard';
import { useAssistantChatStore } from '@/store/assistantChatStore';
import { formatDistanceToNow } from 'date-fns';

interface ChatHistoryModalProps {
  visible: boolean;
  onClose: () => void;
}

export const ChatHistoryModal: React.FC<ChatHistoryModalProps> = ({
  visible,
  onClose,
}) => {
  const {
    conversations,
    currentConversationId,
    setCurrentConversation,
    createNewConversation,
    removeConversation,
    fetchConversations,
    isLoading,
    error
  } = useAssistantChatStore();

  useEffect(() => {
    if (visible) {
      fetchConversations();
    }
  }, [visible]);

  const handleSelectConversation = (id: string) => {
    setCurrentConversation(id);
    onClose();
  };

  const handleNewConversation = async () => {
    try {
      // Criar nova conversa e obter o ID
      const newConversationId = await createNewConversation();

      // Garantir que a nova conversa seja definida como a conversa atual
      setCurrentConversation(newConversationId);

      // Forçar atualização do estado para garantir que a UI reflita a nova conversa
      useAssistantChatStore.setState({
        currentConversationId: newConversationId,
        isLoading: false
      });

      // Fechar o modal
      onClose();
    } catch (error) {
      console.error('Error creating new conversation:', error);
      Alert.alert(
        "Erro",
        "Ocorreu um erro ao criar uma nova conversa. Por favor, tente novamente."
      );
    }
  };

  // Estado local para rastrear quais conversas estão sendo excluídas
  const [deletingConversations, setDeletingConversations] = useState<Set<string>>(new Set());

  const handleDeleteConversation = async (id: string, event: any) => {
    event.stopPropagation();

    // Confirmar exclusão
    Alert.alert(
      "Excluir conversa",
      "Tem certeza que deseja excluir esta conversa? Esta ação não pode ser desfeita.",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            try {
              // Marcar a conversa como sendo excluída
              setDeletingConversations(prev => {
                const newSet = new Set(prev);
                newSet.add(id);
                return newSet;
              });

              // Executar a exclusão
              await removeConversation(id);

              // Remover do conjunto de exclusão após concluir
              setDeletingConversations(prev => {
                const newSet = new Set(prev);
                newSet.delete(id);
                return newSet;
              });
            } catch (error) {
              console.error('Error deleting conversation:', error);

              // Remover do conjunto de exclusão em caso de erro
              setDeletingConversations(prev => {
                const newSet = new Set(prev);
                newSet.delete(id);
                return newSet;
              });
            }
          }
        }
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <GlassCard style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <View style={styles.modalTitleContainer}>
              <Image
                source={images.logo}
                style={styles.modalLogo}
                resizeMode="contain"
                fadeDuration={0}
                cachePolicy="memory-disk"
              />
              <Text style={styles.modalTitle}>Conversas com a Lia</Text>
            </View>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          <TouchableOpacity
            style={styles.newConversationButton}
            onPress={handleNewConversation}
            activeOpacity={0.8}
          >
            <View style={styles.newConversationContent}>
              <View style={styles.newConversationIcon}>
                <Plus size={20} color={colors.white} />
              </View>
              <Text style={styles.newConversationText}>Iniciar Nova Conversa</Text>
            </View>
          </TouchableOpacity>

          <ScrollView style={styles.conversationList}>
            <Text style={styles.sectionTitle}>Conversas Recentes</Text>

            {isLoading && conversations.length === 0 ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Carregando conversas...</Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <AlertCircle size={24} color={colors.danger} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : conversations.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Nenhuma conversa encontrada</Text>
                <Text style={styles.emptySubtext}>Inicie uma nova conversa com a Lia</Text>
              </View>
            ) : (
              conversations.map((conversation) => (
                <Pressable
                  key={conversation.id}
                  style={[
                    styles.conversationItem,
                    currentConversationId === conversation.id && styles.activeConversation,
                  ]}
                  onPress={() => handleSelectConversation(conversation.id)}
                >
                  <Image
                    source={images.logo}
                    style={styles.conversationLogo}
                    resizeMode="contain"
                  />
                  <View style={styles.conversationInfo}>
                    <Text
                      style={styles.conversationTitle}
                      numberOfLines={1}
                    >
                      {conversation.title}
                    </Text>
                    <Text style={styles.conversationMeta}>
                      {conversation.messageCount} mensagens • {formatDistanceToNow(new Date(conversation.lastUpdated), { addSuffix: true })}
                    </Text>
                  </View>
                  <Pressable
                    style={styles.deleteButton}
                    onPress={(e) => handleDeleteConversation(conversation.id, e)}
                    disabled={deletingConversations.has(conversation.id)}
                  >
                    {deletingConversations.has(conversation.id) ? (
                      <ActivityIndicator size="small" color={colors.danger} />
                    ) : (
                      <Trash2 size={18} color={colors.danger} />
                    )}
                  </Pressable>
                </Pressable>
              ))
            )}
          </ScrollView>
        </GlassCard>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.textLight,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: `${colors.danger}10`,
    borderRadius: 12,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.danger,
    textAlign: 'center',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textLight,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: colors.white,
    paddingBottom: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalLogo: {
    width: 30,
    height: 30,
    marginRight: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  conversationList: {
    padding: 16,
  },
  newConversationButton: {
    marginHorizontal: 16,
    marginBottom: 16,
    marginTop: 8,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    backgroundColor: colors.primary,
  },
  newConversationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  newConversationIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  newConversationText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  activeConversation: {
    backgroundColor: `${colors.primary}10`,
    borderColor: colors.primary,
  },
  conversationLogo: {
    width: 36,
    height: 36,
    marginRight: 12,
  },
  conversationInfo: {
    flex: 1,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
  },
  conversationMeta: {
    fontSize: 12,
    color: colors.text,
    opacity: 0.7,
  },
  deleteButton: {
    padding: 8,
  },
});
