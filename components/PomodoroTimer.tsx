import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Pressable, Modal, Alert } from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '@/components/Button';
import { useTimerStore, startTimerTicker, TimerMode } from '@/store/timerStore';
import { Play, Pause, SkipForward, Clock, X, Settings } from 'lucide-react-native';
import * as Notifications from 'expo-notifications';
import { LinearGradient } from 'expo-linear-gradient';

// Configurar notificações
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export const PomodoroTimer: React.FC = () => {
  const {
    isActive,
    mode,
    timeRemaining,
    focusTime,
    breakTime,
    sessionsCompleted,
    startTimer,
    pauseTimer,
    resumeTimer,
    stopTimer,
    switchMode
  } = useTimerStore();

  const [settingsVisible, setSettingsVisible] = useState(false);
  const [customFocusTime, setCustomFocusTime] = useState(focusTime);
  const [customBreakTime, setCustomBreakTime] = useState(breakTime);

  useEffect(() => {
    // Iniciar o ticker quando o componente for montado
    startTimerTicker();

    // Solicitar permissão para notificações
    Notifications.requestPermissionsAsync();

    // Limpar quando o componente for desmontado
    return () => {
      // Não paramos o ticker aqui para que continue funcionando em segundo plano
    };
  }, []);

  useEffect(() => {
    // Enviar notificação quando o modo mudar
    if (mode === 'focus') {
      scheduleNotification('Hora de focar!', 'Sua sessão de foco começou.');
    } else if (mode === 'break') {
      scheduleNotification('Hora de descansar!', 'Sua pausa começou. Aproveite para relaxar.');
    }
  }, [mode]);

  const scheduleNotification = async (title: string, body: string) => {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        sound: true,
      },
      trigger: null, // Enviar imediatamente
    });
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = (mode: TimerMode): string => {
    switch (mode) {
      case 'focus':
        return colors.primary;
      case 'break':
        return colors.success;
      default:
        return colors.textLight;
    }
  };

  const getTimerLabel = (mode: TimerMode): string => {
    switch (mode) {
      case 'focus':
        return 'Foco';
      case 'break':
        return 'Descanso';
      default:
        return 'Pomodoro';
    }
  };

  const handleStartTimer = () => {
    startTimer(customFocusTime, customBreakTime);
  };

  const handleToggleTimer = () => {
    if (isActive) {
      pauseTimer();
    } else {
      resumeTimer();
    }
  };

  const handleSkip = () => {
    switchMode();
  };

  const handleStop = () => {
    Alert.alert(
      'Cancelar Temporizador',
      'Tem certeza que deseja cancelar o temporizador? Seu progresso será salvo, mas a sessão atual será interrompida.',
      [
        {
          text: 'Continuar',
          style: 'cancel',
        },
        {
          text: 'Cancelar Temporizador',
          style: 'destructive',
          onPress: () => stopTimer(),
        },
      ]
    );
  };

  const handleOpenSettings = () => {
    setSettingsVisible(true);
  };

  const handleCloseSettings = () => {
    setSettingsVisible(false);
  };

  const handleApplySettings = () => {
    startTimer(customFocusTime, customBreakTime);
    setSettingsVisible(false);
  };

  const handleIncrementFocus = () => {
    setCustomFocusTime(prev => Math.min(prev + 5, 60));
  };

  const handleDecrementFocus = () => {
    setCustomFocusTime(prev => Math.max(prev - 5, 5));
  };

  const handleIncrementBreak = () => {
    setCustomBreakTime(prev => Math.min(prev + 1, 15));
  };

  const handleDecrementBreak = () => {
    setCustomBreakTime(prev => Math.max(prev - 1, 1));
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={mode === 'focus' ? [colors.primary, colors.primaryDark] : mode === 'break' ? [colors.success, colors.success] : ["#F9FAFB", "#F3F4F6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.timerGradient}
      >
        <View style={styles.timerContainer}>
          <View style={[
            styles.timerCircle,
            {
              borderColor: mode === 'idle' ? getTimerColor(mode) : '#fff',
              backgroundColor: 'rgba(255, 255, 255, 0.2)'
            }
          ]}>
            <Text style={[
              styles.timerText,
              { color: mode === 'idle' ? getTimerColor(mode) : '#fff' }
            ]}>
              {formatTime(timeRemaining)}
            </Text>
          </View>

          <View style={styles.timerHeader}>
            <View style={styles.timerLabelContainer}>
              <Text style={[styles.timerLabel, mode !== 'idle' && { color: '#fff' }]}>
                {getTimerLabel(mode)}
              </Text>
              {mode !== 'idle' && (
                <View style={styles.sessionsBadge}>
                  <Text style={styles.sessionsLabel}>
                    {sessionsCompleted} sessões
                  </Text>
                </View>
              )}
              {mode === 'idle' && (
                <Pressable style={styles.settingsButton} onPress={handleOpenSettings}>
                  <Settings size={24} color={colors.textLight} />
                </Pressable>
              )}
            </View>
          </View>

          <View style={styles.controlsContainer}>
            {mode === 'idle' ? (
              <Button
                title="Iniciar Pomodoro"
                onPress={handleStartTimer}
                variant="primary"
                size="medium"
                icon={Play}
                iconPosition="left"
                fullWidth={true}
                style={styles.startButton}
                rounded={true}
                elevation="medium"
              />
            ) : (
              <>
                <Pressable
                  style={styles.controlButton}
                  onPress={handleToggleTimer}
                >
                  {isActive ? (
                    <Pause size={24} color="#fff" />
                  ) : (
                    <Play size={24} color="#fff" />
                  )}
                </Pressable>

                <Pressable
                  style={styles.controlButton}
                  onPress={handleSkip}
                >
                  <SkipForward size={24} color="#fff" />
                </Pressable>

                <Pressable
                  style={[styles.controlButton, styles.stopButton]}
                  onPress={handleStop}
                >
                  <X size={24} color="#fff" />
                </Pressable>
              </>
            )}
          </View>
        </View>
      </LinearGradient>

      <Modal
        visible={settingsVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCloseSettings}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Configurações</Text>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Tempo de Foco (min)</Text>
              <View style={styles.settingControls}>
                <Pressable style={styles.settingButton} onPress={handleDecrementFocus}>
                  <Text style={styles.settingButtonText}>-</Text>
                </Pressable>
                <Text style={styles.settingValue}>{customFocusTime}</Text>
                <Pressable style={styles.settingButton} onPress={handleIncrementFocus}>
                  <Text style={styles.settingButtonText}>+</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Tempo de Descanso (min)</Text>
              <View style={styles.settingControls}>
                <Pressable style={styles.settingButton} onPress={handleDecrementBreak}>
                  <Text style={styles.settingButtonText}>-</Text>
                </Pressable>
                <Text style={styles.settingValue}>{customBreakTime}</Text>
                <Pressable style={styles.settingButton} onPress={handleIncrementBreak}>
                  <Text style={styles.settingButtonText}>+</Text>
                </Pressable>
              </View>
            </View>

            <View style={styles.modalButtons}>
              <Button
                title="Cancelar"
                onPress={handleCloseSettings}
                variant="outline"
                size="medium"
              />
              <Button
                title="Aplicar"
                onPress={handleApplySettings}
                variant="primary"
                size="medium"
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    overflow: 'hidden',
  },
  timerGradient: {
    borderRadius: 16,
    padding: 20,
  },
  timerContainer: {
    alignItems: 'center',
  },
  timerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 16,
  },
  timerLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  timerLabel: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.text,
  },
  sessionsBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 20,
  },
  sessionsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  timerCircle: {
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timerText: {
    fontSize: 36,
    fontWeight: '700',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  stopButton: {
    backgroundColor: 'rgba(255, 75, 75, 0.8)',
  },
  startButton: {
    width: '100%',
  },
  settingsButton: {
    marginLeft: 16,
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 24,
    width: '80%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 24,
    textAlign: 'center',
  },
  settingItem: {
    marginBottom: 16,
  },
  settingLabel: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 8,
  },
  settingControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
    padding: 8,
  },
  settingButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingButtonText: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.white,
  },
  settingValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 16,
  },
});
