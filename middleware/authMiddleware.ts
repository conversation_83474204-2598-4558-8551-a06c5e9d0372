/**
 * Middleware de autenticação otimizado para verificar se o usuário está autenticado
 * e tem permissão para acessar determinados recursos.
 *
 * Implementa cache de permissões e verificações otimizadas para alta escala (100.000+ usuários)
 * com isolamento de dados por usuário.
 */

import { supabase } from '@/lib/supabase';
import { Alert } from 'react-native';
import { measureFunction } from '@/services/performanceMonitor';
import { useAuthStore } from '@/store/authStore';

// Cache de usuário para evitar chamadas repetidas
let userCache: {
  user: any;
  timestamp: number;
  expiresAt: number;
} | null = null;

// Cache de permissões para recursos
const resourcePermissionCache: Record<string, {
  hasPermission: boolean;
  timestamp: number;
  expiresAt: number;
}> = {};

// Cache de associações a grupos
const groupMembershipCache: Record<string, {
  isMember: boolean;
  isAdmin: boolean;
  isPublic: boolean;
  timestamp: number;
  expiresAt: number;
}> = {};

// Tempo de expiração do cache (5 minutos)
const CACHE_EXPIRATION = 5 * 60 * 1000;

/**
 * Verifica se o usuário está autenticado
 * @param forceRefresh Forçar atualização do cache
 * @returns O usuário autenticado ou null
 */
export const getAuthenticatedUser = async (forceRefresh = false) => {
  try {
    // Verificar se há um usuário em cache e se não expirou
    const now = Date.now();
    if (!forceRefresh && userCache && now < userCache.expiresAt) {
      return userCache.user;
    }

    // Tentar obter o usuário do store primeiro (mais rápido)
    const storeUser = useAuthStore.getState().user;
    if (storeUser && !forceRefresh) {
      // Atualizar cache
      userCache = {
        user: storeUser,
        timestamp: now,
        expiresAt: now + CACHE_EXPIRATION,
      };
      return storeUser;
    }

    // Se não encontrou no store ou forceRefresh=true, buscar da API
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      console.error('Erro ao obter usuário autenticado:', error);
      return null;
    }

    // Atualizar cache
    userCache = {
      user,
      timestamp: now,
      expiresAt: now + CACHE_EXPIRATION,
    };

    return user;
  } catch (error) {
    console.error('Erro ao verificar autenticação:', error);
    return null;
  }
};

/**
 * Verifica se o usuário tem permissão para acessar um recurso específico
 * @param resourceId ID do recurso
 * @param tableName Nome da tabela
 * @param userId ID do usuário
 * @param forceRefresh Forçar atualização do cache
 * @returns true se o usuário tem permissão, false caso contrário
 */
export const checkResourcePermission = async (
  resourceId: string,
  tableName: string,
  userId: string,
  forceRefresh = false
) => {
  try {
    // Gerar chave de cache
    const cacheKey = `${tableName}_${resourceId}_${userId}`;

    // Verificar se a permissão está em cache e não expirou
    const now = Date.now();
    if (!forceRefresh && resourcePermissionCache[cacheKey] && now < resourcePermissionCache[cacheKey].expiresAt) {
      return resourcePermissionCache[cacheKey].hasPermission;
    }

    // Verificar permissão usando RPC para melhor desempenho
    // Esta função usa as políticas RLS otimizadas definidas no banco de dados
    const result = await measureFunction(
      `check_permission_${tableName}`,
      async () => {
        // Tentar usar RPC específica para a tabela, se disponível
        if (tableName === 'study_groups') {
          const { data, error } = await supabase.rpc('check_group_membership', {
            group_id_param: resourceId,
            user_id_param: userId
          });

          if (error) {
            console.error(`Erro ao verificar permissão para ${tableName} via RPC:`, error);
            // Fallback para método padrão
            return checkResourcePermissionDirect(resourceId, tableName, userId);
          }

          return data.is_member || data.is_admin || data.is_public;
        } else {
          // Método padrão para outras tabelas
          return checkResourcePermissionDirect(resourceId, tableName, userId);
        }
      },
      { resourceId, tableName, userId }
    );

    // Armazenar resultado em cache
    resourcePermissionCache[cacheKey] = {
      hasPermission: result,
      timestamp: now,
      expiresAt: now + CACHE_EXPIRATION,
    };

    return result;
  } catch (error) {
    console.error(`Erro ao verificar permissão para ${tableName}:`, error);
    return false;
  }
};

/**
 * Verificação direta de permissão (sem cache)
 * @param resourceId ID do recurso
 * @param tableName Nome da tabela
 * @param userId ID do usuário
 * @returns true se o usuário tem permissão, false caso contrário
 */
const checkResourcePermissionDirect = async (
  resourceId: string,
  tableName: string,
  userId: string
) => {
  try {
    // Verificar se o recurso pertence ao usuário
    const { data, error } = await supabase
      .from(tableName)
      .select('user_id')
      .eq('id', resourceId)
      .single();

    if (error) {
      console.error(`Erro ao verificar permissão para ${tableName}:`, error);
      return false;
    }

    // Verificar se o usuário é o proprietário do recurso
    return data.user_id === userId;
  } catch (error) {
    console.error(`Erro ao verificar permissão direta para ${tableName}:`, error);
    return false;
  }
};

/**
 * Verifica se o usuário é membro de um grupo de estudo
 * @param groupId ID do grupo
 * @param userId ID do usuário
 * @param forceRefresh Forçar atualização do cache
 * @returns Objeto com informações sobre a associação do usuário ao grupo
 */
export const checkGroupMembership = async (
  groupId: string,
  userId: string,
  forceRefresh = false
) => {
  try {
    // Gerar chave de cache
    const cacheKey = `${groupId}_${userId}`;

    // Verificar se a associação está em cache e não expirou
    const now = Date.now();
    if (!forceRefresh && groupMembershipCache[cacheKey] && now < groupMembershipCache[cacheKey].expiresAt) {
      return groupMembershipCache[cacheKey];
    }

    // Usar RPC otimizada para verificar associação ao grupo
    const result = await measureFunction(
      'check_group_membership',
      async () => {
        // Tentar usar RPC para melhor desempenho
        const { data, error } = await supabase.rpc('check_group_membership', {
          group_id_param: groupId,
          user_id_param: userId
        });

        if (error) {
          console.error('Erro ao verificar associação ao grupo via RPC:', error);
          // Fallback para método padrão
          return checkGroupMembershipDirect(groupId, userId);
        }

        return {
          isMember: data.is_member,
          isAdmin: data.is_admin,
          isPublic: data.is_public,
          timestamp: now,
          expiresAt: now + CACHE_EXPIRATION
        };
      },
      { groupId, userId }
    );

    // Armazenar resultado em cache
    groupMembershipCache[cacheKey] = result;

    return result;
  } catch (error) {
    console.error('Erro ao verificar associação ao grupo:', error);
    return { isMember: false, isAdmin: false, isPublic: false, timestamp: Date.now(), expiresAt: 0 };
  }
};

/**
 * Verificação direta de associação ao grupo (sem cache)
 * @param groupId ID do grupo
 * @param userId ID do usuário
 * @returns Objeto com informações sobre a associação do usuário ao grupo
 */
const checkGroupMembershipDirect = async (
  groupId: string,
  userId: string
) => {
  try {
    // Verificar se o grupo existe e é público
    const { data: groupData, error: groupError } = await supabase
      .from('study_groups')
      .select('is_open, admin_id')
      .eq('id', groupId)
      .single();

    if (groupError) {
      console.error('Erro ao verificar grupo:', groupError);
      return {
        isMember: false,
        isAdmin: false,
        isPublic: false,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };
    }

    // Verificar se o usuário é o administrador do grupo
    if (groupData.admin_id === userId) {
      return {
        isMember: true,
        isAdmin: true,
        isPublic: groupData.is_open,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };
    }

    // Verificar se o usuário é membro do grupo
    const { data: memberData, error: memberError } = await supabase
      .from('study_group_members')
      .select('role')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .single();

    if (memberError) {
      // Se o grupo for público, o usuário pode acessar mesmo não sendo membro
      return {
        isMember: false,
        isAdmin: false,
        isPublic: groupData.is_open,
        timestamp: Date.now(),
        expiresAt: Date.now() + CACHE_EXPIRATION
      };
    }

    // Verificar se o usuário é administrador do grupo
    const isAdmin = memberData.role === 'admin';

    return {
      isMember: true,
      isAdmin,
      isPublic: groupData.is_open,
      timestamp: Date.now(),
      expiresAt: Date.now() + CACHE_EXPIRATION
    };
  } catch (error) {
    console.error('Erro ao verificar associação direta ao grupo:', error);
    return {
      isMember: false,
      isAdmin: false,
      isPublic: false,
      timestamp: Date.now(),
      expiresAt: Date.now() + CACHE_EXPIRATION
    };
  }
};

/**
 * Verifica se o usuário tem permissão para acessar um material de grupo
 * @param materialId ID do material
 * @param userId ID do usuário
 * @param forceRefresh Forçar atualização do cache
 * @returns true se o usuário tem permissão, false caso contrário
 */
export const checkGroupMaterialPermission = async (
  materialId: string,
  userId: string,
  forceRefresh = false
) => {
  try {
    // Gerar chave de cache
    const cacheKey = `material_${materialId}_${userId}`;

    // Verificar se a permissão está em cache e não expirou
    const now = Date.now();
    if (!forceRefresh && resourcePermissionCache[cacheKey] && now < resourcePermissionCache[cacheKey].expiresAt) {
      return resourcePermissionCache[cacheKey].hasPermission;
    }

    // Usar RPC otimizada para verificar permissão
    const result = await measureFunction(
      'check_material_permission',
      async () => {
        // Tentar usar RPC para melhor desempenho
        const { data, error } = await supabase.rpc('get_study_group_materials', {
          group_id_param: null,
          material_id_param: materialId
        });

        if (error) {
          console.error('Erro ao verificar permissão para material via RPC:', error);
          // Fallback para método padrão
          return checkGroupMaterialPermissionDirect(materialId, userId);
        }

        // Se a RPC retornou dados, o usuário tem permissão
        return data && data.length > 0;
      },
      { materialId, userId }
    );

    // Armazenar resultado em cache
    resourcePermissionCache[cacheKey] = {
      hasPermission: result,
      timestamp: now,
      expiresAt: now + CACHE_EXPIRATION,
    };

    return result;
  } catch (error) {
    console.error('Erro ao verificar permissão para material:', error);
    return false;
  }
};

/**
 * Verificação direta de permissão para material de grupo (sem cache)
 * @param materialId ID do material
 * @param userId ID do usuário
 * @returns true se o usuário tem permissão, false caso contrário
 */
const checkGroupMaterialPermissionDirect = async (
  materialId: string,
  userId: string
) => {
  try {
    // Obter o material e o grupo associado
    const { data: materialData, error: materialError } = await supabase
      .from('study_group_materials')
      .select('group_id, created_by')
      .eq('id', materialId)
      .single();

    if (materialError) {
      console.error('Erro ao verificar material:', materialError);
      return false;
    }

    // Verificar se o usuário é o criador do material
    if (materialData.created_by === userId) {
      return true;
    }

    // Verificar se o usuário é membro do grupo
    const { isMember, isAdmin } = await checkGroupMembership(
      materialData.group_id,
      userId
    );

    // Administradores têm permissão para todos os materiais do grupo
    if (isAdmin) {
      return true;
    }

    // Membros têm permissão para visualizar materiais do grupo
    return isMember;
  } catch (error) {
    console.error('Erro ao verificar permissão direta para material:', error);
    return false;
  }
};

/**
 * Exibe um alerta de erro de permissão
 * @param message Mensagem personalizada (opcional)
 * @param title Título personalizado (opcional)
 */
export const showPermissionError = (message?: string, title?: string) => {
  Alert.alert(
    title || 'Acesso Negado',
    message || 'Você não tem permissão para acessar este recurso.'
  );
};

/**
 * Limpa o cache de permissões para um recurso específico
 * @param resourceId ID do recurso
 * @param tableName Nome da tabela
 * @param userId ID do usuário
 */
export const clearResourcePermissionCache = (
  resourceId: string,
  tableName: string,
  userId: string
) => {
  const cacheKey = `${tableName}_${resourceId}_${userId}`;
  delete resourcePermissionCache[cacheKey];
};

/**
 * Limpa o cache de associação a um grupo específico
 * @param groupId ID do grupo
 * @param userId ID do usuário
 */
export const clearGroupMembershipCache = (
  groupId: string,
  userId: string
) => {
  const cacheKey = `${groupId}_${userId}`;
  delete groupMembershipCache[cacheKey];
};

/**
 * Limpa o cache de permissão para um material específico
 * @param materialId ID do material
 * @param userId ID do usuário
 */
export const clearMaterialPermissionCache = (
  materialId: string,
  userId: string
) => {
  const cacheKey = `material_${materialId}_${userId}`;
  delete resourcePermissionCache[cacheKey];
};

/**
 * Limpa todo o cache de permissões
 */
export const clearAllPermissionCache = () => {
  // Limpar cache de usuário
  userCache = null;

  // Limpar cache de permissões para recursos
  Object.keys(resourcePermissionCache).forEach(key => {
    delete resourcePermissionCache[key];
  });

  // Limpar cache de associações a grupos
  Object.keys(groupMembershipCache).forEach(key => {
    delete groupMembershipCache[key];
  });

  console.log('Cache de permissões limpo com sucesso');
};

/**
 * Verifica se um usuário tem permissão para acessar um recurso
 * Esta função é uma versão simplificada que combina as verificações de permissão
 * @param userId ID do usuário
 * @param resourceType Tipo de recurso ('subject', 'flashcard_set', 'group', 'material', etc.)
 * @param resourceId ID do recurso
 * @param requiredRole Papel necessário ('owner', 'admin', 'member', 'viewer')
 * @returns true se o usuário tem permissão, false caso contrário
 */
export const hasPermission = async (
  userId: string,
  resourceType: string,
  resourceId: string,
  requiredRole: 'owner' | 'admin' | 'member' | 'viewer' = 'viewer'
): Promise<boolean> => {
  try {
    // Mapear tipo de recurso para tabela
    let tableName: string;
    switch (resourceType) {
      case 'subject':
        tableName = 'subjects';
        break;
      case 'flashcard_set':
        tableName = 'flashcard_sets';
        break;
      case 'flashcard':
        tableName = 'flashcards';
        break;
      case 'quiz':
        tableName = 'quizzes';
        break;
      case 'group':
        tableName = 'study_groups';
        break;
      case 'material':
        tableName = 'study_group_materials';
        break;
      default:
        tableName = resourceType;
    }

    // Verificar permissão com base no tipo de recurso
    if (resourceType === 'group') {
      const { isMember, isAdmin } = await checkGroupMembership(resourceId, userId);

      switch (requiredRole) {
        case 'owner':
        case 'admin':
          return isAdmin;
        case 'member':
          return isMember;
        case 'viewer':
          return isMember || isAdmin;
        default:
          return false;
      }
    } else if (resourceType === 'material') {
      return checkGroupMaterialPermission(resourceId, userId);
    } else {
      // Para outros tipos de recursos, verificar propriedade
      return checkResourcePermission(resourceId, tableName, userId);
    }
  } catch (error) {
    console.error(`Erro ao verificar permissão para ${resourceType}:`, error);
    return false;
  }
};
