{"timestamp": "2025-05-27T14:04:45.624Z", "summary": {"total": 19, "passed": 11, "critical": 0, "high": 2, "medium": 6, "low": 0}, "status": "READY", "results": {"critical": [], "high": [{"test": "Auth Implementation - contexts/AuthContext.tsx", "status": "FAIL", "details": "Sem verificações de autenticação adequadas", "timestamp": "2025-05-27T14:04:45.601Z"}, {"test": "Route Protection", "status": "FAIL", "details": "Apenas 32% das rotas protegidas", "timestamp": "2025-05-27T14:04:45.621Z"}], "medium": [{"test": "Auth Implementation - components/RouteGuard.tsx", "status": "WARN", "details": "Verificações parciais de autenticação", "timestamp": "2025-05-27T14:04:45.601Z"}, {"test": "Env Variables - lib/supabase.ts", "status": "WARN", "details": "Pode não estar usando variáveis de ambiente", "timestamp": "2025-05-27T14:04:45.621Z"}, {"test": "Data Connectivity - store/studyStore.ts", "status": "WARN", "details": "Conectividade não clara", "timestamp": "2025-05-27T14:04:45.623Z"}, {"test": "Data Connectivity - store/quizStore.ts", "status": "WARN", "details": "Conectividade não clara", "timestamp": "2025-05-27T14:04:45.623Z"}, {"test": "Data Connectivity - store/noteStore.ts", "status": "WARN", "details": "Conectividade não clara", "timestamp": "2025-05-27T14:04:45.623Z"}, {"test": "Data Connectivity - store/timerStore.ts", "status": "WARN", "details": "Conectividade não clara", "timestamp": "2025-05-27T14:04:45.623Z"}], "low": [], "passed": [{"test": "Auth Implementation - hooks/useSupabase.ts", "status": "PASS", "details": "Verificações de autenticação presentes", "timestamp": "2025-05-27T14:04:45.594Z"}, {"test": "Auth Implementation - hooks/useSupabaseOptimized.ts", "status": "PASS", "details": "Verificações de autenticação presentes", "timestamp": "2025-05-27T14:04:45.600Z"}, {"test": "Security Config - app.config.js", "status": "PASS", "details": "Sem chaves de serviço expostas", "timestamp": "2025-05-27T14:04:45.621Z"}, {"test": "Env Variables - app.config.js", "status": "PASS", "details": "Usa variáveis de ambiente", "timestamp": "2025-05-27T14:04:45.621Z"}, {"test": "Security Config - config/env.ts", "status": "PASS", "details": "Sem chaves de serviço expostas", "timestamp": "2025-05-27T14:04:45.621Z"}, {"test": "Env Variables - config/env.ts", "status": "PASS", "details": "Usa variáveis de ambiente", "timestamp": "2025-05-27T14:04:45.621Z"}, {"test": "Security Config - lib/supabase.ts", "status": "PASS", "details": "Sem chaves de serviço expostas", "timestamp": "2025-05-27T14:04:45.621Z"}, {"test": "CRUD Operations - store/studyStore.ts", "status": "PASS", "details": "4/4 operações CRUD implementadas", "timestamp": "2025-05-27T14:04:45.623Z"}, {"test": "CRUD Operations - store/quizStore.ts", "status": "PASS", "details": "4/4 operações CRUD implementadas", "timestamp": "2025-05-27T14:04:45.623Z"}, {"test": "CRUD Operations - store/noteStore.ts", "status": "PASS", "details": "4/4 operações CRUD implementadas", "timestamp": "2025-05-27T14:04:45.623Z"}, {"test": "CRUD Operations - store/timerStore.ts", "status": "PASS", "details": "3/4 operações CRUD implementadas", "timestamp": "2025-05-27T14:04:45.623Z"}]}, "recommendations": ["Adicionar verificações de autenticação em todas as rotas", "Implementar logging de segurança", "Revisar configurações de CORS e headers de segurança", "Implementar rate limiting", "Adicionar validação de entrada mais rigorosa", "Implementar monitoramento de anomalias"]}