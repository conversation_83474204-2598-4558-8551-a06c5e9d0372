import Constants from 'expo-constants';

// Obter variáveis de ambiente do app.json ou do processo de build
const getEnvVars = () => {
  // Valores padrão para desenvolvimento local
  const defaultEnvVars = {
    SUPABASE_URL: 'https://wyjpmzfijtufgxgdivgl.supabase.co',
    SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5anBtemZpanR1Zmd4Z2RpdmdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NDY3MTcsImV4cCI6MjA2MDIyMjcxN30.wYZyPO_3q6i9EnSRm2QYeZOMVH0X-pUuBj2pQ7lzmq4',
    API_URL: 'https://api.liaapp.com',
    ENVIRONMENT: 'development',
  };

  // Em produção, usar variáveis de ambiente do processo de build
  if (process.env.NODE_ENV === 'production') {
    return {
      SUPABASE_URL: process.env.SUPABASE_URL || defaultEnvVars.SUPABASE_URL,
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || defaultEnvVars.SUPABASE_ANON_KEY,
      API_URL: process.env.API_URL || defaultEnvVars.API_URL,
      ENVIRONMENT: 'production',
    };
  }

  // Em desenvolvimento, usar variáveis do app.json ou .env via Expo Constants
  return {
    SUPABASE_URL: Constants.expoConfig?.extra?.supabaseUrl || defaultEnvVars.SUPABASE_URL,
    SUPABASE_ANON_KEY: Constants.expoConfig?.extra?.supabaseAnonKey || defaultEnvVars.SUPABASE_ANON_KEY,
    API_URL: Constants.expoConfig?.extra?.apiUrl || defaultEnvVars.API_URL,
    ENVIRONMENT: Constants.expoConfig?.extra?.environment || defaultEnvVars.ENVIRONMENT,
  };
};

// Exportar variáveis de ambiente
export const ENV = getEnvVars();

// Validar variáveis de ambiente
const validateEnv = () => {
  const requiredVars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY'];
  const missingVars = requiredVars.filter(varName => !ENV[varName as keyof typeof ENV]);
  
  if (missingVars.length > 0) {
    console.warn(`Variáveis de ambiente ausentes: ${missingVars.join(', ')}`);
  }
};

// Executar validação
validateEnv();

export default ENV;
