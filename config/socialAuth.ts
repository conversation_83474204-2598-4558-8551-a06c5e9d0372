/**
 * Configurações para autenticação social
 * 
 * IMPORTANTE: Substitua os valores abaixo pelas suas credenciais reais
 * obtidas no Google Cloud Console e Apple Developer Console
 */

export const GOOGLE_CONFIG = {
  // Web Client ID do Google Cloud Console
  // Obtenha em: https://console.cloud.google.com/apis/credentials
  webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID || '**********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
  
  // iOS Client ID do Google Cloud Console (opcional, pode usar o mesmo webClientId)
  iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID || '**********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
  
  // Android Client ID do Google Cloud Console (opcional, pode usar o mesmo webClientId)
  androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID || '**********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
};

export const APPLE_CONFIG = {
  // Service ID do Apple Developer Console
  // Obtenha em: https://developer.apple.com/account/resources/identifiers/list/serviceId
  serviceId: process.env.EXPO_PUBLIC_APPLE_SERVICE_ID || 'com.liastudyapp.signin',
  
  // Redirect URI configurado no Apple Developer Console
  redirectUri: process.env.EXPO_PUBLIC_APPLE_REDIRECT_URI || 'https://your-project.supabase.co/auth/v1/callback',
};

/**
 * URLs de redirecionamento para OAuth
 */
export const REDIRECT_URLS = {
  // URL para redirecionamento após login bem-sucedido
  success: 'com.liastudyapp://login-callback/',
  
  // URL para redirecionamento após erro
  error: 'com.liastudyapp://login-error/',
  
  // URL base do app
  base: 'com.liastudyapp://',
};

/**
 * Configurações específicas do Supabase
 */
export const SUPABASE_AUTH_CONFIG = {
  // Configurações para Google OAuth
  google: {
    queryParams: {
      access_type: 'offline',
      prompt: 'consent',
    },
  },
  
  // Configurações para Apple OAuth
  apple: {
    queryParams: {
      response_mode: 'form_post',
    },
  },
};

/**
 * Escopos solicitados para cada provedor
 */
export const AUTH_SCOPES = {
  google: [
    'openid',
    'profile',
    'email',
  ],
  
  apple: [
    'name',
    'email',
  ],
};

/**
 * Mensagens de erro personalizadas
 */
export const AUTH_ERROR_MESSAGES = {
  google: {
    cancelled: 'Login cancelado pelo usuário',
    inProgress: 'Login já em andamento',
    playServicesNotAvailable: 'Google Play Services não disponível',
    networkError: 'Erro de conexão. Verifique sua internet.',
    invalidCredentials: 'Credenciais inválidas',
    unknown: 'Erro desconhecido no login com Google',
  },
  
  apple: {
    cancelled: 'Login cancelado pelo usuário',
    failed: 'Falha no login com Apple',
    invalidResponse: 'Resposta inválida da Apple',
    notHandled: 'Login com Apple não foi processado',
    notAvailable: 'Apple Sign-In não está disponível neste dispositivo',
    unknown: 'Erro desconhecido no login com Apple',
  },
  
  general: {
    networkError: 'Erro de conexão. Verifique sua internet.',
    serverError: 'Erro no servidor. Tente novamente mais tarde.',
    invalidToken: 'Token inválido. Faça login novamente.',
    sessionExpired: 'Sessão expirada. Faça login novamente.',
  },
};

/**
 * Configurações de timeout
 */
export const AUTH_TIMEOUTS = {
  // Timeout para operações de login (em milissegundos)
  login: 30000, // 30 segundos
  
  // Timeout para verificação de token
  tokenVerification: 10000, // 10 segundos
  
  // Timeout para logout
  logout: 15000, // 15 segundos
};

/**
 * Configurações de retry
 */
export const AUTH_RETRY_CONFIG = {
  // Número máximo de tentativas
  maxRetries: 3,
  
  // Delay entre tentativas (em milissegundos)
  retryDelay: 1000,
  
  // Multiplicador para backoff exponencial
  backoffMultiplier: 2,
};

/**
 * Validação de configuração
 */
export const validateAuthConfig = () => {
  const errors: string[] = [];
  
  // Validar configuração do Google
  if (!GOOGLE_CONFIG.webClientId || GOOGLE_CONFIG.webClientId.includes('**********')) {
    errors.push('Google Web Client ID não configurado corretamente');
  }
  
  // Validar configuração do Apple
  if (!APPLE_CONFIG.serviceId || APPLE_CONFIG.serviceId.includes('com.liastudyapp.signin')) {
    errors.push('Apple Service ID não configurado corretamente');
  }
  
  if (errors.length > 0) {
    console.warn('⚠️ Configuração de autenticação social incompleta:', errors);
    return false;
  }
  
  return true;
};

/**
 * Função para obter configuração baseada no ambiente
 */
export const getAuthConfig = () => {
  const isDevelopment = __DEV__;
  
  return {
    google: {
      ...GOOGLE_CONFIG,
      // Em desenvolvimento, pode usar configurações de teste
      ...(isDevelopment && {
        // Configurações específicas para desenvolvimento
      }),
    },
    apple: {
      ...APPLE_CONFIG,
      // Em desenvolvimento, pode usar configurações de teste
      ...(isDevelopment && {
        // Configurações específicas para desenvolvimento
      }),
    },
    redirectUrls: REDIRECT_URLS,
    scopes: AUTH_SCOPES,
    timeouts: AUTH_TIMEOUTS,
    retryConfig: AUTH_RETRY_CONFIG,
  };
};
