# Instruções para Criar e Enviar o Projeto para o GitHub

Siga estas instruções para criar um repositório no GitHub e enviar o projeto Lia App para ele.

## 1. Criar um Repositório no GitHub

1. Acesse [GitHub](https://github.com) e faça login na sua conta
2. Clique no botão "+" no canto superior direito e selecione "New repository"
3. Dê o nome "lia-study-app" ao repositório
4. Adicione a descrição "Aplicativo de estudo inteligente com IA"
5. Deixe o repositório como público
6. Não inicialize o repositório com README, .gitignore ou licença (já temos esses arquivos)
7. Clique em "Create repository"

## 2. Configurar Autenticação

### Opção 1: Usar HTTPS com Token de Acesso Pessoal

1. No GitHub, clique na sua foto de perfil no canto superior direito
2. <PERSON><PERSON> em "Settings" > "Developer settings" > "Personal access tokens" > "Tokens (classic)"
3. Clique em "Generate new token" e selecione "Generate new token (classic)"
4. Dê um nome ao token, como "Lia App"
5. Selecione o escopo "repo" para acesso completo ao repositório
6. Clique em "Generate token"
7. Copie o token gerado (você não poderá vê-lo novamente)

### Opção 2: Usar SSH

1. Gere uma chave SSH se ainda não tiver uma:
   ```bash
   ssh-keygen -t ed25519 -C "<EMAIL>"
   ```
2. Adicione a chave SSH ao ssh-agent:
   ```bash
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_ed25519
   ```
3. Copie a chave pública:
   ```bash
   cat ~/.ssh/id_ed25519.pub
   ```
4. No GitHub, vá para "Settings" > "SSH and GPG keys" > "New SSH key"
5. Cole a chave pública e dê um título, como "Meu Computador"
6. Clique em "Add SSH key"

## 3. Enviar o Projeto para o GitHub

### Se estiver usando HTTPS com Token:

```bash
# Remover o remote atual
git remote remove origin

# Adicionar o novo remote com o token
git remote add origin https://[SEU-USERNAME]:[SEU-TOKEN]@github.com/[SEU-USERNAME]/lia-study-app.git

# Enviar o código para o GitHub
git push -u origin main
```

### Se estiver usando SSH:

```bash
# Remover o remote atual
git remote remove origin

# Adicionar o novo remote com SSH
git remote <NAME_EMAIL>:[SEU-USERNAME]/lia-study-app.git

# Enviar o código para o GitHub
git push -u origin main
```

## 4. Verificar o Repositório

Após o push, acesse `https://github.com/[SEU-USERNAME]/lia-study-app` para verificar se o código foi enviado corretamente.

## Observações

- Substitua `[SEU-USERNAME]` pelo seu nome de usuário do GitHub
- Substitua `[SEU-TOKEN]` pelo token de acesso pessoal que você gerou
- Se você estiver usando o GitHub Desktop ou outro cliente Git, pode ser mais fácil adicionar o repositório através da interface gráfica
