import { MindMap } from '@/types';
import { colors } from '@/constants/colors';

// Exemplo de mapa mental para teste
export const sampleMindMap: MindMap = {
  id: 'sample_mindmap',
  title: 'Programação Orientada a Objetos',
  subject: 'Ciência da Computação',
  description: 'Conceitos fundamentais de Programação Orientada a Objetos',
  lastEdited: new Date().toISOString(),
  type: 'concept',
  color: colors.primary,
  nodes: [
    {
      id: 'node_1',
      text: 'Programação Orientada a Objetos',
      x: 400,
      y: 100,
      color: '#333333',
      shape: 'rectangle',
      style: 'filled',
      size: 'large',
    },
    {
      id: 'node_2',
      text: 'Classes',
      x: 200,
      y: 250,
      color: colors.primary,
      shape: 'rectangle',
      style: 'outline',
      size: 'medium',
    },
    {
      id: 'node_3',
      text: 'Objetos',
      x: 400,
      y: 250,
      color: colors.primary,
      shape: 'rectangle',
      style: 'outline',
      size: 'medium',
    },
    {
      id: 'node_4',
      text: '<PERSON><PERSON><PERSON>',
      x: 600,
      y: 250,
      color: colors.primary,
      shape: 'rectangle',
      style: 'outline',
      size: 'medium',
    },
    {
      id: 'node_5',
      text: 'Atributos',
      x: 100,
      y: 400,
      color: colors.accent1,
      shape: 'rectangle',
      style: 'outline',
      size: 'small',
    },
    {
      id: 'node_6',
      text: 'Métodos',
      x: 300,
      y: 400,
      color: colors.accent1,
      shape: 'rectangle',
      style: 'outline',
      size: 'small',
    },
    {
      id: 'node_7',
      text: 'Instâncias',
      x: 400,
      y: 400,
      color: colors.accent1,
      shape: 'rectangle',
      style: 'outline',
      size: 'small',
    },
    {
      id: 'node_8',
      text: 'Polimorfismo',
      x: 500,
      y: 400,
      color: colors.accent1,
      shape: 'rectangle',
      style: 'outline',
      size: 'small',
    },
    {
      id: 'node_9',
      text: 'Encapsulamento',
      x: 700,
      y: 400,
      color: colors.accent1,
      shape: 'rectangle',
      style: 'outline',
      size: 'small',
    },
  ],
  connections: [
    {
      id: 'conn_1',
      source: 'node_1',
      target: 'node_2',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_2',
      source: 'node_1',
      target: 'node_3',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_3',
      source: 'node_1',
      target: 'node_4',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_4',
      source: 'node_2',
      target: 'node_5',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_5',
      source: 'node_2',
      target: 'node_6',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_6',
      source: 'node_3',
      target: 'node_7',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_7',
      source: 'node_4',
      target: 'node_8',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
    {
      id: 'conn_8',
      source: 'node_4',
      target: 'node_9',
      color: colors.text,
      width: 2,
      style: 'solid',
    },
  ],
};
