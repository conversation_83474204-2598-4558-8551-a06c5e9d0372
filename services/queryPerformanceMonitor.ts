/**
 * Serviço para monitorar o desempenho das consultas ao banco de dados
 * 
 * Este serviço registra métricas de desempenho para consultas ao banco de dados
 * e fornece ferramentas para analisar e otimizar o desempenho.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { supabase } from '@/lib/supabase';
import { measureFunction } from '@/services/performanceMonitor';

// Constantes
const PERFORMANCE_METRICS_KEY = 'query_performance_metrics';
const MAX_METRICS_COUNT = 1000; // Número máximo de métricas a serem armazenadas
const SLOW_QUERY_THRESHOLD = 500; // Tempo em ms para considerar uma consulta lenta
const METRICS_RETENTION_DAYS = 7; // Número de dias para reter métricas

// Tipos
interface QueryMetric {
  id: string;
  operation: string;
  tableName: string;
  timestamp: number;
  duration: number;
  success: boolean;
  error?: string;
  params?: Record<string, any>;
  fromCache?: boolean;
  userAgent?: string;
  platform?: string;
  appVersion?: string;
}

// Classe para monitorar o desempenho das consultas
export class QueryPerformanceMonitor {
  private static instance: QueryPerformanceMonitor;
  private metrics: QueryMetric[] = [];
  private isInitialized = false;

  // Construtor privado para implementar o padrão Singleton
  private constructor() {}

  // Método para obter a instância única
  public static getInstance(): QueryPerformanceMonitor {
    if (!QueryPerformanceMonitor.instance) {
      QueryPerformanceMonitor.instance = new QueryPerformanceMonitor();
    }
    return QueryPerformanceMonitor.instance;
  }

  // Inicializar o monitor
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Carregar métricas do armazenamento local
      const metricsJson = await AsyncStorage.getItem(PERFORMANCE_METRICS_KEY);
      if (metricsJson) {
        this.metrics = JSON.parse(metricsJson);
      }

      // Limpar métricas antigas
      this.cleanupOldMetrics();

      this.isInitialized = true;
      console.log(`Monitor de desempenho de consultas inicializado. ${this.metrics.length} métricas carregadas.`);
    } catch (error) {
      console.error('Erro ao inicializar o monitor de desempenho de consultas:', error);
    }
  }

  // Registrar uma métrica de consulta
  public async recordQueryMetric(metric: Omit<QueryMetric, 'id' | 'timestamp' | 'platform' | 'userAgent' | 'appVersion'>): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const fullMetric: QueryMetric = {
      ...metric,
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      timestamp: Date.now(),
      platform: Platform.OS,
      userAgent: Platform.OS === 'web' ? navigator.userAgent : `${Platform.OS} ${Platform.Version}`,
      appVersion: '1.0.0', // Substituir pela versão real do aplicativo
    };

    // Adicionar a métrica à lista
    this.metrics.push(fullMetric);

    // Limitar o número de métricas
    if (this.metrics.length > MAX_METRICS_COUNT) {
      this.metrics = this.metrics.slice(-MAX_METRICS_COUNT);
    }

    // Salvar métricas no armazenamento local
    try {
      await AsyncStorage.setItem(PERFORMANCE_METRICS_KEY, JSON.stringify(this.metrics));
    } catch (error) {
      console.error('Erro ao salvar métricas de desempenho:', error);
    }

    // Registrar consultas lentas no console
    if (fullMetric.duration > SLOW_QUERY_THRESHOLD) {
      console.warn(`Consulta lenta detectada: ${fullMetric.operation} (${fullMetric.duration}ms)`, fullMetric);
    }
  }

  // Limpar métricas antigas
  private cleanupOldMetrics(): void {
    const now = Date.now();
    const cutoffTime = now - (METRICS_RETENTION_DAYS * 24 * 60 * 60 * 1000);
    this.metrics = this.metrics.filter(metric => metric.timestamp >= cutoffTime);
  }

  // Obter métricas
  public getMetrics(): QueryMetric[] {
    return [...this.metrics];
  }

  // Obter métricas para uma tabela específica
  public getMetricsForTable(tableName: string): QueryMetric[] {
    return this.metrics.filter(metric => metric.tableName === tableName);
  }

  // Obter métricas para uma operação específica
  public getMetricsForOperation(operation: string): QueryMetric[] {
    return this.metrics.filter(metric => metric.operation === operation);
  }

  // Obter métricas lentas
  public getSlowQueries(threshold: number = SLOW_QUERY_THRESHOLD): QueryMetric[] {
    return this.metrics.filter(metric => metric.duration > threshold);
  }

  // Obter tempo médio de consulta para uma tabela
  public getAverageQueryTimeForTable(tableName: string): number {
    const tableMetrics = this.getMetricsForTable(tableName);
    if (tableMetrics.length === 0) return 0;

    const totalDuration = tableMetrics.reduce((sum, metric) => sum + metric.duration, 0);
    return totalDuration / tableMetrics.length;
  }

  // Obter tempo médio de consulta para uma operação
  public getAverageQueryTimeForOperation(operation: string): number {
    const operationMetrics = this.getMetricsForOperation(operation);
    if (operationMetrics.length === 0) return 0;

    const totalDuration = operationMetrics.reduce((sum, metric) => sum + metric.duration, 0);
    return totalDuration / operationMetrics.length;
  }

  // Limpar todas as métricas
  public async clearMetrics(): Promise<void> {
    this.metrics = [];
    await AsyncStorage.removeItem(PERFORMANCE_METRICS_KEY);
  }

  // Enviar métricas para o servidor
  public async uploadMetrics(): Promise<void> {
    if (this.metrics.length === 0) return;

    try {
      const { error } = await supabase
        .from('performance_metrics')
        .insert(this.metrics);

      if (error) {
        console.error('Erro ao enviar métricas para o servidor:', error);
        return;
      }

      console.log(`${this.metrics.length} métricas enviadas para o servidor.`);
      await this.clearMetrics();
    } catch (error) {
      console.error('Erro ao enviar métricas para o servidor:', error);
    }
  }
}

// Instância única do monitor
export const queryPerformanceMonitor = QueryPerformanceMonitor.getInstance();

// Função para medir o desempenho de uma consulta
export async function measureQuery<T>(
  operation: string,
  tableName: string,
  queryFn: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  let success = true;
  let error: Error | null = null;
  let fromCache = false;

  const result = await measureFunction(
    operation,
    async () => {
      try {
        const result = await queryFn();
        
        // Verificar se o resultado veio do cache
        if (result && typeof result === 'object' && 'fromCache' in result) {
          fromCache = result.fromCache;
        }
        
        return result;
      } catch (err) {
        success = false;
        error = err instanceof Error ? err : new Error(String(err));
        throw err;
      }
    },
    { tableName, ...params }
  );

  // Registrar métrica
  queryPerformanceMonitor.recordQueryMetric({
    operation,
    tableName,
    duration: result.duration,
    success,
    error: error?.message,
    params,
    fromCache,
  });

  return result.result;
}

// Inicializar o monitor
queryPerformanceMonitor.initialize().catch(console.error);
