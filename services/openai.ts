import OpenAI from 'openai';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { OPENAI_API_KEY } from '@/config/apiKeys';

let openaiClient: OpenAI | null = null;

export const initializeOpenAI = async () => {
  try {
    // First try to get the key from AsyncStorage
    let apiKey = await AsyncStorage.getItem('openai_api_key');

    // If not found, use the pre-configured key
    if (!apiKey && OPENAI_API_KEY) {
      apiKey = OPENAI_API_KEY;
      // Save it to AsyncStorage for future use
      await AsyncStorage.setItem('openai_api_key', apiKey);
    }

    if (apiKey) {
      openaiClient = new OpenAI({
        apiKey,
        dangerouslyAllowBrowser: true // Required for client-side usage
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error initializing OpenAI client:', error);
    return false;
  }
};

export const setOpenAIApiKey = async (apiKey: string) => {
  try {
    await AsyncStorage.setItem('openai_api_key', apiKey);
    openaiClient = new OpenAI({
      apiKey,
      dangerouslyAllowBrowser: true
    });
    return true;
  } catch (error) {
    console.error('Error setting OpenAI API key:', error);
    return false;
  }
};

export const getOpenAIApiKey = async () => {
  try {
    return await AsyncStorage.getItem('openai_api_key');
  } catch (error) {
    console.error('Error getting OpenAI API key:', error);
    return null;
  }
};

export const hasApiKey = async () => {
  const key = await getOpenAIApiKey();
  return !!key;
};

export const generateChatCompletion = async (messages: any[], options = {}) => {
  if (!openaiClient) {
    const initialized = await initializeOpenAI();
    if (!initialized) {
      throw new Error('OpenAI client not initialized. Please set your API key.');
    }
  }

  try {
    const response = await openaiClient!.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages,
      temperature: 0.7,
      max_tokens: 1000,
      ...options
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating chat completion:', error);
    throw error;
  }
};

export const generateFlashcards = async (topic: string, count: number = 5) => {
  const prompt = `Generate ${count} flashcards about "${topic}".
  Each flashcard should have a question and an answer.
  Format the response as a JSON array of objects with "question" and "answer" properties.
  Make the questions challenging but concise, and the answers comprehensive but clear.`;

  const messages = [
    { role: 'system', content: 'You are a helpful educational assistant that creates high-quality flashcards for studying.' },
    { role: 'user', content: prompt }
  ];

  try {
    const response = await generateChatCompletion(messages);
    // Parse the JSON response
    const jsonMatch = response?.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to parse flashcards from response');
  } catch (error) {
    console.error('Error generating flashcards:', error);
    throw error;
  }
};

export const generateMindMap = async (topic: string, nodeCount: number = 7) => {
  const prompt = `Create a mind map about "${topic}" with approximately ${nodeCount} nodes.
  Format the response as a JSON object with two arrays:
  1. "nodes": Array of objects with "id", "text", and "level" properties (level 0 is the central node)
  2. "connections": Array of objects with "source" (node id) and "target" (node id) properties
  Make the mind map educational, concise, and well-structured.`;

  const messages = [
    { role: 'system', content: 'You are a helpful educational assistant that creates well-structured mind maps for studying.' },
    { role: 'user', content: prompt }
  ];

  try {
    const response = await generateChatCompletion(messages);
    // Parse the JSON response
    const jsonMatch = response?.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to parse mind map from response');
  } catch (error) {
    console.error('Error generating mind map:', error);
    throw error;
  }
};

export const expandMindMap = async (currentNodes: string[], topic: string, newNodesCount: number = 3) => {
  const prompt = `I have a mind map about "${topic}" with the following nodes: ${currentNodes.join(', ')}.
  Please suggest ${newNodesCount} additional nodes that would enhance this mind map.
  Format the response as a JSON array of objects with "text" and "connections" properties.
  The "connections" property should be an array of existing node texts that this new node should connect to.`;

  const messages = [
    { role: 'system', content: 'You are a helpful educational assistant that expands mind maps with relevant and connected concepts.' },
    { role: 'user', content: prompt }
  ];

  try {
    const response = await generateChatCompletion(messages);
    // Parse the JSON response
    const jsonMatch = response?.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to parse mind map expansion from response');
  } catch (error) {
    console.error('Error expanding mind map:', error);
    throw error;
  }
};

export const generateStudyConversation = async (flashcard: { question: string, answer: string }) => {
  const prompt = `Based on this flashcard:
  Question: "${flashcard.question}"
  Answer: "${flashcard.answer}"

  Generate a short educational conversation between a student and a tutor that explores this concept in more depth.
  The conversation should include follow-up questions, clarifications, and additional context.
  Format the response as a JSON array of objects with "role" (either "student" or "tutor") and "content" properties.
  Keep the conversation to 4-6 exchanges total.`;

  const messages = [
    { role: 'system', content: 'You are a helpful educational assistant that creates realistic and educational study conversations.' },
    { role: 'user', content: prompt }
  ];

  try {
    const response = await generateChatCompletion(messages);
    // Parse the JSON response
    const jsonMatch = response?.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to parse study conversation from response');
  } catch (error) {
    console.error('Error generating study conversation:', error);
    throw error;
  }
};

export const generateNote = async (subject: string, complexity: string = 'medium') => {
  const prompt = `Create a comprehensive study note about "${subject}".
  The note should be educational, well-structured, and at a ${complexity} complexity level.
  Include key concepts, explanations, and examples where appropriate.
  Format the response as a well-structured text with sections and bullet points where appropriate.
  The note should be comprehensive but concise, focusing on the most important aspects of the topic.`;

  const messages = [
    { role: 'system', content: 'You are a helpful educational assistant that creates high-quality study notes for students.' },
    { role: 'user', content: prompt }
  ];

  try {
    const response = await generateChatCompletion(messages, { max_tokens: 2000 });
    return response || '';
  } catch (error) {
    console.error('Error generating note:', error);
    throw error;
  }
};
