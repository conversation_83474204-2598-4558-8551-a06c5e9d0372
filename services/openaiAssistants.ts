import OpenAI from 'openai';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
// Não podemos usar hooks diretamente em funções assíncronas
import { OPENAI_API_KEY } from '@/config/apiKeys';

// Timeout para requisições à API da OpenAI (otimizado para melhor desempenho)
const API_TIMEOUT = 8000; // Reduzido para 8 segundos

// Função auxiliar para criar uma promessa com timeout
const withTimeout = (promise, timeoutMs, errorMessage) => {
  let timeoutId;
  const timeoutPromise = new Promise((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(errorMessage || `Operação excedeu o tempo limite de ${timeoutMs}ms`));
    }, timeoutMs);
  });

  return Promise.race([promise, timeoutPromise])
    .finally(() => clearTimeout(timeoutId));
};

let openaiClient: OpenAI | null = null;
const ASSISTANT_ID = 'asst_XhJJPU4A7l0neFMxnZImtvMk';

export const initializeOpenAIAssistants = async () => {
  try {
    // First try to get the key from AsyncStorage
    let apiKey = await AsyncStorage.getItem('openai_api_key');

    // If not found, use the pre-configured key
    if (!apiKey && OPENAI_API_KEY) {
      apiKey = OPENAI_API_KEY;
      // Save it to AsyncStorage for future use
      await AsyncStorage.setItem('openai_api_key', apiKey);
    }

    if (apiKey) {
      openaiClient = new OpenAI({
        apiKey,
        dangerouslyAllowBrowser: true // Required for client-side usage
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error initializing OpenAI client:', error);
    return false;
  }
};

export const hasApiKey = async () => {
  const key = await AsyncStorage.getItem('openai_api_key');
  return !!key;
};

// Create a new conversation in Supabase and return the conversation ID
export const createConversation = async (title: string = 'Nova Conversa') => {
  try {
    // Obter o usuário atual diretamente do Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    // Create a thread in OpenAI
    if (!openaiClient) {
      const initialized = await initializeOpenAIAssistants();
      if (!initialized) {
        throw new Error('OpenAI client not initialized');
      }
    }

    const thread = await openaiClient!.beta.threads.create();

    // Create a conversation in Supabase
    const { data, error } = await supabase
      .from('ai_conversations')
      .insert({
        user_id: user.id,
        title,
        thread_id: thread.id,
        assistant_id: ASSISTANT_ID,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

// Get all conversations for the current user
export const getConversations = async () => {
  try {
    // Obter o usuário atual diretamente do Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('ai_conversations')
      .select(`
        id,
        title,
        created_at,
        updated_at,
        thread_id,
        assistant_id,
        ai_messages (
          id,
          role,
          content,
          created_at,
          message_id
        )
      `)
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data.map(conversation => ({
      ...conversation,
      messageCount: conversation.ai_messages?.length || 0,
      lastUpdated: conversation.updated_at,
    }));
  } catch (error) {
    console.error('Error getting conversations:', error);
    throw error;
  }
};

// Get a specific conversation by ID
export const getConversation = async (conversationId: string) => {
  try {
    const { data, error } = await supabase
      .from('ai_conversations')
      .select(`
        id,
        title,
        created_at,
        updated_at,
        thread_id,
        assistant_id,
        ai_messages (
          id,
          role,
          content,
          created_at,
          message_id
        )
      `)
      .eq('id', conversationId)
      .single();

    if (error) {
      throw error;
    }

    return {
      ...data,
      messages: data.ai_messages || [],
    };
  } catch (error) {
    console.error('Error getting conversation:', error);
    throw error;
  }
};

// Delete a conversation
export const deleteConversation = async (conversationId: string) => {
  try {
    console.log(`Iniciando exclusão da conversa ${conversationId}`);

    // Get the thread ID first
    const { data: conversation, error: getError } = await supabase
      .from('ai_conversations')
      .select('thread_id')
      .eq('id', conversationId)
      .single();

    if (getError) {
      throw getError;
    }

    // Primeiro, excluir todas as mensagens associadas à conversa
    console.log(`Excluindo mensagens da conversa ${conversationId}`);
    const { error: messagesError } = await supabase
      .from('ai_messages')
      .delete()
      .eq('conversation_id', conversationId);

    if (messagesError) {
      console.error('Erro ao excluir mensagens:', messagesError);
      // Continuar mesmo com erro para tentar excluir a conversa
    } else {
      console.log(`Mensagens da conversa ${conversationId} excluídas com sucesso`);
    }

    // Delete the thread from OpenAI if it exists
    if (conversation?.thread_id && openaiClient) {
      try {
        console.log(`Excluindo thread ${conversation.thread_id} da OpenAI`);
        await openaiClient.beta.threads.del(conversation.thread_id);
        console.log(`Thread ${conversation.thread_id} excluído com sucesso da OpenAI`);
      } catch (openaiError) {
        console.error('Error deleting thread from OpenAI:', openaiError);
        // Continue with Supabase deletion even if OpenAI deletion fails
      }
    }

    // Delete the conversation from Supabase
    console.log(`Excluindo conversa ${conversationId} do Supabase`);
    const { error } = await supabase
      .from('ai_conversations')
      .delete()
      .eq('id', conversationId);

    if (error) {
      throw error;
    }

    console.log(`Conversa ${conversationId} excluída com sucesso`);
    return true;
  } catch (error) {
    console.error('Error deleting conversation:', error);
    throw error;
  }
};

// Mapa para rastrear threads ativos
const activeThreads = new Map();

// Função para verificar se um thread está ativo
const isThreadActive = (threadId: string) => {
  return activeThreads.has(threadId);
};

// Função para marcar um thread como ativo ou inativo
const setThreadActive = (threadId: string, active: boolean) => {
  if (active) {
    activeThreads.set(threadId, true);
    // Não usamos mais timeout de segurança para evitar liberação prematura
    // A liberação é feita explicitamente após o processamento
  } else {
    activeThreads.delete(threadId);
  }
};

// Função para limpar todos os threads ativos
// Útil para resetar o estado em caso de erros
const clearActiveThreads = () => {
  activeThreads.clear();
  console.log('Todos os threads ativos foram liberados');
};

// Exportar a função para uso externo
export const resetAssistantThreads = clearActiveThreads;

// Função para cancelar explicitamente todos os runs ativos
export const cancelAllActiveRuns = async () => {
  try {
    console.log('Tentando cancelar todos os runs ativos...');

    // Obter todos os threads ativos
    const activeThreadIds = Array.from(activeThreads.keys());

    if (activeThreadIds.length === 0) {
      console.log('Nenhum thread ativo para cancelar');
      return;
    }

    console.log(`Cancelando ${activeThreadIds.length} threads ativos`);

    // Para cada thread ativo, tentar cancelar qualquer run ativo
    for (const threadId of activeThreadIds) {
      try {
        if (!openaiClient) {
          await initializeOpenAIAssistants();
          if (!openaiClient) continue;
        }

        // Listar runs para este thread
        const runs = await withTimeout(
          openaiClient.beta.threads.runs.list(threadId),
          API_TIMEOUT,
          'Tempo limite excedido ao listar runs'
        );

        // Pegar o run mais recente
        const activeRun = runs.data[0];

        if (activeRun && ['queued', 'in_progress'].includes(activeRun.status)) {
          // Cancelar o run
          await withTimeout(
            openaiClient.beta.threads.runs.cancel(threadId, activeRun.id),
            API_TIMEOUT,
            'Tempo limite excedido ao cancelar run'
          );
          console.log(`Run ${activeRun.id} cancelado com sucesso`);
        }
      } catch (error) {
        console.error(`Erro ao cancelar runs para thread ${threadId}:`, error);
      } finally {
        // Sempre marcar o thread como inativo
        setThreadActive(threadId, false);
      }
    }

    // Limpar todos os threads ativos
    clearActiveThreads();
    console.log('Todos os runs ativos foram cancelados');
  } catch (error) {
    console.error('Erro ao cancelar todos os runs ativos:', error);
    // Em caso de erro, limpar todos os threads ativos
    clearActiveThreads();
  }
};

// Send a message to the assistant and get the response
export const sendMessage = async (conversationId: string, message: string) => {
  try {
    if (!openaiClient) {
      const initialized = await initializeOpenAIAssistants();
      if (!initialized) {
        throw new Error('OpenAI client not initialized');
      }
    }

    // Get the conversation to get the thread ID
    const { data: conversation, error: getError } = await supabase
      .from('ai_conversations')
      .select('thread_id, assistant_id')
      .eq('id', conversationId)
      .single();

    if (getError) {
      throw getError;
    }

    const threadId = conversation.thread_id;
    const assistantId = conversation.assistant_id || ASSISTANT_ID;

    // Verificar se o thread já está ativo
    if (isThreadActive(threadId)) {
      // Verificar se há algum run ativo para este thread
      try {
        const runs = await openaiClient!.beta.threads.runs.list(threadId);
        // Verificar se há algum run ativo ou recentemente completado
        // Isso ajuda a lidar com runs que acabaram de ser concluídos
        const activeRun = runs.data[0]; // Pegar o run mais recente

        if (activeRun) {
          // Verificar se o run está em um estado que indica processamento ativo
          const isActiveState = ['queued', 'in_progress'].includes(activeRun.status);
          const isRecentlyCompleted = ['completed', 'failed', 'cancelled'].includes(activeRun.status) &&
                                     (Date.now() - new Date(activeRun.completed_at || activeRun.created_at).getTime() < 2000);

          if (isActiveState || isRecentlyCompleted) {
            // Se o run está ativo ou foi concluído muito recentemente, cancelar
            if (isActiveState) {
              try {
                await openaiClient!.beta.threads.runs.cancel(threadId, activeRun.id);
                console.log(`Run ${activeRun.id} cancelado com sucesso`);
              } catch (innerError) {
                console.log(`Não foi possível cancelar o run ${activeRun.id}: ${innerError.message}`);
              }
            }

            // Liberar o thread para permitir nova mensagem
            console.log(`Liberando thread ${threadId}`);
            setThreadActive(threadId, false);
          } else {
            // Run está em um estado final e não é recente, podemos liberar o thread
            console.log(`Run ${activeRun.id} está no estado ${activeRun.status}, liberando thread`);
            setThreadActive(threadId, false);
          }
        } else {
          // Não há runs ativos, então podemos liberar o thread
          console.log('Nenhum run ativo encontrado, liberando thread');
          setThreadActive(threadId, false);
        }
      } catch (listError) {
        console.error('Erro ao listar runs:', listError);
        // Em caso de erro ao listar runs, vamos liberar o thread para evitar bloqueios
        setThreadActive(threadId, false);
      }
    }

    // Marcar o thread como ativo
    setThreadActive(threadId, true);

    try {
      // Add the user message to the thread com timeout
      const userMessage = await withTimeout(
        openaiClient!.beta.threads.messages.create(
          threadId,
          {
            role: 'user',
            content: message,
          }
        ),
        API_TIMEOUT,
        'Tempo limite excedido ao enviar mensagem. Verifique sua conexão com a internet.'
      );

      // Save the user message to Supabase
      const { error: insertUserError } = await supabase
        .from('ai_messages')
        .insert({
          conversation_id: conversationId,
          role: 'user',
          content: message,
          message_id: userMessage.id,
        });

      if (insertUserError) {
        throw insertUserError;
      }

      // Run the assistant on the thread com timeout
      const run = await withTimeout(
        openaiClient!.beta.threads.runs.create(
          threadId,
          {
            assistant_id: assistantId,
          }
        ),
        API_TIMEOUT,
        'Tempo limite excedido ao iniciar processamento. Verifique sua conexão com a internet.'
      );

      // Poll for the run to complete, com intervalo mais curto para respostas mais rápidas
      let runStatus = await withTimeout(
        openaiClient!.beta.threads.runs.retrieve(
          threadId,
          run.id
        ),
        API_TIMEOUT,
        'Tempo limite excedido ao verificar status. Verifique sua conexão com a internet.'
      );

      // Wait for the run to complete with ultra-short polling interval
      // Verificar status válidos: queued, in_progress, completed, failed, cancelled, expired
      while (['queued', 'in_progress'].includes(runStatus.status)) {
        // Polling adaptativo: começa rápido e vai aumentando o intervalo
        const waitTime = runStatus.status === 'queued' ? 100 : 30; // 30ms para in_progress, 100ms para queued
        await new Promise(resolve => setTimeout(resolve, waitTime));

        try {
          runStatus = await withTimeout(
            openaiClient!.beta.threads.runs.retrieve(
              threadId,
              run.id
            ),
            API_TIMEOUT,
            'Tempo limite excedido ao verificar status. Verifique sua conexão com a internet.'
          );

          // Log para debug de desempenho
          console.log(`Status do run ${run.id}: ${runStatus.status}`);
        } catch (retrieveError) {
          console.error('Erro ao recuperar status do run:', retrieveError);
          // Se houver erro ao recuperar o status, assumir que o run foi cancelado
          runStatus = { status: 'cancelled' } as any;
          break;
        }
      }

      // Verificar o status do run e tratar adequadamente
      if (runStatus.status === 'completed') {
        // Get the assistant's response com timeout
        const messages = await withTimeout(
          openaiClient!.beta.threads.messages.list(
            threadId
          ),
          API_TIMEOUT,
          'Tempo limite excedido ao buscar mensagens. Verifique sua conexão com a internet.'
        );

        // Find the most recent assistant message
        const assistantMessages = messages.data.filter(msg => msg.role === 'assistant');
        const latestMessage = assistantMessages[0]; // Messages are returned in reverse chronological order

        if (latestMessage) {
          // Extrair o conteúdo da mensagem
          const messageContent = typeof latestMessage.content[0].text === 'object'
            ? latestMessage.content[0].text.value
            : latestMessage.content[0].text;

          // Save the assistant message to Supabase
          const { error: insertAssistantError } = await supabase
            .from('ai_messages')
            .insert({
              conversation_id: conversationId,
              role: 'assistant',
              content: messageContent,
              message_id: latestMessage.id,
            });

          if (insertAssistantError) {
            throw insertAssistantError;
          }

          // Update the conversation's updated_at timestamp
          const { error: updateError } = await supabase
            .from('ai_conversations')
            .update({ updated_at: new Date().toISOString() })
            .eq('id', conversationId);

          if (updateError) {
            throw updateError;
          }

          return {
            role: 'assistant',
            content: messageContent,
            id: latestMessage.id,
          };
        }
      } else if (runStatus.status === 'failed') {
        throw new Error(`Run failed: ${runStatus.last_error?.message || 'Unknown error'}`);
      } else if (runStatus.status === 'cancelled') {
        // Quando um run é cancelado, criar uma resposta amigável
        const messageId = `assistant_${Date.now()}`;
        const messageContent = "Desculpe, tive que reiniciar meu processamento. Por favor, continue com sua pergunta ou tente novamente.";

        // Salvar a mensagem no Supabase
        try {
          await supabase
            .from('ai_messages')
            .insert({
              conversation_id: conversationId,
              role: 'assistant',
              content: messageContent,
              message_id: messageId,
            });

          // Atualizar o timestamp da conversa
          await supabase
            .from('ai_conversations')
            .update({ updated_at: new Date().toISOString() })
            .eq('id', conversationId);

          return {
            role: 'assistant',
            content: messageContent,
            id: messageId,
          };
        } catch (error) {
          console.error('Erro ao salvar mensagem após cancelamento:', error);
          // Mesmo com erro no Supabase, retornar uma resposta para o usuário
          return {
            role: 'assistant',
            content: messageContent,
            id: messageId,
          };
        }
      } else if (runStatus.status === 'expired') {
        // Quando um run expira, criar uma resposta amigável
        const messageId = `assistant_${Date.now()}`;
        const messageContent = "Desculpe, minha resposta demorou muito tempo para ser processada. Por favor, tente novamente com uma pergunta mais específica.";

        // Retornar a mensagem sem tentar salvar no Supabase (para evitar mais atrasos)
        return {
          role: 'assistant',
          content: messageContent,
          id: messageId,
        };
      } else {
        // Para qualquer outro status inesperado
        const messageId = `assistant_${Date.now()}`;
        const messageContent = `Desculpe, ocorreu um problema inesperado (status: ${runStatus.status}). Por favor, tente novamente.`;

        return {
          role: 'assistant',
          content: messageContent,
          id: messageId,
        };
      }
    } finally {
      // Sempre marcar o thread como inativo ao finalizar, mesmo em caso de erro
      setThreadActive(threadId, false);
    }
  } catch (error) {
    console.error('Error sending message:', error);

    // Sempre liberar o thread atual para evitar bloqueios
    // Verificar se threadId existe antes de tentar usar
    if (threadId) {
      setThreadActive(threadId, false);
    } else {
      console.warn('threadId não definido ao tentar liberar thread');
      // Limpar todos os threads ativos como precaução
      clearActiveThreads();
    }

    // Se for um erro grave, limpar todos os threads ativos
    // para evitar bloqueios permanentes
    if (error instanceof Error &&
        (error.message.includes('500') ||
         error.message.includes('503') ||
         error.message.includes('timeout') ||
         error.message.includes('Tempo limite excedido') ||
         error.message.includes('network') ||
         error.message.includes('cancelled') ||
         error.message.includes('Can\'t add messages') ||
         error.message.includes('while a run') ||
         error.message.includes('is active'))) {
      console.log('Erro grave detectado, limpando todos os threads ativos');
      clearActiveThreads();
    }

    throw error;
  }
};

// Get messages for a conversation
export const getMessages = async (conversationId: string) => {
  try {
    const { data, error } = await supabase
      .from('ai_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    return data.map(message => ({
      id: message.id,
      role: message.role,
      content: message.content,
      created_at: message.created_at,
    }));
  } catch (error) {
    console.error('Error getting messages:', error);
    throw error;
  }
};

// Update conversation title
export const updateConversationTitle = async (conversationId: string, title: string) => {
  try {
    const { error } = await supabase
      .from('ai_conversations')
      .update({ title, updated_at: new Date().toISOString() })
      .eq('id', conversationId);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error updating conversation title:', error);
    throw error;
  }
};
