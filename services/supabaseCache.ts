import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
import NetInfo from '@react-native-community/netinfo';
import { debounce, throttle, memoize } from '@/utils/performance';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { measureFunction } from '@/services/performanceMonitor';

// Constants
const CACHE_PREFIX = 'supabase_cache_';
const CACHE_METADATA_KEY = 'supabase_cache_metadata';
const CACHE_VERSION = '1.0.0';
const DEFAULT_CACHE_TIME = 10 * 60 * 1000; // 10 minutes
const MAX_CACHE_SIZE = 200; // Maximum number of cached queries
const MAX_CACHE_SIZE_MB = 50; // Maximum cache size in MB
const CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
const PRIORITY_TABLES = ['subjects', 'flashcard_sets', 'quizzes', 'study_groups']; // Tables that should be prioritized in cache

// Types
interface CacheMetadata {
  version: string;
  keys: string[];
  lastCleanup: number;
  totalSize: number; // Total size in bytes
  tables: Record<string, number>; // Count of entries per table
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  size: number; // Size in bytes
  priority: number; // 0-10, higher is more important
  accessCount: number; // Number of times this entry has been accessed
  lastAccessed: number; // Timestamp of last access
  table: string; // Table name
}

// Cache storage strategy
type StorageStrategy = 'memory' | 'asyncstorage' | 'filesystem';

// Memory cache for faster access
const memoryCache: Record<string, CacheEntry<any>> = {};

// Initialize cache metadata
let cacheMetadata: CacheMetadata = {
  version: CACHE_VERSION,
  keys: [],
  lastCleanup: Date.now(),
  totalSize: 0,
  tables: {},
};

// Determine best storage strategy based on platform
const getStorageStrategy = (): StorageStrategy => {
  if (Platform.OS === 'web') {
    return 'asyncstorage';
  }

  // Use filesystem for native platforms if available
  if (FileSystem.documentDirectory) {
    return 'filesystem';
  }

  return 'asyncstorage';
};

// Current storage strategy
const storageStrategy = getStorageStrategy();

// Ensure cache directory exists for filesystem storage
const ensureCacheDirectory = async (): Promise<void> => {
  if (storageStrategy === 'filesystem') {
    const cacheDir = `${FileSystem.cacheDirectory}supabase_cache/`;
    const dirInfo = await FileSystem.getInfoAsync(cacheDir);

    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(cacheDir, { intermediates: true });
    }
  }
};

// Get cache file path for filesystem storage
const getCacheFilePath = (key: string): string => {
  return `${FileSystem.cacheDirectory}supabase_cache/${key}`;
};

// Calculate size of data in bytes
const calculateSize = (data: any): number => {
  try {
    const jsonString = JSON.stringify(data);
    return jsonString.length * 2; // Approximate size in bytes (UTF-16)
  } catch (error) {
    return 0;
  }
};

// Load cache metadata
const loadCacheMetadata = async (): Promise<void> => {
  try {
    await ensureCacheDirectory();

    const data = await AsyncStorage.getItem(CACHE_METADATA_KEY);
    if (data) {
      const parsedData = JSON.parse(data);

      // Handle version upgrades
      if (parsedData.version !== CACHE_VERSION) {
        console.log(`Cache version changed from ${parsedData.version} to ${CACHE_VERSION}, clearing cache`);
        await clearCache();
        return;
      }

      cacheMetadata = parsedData;
    }
  } catch (error) {
    console.error('Error loading cache metadata:', error);
    // Reset metadata on error
    cacheMetadata = {
      version: CACHE_VERSION,
      keys: [],
      lastCleanup: Date.now(),
      totalSize: 0,
      tables: {},
    };
  }
};

// Save cache metadata
const saveCacheMetadata = debounce(async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(CACHE_METADATA_KEY, JSON.stringify(cacheMetadata));
  } catch (error) {
    console.error('Error saving cache metadata:', error);
  }
}, 500);

// Clean up old cache entries
const cleanupCache = async (): Promise<void> => {
  // Only clean up at specified interval
  const now = Date.now();
  if (now - cacheMetadata.lastCleanup < CLEANUP_INTERVAL) {
    return;
  }

  try {
    await measureFunction('cache_cleanup', async () => {
      console.log('Starting cache cleanup...');

      // Get all entries with their metadata
      const entries: Array<{
        key: string;
        entry: CacheEntry<any>;
      }> = [];

      // Collect all entries and their metadata
      for (const key of cacheMetadata.keys) {
        try {
          const entry = await getFromCache<any>(key, false);

          if (entry) {
            entries.push({ key, entry });
          } else {
            // Remove from metadata if entry doesn't exist
            removeKeyFromMetadata(key);
          }
        } catch (error) {
          console.error(`Error processing cache key ${key}:`, error);
          removeKeyFromMetadata(key);
        }
      }

      // First, remove expired entries
      const validEntries = entries.filter(({ key, entry }) => {
        const isExpired = now > entry.expiresAt;

        if (isExpired) {
          removeFromCache(key);
          return false;
        }

        return true;
      });

      // Check if we need to reduce cache size
      const totalSizeMB = cacheMetadata.totalSize / (1024 * 1024);
      const exceedsSizeLimit = totalSizeMB > MAX_CACHE_SIZE_MB;
      const exceedsCountLimit = validEntries.length > MAX_CACHE_SIZE;

      if (exceedsSizeLimit || exceedsCountLimit) {
        console.log(`Cache size: ${totalSizeMB.toFixed(2)}MB, entries: ${validEntries.length}`);
        console.log(`Limits: ${MAX_CACHE_SIZE_MB}MB, ${MAX_CACHE_SIZE} entries`);

        // Score entries based on priority, access count, and age
        const scoredEntries = validEntries.map(({ key, entry }) => {
          // Calculate score: higher = more important to keep
          const ageScore = (now - entry.timestamp) / (24 * 60 * 60 * 1000); // Age in days
          const accessScore = Math.min(entry.accessCount, 100) / 10; // Max 10 points for access count
          const recencyScore = Math.max(0, 10 - (now - entry.lastAccessed) / (24 * 60 * 60 * 1000)); // More recent = higher score
          const priorityScore = entry.priority;

          // Tables that are prioritized get a bonus
          const tablePriorityBonus = PRIORITY_TABLES.includes(entry.table) ? 5 : 0;

          const totalScore = priorityScore + accessScore + recencyScore - ageScore + tablePriorityBonus;

          return { key, entry, score: totalScore };
        });

        // Sort by score (lowest first, to be removed)
        scoredEntries.sort((a, b) => a.score - b.score);

        // Calculate how many entries to remove
        let entriesToRemoveCount = 0;
        let sizeToFree = 0;

        if (exceedsCountLimit) {
          entriesToRemoveCount = validEntries.length - MAX_CACHE_SIZE;
        }

        if (exceedsSizeLimit) {
          sizeToFree = (totalSizeMB - (MAX_CACHE_SIZE_MB * 0.8)) * 1024 * 1024; // Free to 80% of max

          // Count how many entries we need to remove to free up enough space
          let cumulativeSize = 0;
          for (let i = 0; i < scoredEntries.length; i++) {
            cumulativeSize += scoredEntries[i].entry.size;
            if (cumulativeSize >= sizeToFree) {
              entriesToRemoveCount = Math.max(entriesToRemoveCount, i + 1);
              break;
            }
          }
        }

        // Remove the lowest-scored entries
        const entriesToRemove = scoredEntries.slice(0, entriesToRemoveCount);
        console.log(`Removing ${entriesToRemove.length} cache entries`);

        for (const { key } of entriesToRemove) {
          await removeFromCache(key);
        }
      }

      // Update metadata
      cacheMetadata.lastCleanup = now;
      await saveCacheMetadata();

      console.log(`Cache cleanup complete. Entries: ${cacheMetadata.keys.length}, Size: ${(cacheMetadata.totalSize / (1024 * 1024)).toFixed(2)}MB`);
    });
  } catch (error) {
    console.error('Error cleaning up cache:', error);
  }
};

// Helper to remove a key from metadata
const removeKeyFromMetadata = (key: string): void => {
  const index = cacheMetadata.keys.indexOf(key);
  if (index !== -1) {
    cacheMetadata.keys.splice(index, 1);
  }
};

// Generate a cache key from a query
const generateCacheKey = (table: string, query: any): string => {
  const queryString = JSON.stringify(query);
  return `${table}_${queryString}`;
};

// Get table name from cache key
const getTableFromKey = (key: string): string => {
  const parts = key.split('_');
  return parts[0] || 'unknown';
};

// Calculate priority for a cache entry
const calculatePriority = (table: string, data: any): number => {
  // Priority scale: 0-10 (higher = more important)
  let priority = 5; // Default priority

  // Prioritize important tables
  if (PRIORITY_TABLES.includes(table)) {
    priority += 2;
  }

  // Prioritize smaller datasets
  const size = calculateSize(data);
  if (size < 1024) { // Less than 1KB
    priority += 1;
  } else if (size > 100 * 1024) { // More than 100KB
    priority -= 1;
  }

  // Prioritize datasets with fewer items
  if (Array.isArray(data)) {
    if (data.length < 10) {
      priority += 1;
    } else if (data.length > 100) {
      priority -= 1;
    }
  }

  return Math.max(0, Math.min(10, priority));
};

// Remove an entry from the cache
const removeFromCache = async (key: string): Promise<void> => {
  try {
    // Get the entry first to update metadata correctly
    const cacheKey = `${CACHE_PREFIX}${key}`;

    // Check memory cache first
    if (memoryCache[key]) {
      const entry = memoryCache[key];
      delete memoryCache[key];

      // Update metadata
      cacheMetadata.totalSize -= entry.size;

      const table = entry.table;
      if (cacheMetadata.tables[table]) {
        cacheMetadata.tables[table]--;
        if (cacheMetadata.tables[table] <= 0) {
          delete cacheMetadata.tables[table];
        }
      }
    }

    // Remove from storage
    if (storageStrategy === 'asyncstorage') {
      await AsyncStorage.removeItem(cacheKey);
    } else if (storageStrategy === 'filesystem') {
      const filePath = getCacheFilePath(key);
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(filePath);
      }
    }

    // Update metadata
    removeKeyFromMetadata(key);
    await saveCacheMetadata();
  } catch (error) {
    console.error(`Error removing from cache: ${key}`, error);
  }
};

// Add a new entry to the cache
const addToCache = async <T>(
  key: string,
  data: T,
  table: string,
  expiresIn: number = DEFAULT_CACHE_TIME
): Promise<void> => {
  try {
    const now = Date.now();
    const size = calculateSize(data);
    const priority = calculatePriority(table, data);

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + expiresIn,
      size,
      priority,
      accessCount: 1,
      lastAccessed: now,
      table,
    };

    // Store in memory cache for faster access
    memoryCache[key] = entry as CacheEntry<any>;

    // Store in persistent storage
    const cacheKey = `${CACHE_PREFIX}${key}`;
    const entryString = JSON.stringify(entry);

    if (storageStrategy === 'asyncstorage') {
      await AsyncStorage.setItem(cacheKey, entryString);
    } else if (storageStrategy === 'filesystem') {
      const filePath = getCacheFilePath(key);
      await FileSystem.writeAsStringAsync(filePath, entryString);
    }

    // Update metadata
    if (!cacheMetadata.keys.includes(key)) {
      cacheMetadata.keys.push(key);
      cacheMetadata.totalSize += size;

      // Update table counts
      cacheMetadata.tables[table] = (cacheMetadata.tables[table] || 0) + 1;

      await saveCacheMetadata();
    }

    // Clean up if needed
    const totalSizeMB = cacheMetadata.totalSize / (1024 * 1024);
    if (cacheMetadata.keys.length > MAX_CACHE_SIZE || totalSizeMB > MAX_CACHE_SIZE_MB) {
      await cleanupCache();
    }
  } catch (error) {
    console.error('Error adding to cache:', error);
  }
};

// Get an entry from the cache
const getFromCache = async <T>(key: string, updateStats = true): Promise<CacheEntry<T> | null> => {
  try {
    // Check memory cache first for faster access
    if (memoryCache[key]) {
      const entry = memoryCache[key] as CacheEntry<T>;
      const now = Date.now();

      // Check if expired
      if (now > entry.expiresAt) {
        delete memoryCache[key];
        await removeFromCache(key);
        return null;
      }

      // Update access stats
      if (updateStats) {
        entry.accessCount++;
        entry.lastAccessed = now;
        memoryCache[key] = entry as CacheEntry<any>;
      }

      return entry;
    }

    // Not in memory cache, check persistent storage
    const cacheKey = `${CACHE_PREFIX}${key}`;
    let dataString: string | null = null;

    if (storageStrategy === 'asyncstorage') {
      dataString = await AsyncStorage.getItem(cacheKey);
    } else if (storageStrategy === 'filesystem') {
      const filePath = getCacheFilePath(key);
      const fileInfo = await FileSystem.getInfoAsync(filePath);

      if (fileInfo.exists) {
        dataString = await FileSystem.readAsStringAsync(filePath);
      }
    }

    if (!dataString) {
      return null;
    }

    const entry: CacheEntry<T> = JSON.parse(dataString);
    const now = Date.now();

    // Check if expired
    if (now > entry.expiresAt) {
      await removeFromCache(key);
      return null;
    }

    // Update access stats
    if (updateStats) {
      entry.accessCount++;
      entry.lastAccessed = now;

      // Save updated entry
      if (storageStrategy === 'asyncstorage') {
        await AsyncStorage.setItem(cacheKey, JSON.stringify(entry));
      } else if (storageStrategy === 'filesystem') {
        const filePath = getCacheFilePath(key);
        await FileSystem.writeAsStringAsync(filePath, JSON.stringify(entry));
      }
    }

    // Add to memory cache for faster future access
    memoryCache[key] = entry as CacheEntry<any>;

    return entry;
  } catch (error) {
    console.error('Error getting from cache:', error);
    return null;
  }
};

// Clear the entire cache
export const clearCache = async (): Promise<void> => {
  try {
    await measureFunction('clear_cache', async () => {
      // Clear memory cache
      Object.keys(memoryCache).forEach(key => {
        delete memoryCache[key];
      });

      // Remove all cached items from storage
      if (storageStrategy === 'asyncstorage') {
        for (const key of cacheMetadata.keys) {
          await AsyncStorage.removeItem(`${CACHE_PREFIX}${key}`);
        }
      } else if (storageStrategy === 'filesystem') {
        const cacheDir = `${FileSystem.cacheDirectory}supabase_cache/`;
        await FileSystem.deleteAsync(cacheDir, { idempotent: true });
        await ensureCacheDirectory();
      }

      // Reset metadata
      cacheMetadata = {
        version: CACHE_VERSION,
        keys: [],
        lastCleanup: Date.now(),
        totalSize: 0,
        tables: {},
      };

      await saveCacheMetadata();

      console.log('Cache cleared successfully');
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
};

// Initialize the cache
export const initCache = async (): Promise<void> => {
  try {
    await ensureCacheDirectory();
    await loadCacheMetadata();

    // Perform initial cleanup if needed
    const now = Date.now();
    if (now - cacheMetadata.lastCleanup > CLEANUP_INTERVAL) {
      await cleanupCache();
    }

    console.log(`Cache initialized. Entries: ${cacheMetadata.keys.length}, Size: ${(cacheMetadata.totalSize / (1024 * 1024)).toFixed(2)}MB`);
  } catch (error) {
    console.error('Error initializing cache:', error);
  }
};

// Cached query function
export const cachedQuery = async <T>(
  table: string,
  query: any,
  options: {
    forceRefresh?: boolean;
    cacheTime?: number;
    offlineOnly?: boolean;
    priority?: number;
  } = {}
): Promise<{ data: T | null; error: any; fromCache: boolean; count?: number }> => {
  const {
    forceRefresh = false,
    cacheTime = DEFAULT_CACHE_TIME,
    offlineOnly = false,
    priority = 5
  } = options;

  // Generate cache key
  const cacheKey = generateCacheKey(table, query);

  // Check network status
  const netInfo = await NetInfo.fetch();
  const isOnline = netInfo.isConnected && netInfo.isInternetReachable;

  // If offline or offline-only mode, try to get from cache
  if (!isOnline || offlineOnly) {
    const cachedEntry = await getFromCache<T>(cacheKey);

    if (cachedEntry) {
      return { data: cachedEntry.data, error: null, fromCache: true };
    }

    // If offline and no cache, return error
    if (!isOnline) {
      return {
        data: null,
        error: new Error('No network connection and no cached data available'),
        fromCache: false,
      };
    }
  }

  // If not forcing refresh, try to get from cache first
  if (!forceRefresh) {
    const cachedEntry = await getFromCache<T>(cacheKey);

    if (cachedEntry) {
      return { data: cachedEntry.data, error: null, fromCache: true };
    }
  }

  // If online and no cache or force refresh, fetch from Supabase
  try {
    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    // Cache the result
    if (data) {
      await addToCache(cacheKey, data, table, cacheTime);
    }

    return { data, error: null, fromCache: false, count };
  } catch (error) {
    console.error('Error executing Supabase query:', error);

    // If error, try to get from cache as fallback
    const cachedEntry = await getFromCache<T>(cacheKey);

    if (cachedEntry) {
      return {
        data: cachedEntry.data,
        error,
        fromCache: true,
      };
    }

    return { data: null, error, fromCache: false };
  }
};

// Helper function for SELECT queries
export const cachedSelect = async <T>(
  table: string,
  options: {
    select?: string;
    filter?: Record<string, any>;
    order?: { column: string; ascending?: boolean };
    limit?: number;
    page?: number;
    forceRefresh?: boolean;
    cacheTime?: number;
    offlineOnly?: boolean;
    priority?: number;
  } = {}
): Promise<{ data: T[] | null; error: any; fromCache: boolean; count?: number }> => {
  const {
    select = '*',
    filter = {},
    order,
    limit,
    page = 1,
    forceRefresh = false,
    cacheTime = DEFAULT_CACHE_TIME,
    offlineOnly = false,
    priority = PRIORITY_TABLES.includes(table) ? 7 : 5,
  } = options;

  // Verificar se o usuário está autenticado
  const { data: { user } } = await supabase.auth.getUser();

  // Build the query
  let query = supabase.from(table).select(select, { count: 'exact' });

  // Preparar os filtros
  const filters = { ...filter };

  // Adicionar filtro de usuário automaticamente se não estiver presente e não for a tabela users
  if (!filters.user_id && table !== 'users' && user) {
    filters.user_id = user.id;
  }

  // Apply filters
  Object.entries(filters).forEach(([key, value]) => {
    if (value === null) {
      query = query.is(key, null);
    } else if (Array.isArray(value)) {
      query = query.in(key, value);
    } else if (typeof value === 'object' && value !== null) {
      // Handle range queries
      if (value.gt !== undefined) query = query.gt(key, value.gt);
      if (value.gte !== undefined) query = query.gte(key, value.gte);
      if (value.lt !== undefined) query = query.lt(key, value.lt);
      if (value.lte !== undefined) query = query.lte(key, value.lte);
      if (value.like !== undefined) query = query.like(key, value.like);
      if (value.ilike !== undefined) query = query.ilike(key, value.ilike);
    } else {
      query = query.eq(key, value);
    }
  });

  // Apply ordering
  if (order) {
    query = query.order(order.column, {
      ascending: order.ascending !== false,
    });
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit);

    if (page > 1 && limit) {
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);
    }
  }

  // Execute the cached query
  return cachedQuery<T[]>(
    table,
    query,
    { forceRefresh, cacheTime, offlineOnly, priority }
  );
};

// Get cache stats
export const getCacheStats = async (): Promise<{
  entries: number;
  size: number;
  tables: Record<string, number>;
  lastCleanup: Date;
}> => {
  return {
    entries: cacheMetadata.keys.length,
    size: cacheMetadata.totalSize,
    tables: cacheMetadata.tables,
    lastCleanup: new Date(cacheMetadata.lastCleanup),
  };
};

// Initialize the cache when this module is imported
initCache().catch(console.error);
