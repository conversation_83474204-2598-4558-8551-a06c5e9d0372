import { Platform, Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import Constants from 'expo-constants';

// Verificar se estamos no Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Configurações para OAuth web
const REDIRECT_URLS = {
  success: 'com.liastudyapp://login-callback/',
  error: 'com.liastudyapp://login-error/',
  base: 'com.liastudyapp://',
};

const AUTH_ERROR_MESSAGES = {
  google: {
    cancelled: 'Login cancelado pelo usuário',
    inProgress: 'Login já em andamento',
    playServicesNotAvailable: 'Google Play Services não disponível',
    networkError: 'Erro de conexão. Verifique sua internet.',
    invalidCredentials: 'Credenciais inválidas',
    unknown: 'Erro desconhecido no login com Google',
  },
  apple: {
    cancelled: 'Login cancelado pelo usuário',
    failed: 'Falha no login com Apple',
    invalidResponse: 'Resposta inválida da Apple',
    notHandled: 'Login com Apple não foi processado',
    notAvailable: 'Apple Sign-In não está disponível neste dispositivo',
    unknown: 'Erro desconhecido no login com Apple',
  },
  general: {
    networkError: 'Erro de conexão. Verifique sua internet.',
    serverError: 'Erro no servidor. Tente novamente mais tarde.',
    invalidToken: 'Token inválido. Faça login novamente.',
    sessionExpired: 'Sessão expirada. Faça login novamente.',
  },
};

// Configuração do Google Sign-In (apenas para compatibilidade)
export const configureGoogleSignIn = () => {
  if (isExpoGo) {
    console.log('📱 Expo Go detectado - usando OAuth web para login social');
    return;
  }

  console.log('🔧 Configuração de login social para build de desenvolvimento');
};

// Login com Google usando SDK nativo (não disponível no Expo Go)
export const signInWithGoogleNative = async () => {
  console.log('📱 SDK nativo não disponível no Expo Go');
  return { data: null, error: new Error('SDK nativo não disponível no Expo Go') };
};

// Login com Apple usando SDK nativo (não disponível no Expo Go)
export const signInWithAppleNative = async () => {
  console.log('📱 SDK nativo não disponível no Expo Go');
  return { data: null, error: new Error('SDK nativo não disponível no Expo Go') };
};

// Logout do Google (não necessário no Expo Go)
export const signOutGoogle = async () => {
  console.log('📱 Logout do Google não necessário no Expo Go');
};

// Verificar se o usuário está logado no Google (sempre false no Expo Go)
export const isGoogleSignedIn = async () => {
  return false;
};

// Obter informações do usuário do Google (sempre null no Expo Go)
export const getCurrentGoogleUser = async () => {
  return null;
};

// Fallback para OAuth web (caso o login nativo falhe)
export const signInWithGoogleWeb = async () => {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: REDIRECT_URLS.success,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      throw error;
    }

    return { error: null };
  } catch (error: any) {
    console.error('Erro no login web com Google:', error);
    return { error };
  }
};

// Fallback para OAuth web Apple
export const signInWithAppleWeb = async () => {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'apple',
      options: {
        redirectTo: REDIRECT_URLS.success,
      },
    });

    if (error) {
      throw error;
    }

    return { error: null };
  } catch (error: any) {
    console.error('Erro no login web com Apple:', error);
    return { error };
  }
};

// Função principal para login com Google (tenta nativo primeiro, depois web)
export const signInWithGoogle = async () => {
  try {
    // Tentar login nativo primeiro
    const nativeResult = await signInWithGoogleNative();
    
    if (nativeResult.data) {
      return nativeResult;
    }
    
    // Se o login nativo falhar, tentar web OAuth
    console.log('Login nativo falhou, tentando web OAuth...');
    const webResult = await signInWithGoogleWeb();
    
    return { data: null, error: webResult.error };
  } catch (error: any) {
    console.error('Erro geral no login com Google:', error);
    return { data: null, error };
  }
};

// Função principal para login com Apple (tenta nativo primeiro, depois web)
export const signInWithApple = async () => {
  try {
    if (Platform.OS === 'ios') {
      // Tentar login nativo no iOS
      const nativeResult = await signInWithAppleNative();
      
      if (nativeResult.data) {
        return nativeResult;
      }
    }
    
    // Se o login nativo falhar ou não estiver no iOS, tentar web OAuth
    console.log('Login nativo falhou ou não disponível, tentando web OAuth...');
    const webResult = await signInWithAppleWeb();
    
    return { data: null, error: webResult.error };
  } catch (error: any) {
    console.error('Erro geral no login com Apple:', error);
    return { data: null, error };
  }
};
