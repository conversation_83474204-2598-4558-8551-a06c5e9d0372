import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { CalendarEvent, TodoItem } from '@/types';
import { parseISO, addMinutes, isBefore } from 'date-fns';

// Configurar o comportamento das notificações
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Solicitar permissões de notificação
export const requestNotificationPermissions = async () => {
  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }
  
  if (finalStatus !== 'granted') {
    console.log('Permissão de notificação não concedida!');
    return false;
  }
  
  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }
  
  return true;
};

// Agendar notificação para um evento
export const scheduleEventNotification = async (event: CalendarEvent) => {
  try {
    // Verificar se o evento tem um lembrete configurado
    if (!event.reminder || !event.reminderTime) {
      return null;
    }
    
    const reminderDate = parseISO(event.reminderTime);
    
    // Verificar se a data do lembrete já passou
    if (isBefore(reminderDate, new Date())) {
      console.log('Data do lembrete já passou:', event.title);
      return null;
    }
    
    // Agendar a notificação
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: `Lembrete: ${event.title}`,
        body: event.description || 'Você tem um evento agendado',
        data: { eventId: event.id, type: 'event' },
      },
      trigger: {
        date: reminderDate,
      },
    });
    
    console.log('Notificação agendada com ID:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Erro ao agendar notificação de evento:', error);
    return null;
  }
};

// Agendar notificação para uma tarefa
export const scheduleTodoNotification = async (todo: TodoItem) => {
  try {
    // Verificar se a tarefa tem um lembrete configurado
    if (!todo.reminderTime) {
      return null;
    }
    
    const reminderDate = parseISO(todo.reminderTime);
    
    // Verificar se a data do lembrete já passou
    if (isBefore(reminderDate, new Date())) {
      console.log('Data do lembrete já passou:', todo.title);
      return null;
    }
    
    // Agendar a notificação
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: `Tarefa: ${todo.title}`,
        body: todo.description || 'Você tem uma tarefa para concluir',
        data: { todoId: todo.id, type: 'todo' },
      },
      trigger: {
        date: reminderDate,
      },
    });
    
    console.log('Notificação agendada com ID:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Erro ao agendar notificação de tarefa:', error);
    return null;
  }
};

// Agendar notificações para eventos e tarefas criados a partir do cronograma
export const scheduleNotificationsForSchedule = async (events: CalendarEvent[], todos: TodoItem[]) => {
  const eventNotifications = await Promise.all(
    events.map(event => scheduleEventNotification(event))
  );
  
  const todoNotifications = await Promise.all(
    todos.map(todo => scheduleTodoNotification(todo))
  );
  
  return {
    eventNotifications: eventNotifications.filter(Boolean),
    todoNotifications: todoNotifications.filter(Boolean),
  };
};

// Cancelar uma notificação específica
export const cancelNotification = async (notificationId: string) => {
  try {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
    console.log('Notificação cancelada:', notificationId);
    return true;
  } catch (error) {
    console.error('Erro ao cancelar notificação:', error);
    return false;
  }
};

// Cancelar todas as notificações agendadas
export const cancelAllNotifications = async () => {
  try {
    await Notifications.cancelAllScheduledNotificationsAsync();
    console.log('Todas as notificações foram canceladas');
    return true;
  } catch (error) {
    console.error('Erro ao cancelar todas as notificações:', error);
    return false;
  }
};

// Obter todas as notificações agendadas
export const getAllScheduledNotifications = async () => {
  try {
    const notifications = await Notifications.getAllScheduledNotificationsAsync();
    return notifications;
  } catch (error) {
    console.error('Erro ao obter notificações agendadas:', error);
    return [];
  }
};

// Configurar um listener para notificações recebidas
export const addNotificationReceivedListener = (callback: (notification: Notifications.Notification) => void) => {
  return Notifications.addNotificationReceivedListener(callback);
};

// Configurar um listener para notificações respondidas
export const addNotificationResponseReceivedListener = (callback: (response: Notifications.NotificationResponse) => void) => {
  return Notifications.addNotificationResponseReceivedListener(callback);
};

// Remover listeners
export const removeNotificationSubscription = (subscription: Notifications.Subscription) => {
  Notifications.removeNotificationSubscription(subscription);
};
