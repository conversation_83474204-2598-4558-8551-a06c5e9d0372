// Simulador de respostas da assistente Lia
// Este arquivo fornece respostas instantâneas para testes

// Função para gerar um ID único
const generateId = () => `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

// Função para simular uma resposta da assistente
export const generateInstantResponse = (message: string): { id: string, content: string } => {
  // Converter a mensagem para minúsculas para facilitar a comparação
  const lowerMessage = message.toLowerCase();
  
  // Respostas para diferentes tipos de mensagens
  if (lowerMessage.includes('olá') || lowerMessage.includes('oi') || lowerMessage.includes('ola')) {
    return {
      id: generateId(),
      content: 'Olá! Sou a Lia, sua assistente de estudos. Como posso ajudar você hoje?'
    };
  } 
  
  if (lowerMessage.includes('ajuda') || lowerMessage.includes('ajudar')) {
    return {
      id: generateId(),
      content: 'Posso ajudar você com seus estudos! Posso responder perguntas, explicar conceitos, criar resumos e muito mais. O que você gostaria de aprender hoje?'
    };
  }
  
  if (lowerMessage.includes('matemática') || lowerMessage.includes('cálculo') || lowerMessage.includes('matematica')) {
    return {
      id: generateId(),
      content: 'Matemática é uma das minhas especialidades! Posso ajudar com álgebra, cálculo, geometria, estatística e muito mais. Qual é sua dúvida específica?'
    };
  }
  
  if (lowerMessage.includes('física') || lowerMessage.includes('fisica')) {
    return {
      id: generateId(),
      content: 'Física é fascinante! Posso ajudar com mecânica, eletromagnetismo, termodinâmica e outros tópicos. Qual conceito você gostaria de entender melhor?'
    };
  }
  
  if (lowerMessage.includes('história') || lowerMessage.includes('historia')) {
    return {
      id: generateId(),
      content: 'A história nos ajuda a entender o presente! Posso falar sobre diferentes períodos históricos, civilizações, eventos importantes e muito mais. Qual época ou evento você está estudando?'
    };
  }
  
  if (lowerMessage.includes('biologia')) {
    return {
      id: generateId(),
      content: 'Biologia é o estudo da vida! Posso ajudar com genética, ecologia, anatomia, evolução e outros tópicos. Qual é sua dúvida?'
    };
  }
  
  if (lowerMessage.includes('química') || lowerMessage.includes('quimica')) {
    return {
      id: generateId(),
      content: 'Química é essencial para entender o mundo! Posso ajudar com tabela periódica, reações químicas, química orgânica e mais. O que você gostaria de saber?'
    };
  }
  
  if (lowerMessage.includes('literatura') || lowerMessage.includes('português') || lowerMessage.includes('portugues')) {
    return {
      id: generateId(),
      content: 'Literatura e português são fundamentais! Posso ajudar com análise de textos, gramática, movimentos literários e mais. Como posso ajudar você?'
    };
  }
  
  if (lowerMessage.includes('obrigad')) {
    return {
      id: generateId(),
      content: 'De nada! Estou sempre à disposição para ajudar com seus estudos. Se tiver mais perguntas, é só me chamar!'
    };
  }
  
  if (lowerMessage.includes('tchau') || lowerMessage.includes('adeus') || lowerMessage.includes('até logo')) {
    return {
      id: generateId(),
      content: 'Até logo! Volte sempre que precisar de ajuda com seus estudos. Estou aqui para ajudar!'
    };
  }
  
  // Resposta padrão para outras mensagens
  return {
    id: generateId(),
    content: 'Entendi sua pergunta. Como sua assistente de estudos, estou aqui para ajudar com qualquer assunto acadêmico. Posso fornecer explicações, resumos, exemplos práticos e dicas de estudo. Vamos explorar esse tema juntos!'
  };
};

// Função para simular o envio de uma mensagem
export const sendInstantMessage = async (conversationId: string, message: string) => {
  // Simular uma resposta instantânea
  const response = generateInstantResponse(message);
  
  // Retornar a resposta no formato esperado pelo store
  return {
    role: 'assistant',
    content: response.content,
    id: response.id,
  };
};

// Exportar a função para uso externo
export default sendInstantMessage;
