import { InteractionManager, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Constants
const PERFORMANCE_LOG_KEY = 'performance_logs';
const MAX_LOGS = 100;

// Types
interface PerformanceLog {
  operation: string;
  startTime: number;
  endTime: number;
  duration: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

interface PerformanceMetrics {
  averageLoadTime: number;
  slowestOperation: {
    operation: string;
    duration: number;
  };
  fastestOperation: {
    operation: string;
    duration: number;
  };
  totalOperations: number;
}

// In-memory cache for performance logs
let performanceLogs: PerformanceLog[] = [];
let isInitialized = false;

// Initialize the performance monitor
export const initPerformanceMonitor = async (): Promise<void> => {
  if (isInitialized) return;
  
  try {
    const storedLogs = await AsyncStorage.getItem(PERFORMANCE_LOG_KEY);
    if (storedLogs) {
      performanceLogs = JSON.parse(storedLogs);
    }
    isInitialized = true;
  } catch (error) {
    console.error('Error initializing performance monitor:', error);
  }
};

// Start measuring performance for an operation
export const startMeasure = (operation: string): number => {
  return performance.now();
};

// End measuring performance for an operation
export const endMeasure = async (
  operation: string,
  startTime: number,
  metadata?: Record<string, any>
): Promise<number> => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  // Create a log entry
  const log: PerformanceLog = {
    operation,
    startTime,
    endTime,
    duration,
    timestamp: new Date().toISOString(),
    metadata,
  };
  
  // Add to in-memory cache
  performanceLogs.push(log);
  
  // Trim logs if they exceed the maximum
  if (performanceLogs.length > MAX_LOGS) {
    performanceLogs = performanceLogs.slice(-MAX_LOGS);
  }
  
  // Save logs in the background
  InteractionManager.runAfterInteractions(() => {
    AsyncStorage.setItem(PERFORMANCE_LOG_KEY, JSON.stringify(performanceLogs))
      .catch(error => console.error('Error saving performance logs:', error));
  });
  
  return duration;
};

// Measure a function's execution time
export const measureFunction = async <T>(
  operation: string,
  func: () => Promise<T> | T,
  metadata?: Record<string, any>
): Promise<T> => {
  const startTime = startMeasure(operation);
  
  try {
    const result = await func();
    await endMeasure(operation, startTime, metadata);
    return result;
  } catch (error) {
    await endMeasure(operation, startTime, { ...metadata, error: true });
    throw error;
  }
};

// Get performance metrics
export const getPerformanceMetrics = async (): Promise<PerformanceMetrics> => {
  if (!isInitialized) {
    await initPerformanceMonitor();
  }
  
  if (performanceLogs.length === 0) {
    return {
      averageLoadTime: 0,
      slowestOperation: { operation: 'none', duration: 0 },
      fastestOperation: { operation: 'none', duration: 0 },
      totalOperations: 0,
    };
  }
  
  // Calculate metrics
  let totalDuration = 0;
  let slowestOperation = performanceLogs[0];
  let fastestOperation = performanceLogs[0];
  
  performanceLogs.forEach(log => {
    totalDuration += log.duration;
    
    if (log.duration > slowestOperation.duration) {
      slowestOperation = log;
    }
    
    if (log.duration < fastestOperation.duration) {
      fastestOperation = log;
    }
  });
  
  return {
    averageLoadTime: totalDuration / performanceLogs.length,
    slowestOperation: {
      operation: slowestOperation.operation,
      duration: slowestOperation.duration,
    },
    fastestOperation: {
      operation: fastestOperation.operation,
      duration: fastestOperation.duration,
    },
    totalOperations: performanceLogs.length,
  };
};

// Clear performance logs
export const clearPerformanceLogs = async (): Promise<void> => {
  performanceLogs = [];
  await AsyncStorage.removeItem(PERFORMANCE_LOG_KEY);
};

// Get all performance logs
export const getPerformanceLogs = (): PerformanceLog[] => {
  return [...performanceLogs];
};
