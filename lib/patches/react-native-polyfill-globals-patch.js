/**
 * This is a patched version of the readable-stream.js file from react-native-polyfill-globals
 * The original file tries to import from 'web-streams-polyfill/ponyfill/es6' which doesn't exist
 * in the version of web-streams-polyfill we're using.
 */

import { polyfillGlobal } from 'react-native/Libraries/Utilities/PolyfillFunctions';

export const polyfill = () => {
    // Use the main export instead of the specific path
    const { ReadableStream } = require('web-streams-polyfill');
    
    polyfillGlobal('ReadableStream', () => ReadableStream);
};
