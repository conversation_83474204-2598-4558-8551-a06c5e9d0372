// Polyfill for require.resolve in Hermes JS engine
if (typeof global.require === 'function') {
  if (!global.require.resolve) {
    global.require.resolve = function(moduleName) {
      return moduleName;
    };
  }
}

// Export the polyfill function
export function applyRequireResolvePolyfill() {
  if (typeof global.require === 'function') {
    if (!global.require.resolve) {
      global.require.resolve = function(moduleName) {
        return moduleName;
      };
    }
  }
}
