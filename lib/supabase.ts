import * as URLPolyfill from 'react-native-url-polyfill';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import ENV from '@/config/env';

// Usar variáveis de ambiente para as credenciais do Supabase
const supabaseUrl = ENV.SUPABASE_URL;
const supabaseAnonKey = ENV.SUPABASE_ANON_KEY;

// Criar cliente Supabase com configurações seguras
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage as any,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  // Adicionar configurações de segurança
  global: {
    headers: {
      'X-Client-Info': 'lia-app-mobile',
    },
  },
});
