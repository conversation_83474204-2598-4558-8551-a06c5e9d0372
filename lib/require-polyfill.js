/**
 * Polyfill for require.resolve in Hermes JS engine
 * This file should be imported before any other imports in the app entry point
 */

// Create a global variable to track if the polyfill has been applied
global.__requireResolvePolyfillApplied = false;

// Apply the polyfill if it hasn't been applied yet
if (!global.__requireResolvePolyfillApplied) {
  // Check if require exists but resolve doesn't
  if (typeof global.require === 'function' && !global.require.resolve) {
    // Add a mock implementation of resolve
    global.require.resolve = function(moduleName) {
      return moduleName;
    };
    
    // Mark the polyfill as applied
    global.__requireResolvePolyfillApplied = true;
    console.log('require.resolve polyfill applied');
  }
}

// Export a function to explicitly apply the polyfill
export function applyRequireResolvePolyfill() {
  if (!global.__requireResolvePolyfillApplied) {
    if (typeof global.require === 'function' && !global.require.resolve) {
      global.require.resolve = function(moduleName) {
        return moduleName;
      };
      global.__requireResolvePolyfillApplied = true;
      console.log('require.resolve polyfill explicitly applied');
    }
  }
}

// Apply the polyfill immediately when this file is imported
applyRequireResolvePolyfill();
