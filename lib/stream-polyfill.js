/**
 * Enhanced stream polyfill for React Native
 * This file provides a more robust implementation of the stream module
 * specifically for the ws package used by Supabase and other libraries
 */

import { Readable, Writable, Duplex, Transform } from 'stream-browserify';

// Create a more complete stream implementation
const stream = {
  Readable,
  Writable,
  Duplex,
  Transform,
  Stream: require('stream-browserify').Stream,
  // Add any additional stream classes or methods needed
};

// Export the stream implementation
export default stream;

// Apply the stream polyfill globally
export const applyStreamPolyfill = () => {
  if (typeof global !== 'undefined') {
    global.stream = stream;
    
    // Ensure the Stream constructor is available globally
    if (!global.Stream) {
      global.Stream = stream.Stream;
    }
  }
};

// Apply the polyfill immediately
applyStreamPolyfill();
