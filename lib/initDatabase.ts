import { supabase } from './supabase';

/**
 * Verifica as tabelas existentes no banco de dados
 * Esta função deve ser chamada quando o aplicativo é iniciado
 */
export async function initDatabase() {
  await checkTables();
  // Skip study group tables verification to avoid infinite recursion
  // await createStudyGroupTables();
}

/**
 * Verifica se as tabelas necessárias existem no banco de dados
 */
async function checkTables() {
  try {
    console.log('Verificando banco de dados...');

    // Verificar se o usuário está autenticado
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('Usuário não autenticado, pulando verificação do banco de dados');
      return;
    }

    // Verificar a tabela quizzes
    try {
      const { data, error } = await supabase
        .from('quizzes')
        .select('*')
        .limit(1);

      if (error) {
        console.error('Erro ao verificar tabela quizzes:', error);
      } else {
        console.log('<PERSON><PERSON><PERSON> quizzes verificada com sucesso!');
        if (data && data.length > 0) {
          console.log('Colunas da tabela quizzes:', Object.keys(data[0]));
        }
      }
    } catch (error) {
      console.error('Erro ao verificar tabela quizzes:', error);
    }

    // Verificar a tabela quiz_questions
    try {
      const { data, error } = await supabase
        .from('quiz_questions')
        .select('*')
        .limit(1);

      if (error) {
        console.error('Erro ao verificar tabela quiz_questions:', error);
      } else {
        console.log('Tabela quiz_questions verificada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao verificar tabela quiz_questions:', error);
    }

    // Verificar a tabela quiz_attempts
    try {
      const { data, error } = await supabase
        .from('quiz_attempts')
        .select('*')
        .limit(1);

      if (error) {
        console.error('Erro ao verificar tabela quiz_attempts:', error);
      } else {
        console.log('Tabela quiz_attempts verificada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao verificar tabela quiz_attempts:', error);
    }

    // Skip study group tables verification to avoid infinite recursion
    console.log('Pulando verificação das tabelas de grupos de estudo para evitar recursão infinita');

    console.log('Verificação do banco de dados concluída!');
  } catch (error) {
    console.error('Erro ao verificar banco de dados:', error);
  }
}

/**
 * Verifica as tabelas de grupos de estudo no Supabase
 */
export async function createStudyGroupTables() {
  try {
    console.log('Verificando tabelas de grupos de estudo...');

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('Usuário não autenticado, pulando verificação de tabelas');
      return;
    }

    // Verificar se a tabela study_groups existe
    const { data: studyGroupsData, error: studyGroupsError } = await supabase
      .from('study_groups')
      .select('*')
      .limit(1);

    if (studyGroupsError) {
      console.log('Tabela study_groups não existe. Por favor, crie as tabelas necessárias no painel de administração do Supabase.');
    } else {
      console.log('Tabela study_groups já existe');
    }

    console.log('Verificação de tabelas de grupos de estudo concluída!');
  } catch (error) {
    console.error('Erro ao verificar tabelas de grupos de estudo:', error);
  }
}
