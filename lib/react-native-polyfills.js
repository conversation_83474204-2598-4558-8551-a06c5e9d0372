/**
 * This file provides polyfills for Node.js core modules in React Native
 * It should be imported at the very beginning of your app's entry point
 */

// Polyfill global objects and functions
global.process = global.process || {};
global.process.env = global.process.env || {};
global.process.browser = true;

// Polyfill for the 'events' module
if (!global.EventEmitter) {
  const EventEmitter = require('events');
  global.EventEmitter = EventEmitter;
  global.process.EventEmitter = EventEmitter;
}

// Polyfill for the 'stream' module
if (!global.Stream) {
  const Stream = require('stream-browserify');
  global.Stream = Stream;
}

// Polyfill for Buffer if needed
if (!global.Buffer) {
  global.Buffer = require('buffer/').Buffer;
}

// Polyfill for TextEncoder and TextDecoder
if (!global.TextEncoder || !global.TextDecoder) {
  try {
    const { TextEncoder, TextDecoder } = require('text-encoding');
    if (!global.TextEncoder) global.TextEncoder = TextEncoder;
    if (!global.TextDecoder) global.TextDecoder = TextDecoder;
  } catch (e) {
    console.warn('Failed to polyfill TextEncoder/TextDecoder:', e);
  }
}

// Polyfill for Web Streams API
try {
  const webStreams = require('web-streams-polyfill');
  if (!global.ReadableStream) global.ReadableStream = webStreams.ReadableStream;
  if (!global.WritableStream) global.WritableStream = webStreams.WritableStream;
  if (!global.TransformStream) global.TransformStream = webStreams.TransformStream;
} catch (e) {
  console.warn('Failed to polyfill Web Streams API:', e);
}

// Polyfill for setImmediate and clearImmediate if needed
if (!global.setImmediate) {
  global.setImmediate = setTimeout;
  global.clearImmediate = clearTimeout;
}

// Add any other polyfills your app might need
