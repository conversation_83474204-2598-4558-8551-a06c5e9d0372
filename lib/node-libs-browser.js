/**
 * This file initializes the node-libs-react-native polyfills
 * It should be imported at the very beginning of your app's entry point
 */

// Import our custom polyfills for ws
import { net, tls, http, https } from './ws-polyfill';

// Import our enhanced stream polyfill
import streamPolyfill from './stream-polyfill';

// Initialize node-libs-react-native
import { <PERSON><PERSON><PERSON> } from 'buffer';
import nodeLibs from 'node-libs-react-native';

// Polyfill global objects
global.Buffer = Buffer;
global.process = nodeLibs.process;

// Polyfill Node.js modules with our custom implementations for ws
global.net = net;
global.tls = tls;
global.http = http;
global.https = https;

// Use our enhanced stream polyfill instead of the default one
global.stream = streamPolyfill;

// Polyfill other Node.js modules
global.crypto = nodeLibs.crypto;
global.fs = nodeLibs.fs;
global.path = nodeLibs.path;
global.url = nodeLibs.url;
global.util = nodeLibs.util;
global.os = nodeLibs.os;
global.zlib = nodeLibs.zlib;
global.assert = nodeLibs.assert;

// Use import instead of require for events
import events from 'events';
global.events = events;

// Add a mock for require.resolve
if (typeof global.require === 'function' && !global.require.resolve) {
  global.require.resolve = function(moduleName) {
    return moduleName;
  };
}

// Export for use in other files
export default {
  ...nodeLibs,
  net,
  tls,
  http,
  https,
  stream: streamPolyfill
};
