import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import { synchronize } from '@nozbe/watermelondb/sync';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

import schema from './schema';
import migrations from './migrations';
import { Subject, Activity } from './models';
import { supabase } from '../supabase';

// Verificar se estamos no Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Classe de banco de dados simulado para Expo Go
class MockDatabase {
  collections: Record<string, any> = {};

  get(collectionName: string) {
    if (!this.collections[collectionName]) {
      this.collections[collectionName] = new MockCollection(collectionName);
    }
    return this.collections[collectionName];
  }

  write(callback: () => Promise<void>) {
    return callback();
  }

  // Propriedade simulada para evitar erros
  adapter = {
    initializingPromise: Promise.resolve()
  };
}

// Coleção simulada para Expo Go
class MockCollection {
  name: string;
  items: Record<string, any> = {};

  constructor(name: string) {
    this.name = name;
  }

  async find(id: string) {
    const key = `${this.name}_${id}`;
    const data = await AsyncStorage.getItem(key);
    if (data) {
      return JSON.parse(data);
    }
    return null;
  }

  async create(creator: (item: any) => void) {
    const item: any = {
      id: `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      update: async function(updater: (item: any) => void) {
        updater(this);
        const key = `${this.name}_${this.id}`;
        await AsyncStorage.setItem(key, JSON.stringify(this));
        return this;
      },
      markAsDeleted: async function() {
        const key = `${this.name}_${this.id}`;
        await AsyncStorage.removeItem(key);
      }
    };

    creator(item);

    // Salvar no AsyncStorage
    const key = `${this.name}_${item.id}`;
    await AsyncStorage.setItem(key, JSON.stringify(item));

    return item;
  }

  async query(queryFn: any) {
    // Implementação simplificada para Expo Go
    // Buscar todos os itens do AsyncStorage que começam com o nome da coleção
    const keys = await AsyncStorage.getAllKeys();
    const collectionKeys = keys.filter(key => key.startsWith(`${this.name}_`));

    const items = [];
    for (const key of collectionKeys) {
      const data = await AsyncStorage.getItem(key);
      if (data) {
        items.push(JSON.parse(data));
      }
    }

    // Filtrar com base na função de consulta (simplificado)
    return items;
  }
}

// Criar a instância apropriada do banco de dados
let database: any;

if (isExpoGo) {
  console.log('Usando banco de dados simulado para Expo Go');
  database = new MockDatabase();
} else {
  // Initialize the database with SQLite adapter for development/production builds
  const adapter = new SQLiteAdapter({
    schema,
    migrations,
    jsi: Platform.OS !== 'web', // Enable JSI for better performance (except on web)
    onSetUpError: error => {
      console.error('Database setup error:', error);
    }
  });

  database = new Database({
    adapter,
    modelClasses: [
      Subject,
      Activity,
      // Add other models as they are created
    ],
  });
}

export { database };

// Sync function to synchronize local database with Supabase
export async function syncDatabase() {
  try {
    // Only log in development mode to reduce console spam
    if (__DEV__) {
      console.log('Starting database synchronization...');
    }

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      if (__DEV__) {
        console.log('No authenticated user, skipping sync');
      }
      return;
    }

    // Get the last sync timestamp from local storage
    const lastSyncTimestamp = await getLastSyncTimestamp();
    const now = Date.now();

    // Verificar se estamos no Expo Go
    if (isExpoGo) {
      // Implementação simplificada para Expo Go usando AsyncStorage
      await syncDatabaseExpoGo(user.id, lastSyncTimestamp, now);
    } else {
      // Implementação completa com WatermelonDB
      await synchronize({
        database,
        pullChanges: async ({ lastPulledAt }) => {
          console.log('Pulling changes since', new Date(lastPulledAt || 0).toISOString());

          // Fetch subjects that have been updated since last sync
          const { data: subjects, error: subjectsError } = await supabase
            .from('subjects')
            .select('*')
            .eq('user_id', user.id)
            .gt('created_at', new Date(lastPulledAt || 0).toISOString());

          if (subjectsError) {
            console.error('Error fetching subjects:', subjectsError);
            throw subjectsError;
          }

          // Fetch activities that have been created since last sync
          const { data: activities, error: activitiesError } = await supabase
            .from('activities')
            .select('*')
            .eq('user_id', user.id)
            .gt('created_at', new Date(lastPulledAt || 0).toISOString());

          if (activitiesError) {
            console.error('Error fetching activities:', activitiesError);
            throw activitiesError;
          }

          // Format the changes for WatermelonDB
          const changes = {
            subjects: {
              created: subjects.map(formatSubjectForLocal),
              updated: [],
              deleted: [],
            },
            activities: {
              created: activities.map(formatActivityForLocal),
              updated: [],
              deleted: [],
            },
          };

          return { changes, timestamp: now };
        },
        pushChanges: async ({ changes, lastPulledAt }) => {
          console.log('Pushing local changes to server...');

          // Push created subjects
          if (changes.subjects.created.length > 0) {
            const { error } = await supabase
              .from('subjects')
              .insert(changes.subjects.created.map(formatSubjectForRemote));

            if (error) {
              console.error('Error pushing created subjects:', error);
              throw error;
            }
          }

          // Push updated subjects
          if (changes.subjects.updated.length > 0) {
            for (const subject of changes.subjects.updated) {
              const { error } = await supabase
                .from('subjects')
                .update(formatSubjectForRemote(subject))
                .eq('id', subject.id);

              if (error) {
                console.error('Error pushing updated subject:', error);
                throw error;
              }
            }
          }

          // Push created activities
          if (changes.activities.created.length > 0) {
            const { error } = await supabase
              .from('activities')
              .insert(changes.activities.created.map(formatActivityForRemote));

            if (error) {
              console.error('Error pushing created activities:', error);
              throw error;
            }
          }

          // Push updated activities
          if (changes.activities.updated.length > 0) {
            for (const activity of changes.activities.updated) {
              const { error } = await supabase
                .from('activities')
                .update(formatActivityForRemote(activity))
                .eq('id', activity.id);

              if (error) {
                console.error('Error pushing updated activity:', error);
                throw error;
              }
            }
          }
        },
      });
    }

    // Update the last sync timestamp
    await setLastSyncTimestamp(now);
    if (__DEV__) {
      console.log('Database synchronization completed successfully');
    }

  } catch (error) {
    console.error('Error during database synchronization:', error);
    throw error;
  }
}

// Implementação simplificada de sincronização para Expo Go
async function syncDatabaseExpoGo(userId: string, lastSyncTimestamp: number, now: number) {
  if (__DEV__) {
    console.log('Usando sincronização simplificada para Expo Go');
  }

  try {
    // Buscar dados do Supabase
    const lastSyncDate = new Date(lastSyncTimestamp || 0).toISOString();

    // Buscar subjects criados desde a última sincronização
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('*')
      .eq('user_id', userId)
      .gt('created_at', lastSyncDate);

    if (subjectsError) {
      console.error('Error fetching subjects:', subjectsError);
      throw subjectsError;
    }

    // Buscar activities criados desde a última sincronização
    const { data: activities, error: activitiesError } = await supabase
      .from('activities')
      .select('*')
      .eq('user_id', userId)
      .gt('created_at', lastSyncDate);

    if (activitiesError) {
      console.error('Error fetching activities:', activitiesError);
      throw activitiesError;
    }

    // Salvar subjects no AsyncStorage
    for (const subject of subjects) {
      const key = `subjects_${subject.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(formatSubjectForLocal(subject)));
    }

    // Salvar activities no AsyncStorage
    for (const activity of activities) {
      const key = `activities_${activity.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(formatActivityForLocal(activity)));
    }

    // Enviar dados locais para o Supabase
    // Buscar todos os subjects locais não sincronizados
    const keys = await AsyncStorage.getAllKeys();
    const subjectKeys = keys.filter(key => key.startsWith('subjects_'));
    const activityKeys = keys.filter(key => key.startsWith('activities_'));

    // Processar subjects
    for (const key of subjectKeys) {
      const data = await AsyncStorage.getItem(key);
      if (data) {
        const subject = JSON.parse(data);
        if (!subject.synced) {
          // Enviar para o Supabase
          const { error } = await supabase
            .from('subjects')
            .upsert(formatSubjectForRemote(subject));

          if (error) {
            console.error('Error pushing subject to Supabase:', error);
          } else {
            // Marcar como sincronizado
            subject.synced = true;
            await AsyncStorage.setItem(key, JSON.stringify(subject));
          }
        }
      }
    }

    // Processar activities
    for (const key of activityKeys) {
      const data = await AsyncStorage.getItem(key);
      if (data) {
        const activity = JSON.parse(data);
        if (!activity.synced) {
          // Enviar para o Supabase
          const { error } = await supabase
            .from('activities')
            .upsert(formatActivityForRemote(activity));

          if (error) {
            console.error('Error pushing activity to Supabase:', error);
          } else {
            // Marcar como sincronizado
            activity.synced = true;
            await AsyncStorage.setItem(key, JSON.stringify(activity));
          }
        }
      }
    }

    if (__DEV__) {
      console.log('Sincronização simplificada concluída');
    }
  } catch (error) {
    console.error('Erro na sincronização simplificada:', error);
    throw error;
  }
}

// Helper functions for data formatting
function formatSubjectForLocal(subject) {
  return {
    id: subject.id,
    title: subject.title,
    description: subject.description || '',
    color: subject.color,
    icon: subject.icon,
    user_id: subject.user_id,
    created_at: new Date(subject.created_at).getTime(),
    // A tabela subjects pode não ter coluna updated_at, então usamos created_at
    updated_at: new Date(subject.created_at).getTime(),
    synced: true,
  };
}

function formatSubjectForRemote(subject) {
  return {
    id: subject.id,
    title: subject.title,
    description: subject.description,
    color: subject.color,
    icon: subject.icon,
    user_id: subject.userId || subject.user_id,
    created_at: new Date(subject.createdAt || subject.created_at).toISOString(),
    // Não incluímos updated_at pois a tabela pode não ter essa coluna
  };
}

function formatActivityForLocal(activity) {
  return {
    id: activity.id,
    title: activity.title,
    description: activity.description || '',
    type: activity.activity_type || activity.type,
    subject_id: activity.subject_id,
    user_id: activity.user_id,
    created_at: new Date(activity.created_at).getTime(),
    // A tabela activities não tem coluna updated_at, então usamos created_at
    updated_at: new Date(activity.created_at).getTime(),
    synced: true,
  };
}

function formatActivityForRemote(activity) {
  return {
    id: activity.id,
    title: activity.title,
    description: activity.description,
    activity_type: activity.type, // Coluna correta no Supabase é activity_type
    subject_id: activity.subjectId || activity.subject_id,
    user_id: activity.userId || activity.user_id,
    created_at: new Date(activity.createdAt || activity.created_at).toISOString(),
    // Não incluímos updated_at pois a tabela não tem essa coluna
  };
}

// Helper functions for sync timestamp management
async function getLastSyncTimestamp() {
  try {
    const timestamp = await AsyncStorage.getItem('lastSyncTimestamp');
    return timestamp ? parseInt(timestamp, 10) : 0;
  } catch (error) {
    console.error('Error getting last sync timestamp:', error);
    return 0;
  }
}

async function setLastSyncTimestamp(timestamp) {
  try {
    await AsyncStorage.setItem('lastSyncTimestamp', timestamp.toString());
  } catch (error) {
    console.error('Error setting last sync timestamp:', error);
  }
}
