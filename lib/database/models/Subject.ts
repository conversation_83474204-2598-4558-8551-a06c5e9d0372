import { Model } from '@nozbe/watermelondb';
import { field, date, readonly, text, relation, children } from '@nozbe/watermelondb/decorators';

export default class Subject extends Model {
  static table = 'subjects';

  @text('title') title;
  @text('description') description;
  @text('color') color;
  @text('icon') icon;
  @text('user_id') userId;
  @readonly @date('created_at') createdAt;
  @readonly @date('updated_at') updatedAt;
  @field('synced') synced;

  @children('activities') activities;
  @children('flashcard_sets') flashcardSets;
  @children('quizzes') quizzes;
}
