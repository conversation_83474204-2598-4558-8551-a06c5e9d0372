import { Model } from '@nozbe/watermelondb';
import { field, date, readonly, text, relation } from '@nozbe/watermelondb/decorators';

export default class Activity extends Model {
  static table = 'activities';

  @text('title') title;
  @text('description') description;
  @text('type') type;
  @text('subject_id') subjectId;
  @text('user_id') userId;
  @readonly @date('created_at') createdAt;
  @readonly @date('updated_at') updatedAt;
  @field('synced') synced;

  @relation('subjects', 'subject_id') subject;
}
