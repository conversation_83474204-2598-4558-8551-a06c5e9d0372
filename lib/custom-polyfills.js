/**
 * Custom polyfills for React Native
 * This file provides simplified implementations of various polyfills
 * that are needed for the app to run in Expo Go
 */

import { polyfillGlobal } from 'react-native/Libraries/Utilities/PolyfillFunctions';
import { ReadableStream, WritableStream, TransformStream } from 'web-streams-polyfill';

// Base64 polyfill
export const polyfillBase64 = () => {
  if (global.btoa === undefined) {
    global.btoa = (input) => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
      let output = '';
      let i = 0;

      while (i < input.length) {
        const chr1 = input.charCodeAt(i++);
        const chr2 = input.charCodeAt(i++);
        const chr3 = input.charCodeAt(i++);

        const enc1 = chr1 >> 2;
        const enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        const enc3 = isNaN(chr2) ? 64 : ((chr2 & 15) << 2) | (chr3 >> 6);
        const enc4 = isNaN(chr3) ? 64 : (chr3 & 63);

        output += chars.charAt(enc1) + chars.charAt(enc2) + chars.charAt(enc3) + chars.charAt(enc4);
      }

      return output;
    };
  }

  if (global.atob === undefined) {
    global.atob = (input) => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
      let output = '';
      let i = 0;

      input = input.replace(/[^A-Za-z0-9\+\/\=]/g, '');

      while (i < input.length) {
        const enc1 = chars.indexOf(input.charAt(i++));
        const enc2 = chars.indexOf(input.charAt(i++));
        const enc3 = chars.indexOf(input.charAt(i++));
        const enc4 = chars.indexOf(input.charAt(i++));

        const chr1 = (enc1 << 2) | (enc2 >> 4);
        const chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        const chr3 = ((enc3 & 3) << 6) | enc4;

        output += String.fromCharCode(chr1);

        if (enc3 !== 64) {
          output += String.fromCharCode(chr2);
        }
        if (enc4 !== 64) {
          output += String.fromCharCode(chr3);
        }
      }

      return output;
    };
  }
};

// TextEncoder and TextDecoder polyfill
export const polyfillEncoding = () => {
  try {
    const { TextEncoder, TextDecoder } = require('text-encoding');

    if (!global.TextEncoder) {
      polyfillGlobal('TextEncoder', () => TextEncoder);
    }

    if (!global.TextDecoder) {
      polyfillGlobal('TextDecoder', () => TextDecoder);
    }
  } catch (e) {
    console.warn('Failed to polyfill TextEncoder/TextDecoder:', e);
  }
};

// URL polyfill
export const polyfillURL = () => {
  // URL and URLSearchParams are already available in React Native
};

// ReadableStream polyfill
export const polyfillReadableStream = () => {
  if (!global.ReadableStream) {
    polyfillGlobal('ReadableStream', () => ReadableStream);
  }

  if (!global.WritableStream) {
    polyfillGlobal('WritableStream', () => WritableStream);
  }

  if (!global.TransformStream) {
    polyfillGlobal('TransformStream', () => TransformStream);
  }
};

// WebSocket polyfill
export const polyfillWebSocket = () => {
  // Ensure the net module is properly mocked for WebSocket
  if (!global.net) {
    const { net } = require('./ws-polyfill');
    global.net = net;
  }

  // Ensure the tls module is properly mocked for WebSocket
  if (!global.tls) {
    const { tls } = require('./ws-polyfill');
    global.tls = tls;
  }

  // Ensure the http module is properly mocked for WebSocket
  if (!global.http) {
    const { http } = require('./ws-polyfill');
    global.http = http;
  }

  // Ensure the https module is properly mocked for WebSocket
  if (!global.https) {
    const { https } = require('./ws-polyfill');
    global.https = https;
  }

  // Ensure the stream module is properly polyfilled for WebSocket
  if (!global.stream) {
    const streamPolyfill = require('./stream-polyfill').default;
    global.stream = streamPolyfill;
  }
};

// Apply all polyfills
export const applyAllPolyfills = () => {
  polyfillBase64();
  polyfillEncoding();
  polyfillURL();
  polyfillReadableStream();
  polyfillWebSocket();
};
