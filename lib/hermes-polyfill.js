/**
 * Hermes-specific polyfills
 * This file contains polyfills for functions that are missing in the Hermes JS engine
 */

// Polyfill for require.resolve
if (typeof global !== 'undefined') {
  // Create a mock require object if it doesn't exist
  if (!global.require) {
    global.require = function(moduleName) {
      throw new Error(`Cannot find module '${moduleName}'`);
    };
  }
  
  // Add resolve function to require if it doesn't exist
  if (typeof global.require === 'function' && !global.require.resolve) {
    global.require.resolve = function(moduleName) {
      return moduleName;
    };
  }
}

// Export the polyfill function for explicit usage
export function applyHermesPolyfills() {
  if (typeof global !== 'undefined') {
    // Create a mock require object if it doesn't exist
    if (!global.require) {
      global.require = function(moduleName) {
        throw new Error(`Cannot find module '${moduleName}'`);
      };
    }
    
    // Add resolve function to require if it doesn't exist
    if (typeof global.require === 'function' && !global.require.resolve) {
      global.require.resolve = function(moduleName) {
        return moduleName;
      };
    }
  }
}
