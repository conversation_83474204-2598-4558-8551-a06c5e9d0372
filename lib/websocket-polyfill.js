/**
 * Este arquivo fornece polyfills específicos para o WebSocket
 * Ele deve ser importado no início do aplicativo
 */

// Polyfill para o módulo 'net' usado pelo WebSocket
class MockSocket {
  constructor(options) {
    this.options = options || {};
    this.connecting = false;
    this.destroyed = false;
    this._events = {};
  }

  connect() {
    this.connecting = true;
    setTimeout(() => {
      this.connecting = false;
      this.emit('connect');
    }, 0);
    return this;
  }

  on(event, callback) {
    this._events[event] = this._events[event] || [];
    this._events[event].push(callback);
    return this;
  }

  emit(event, ...args) {
    if (this._events[event]) {
      this._events[event].forEach(callback => callback(...args));
    }
    return this;
  }

  setNoDelay() { return this; }
  setKeepAlive() { return this; }
  setTimeout() { return this; }
  destroy() {
    this.destroyed = true;
    return this;
  }
  end() { return this; }
  write() { return true; }
}

// Mock para o módulo 'net'
const net = {
  Socket: MockSocket,
  createConnection: function() {
    return new MockSocket();
  }
};

// Mock para o módulo 'tls'
const tls = {
  connect: function() {
    const socket = new MockSocket();
    return socket.connect();
  },
  TLSSocket: class MockTLSSocket extends MockSocket {
    constructor(options) {
      super(options);
      this.authorized = true;
    }
  }
};

// Mock para o módulo 'http'
const http = {
  createServer: function() {
    return {
      listen: function() { return this; },
      on: function() { return this; },
      close: function() { return this; }
    };
  },
  get: function() {
    return {
      on: function() { return this; },
      end: function() {}
    };
  }
};

// Mock para o módulo 'https'
const https = {
  createServer: function() {
    return http.createServer();
  },
  get: function() {
    return http.get();
  }
};

// Aplicar os polyfills globalmente
export const applyWebSocketPolyfills = () => {
  if (typeof global !== 'undefined') {
    // Verificar se os objetos já existem antes de atribuir
    if (!global.net) global.net = net;
    if (!global.tls) global.tls = tls;
    if (!global.http) global.http = http;
    if (!global.https) global.https = https;
  }
};

// Exportar os mocks
export { net, tls, http, https };

// Aplicar automaticamente os polyfills
applyWebSocketPolyfills();
