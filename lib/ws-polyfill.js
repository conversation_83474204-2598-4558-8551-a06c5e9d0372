/**
 * This file provides mock implementations for Node.js modules required by the ws package
 * These are minimal implementations that allow the package to load but may not provide full functionality
 */

// Mock net module
const net = {
  Socket: class MockSocket extends require('events').EventEmitter {
    constructor(options) {
      super();
      this.options = options || {};
      this.connecting = false;
      this.destroyed = false;
    }
    
    connect() {
      this.connecting = true;
      setTimeout(() => {
        this.connecting = false;
        this.emit('connect');
      }, 0);
      return this;
    }
    
    setNoDelay() { return this; }
    setKeepAlive() { return this; }
    setTimeout() { return this; }
    destroy() { 
      this.destroyed = true;
      return this;
    }
    end() { return this; }
    write() { return true; }
  },
  createConnection: function() {
    return new net.Socket();
  }
};

// Mock tls module
const tls = {
  connect: function() {
    const socket = new net.Socket();
    return socket.connect();
  },
  TLSSocket: class MockTLSSocket extends net.Socket {
    constructor(options) {
      super(options);
      this.authorized = true;
    }
  }
};

// Mock http module
const http = {
  createServer: function() {
    return {
      listen: function() { return this; },
      on: function() { return this; },
      close: function() { return this; }
    };
  },
  get: function() {
    return {
      on: function() { return this; },
      end: function() {}
    };
  }
};

// Mock https module
const https = {
  createServer: function() {
    return http.createServer();
  },
  get: function() {
    return http.get();
  }
};

// Export the mocks
export { net, tls, http, https };
