/**
 * Specific polyfill for the ws module's stream usage
 * This file provides a compatibility layer for the ws module
 * which is used by Supabase and other libraries
 */

import stream from './stream-polyfill';

// Create a specific implementation for the ws module
const wsStreamPolyfill = {
  ...stream,
  
  // Add any ws-specific stream methods or properties here
  Duplex: class WSDuplex extends stream.Duplex {
    constructor(options) {
      super(options);
      // Add any ws-specific initialization
    }
  }
};

// Apply the ws-specific stream polyfill
export const applyWSStreamPolyfill = () => {
  // Make the polyfill available globally
  if (typeof global !== 'undefined') {
    // Ensure the ws module can find the stream implementation it needs
    if (!global._streamPolyfill) {
      global._streamPolyfill = wsStreamPolyfill;
    }
    
    // Patch the require function to return our polyfill for the 'stream' module
    // This is a bit hacky but necessary for some modules that use direct require('stream')
    const originalRequire = global.require;
    if (originalRequire && !global._requirePatched) {
      global._requirePatched = true;
      global.require = function(name) {
        if (name === 'stream') {
          return wsStreamPolyfill;
        }
        return originalRequire(name);
      };
    }
  }
};

// Apply the polyfill immediately
applyWSStreamPolyfill();

export default wsStreamPolyfill;
