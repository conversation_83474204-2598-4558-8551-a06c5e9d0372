// Import Node.js polyfills first
import './node-libs-browser';

// Import comprehensive polyfills
import './react-native-polyfills';

// Import WebSocket polyfills
import './websocket-polyfill';

// Import stream polyfills
import './stream-polyfill';

// Import ws-specific stream polyfill
import './ws-stream-polyfill';

// Import our custom polyfills implementation
import { applyAllPolyfills } from './custom-polyfills';
import { applyRequireResolvePolyfill } from './require-resolve-polyfill';

// Apply all the polyfills
applyAllPolyfills();

// Apply require.resolve polyfill
applyRequireResolvePolyfill();

// This file should be imported early in your app's entry point
