# Configuração do Banco de Dados Supabase para o Lia App

Este documento contém instruções para configurar o banco de dados Supabase para o aplicativo Lia.

## Tabelas Necessárias

O aplicativo requer as seguintes tabelas no Supabase:

1. `quizzes` - Armazena informações sobre os quizzes
2. `quiz_questions` - Armazena as perguntas dos quizzes
3. `quiz_attempts` - Armazena as tentativas de quiz dos usuários

## Estrutura das Tabelas

### Tabela `quizzes`

| Coluna | Tipo | Descrição |
|--------|------|------------|
| id | UUID | Identificador único do quiz |
| user_id | UUID | Referência ao usuário que criou o quiz |
| subject_id | UUID | Referência à tabela subjects |
| title | TEXT | Título do quiz |
| description | TEXT | Descrição do quiz |
| created_at | TIMESTAMP | Data de criação do quiz |
| updated_at | TIMESTAMP | Data de atualização do quiz |
| last_attempt | TIMESTAMP | Data da última tentativa do quiz |
| best_score | NUMERIC | Melhor pontuação obtida no quiz |
| time_limit | INTEGER | Limite de tempo para completar o quiz (em segundos) |

### Tabela `quiz_questions`

| Coluna | Tipo | Descrição |
|--------|------|------------|
| id | UUID | Identificador único da pergunta |
| quiz_id | UUID | Referência ao quiz |
| question | TEXT | Texto da pergunta |
| options | JSONB | Opções de resposta |
| correct_option | INTEGER | Índice da opção correta |
| explanation | TEXT | Explicação da resposta |
| created_at | TIMESTAMP | Data de criação da pergunta |
| updated_at | TIMESTAMP | Data de atualização da pergunta |

### Tabela `quiz_attempts`

| Coluna | Tipo | Descrição |
|--------|------|------------|
| id | UUID | Identificador único da tentativa |
| quiz_id | UUID | Referência ao quiz |
| user_id | UUID | Referência ao usuário que fez a tentativa |
| score | INTEGER | Pontuação obtida |
| total_questions | INTEGER | Número total de perguntas |
| time_spent | INTEGER | Tempo gasto (em segundos) |
| date | TIMESTAMP | Data da tentativa |
| answers | JSONB | Respostas dadas pelo usuário |
| created_at | TIMESTAMP | Data de criação do registro |

## SQL para Criar as Tabelas

```sql
-- Tabela de quizzes
CREATE TABLE IF NOT EXISTS public.quizzes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    subject_id UUID REFERENCES public.subjects(id),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_attempt TIMESTAMP WITH TIME ZONE,
    best_score NUMERIC,
    time_limit INTEGER
);

-- Tabela de perguntas de quiz
CREATE TABLE IF NOT EXISTS public.quiz_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quiz_id UUID REFERENCES public.quizzes(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    options JSONB NOT NULL,
    correct_option INTEGER NOT NULL,
    explanation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de tentativas de quiz
CREATE TABLE IF NOT EXISTS public.quiz_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quiz_id UUID REFERENCES public.quizzes(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    score INTEGER NOT NULL,
    total_questions INTEGER NOT NULL,
    time_spent INTEGER,
    date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    answers JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Políticas de Segurança RLS (Row Level Security)

Para garantir que os usuários só possam acessar seus próprios dados, é necessário configurar políticas de segurança RLS:

```sql
-- Políticas para quizzes
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Usuários podem ver seus próprios quizzes"
    ON public.quizzes FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem inserir seus próprios quizzes"
    ON public.quizzes FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem atualizar seus próprios quizzes"
    ON public.quizzes FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem excluir seus próprios quizzes"
    ON public.quizzes FOR DELETE
    USING (auth.uid() = user_id);

-- Políticas para perguntas de quiz
ALTER TABLE public.quiz_questions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Usuários podem ver perguntas de seus próprios quizzes"
    ON public.quiz_questions FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.quizzes
        WHERE quizzes.id = quiz_questions.quiz_id
        AND quizzes.user_id = auth.uid()
    ));

CREATE POLICY "Usuários podem inserir perguntas em seus próprios quizzes"
    ON public.quiz_questions FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.quizzes
        WHERE quizzes.id = quiz_questions.quiz_id
        AND quizzes.user_id = auth.uid()
    ));

CREATE POLICY "Usuários podem atualizar perguntas de seus próprios quizzes"
    ON public.quiz_questions FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.quizzes
        WHERE quizzes.id = quiz_questions.quiz_id
        AND quizzes.user_id = auth.uid()
    ));

CREATE POLICY "Usuários podem excluir perguntas de seus próprios quizzes"
    ON public.quiz_questions FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.quizzes
        WHERE quizzes.id = quiz_questions.quiz_id
        AND quizzes.user_id = auth.uid()
    ));

-- Políticas para tentativas de quiz
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Usuários podem ver suas próprias tentativas de quiz"
    ON public.quiz_attempts FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem inserir suas próprias tentativas de quiz"
    ON public.quiz_attempts FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem atualizar suas próprias tentativas de quiz"
    ON public.quiz_attempts FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem excluir suas próprias tentativas de quiz"
    ON public.quiz_attempts FOR DELETE
    USING (auth.uid() = user_id);
```

## Índices para Melhorar o Desempenho

```sql
CREATE INDEX IF NOT EXISTS idx_quizzes_user_id ON public.quizzes(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_quiz_id ON public.quiz_questions(quiz_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_quiz_id ON public.quiz_attempts(quiz_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_user_id ON public.quiz_attempts(user_id);
```

## Verificação das Tabelas

Para verificar se as tabelas existem e têm a estrutura correta, você pode executar as seguintes consultas:

```sql
-- Verificar se a tabela quizzes existe
SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quizzes');

-- Verificar a estrutura da tabela quizzes
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'quizzes';

-- Verificar se a tabela quiz_questions existe
SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quiz_questions');

-- Verificar se a tabela quiz_attempts existe
SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quiz_attempts');
```

## Solução de Problemas

Se você encontrar erros relacionados a colunas ausentes, verifique se as tabelas foram criadas corretamente no Supabase. Os erros mais comuns são:

1. `Could not find the 'time_limit' column of 'quizzes'` - A coluna não existe ou tem um nome diferente
2. `Error adding quiz attempt` - A tabela `quiz_attempts` não existe ou tem uma estrutura diferente

Nestes casos, execute o script SQL para criar as tabelas novamente.
