export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: string
          created_at: string | null
          description: string | null
          id: string
          related_id: string | null
          subject_id: string | null
          title: string
          user_id: string
          xp_earned: number | null
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          description?: string | null
          id?: string
          related_id?: string | null
          subject_id?: string | null
          title: string
          user_id: string
          xp_earned?: number | null
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          description?: string | null
          id?: string
          related_id?: string | null
          subject_id?: string | null
          title?: string
          user_id?: string
          xp_earned?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_conversations: {
        Row: {
          created_at: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_conversations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_messages: {
        Row: {
          content: string
          conversation_id: string
          created_at: string | null
          id: string
          role: string
        }
        Insert: {
          content: string
          conversation_id: string
          created_at?: string | null
          id?: string
          role: string
        }
        Update: {
          content?: string
          conversation_id?: string
          created_at?: string | null
          id?: string
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "ai_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      calendar_events: {
        Row: {
          all_day: boolean | null
          color: string | null
          completed: boolean | null
          created_at: string | null
          description: string | null
          end_date: string | null
          has_todos: boolean | null
          id: string
          recurrence: string | null
          recurrence_end_date: string | null
          recurrence_settings: Json | null
          reminder: boolean | null
          reminder_time: string | null
          schedule_id: string | null
          start_date: string
          subject: string | null
          subject_id: string | null
          title: string
          type: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          all_day?: boolean | null
          color?: string | null
          completed?: boolean | null
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          has_todos?: boolean | null
          id?: string
          recurrence?: string | null
          recurrence_end_date?: string | null
          recurrence_settings?: Json | null
          reminder?: boolean | null
          reminder_time?: string | null
          schedule_id?: string | null
          start_date: string
          subject?: string | null
          subject_id?: string | null
          title: string
          type?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          all_day?: boolean | null
          color?: string | null
          completed?: boolean | null
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          has_todos?: boolean | null
          id?: string
          recurrence?: string | null
          recurrence_end_date?: string | null
          recurrence_settings?: Json | null
          reminder?: boolean | null
          reminder_time?: string | null
          schedule_id?: string | null
          start_date?: string
          subject?: string | null
          subject_id?: string | null
          title?: string
          type?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "calendar_events_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "schedules"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "calendar_events_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcard_sets: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          last_studied: string | null
          subject_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          last_studied?: string | null
          subject_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          last_studied?: string | null
          subject_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flashcard_sets_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcard_sets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      flashcards: {
        Row: {
          back: string
          created_at: string | null
          difficulty: number | null
          e_factor: number | null
          front: string
          id: string
          image_url: string | null
          last_reviewed: string | null
          next_review: string | null
          review_count: number | null
          set_id: string
          subject_id: string | null
          updated_at: string | null
        }
        Insert: {
          back: string
          created_at?: string | null
          difficulty?: number | null
          e_factor?: number | null
          front: string
          id?: string
          image_url?: string | null
          last_reviewed?: string | null
          next_review?: string | null
          review_count?: number | null
          set_id: string
          subject_id?: string | null
          updated_at?: string | null
        }
        Update: {
          back?: string
          created_at?: string | null
          difficulty?: number | null
          e_factor?: number | null
          front?: string
          id?: string
          image_url?: string | null
          last_reviewed?: string | null
          next_review?: string | null
          review_count?: number | null
          set_id?: string
          subject_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "flashcards_set_id_fkey"
            columns: ["set_id"]
            isOneToOne: false
            referencedRelation: "flashcard_sets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flashcards_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      mind_maps: {
        Row: {
          color: string | null
          connections: Json | null
          content: Json
          created_at: string | null
          description: string | null
          id: string
          nodes: Json | null
          subject_id: string | null
          title: string
          type: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          connections?: Json | null
          content?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          nodes?: Json | null
          subject_id?: string | null
          title: string
          type?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          connections?: Json | null
          content?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          nodes?: Json | null
          subject_id?: string | null
          title?: string
          type?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "mind_maps_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mind_maps_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      notes: {
        Row: {
          attachments: Json | null
          blocks: Json | null
          content: Json
          created_at: string | null
          id: string
          subject_id: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          attachments?: Json | null
          blocks?: Json | null
          content?: Json
          created_at?: string | null
          id?: string
          subject_id?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          attachments?: Json | null
          blocks?: Json | null
          content?: Json
          created_at?: string | null
          id?: string
          subject_id?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notes_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_attempts: {
        Row: {
          answers: Json | null
          created_at: string | null
          date: string | null
          id: string
          quiz_id: string
          score: number
          time_spent: number | null
          total_questions: number
          user_id: string
        }
        Insert: {
          answers?: Json | null
          created_at?: string | null
          date?: string | null
          id?: string
          quiz_id: string
          score: number
          time_spent?: number | null
          total_questions: number
          user_id: string
        }
        Update: {
          answers?: Json | null
          created_at?: string | null
          date?: string | null
          id?: string
          quiz_id?: string
          score?: number
          time_spent?: number | null
          total_questions?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "quiz_attempts_quiz_id_fkey"
            columns: ["quiz_id"]
            isOneToOne: false
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_questions: {
        Row: {
          correct_option: number
          created_at: string | null
          explanation: string | null
          id: string
          options: string[]
          question: string
          quiz_id: string
          updated_at: string | null
        }
        Insert: {
          correct_option: number
          created_at?: string | null
          explanation?: string | null
          id?: string
          options: string[]
          question: string
          quiz_id: string
          updated_at?: string | null
        }
        Update: {
          correct_option?: number
          created_at?: string | null
          explanation?: string | null
          id?: string
          options?: string[]
          question?: string
          quiz_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quiz_questions_quiz_id_fkey"
            columns: ["quiz_id"]
            isOneToOne: false
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          },
        ]
      }
      quizzes: {
        Row: {
          best_score: number | null
          created_at: string | null
          description: string | null
          id: string
          last_attempt: string | null
          subject_id: string | null
          time_limit: number | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          best_score?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          last_attempt?: string | null
          subject_id?: string | null
          time_limit?: number | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          best_score?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          last_attempt?: string | null
          subject_id?: string | null
          time_limit?: number | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "quizzes_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quizzes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      schedule_items: {
        Row: {
          color: string | null
          create_event: boolean | null
          create_todo: boolean | null
          created_at: string | null
          day_of_month: number | null
          day_of_week: number | null
          end_time: string
          icon: string | null
          id: string
          order: number | null
          schedule_id: string
          specific_date: string | null
          start_time: string
          subject_id: string | null
          subject_title: string
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          create_event?: boolean | null
          create_todo?: boolean | null
          created_at?: string | null
          day_of_month?: number | null
          day_of_week?: number | null
          end_time: string
          icon?: string | null
          id?: string
          order?: number | null
          schedule_id: string
          specific_date?: string | null
          start_time: string
          subject_id?: string | null
          subject_title: string
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          create_event?: boolean | null
          create_todo?: boolean | null
          created_at?: string | null
          day_of_month?: number | null
          day_of_week?: number | null
          end_time?: string
          icon?: string | null
          id?: string
          order?: number | null
          schedule_id?: string
          specific_date?: string | null
          start_time?: string
          subject_id?: string | null
          subject_title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "schedule_items_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "schedules"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedule_items_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      schedules: {
        Row: {
          active: boolean | null
          created_at: string | null
          description: string | null
          end_date: string | null
          id: string
          repeat_monthly: boolean | null
          repeat_weekly: boolean | null
          settings: Json | null
          start_date: string
          title: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          id?: string
          repeat_monthly?: boolean | null
          repeat_weekly?: boolean | null
          settings?: Json | null
          start_date: string
          title: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          id?: string
          repeat_monthly?: boolean | null
          repeat_weekly?: boolean | null
          settings?: Json | null
          start_date?: string
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      separators: {
        Row: {
          color: string
          created_at: string | null
          description: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color: string
          created_at?: string | null
          description?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string
          created_at?: string | null
          description?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      study_group_invites: {
        Row: {
          created_at: string | null
          expires_at: string | null
          group_id: string
          id: string
          invitee_email: string
          inviter_id: string
          status: string | null
        }
        Insert: {
          created_at?: string | null
          expires_at?: string | null
          group_id: string
          id?: string
          invitee_email: string
          inviter_id: string
          status?: string | null
        }
        Update: {
          created_at?: string | null
          expires_at?: string | null
          group_id?: string
          id?: string
          invitee_email?: string
          inviter_id?: string
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_invites_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_materials: {
        Row: {
          content: Json | null
          created_at: string | null
          description: string | null
          file_url: string | null
          group_id: string
          id: string
          title: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content?: Json | null
          created_at?: string | null
          description?: string | null
          file_url?: string | null
          group_id: string
          id?: string
          title: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: Json | null
          created_at?: string | null
          description?: string | null
          file_url?: string | null
          group_id?: string
          id?: string
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_group_materials_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_members: {
        Row: {
          flashcards_created: number | null
          group_id: string
          id: string
          joined_at: string | null
          role: string | null
          study_time_minutes: number | null
          user_id: string
        }
        Insert: {
          flashcards_created?: number | null
          group_id: string
          id?: string
          joined_at?: string | null
          role?: string | null
          study_time_minutes?: number | null
          user_id: string
        }
        Update: {
          flashcards_created?: number | null
          group_id?: string
          id?: string
          joined_at?: string | null
          role?: string | null
          study_time_minutes?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      study_groups: {
        Row: {
          admin_id: string
          cover_image: string | null
          created_at: string | null
          description: string | null
          id: string
          invite_code: string | null
          is_open: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          admin_id: string
          cover_image?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          invite_code?: string | null
          is_open?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          admin_id?: string
          cover_image?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          invite_code?: string | null
          is_open?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      study_sessions: {
        Row: {
          created_at: string | null
          date: string
          id: string
          sessions_completed: number
          total_time: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          date: string
          id?: string
          sessions_completed?: number
          total_time?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          date?: string
          id?: string
          sessions_completed?: number
          total_time?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      subjects: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          icon: string | null
          id: string
          separator_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          separator_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          separator_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subjects_separator_id_fkey"
            columns: ["separator_id"]
            isOneToOne: false
            referencedRelation: "separators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subjects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      todos: {
        Row: {
          completed: boolean | null
          created_at: string | null
          description: string | null
          due_date: string | null
          event_id: string | null
          id: string
          priority: string | null
          recurrence: string | null
          recurrence_end_date: string | null
          recurrence_settings: Json | null
          reminder_time: string | null
          schedule_id: string | null
          subject: string | null
          subject_id: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          completed?: boolean | null
          created_at?: string | null
          description?: string | null
          due_date?: string | null
          event_id?: string | null
          id?: string
          priority?: string | null
          recurrence?: string | null
          recurrence_end_date?: string | null
          recurrence_settings?: Json | null
          reminder_time?: string | null
          schedule_id?: string | null
          subject?: string | null
          subject_id?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          completed?: boolean | null
          created_at?: string | null
          description?: string | null
          due_date?: string | null
          event_id?: string | null
          id?: string
          priority?: string | null
          recurrence?: string | null
          recurrence_end_date?: string | null
          recurrence_settings?: Json | null
          reminder_time?: string | null
          schedule_id?: string | null
          subject?: string | null
          subject_id?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "todos_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "calendar_events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "todos_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "schedules"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "todos_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          id: string
          is_premium: boolean
          last_streak_date: string | null
          level: number
          name: string
          streak: number
          total_study_time: number
          updated_at: string | null
          xp: number
          xp_to_next_level: number
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          id: string
          is_premium?: boolean
          last_streak_date?: string | null
          level?: number
          name: string
          streak?: number
          total_study_time?: number
          updated_at?: string | null
          xp?: number
          xp_to_next_level?: number
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          id?: string
          is_premium?: boolean
          last_streak_date?: string | null
          level?: number
          name?: string
          streak?: number
          total_study_time?: number
          updated_at?: string | null
          xp?: number
          xp_to_next_level?: number
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_quiz_attempts_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_quiz_questions_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_quizzes_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
