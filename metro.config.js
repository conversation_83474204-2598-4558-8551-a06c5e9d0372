// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');
const { resolver: { sourceExts, assetExts } } = getDefaultConfig(__dirname);
const nodeLibs = require('node-libs-react-native');
const path = require('path');

// Import and apply polyfills
require('./metro-polyfill').applyPolyfills();

const config = getDefaultConfig(__dirname);

// Add polyfills for Node.js core modules
config.resolver.extraNodeModules = {
  ...nodeLibs,
  // Additional polyfills
  'text-encoding': 'text-encoding',
  'web-streams-polyfill': 'web-streams-polyfill',
  // Specific polyfills for WebSocket and other Node.js modules
  'stream': path.resolve(__dirname, './lib/stream-polyfill.js'),
  'buffer': 'buffer',
  'process': 'process/browser',
  'events': 'events',
  // Mock modules for WebSocket
  'net': path.resolve(__dirname, './lib/ws-polyfill.js'),
  'tls': path.resolve(__dirname, './lib/ws-polyfill.js'),
  'http': path.resolve(__dirname, './lib/ws-polyfill.js'),
  'https': path.resolve(__dirname, './lib/ws-polyfill.js'),
};

// Make sure to include all file extensions your project uses
config.resolver.sourceExts = [...sourceExts, 'mjs', 'cjs'];

// Ensure that import/require works for packages that use Node.js modules
config.resolver.assetExts = assetExts.filter(ext => ext !== 'svg');
config.resolver.sourceExts = [...sourceExts, 'svg'];

// Add additional configuration for better module resolution
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

module.exports = config;
