import React, { createContext, useState, useEffect, useContext } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { Alert } from 'react-native';
import { router } from 'expo-router';

// Definir o tipo para o contexto
type AuthContextType = {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isAuthenticated: boolean;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  requireAuth: () => void;
  checkAuthStatus: () => Promise<boolean>;
};

// Criar o contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook personalizado para usar o contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  // Verificação adicional de segurança
  if (!context.loading && !context.user && !context.session) {
    console.warn('Usuário não autenticado detectado');
  }

  return context;
};

// Provedor do contexto
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // Computed property para verificar autenticação
  const isAuthenticated = Boolean(user && session && user.id);

  useEffect(() => {
    // Verificar se há uma sessão ativa
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Configurar listener para mudanças na autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Função para cadastro
  const signUp = async (email: string, password: string, name: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) throw error;
      Alert.alert('Cadastro realizado!', 'Verifique seu e-mail para confirmar o cadastro.');
    } catch (error: any) {
      Alert.alert('Erro no cadastro', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Função para login
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      router.replace('/(tabs)');
    } catch (error: any) {
      Alert.alert('Erro no login', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Função para logout
  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      router.replace('/login');
    } catch (error: any) {
      Alert.alert('Erro ao sair', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Função para login com Google
  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'com.liastudyapp://login-callback/',
        },
      });
      if (error) throw error;
    } catch (error: any) {
      Alert.alert('Erro no login com Google', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Função para login com Apple
  const signInWithApple = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: 'com.liastudyapp://login-callback/',
        },
      });
      if (error) throw error;
    } catch (error: any) {
      Alert.alert('Erro no login com Apple', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Função para redefinir senha
  const resetPassword = async (email: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'com.liastudyapp://reset-password/',
      });
      if (error) throw error;
      Alert.alert('E-mail enviado', 'Verifique seu e-mail para redefinir sua senha.');
    } catch (error: any) {
      Alert.alert('Erro ao redefinir senha', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Função para exigir autenticação
  const requireAuth = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Acesso Negado',
        'Você precisa estar logado para acessar esta funcionalidade.',
        [
          {
            text: 'Fazer Login',
            onPress: () => router.replace('/login'),
          },
          {
            text: 'Cancelar',
            style: 'cancel',
          },
        ]
      );
      throw new Error('Usuário não autenticado');
    }
  };

  // Função para verificar status de autenticação
  const checkAuthStatus = async (): Promise<boolean> => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Erro ao verificar status de autenticação:', error);
        return false;
      }

      const isValid = Boolean(session && session.user && session.user.id);

      if (!isValid && !loading) {
        console.warn('Sessão inválida detectada');
        setUser(null);
        setSession(null);
      }

      return isValid;
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      return false;
    }
  };

  const value = {
    user,
    session,
    loading,
    isAuthenticated,
    signUp,
    signIn,
    signOut,
    signInWithGoogle,
    signInWithApple,
    resetPassword,
    requireAuth,
    checkAuthStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
