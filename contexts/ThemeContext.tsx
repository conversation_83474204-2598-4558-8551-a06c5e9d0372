import React, { createContext, useState, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { theme as lightTheme } from '@/constants/theme';

// Criar tema escuro baseado no tema claro
const darkTheme = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    // Cores primárias
    primary: "#3399FF", // Mantém a mesma cor primária
    primaryLight: "#1E88E5",
    primaryDark: "#0D47A1",
    
    // Cores de texto
    text: "#F9FAFB",
    textLight: "#D1D5DB",
    textMedium: "#9CA3AF",
    textDark: "#F3F4F6",
    
    // Cores de fundo
    background: "#121212",
    backgroundLight: "#1E1E1E",
    backgroundDark: "#0A0A0A",
    backgroundGradient: ["#1E1E1E", "#121212"] as const,
    
    // Cores de UI
    border: "#333333",
    borderDark: "#444444",
    card: "#1E1E1E",
    divider: "#333333",
    
    // Efeitos
    glass: "rgba(30, 30, 30, 0.8)",
    glassDark: "rgba(18, 18, 18, 0.6)",
    shadow: "rgba(0, 0, 0, 0.3)",
    overlay: "rgba(0, 0, 0, 0.7)",
  },
  shadows: {
    none: {
      shadowColor: 'transparent',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.5,
      shadowRadius: 8,
      elevation: 8,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.6,
      shadowRadius: 16,
      elevation: 16,
    },
  },
};

// Definir tipos para o contexto
type ThemeMode = 'light' | 'dark' | 'system';
type ThemeContextType = {
  theme: typeof lightTheme;
  themeMode: ThemeMode;
  isDark: boolean;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
};

// Criar o contexto
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Hook personalizado para usar o contexto
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Chave para armazenar o tema no AsyncStorage
const THEME_STORAGE_KEY = '@lia_theme_mode';

// Provedor do contexto
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Obter o esquema de cores do sistema
  const systemColorScheme = useColorScheme();
  
  // Estado para o modo de tema
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  
  // Determinar se o tema atual é escuro
  const isDark = themeMode === 'dark' || (themeMode === 'system' && systemColorScheme === 'dark');
  
  // Selecionar o tema com base no modo
  const theme = isDark ? darkTheme : lightTheme;

  // Carregar o tema salvo ao iniciar
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme) {
          setThemeModeState(savedTheme as ThemeMode);
        }
      } catch (error) {
        console.error('Erro ao carregar o tema:', error);
      }
    };
    
    loadTheme();
  }, []);

  // Função para definir o modo de tema
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
      setThemeModeState(mode);
    } catch (error) {
      console.error('Erro ao salvar o tema:', error);
    }
  };

  // Função para alternar entre temas claro e escuro
  const toggleTheme = () => {
    const newMode = themeMode === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);
  };

  // Valor do contexto
  const value = {
    theme,
    themeMode,
    isDark,
    setThemeMode,
    toggleTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

export default ThemeProvider;
